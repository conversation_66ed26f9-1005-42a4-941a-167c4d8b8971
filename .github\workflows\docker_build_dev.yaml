name: Service Docker Build

on:
  push:
    branches:
      # - main
      - beta
    paths:
      - public/**/*
      - src/**/*
      - .yarn/**/*
      - .env.*
      - .yarnrc.yml
      - components.json
      - Dockerfile
      - next-env.d.ts
      - global.d.ts
      - new-types.d.ts
      - tailwind.config.*
      - components.json
      - next.config.js
      - postcss.config.js
      - tsconfig.json
      - package.json
      - yarn.lock
      - .github/workflows/docker_build.yaml

jobs:
  build-dev-ar:
    if: github.ref == 'refs/heads/beta'
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: doctor-app-ar-dev
      env-file: .env.development.ar
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}

  build-dev-farma:
    if: github.ref == 'refs/heads/beta'
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: doctor-app-farma-dev
      env-file: .env.development.farma
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}

  build-dev-mx:
    if: github.ref == 'refs/heads/beta'
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: doctor-app-mx-dev
      env-file: .env.development.mx
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}