name: Docker Build on Release

on:
  release:
    types:
      - published

jobs:
  build-stg-ar:
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: doctor-app-ar-stg
      env-file: .env.staging.ar
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}

  build-stg-farma:
    needs: build-stg-ar
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: doctor-app-farma-stg
      env-file: .env.staging.farma
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}

  build-stg-mx:
    needs: build-stg-ar
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: doctor-app-mx-stg
      env-file: .env.staging.mx
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}
  
  build-prd-ar:
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: doctor-app-ar-prd
      env-file: .env.production.ar
      docker-tag: ${{ github.ref_name }}
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}

  slack-notification-prd-ar:
    needs: build-prd-ar
    uses: umahealth/ci-workflows/.github/workflows/send-slack-block.yaml@main
    with:
      channel-id: C03RE892B5H
      message-header: "doctor-app-ar - New Version: ${{ github.event.release.tag_name }}"
      message-body: "${{ github.event.release.body }}"
    secrets:
      slack-token: ${{ secrets.SLACK_BOT_TOKEN }}

  build-prd-farma:
    needs: build-prd-ar
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: doctor-app-farma-prd
      env-file: .env.production.farma
      docker-tag: ${{ github.ref_name }}
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}

  build-prd-mx:
    needs: build-prd-ar
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: doctor-app-mx-prd
      env-file: .env.production.mx
      docker-tag: ${{ github.ref_name }}
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}