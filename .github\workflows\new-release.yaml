name: Release

on:
  push:
    branches:
      - main
    paths:
      - public/**/*
      - src/**/*
      - .yarn/**/*
      - .env.*
      - .yarnrc.yml
      - components.json
      - Dockerfile
      - next-env.d.ts
      - global.d.ts
      - new-types.d.ts
      - tailwind.config.*
      - components.json
      - next.config.js
      - postcss.config.js
      - tsconfig.json
      - package.json
      - yarn.lock
      - .github/workflows/new-release.yaml
      - .changeset/*.md

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    uses: umahealth/ci-workflows/.github/workflows/github-release-changeset-yarn.yaml@main
    secrets:
      github-release-token: ${{ secrets.UMA_GITHUB_PUBLISH_RELEASE_TOKEN }}
