name: WebApp Testing

on:
  pull_request:
    branches: 
      - main
      - beta
    paths:
      - src/**/*
      - public/**/*
      - package.json
      - yarn.lock
      - .github/workflows/pr_check.yaml
      - .eslintrc.json
      - .changeset/**/*

jobs:
  checks:
    uses: umahealth/ci-workflows/.github/workflows/yarn-checks.yaml@main
    with:
      node-version: 18
      lint-job: true
      build-job: true
    secrets:
      npm-token: ${{ secrets.NPM_READ_TOKEN }}