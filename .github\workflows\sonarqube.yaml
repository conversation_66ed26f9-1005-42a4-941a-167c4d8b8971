name: Sonarqube

on:
  push: 
    branches:
      - main
    paths:
      - public/**/*
      - src/**/*
      - .yarn/**/*
      - .env.*
      - .yarnrc.yml
      - components.json
      - Dockerfile
      - next-env.d.ts
      - global.d.ts
      - new-types.d.ts
      - tailwind.config.*
      - components.json
      - next.config.js
      - postcss.config.js
      - tsconfig.json
      - package.json
      - yarn.lock
      - .github/workflows/sonarqube.yaml

jobs:
  sonarqube:
    uses: umahealth/ci-workflows/.github/workflows/sonarqube.yaml@main
    with:
      sonar-host-url: ${{ vars.SONAR_HOST_URL }}
      node-version: 18
    secrets:
      sonar-token: ${{ secrets.SONAR_TOKEN }}
      sonar-project-key: ${{ secrets.SONAR_PROJECT_KEY }}