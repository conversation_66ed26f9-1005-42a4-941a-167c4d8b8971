name: Publish Storybook

on:
  push:
    branches:
      - main
    paths:
      - .storybook/**
      - src/storybook/**
      - .github/workflows/storybook.yaml

jobs:
  storybook:
    uses: umahealth/ci-workflows/.github/workflows/storybook.yaml@main
    with:
      node-version: 18
      project-name: onlinedoctor-app
    secrets:
      npm-token: ${{ secrets.NPM_READ_TOKEN }}
      chromatic-token: ${{ secrets.CHROMATIC_DOCTOR_TOKEN }}
      slack-webhook-url: ${{ secrets.SLACK_DESIGN_SYSTEM_WEBHOOK_URL }}