import type { StorybookConfig } from '@storybook/nextjs'

const config: StorybookConfig = {
	stories: [
		'../src/storybook/**/*.mdx',
		'../src/storybook/**/*.stories.@(js|jsx|mjs|ts|tsx)',
	],
	addons: [
		'@storybook/addon-links',
		'@storybook/addon-essentials',
		'@storybook/addon-onboarding',
		'@storybook/addon-interactions',
		'@storybook/addon-themes',
		'@chromatic-com/storybook',
		'msw-storybook-addon'
	],
	framework: {
		name: '@storybook/nextjs',
		options: {
			nextConfigPath: '../next.config.js',
		}
	},
	features: {
		experimentalRSC: true,
	},
	docs: {
		autodocs: true
	},
	typescript: {
		reactDocgen: 'react-docgen-typescript',
	},
	staticDirs: ['../public'],
	core: {
		disableTelemetry: true,
		enableCrashReports: false
	}
}

export default config
