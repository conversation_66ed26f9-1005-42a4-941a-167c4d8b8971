import type { Preview } from '@storybook/react'
import React, { useState } from 'react'
import { initialize, mswLoader } from 'msw-storybook-addon'
import { cookies } from '@storybook/nextjs/headers.mock'
import { Poppins } from 'next/font/google'
import { QueryClient, QueryClientProvider } from 'react-query'
import { QueryClientProvider as QueryClientProviderNew } from '@tanstack/react-query'
import { QueryClient as QueryClientNew } from '@tanstack/react-query'
import { Provider } from 'react-redux'
import { createStore } from 'redux'
import '@/styles/index.scss'

/* Initializes MSW */
initialize()

const PoppinsSet = Poppins({ 
	subsets: ['latin'],
	display: 'swap',
	weight: ['200', '300', '400', '600', '700', '800', '900'],
	variable: '--font-poppins-sans',
	fallback: ['system-ui', 'arial'],
	preload: true, 
	adjustFontFallback: true,
})

export const store = createStore(() => {})

export const storybookMSWHandlers = []

const preview = {
	parameters: {
		msw: storybookMSWHandlers,
		controls: {
			matchers: {
				date: /Date$/i,
			},
		},
		backgrounds: {
			default: 'light',
		},
		nextjs: {
			appDirectory: true,
		},
	},
	async beforeEach() {
		cookies().set('uid', 'uid')
	},
	decorators: [
		(Story) => {
			return (
				<div id="root" className="root">
					<div className="flex flex-col">
						<Story />
					</div>
				</div>
			)
		},
		(Story) => {
			return (
				<div className={`${PoppinsSet.className}`}>
					<Story />
				</div>
			)
		},
		(Story) => {
			const [queryClient] = useState(() => new QueryClient())
			const [queryClientNew] = useState(() => new QueryClientNew())

			return (
				<div className="w-dvw">
					<Provider store={store}>
						<QueryClientProviderNew client={queryClientNew}>
							<QueryClientProvider client={queryClient}>
								<Story />
							</QueryClientProvider>
						</QueryClientProviderNew>
					</Provider>
				</div>
			)
		},
	],
	loaders: [mswLoader],
} satisfies Preview

export default preview
