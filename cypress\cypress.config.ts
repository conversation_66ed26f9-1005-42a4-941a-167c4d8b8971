import { defineConfig } from 'cypress'
import cypressMochawesomeReporter from 'cypress-mochawesome-reporter/plugin'

export default defineConfig({
  video: false,
  e2e: {
    defaultCommandTimeout: 5000,
    chromeWebSecurity: false,
    viewportWidth: 1920,
    viewportHeight: 1080,
    screenshotOnRunFailure: true,
    setupNodeEvents(on, config) {
      cypressMochawesomeReporter(on)
      on('before:browser:launch', (browser, launchOptions) => {
        if (browser.name === 'chrome' || browser.name === 'chromium') {
          launchOptions.args.push('--disable-cache')
          launchOptions.args.push('--incognito')
        }
        return launchOptions
      })
      return config
    },
  },
  reporter: 'cypress-mochawesome-reporter',
  reporterOptions: {
    reportDir: 'cypress/reports',
    overwrite: true,
    html: true,
    json: false,
    embeddedScreenshots: true,
    charts: true,
    inlineAssets: true,
    saveJson: false,
  },
})
