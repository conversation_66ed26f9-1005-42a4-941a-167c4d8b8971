import { defineConfig } from 'cypress'
import baseConfig from 'cypress/cypress.config'

export default defineConfig({
  ...baseConfig,
  e2e: {
    ...baseConfig.e2e,
    baseUrl: 'https://stag.doctor.umasalud.com',
    env: {
      USER_DOCTOR_EMAIL: '<EMAIL>',
      USER_DOCTOR_PASSWORD: 'doctor-automation-test',
      UID_PATIENT: '5CiVjVt6VlT2eumUMWBK1QsjdB23',
      UMA_BACKEND_URL: 'https://stag.apis.umasalud.com',
    },
  },
})
