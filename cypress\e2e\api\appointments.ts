class ApiAppointmentsPatient {
  static createAppointment(uid: string) {
    return cy
      .request({
        method: 'POST',
        url: `${
          Cypress.env().UMA_BACKEND_URL
        }/patient/appointments/makeAppointment`,

        headers: {
          accept: 'application/json, text/plain, */*',
          'content-type': 'application/json',
          'x-api-key': Cypress.env().NEXT_PUBLIC_UMA_BACKEND_APIKEY,
        },
        body: {
          age: '2001-01-30',
          biomarker: [],
          category: 'GUARDIA_RANDOM',
          country: 'AR',
          cuit: '',
          destino_final: '',
          diagnostico: '',
          dt: '',
          dni: '**********',
          epicrisis: '',
          incidente_id: false,
          geo: { lat: null, lon: null },
          msg: 'make_appointment',
          motivo_de_consulta:
            'Hipertensión.test.No refiere dolor torácico.No refiere dolor tor<PERSON>',
          alertas: '',
          origin: '',
          ruta: '',
          sex: 'F',
          specialty: 'online_clinica_medica',
          ws: '5491212121212',
          uid: uid,
          dependantUid: false,
          track_id: '',
          isdependant: false,
          vip: false,
          payment_data: {
            method: 'UmaCreditos',
            id: 'UmaCreditos',
            price: 5300,
            full_price: 5300,
            currency: 'ARS',
          },
        },
      })
      .then((response) => {
        expect(response.status).to.eq(200)
      })
  }
}

export default ApiAppointmentsPatient
