class UserApi {
  static getUserByUid() {
    return cy.window().then((win) => {
      const token = win.localStorage.getItem('authToken')
      return cy
        .request({
          method: 'GET',
          url: `${Cypress.env().UMA_BACKEND_URL}/patient/user/getByUid`,
          headers: {
            accept: '*/*',
            authorization: `Bearer ${token}`,
            'content-type': 'application/json',
          },
        })
        .then((response) => {
          expect(response.status).to.eq(200)
          return response
        })
    })
  }
}
