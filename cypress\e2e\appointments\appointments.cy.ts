import onlineDoctorPage from 'cypress/pages/onlineDoctorPage'
import ApiAppointmentsPatient from '../api/appointments'
describe('Appointments', () => {
  before(() => {
    ApiAppointmentsPatient.createAppointment(Cypress.env('UID_PATIENT'))
  })

  beforeEach(() => {
    cy.login({
      email: Cypress.env('USER_DOCTOR_EMAIL'),
      password: Cypress.env('USER_DOCTOR_PASSWORD'),
    })
    cy.setOriginHeader()

    onlineDoctorPage.visitAppointmentsPage().shouldViewOnlineAppointments()
  })
  it('The doctor should be able to return to an in-progress appointment.', () => {
    onlineDoctorPage
      .startAppointment()
      .shouldEnterConsultationRoom()
      .exitConsultation()
      .shouldReturnToConsultation()
      .restarConsultation()
      .shouldEnterConsultationRoom()
      .shouldViewPatientDetails()
  })

  it('The doctor should not be allowed to close an appointment without a diagnosis.', () => {
    onlineDoctorPage
      .startAppointment()
      .shouldEnterConsultationRoom()
      .shouldViewPatientDetails()
      .endSession()
      .shouldShowMessageInModal(
        'Si el paciente no está ausente, debe cerrar la atención con algún diagnóstico.'
      )
  })

  it("The doctor should be able to close an appointment with the reason 'patient cancels'", () => {
    onlineDoctorPage
      .startAppointment()
      .shouldEnterConsultationRoom()
      .shouldViewPatientDetails()
      .setFinalDestination('Anula el paciente')
      .endSession()
      .shouldShowMessageInModal('¿Finalizar videoconsulta?')
      .confirEndSession()
      .shouldBeRedirectedToAppointmentsView()
  })
})
