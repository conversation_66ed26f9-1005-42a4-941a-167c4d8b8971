import OnlineDoctorPage from 'cypress/pages/onlineDoctorPage'
import ApiAppointmentsPatient from '../api/appointments'
import PrescriptionPage from 'cypress/pages/prescriptionPage'
describe('Order and Prescription Creation', () => {
  before(() => {
    ApiAppointmentsPatient.createAppointment(Cypress.env('UID_PATIENT'))
  })

  beforeEach(() => {
    cy.login({
      email: Cypress.env('USER_DOCTOR_EMAIL'),
      password: Cypress.env('USER_DOCTOR_PASSWORD'),
    })

    cy.setOriginHeader()

    OnlineDoctorPage.visitAppointmentsPage()
      .shouldViewOnlineAppointments()
      .startAppointment()
      .shouldEnterConsultationRoom()
    PrescriptionPage.openPrescriptionPage().verifyPrescriptionPageIsVisible()
  })

  it.skip('should create an recipe successfully', () => {
    PrescriptionPage.openRecipe()
      .searchDrug()
      .addDrug()
      .addIndication()
      .associateDiagnosis()
      .generatePrescription()
      .prescriptionShouldBeCreatedSuccessfully()
  })

  it('should create a prescription successfully', () => {
    PrescriptionPage.openOrder()
      .createOrder()
      .confirmOrderCreation()
      .verifyOrderIsCreated()
  })
})
