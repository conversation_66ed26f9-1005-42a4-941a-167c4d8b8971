import homePage from 'cypress/pages/homePage'
import appointmentsPage from 'cypress/pages/appointmentPage'
import chatAttPage from 'cypress/pages/chatAttPage'
import profilePage from 'cypress/pages/profilePage'
import payrollPage from 'cypress/pages/payrollPage'
import schedulePage from 'cypress/pages/schedulePage'
import recipesPage from 'cypress/pages/recipesPage'
import historyPage from 'cypress/pages/historyPage'
import loginPage from 'cypress/pages/loginPage'

describe('Home', () => {
  beforeEach(() => {
    cy.login({
      email: Cypress.env('USER_DOCTOR_EMAIL'),
      password: Cypress.env('USER_DOCTOR_PASSWORD'),
    })

    homePage.visitHomePage().shouldBeOnHomePage().waitForLoaderToDisappear()
  })

  afterEach(() => {
    cy.clearAllSessionStorage()
    cy.clearAllCookies()
  })

  it('should display the access to the different sections', () => {
    const links = [
      '/appointments',
      '/chatAtt',
      '/history',
      '/recetas',
      '/schedule',
      '/liquidacion',
    ]
    links.forEach((link) => {
      cy.get(`a[href="${link}"]`).should('have.attr', 'href', link)
    })
  })

  it('should display the open menu', () => {
    homePage.openMenu().shouldBeVisibleTheDropdownMenu()
  })

  it('the doctor should be able to close the session', () => {
    homePage.closeSession()
    loginPage.shouldBeOnLoginPage()
  })

  it('the doctor should be able to access the online appointments', () => {
    homePage.navigateToAppointments()
    appointmentsPage.shouldBeOnAppointmentPage()
  })

  it('the doctor should be able to access the chat', () => {
    homePage.navigateToChat()
    chatAttPage.shouldBeOnChatAttPage()
  })

  it('the doctor should be able to access to history', () => {
    homePage.navigateToHistory()
    historyPage.shouldBeOnHistoryPage()
  })

  it('the doctor should be able to access to recetas', () => {
    homePage.navigateToRecetas()
    recipesPage.shouldBeOnRecipesPage()
  })

  it('the doctor should be able to access to schedule', () => {
    homePage.navigateToSchedule()
    schedulePage.shouldBeOnSchedulePage()
  })

  it('the doctor should be able to access to liquidacion', () => {
    homePage.navigateToLiquidacion()
    payrollPage.shouldBeOnPayrollPage()
  })

  it('the doctor should be able to access to profile', () => {
    homePage.navigateToProfile()
    profilePage.shouldBeOnProfilePage()
  })
})
