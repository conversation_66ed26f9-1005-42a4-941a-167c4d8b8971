import loginPage from 'cypress/pages/loginPage'
import homePage from 'cypress/pages/homePage'

describe('Login', () => {
  beforeEach(() => {
    loginPage.visitLogin()
    loginPage.interceptRequestsLogin()
  })

  it('should display an error message for non-existent user', () => {
    loginPage
      .attemptLogin('<EMAIL>', 'password123')
      .shouldSeeErrorMessage('Email o contraseña no válida')
  })

  it('should be able to log in with an existing user', () => {
    loginPage
      .attemptLogin(
        Cypress.env().USER_DOCTOR_EMAIL,
        Cypress.env().USER_DOCTOR_PASSWORD
      )
      .loginShouldBeSuccessful()

    homePage.shouldBeOnHomePage()
  })

  it.skip('should be able to log out when the user is logged in', () => {
    loginPage
      .attemptLogin(
        Cypress.env().USER_DOCTOR_EMAIL,
        Cypress.env().USER_DOCTOR_PASSWORD
      )
      .loginShouldBeSuccessful()
    homePage.shouldBeOnHomePage().closeSession().shouldRedirectToLogin()
  })
})
