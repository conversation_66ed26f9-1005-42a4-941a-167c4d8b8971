import profilePage from 'cypress/pages/profilePage'

describe('Profile', () => {
  const Chance = require('chance')
  const chance = new Chance()
  beforeEach(() => {
    cy.login({
      email: Cypress.env('USER_DOCTOR_EMAIL'),
      password: Cypress.env('USER_DOCTOR_PASSWORD'),
    })
    cy.setOriginHeader()
    cy.visit('/home')
  })

  it.skip('The doctor should be able to edit their personal information.', () => {
    const genders = ['Mujer', 'Hombre', 'Otro']
    const randomGender = genders[Math.floor(Math.random() * genders.length)]
    const randomYear = chance.year({ min: 1960, max: 1990 })
    const randomName = chance.name()
    const randomBirthDate = chance
      .birthday({ year: randomYear })
      .toLocaleDateString('en-GB')

    profilePage
      .visitProfile()
      .openProfessionalData()
      .shouldDisplayProfessionalData()
      .updateAndVerifyName(randomName)
      .updateAndVerifyBirthDate(randomBirthDate)
      .updateAndVerifyGender(randomGender)
  })

  it('The doctor should be able to update their profile picture.', () => {
    const photo = 'imagen-test-1.jpg'
    profilePage
      .visitProfile()
      .openProfessionalData()
      .shouldDisplayProfessionalData()
      .uploadProfessionalPhoto(photo)
  })
})
