import BasePage from './basePage'

class AppointmentPage extends BasePage {
  private selectors = {
    titleHeader: '[data-cy="header"]',
    loader: '[data-testid="occipital-fullloader"]',
  }

  shouldBeOnAppointmentPage() {
    cy.url({ timeout: 10000 }).should('include', '/appointments')
    cy.get(this.selectors.loader, { timeout: 10000 }).should('not.exist')
    cy.get(this.selectors.titleHeader)
      .should('be.visible')
      .and('contain', 'Consultas')

    return this
  }
}

export default new AppointmentPage()
