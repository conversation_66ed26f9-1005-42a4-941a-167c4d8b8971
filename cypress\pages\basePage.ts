class BasePage {
  fillField(selector: string, value: string) {
    cy.get(selector, { timeout: 10000 })
      .should('be.visible')
      .clear()
      .type(value)
    return this
  }
  verifyTextsInContainer(containerSelector: string, expectedTexts: string[]) {
    cy.get(containerSelector)
      .should('be.visible')
      .then(($container) => {
        expectedTexts.forEach((text) => {
          expect($container).to.contain(text)
        })
      })

    return this
  }

  interceptFirestore() {
    cy.intercept({
      method: 'POST',
      url: 'https://firestore.googleapis.com/**',
    }).as('firestore')
    return this
  }

  waitForFirestoreResponse() {
    cy.wait('@firestore', { timeout: 20000 }).then((interception) => {
      expect(interception.response?.statusCode).to.eq(200)
    })
    return this
  }

  waitForLoaderToDisappear(timeout = 10000) {
    cy.wait(5000)
    cy.get('[data-testid="occipital-fullloader"]', { timeout }).should(
      'not.exist'
    )
    return this
  }
}

export default BasePage
