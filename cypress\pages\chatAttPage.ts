import BasePage from './basePage'

class ChatAttPage extends BasePage {
  private selectors = {
    titleHeader: '[data-cy="header"]',
    loader: '[data-testid="occipital-fullloader"]',
  }

  shouldBeOnChatAttPage() {
    cy.url({ timeout: 10000 }).should('include', '/chatAtt')
    cy.get(this.selectors.loader, { timeout: 10000 }).should('not.exist')
    cy.get(this.selectors.titleHeader)
      .should('be.visible')
      .and('contain', 'Consultorio chat')

    return this
  }
}

export default new ChatAttPage()
