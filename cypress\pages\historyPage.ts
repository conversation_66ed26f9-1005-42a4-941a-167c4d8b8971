import BasePage from './basePage'

class HistoryPage extends BasePage {
  private selectors = {
    titleHeader: '[data-cy="header"]',

    loader: '[data-testid="occipital-fullloader"]',
  }

  shouldBeOnHistoryPage() {
    cy.url({ timeout: 10000 }).should('include', '/history')
    cy.get(this.selectors.loader, { timeout: 10000 }).should('not.exist')
    cy.get(this.selectors.titleHeader)
      .should('be.visible')
      .and('contain', 'Historial')

    return this
  }
}

export default new HistoryPage()
