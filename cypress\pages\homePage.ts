import { tr } from 'date-fns/locale'
import BasePage from './basePage'

class HomePage extends BasePage {
  private selectors = {
    closeSession: '[href="/"]',
    openMenu: '[aria-label="abrir menú"]',
    header: '[data-cy="header"]',
    titleHeader: '[data-cy="header"]',
    profileBox: '[class*=profileBox]',
    loader: '[data-testid="occipital-fullloader"]',
    menuItems: '.align-middle',
    appointmentLink: '[href="/appointments"]',
    chatLink: 'a[href="/chatAtt"]',
    historyLink: 'a[href="/history"]',
    recetasLink: 'a[href="/recetas"]',
    scheduleLink: 'a[href="/schedule"]',
    liquidacionLink: 'a[href="/liquidacion"]',
    profileLink: 'a[href="/profile"]',
  }
  visitHomePage() {
    cy.visit('/home')
    return this
  }

  closeSession() {
    cy.get(this.selectors.closeSession).click()
    return this
  }

  shouldRedirectToLogin() {
    cy.url({ timeout: 10000 }).should('include', '/login')
  }
  shouldBeOnHomePage() {
    cy.url().should('include', '/home')
    cy.get(this.selectors.header)
      .should('be.visible')
      .contains('Visión General')

    return this
  }

  navigateToAppointments() {
    cy.get(this.selectors.appointmentLink)
      .should('exist')
      .and('be.visible')
      .click({ force: true })
    return this
  }

  navigateToChat() {
    cy.get(this.selectors.chatLink).should('exist').and('be.visible').click()
    return this
  }

  navigateToHistory() {
    cy.get(this.selectors.historyLink).should('exist').and('be.visible').click()
    return this
  }

  navigateToRecetas() {
    cy.get(this.selectors.recetasLink).should('exist').and('be.visible').click()
    return this
  }

  navigateToSchedule() {
    cy.get(this.selectors.scheduleLink)
      .should('exist')
      .and('be.visible')
      .click()
    return this
  }

  navigateToLiquidacion() {
    cy.get(this.selectors.liquidacionLink)
      .should('exist')
      .and('be.visible')
      .click()
    return this
  }

  navigateToProfile() {
    cy.get('.h-screen').find(this.selectors.profileLink).click()
    return this
  }

  openMenu() {
    cy.get(this.selectors.openMenu, { timeout: 10000 })
      .should('be.visible')
      .click()
    return this
  }

  shouldBeVisibleTheDropdownMenu() {
    const expectedMenuItems = [
      'Consultas',
      'Chat',
      'Historial',
      'Recetas',
      'Agenda',
      'Liquidaciones',
      'Explorar IA',
      'Perfil',
      'Tutoriales',
      'Cerrar Sesión',
    ]

    cy.get(this.selectors.menuItems, { timeout: 10000 })
      .should('be.visible')
      .each(($el) => {
        const itemText = $el.text().trim()
        expect(expectedMenuItems).to.include(itemText)
      })
    return this
  }
}

export default new HomePage()
