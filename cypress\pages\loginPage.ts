import BasePage from './basePage'

class LoginPage extends BasePage {
  private selectors = {
    emailInput: 'input[type="email"]',
    passwordInput: 'input[type="password"]',
    loginButton: 'Continuar',
    errorMessage: '[color="text-error"]',
    logoutButton: '[icon="logout"]',
  }

  visitLogin() {
    cy.visit('/login')
    return this
  }

  interceptRequestsLogin() {
    this.interceptFirestore()
    cy.intercept(
      {
        method: 'POST',
        url: '/v1/accounts:signInWithPassword**',
      },
      (req) => {
        req.headers['origin'] = Cypress.config('baseUrl') as string
        req.continue()
      }
    ).as('signInWithPassword')

    cy.intercept(
      {
        method: 'POST',
        url: '/v1/accounts:lookup**',
      },
      (req) => {
        req.headers['origin'] = Cypress.config('baseUrl') as string
      }
    ).as('lookupRequest')

    return this
  }

  attemptLogin(email: string, password: string) {
    cy.get(this.selectors.emailInput).first().type(email)
    cy.get(this.selectors.passwordInput).last().type(password)
    cy.contains(this.selectors.loginButton).click()
    return this
  }

  shouldSeeErrorMessage(message: string) {
    cy.get(this.selectors.errorMessage).should('contain', message)
    return this
  }

  loginShouldBeSuccessful() {
    cy.wait('@firestore', { timeout: 30000 }).then((interception) => {
      expect(interception.response?.statusCode).to.eq(200)
    })

    cy.wait('@lookupRequest').then((interception) => {
      expect(interception.response?.statusCode).to.eq(200)
    })
    cy.wait('@signInWithPassword').then((interception) => {
      expect(interception.response?.statusCode).to.eq(200)
      const idToken = interception.response?.body.idToken
      cy.window().then((win) => {
        win.localStorage.setItem('authToken', idToken)
      })
    })

    return this
  }

  shouldBeOnLoginPage() {
    cy.url({ timeout: 10000 }).should('contain', '/login')
    return this
  }
}

export default new LoginPage()
