import BasePage from './basePage'

class OnlineDoctorPage extends BasePage {
  private selectors = {
    appointmentsPage: '.status__container',
    specialistContainer: '.specialist_container',
    onlineAppointments: 'h3.flex',
    startAppointment: '.justify-between > .justify-center',
    loader: '[data-testid="occipital-fullloader"]',
    titleHeader: '[data-cy="header"]',
    patientDetailsContainer: '.attFile__container',
    closeSession: '[href="/"]',
    menuOption: '[aria-label="abrir menú"]',
    notification: '.notifications',
    exitAppointment: '[aria-label="retroceder"]',
    resumeButton: '.justify-between > .justify-center',
  }

  visitAppointmentsPage() {
    cy.get('[class*=FullLoader]', { timeout: 20000 }).should('not.exist')
    cy.get('[href="/appointments"]', { timeout: 10000 })
      .should('be.visible')
      .click()
    return this
  }

  shouldViewOnlineAppointments() {
    cy.get(this.selectors.appointmentsPage, { timeout: 20000 })
      .should('be.visible')
      .and('contain', 'Todas sus consultas')
      .and('contain', 'Guardia Online')
    cy.get(this.selectors.specialistContainer)
      .should('be.visible')
      .and('contain', 'Consultas en curso')
    cy.get(this.selectors.onlineAppointments)
      .first()
      .within(() => {
        cy.contains('Guardia Online').should('be.visible')
        cy.contains('test cypress').should('be.visible')
      })

    return this
  }

  shouldEnterConsultationRoom() {
    cy.get(this.selectors.loader, { timeout: 20000 }).should('not.exist')
    cy.url({ timeout: 10000 }).should('match', /\/doctor\?patientUid=[^&]+/)
    cy.get(this.selectors.titleHeader, { timeout: 10000 })
      .find('h1')
      .should('contain.text', 'Consultorio')
    return this
  }

  shouldViewPatientDetails() {
    this.verifyTextsInContainer(this.selectors.patientDetailsContainer, [
      'Motivo de consulta',
      'Ficha de atención',
      'Cobertura',
      'Nombre',
      'Edad',
    ])

    return this
  }

  exitConsultation() {
    cy.get(this.selectors.exitAppointment).click()
    return this
  }

  shouldReturnToConsultation() {
    cy.get(this.selectors.resumeButton)
      .should('be.visible')
      .and('contain', 'Retomar')
    return this
  }

  restarConsultation() {
    this.startAppointment()
    return this
  }

  startAppointment() {
    cy.intercept(
      { method: 'POST', path: '/doctor/appointments/startAtt' },
      (req) => {
        req.headers['Origin'] = Cypress.config('baseUrl') as string
      }
    ).as('startAtt')

    cy.get(this.selectors.startAppointment)
      .first()
      .should('be.visible')
      .click({ force: true })
    cy.wait('@startAtt', { timeout: 20000 }).then((intercept) => {
      expect(intercept.response?.statusCode).to.eq(201)
    })
    return this
  }

  setFinalDestination(finalDestination: string) {
    cy.contains('label', 'Destino Final:')
      .next()
      .find('input[type="text"]')
      .should('exist')
      .type(finalDestination.substring(0, 2), { delay: 100 })
      .type('{enter}')

    return this
  }
  endSession() {
    cy.contains('button', 'Finalizar consulta').click()
    return this
  }
  confirEndSession() {
    cy.intercept({
      method: 'POST',

      url: `${Cypress.env('UMA_BACKEND_URL')}/doctor/appointments/closeAtt`,
    }).as('closeAtt')

    cy.contains('button', 'Finalizar videoconsulta', { timeout: 10000 })
      .should('be.visible')
      .click()

    //  cy.wait('@closeAtt', { timeout: 30000 }).then((interception) => {
    //    expect(interception.response?.statusCode).to.eq(200)
    //  })

    return this
  }
  shouldShowMessageInModal(message: string) {
    cy.get('[role="dialog"]').should('be.visible').should('contain', message)
    return this
  }

  shouldBeRedirectedToAppointmentsView() {
    cy.url({ timeout: 10000 }).should('include', '/appointments')

    return this
  }
}

export default new OnlineDoctorPage()
