import BasePage from './basePage'

class PayrollPage extends BasePage {
  private selectors = {
    titleHeader: '[data-cy="header"]',

    loader: '[data-testid="occipital-fullloader"]',
  }

  shouldBeOnPayrollPage() {
    cy.url({ timeout: 10000 }).should('include', '/liquidacion')
    cy.get(this.selectors.loader, { timeout: 10000 }).should('not.exist')
    cy.get(this.selectors.titleHeader)
      .should('be.visible')
      .and('contain', 'Liquidaciones')

    return this
  }
}

export default new PayrollPage()
