import BasePage from './basePage'

class PrescriptionPage extends BasePage {
  private selectors = {
    prescriptionsContainer: '.prescriptions__container',

    searchStudiesButton: 'Buscar estudios',
    studiesComboButton: 'Buscar combo de estudios',
    symptomsButton: '¿Posee síntomas asociados?',
    textArea: 'textarea[placeholder="Escribí tus especificaciones aquí"]',
    requestStudiesButton: 'Solicitar estudios',
    swalModal: '.swal-modal',
    swalButton: '.swal-modal button',

    drugInput: '[placeholder="Medicamento"]',

    quantitySelect: 'select',
    indicationInput: '[name="medicine.details"]',

    diagnosisInput: '[placeholder="Fiebre"]',
  }

  openPrescriptionPage() {
    cy.contains('button', 'Prescripciones', { timeout: 20000 })
      .should('be.visible')
      .click()
    return this
  }

  verifyPrescriptionPageIsVisible() {
    cy.get(this.selectors.prescriptionsContainer)
      .should('contain', 'Recetas')
      .and('contain', 'Órden<PERSON>')
    return this
  }

  openOrder() {
    cy.contains('button', 'Orden').click()

    return this
  }

  createOrder() {
    cy.contains(this.selectors.searchStudiesButton)
      .next()
      .find('input')
      .type('resonancia', { delay: 100 })

    cy.get('[role="listbox"]').find('[role="option"]').first().click()

    cy.contains(this.selectors.studiesComboButton, { timeout: 10000 }).click({
      force: true,
    })
    cy.get('[role="listbox"]').find('[role="option"]').first().click()

    cy.contains(this.selectors.symptomsButton, { timeout: 10000 }).click({
      force: true,
    })

    cy.get('[role="listbox"]').find('[role="option"]').first().click()

    cy.get(this.selectors.textArea, { timeout: 10000 }).type(
      'Este es un texto de prueba'
    )

    cy.contains(this.selectors.requestStudiesButton, { timeout: 10000 })
      .should('be.enabled')
      .click()
    return this
  }

  confirmOrderCreation() {
    cy.get(this.selectors.swalModal, { timeout: 20000 })
      .should('be.visible')
      .and('contain', 'Confirmación')
      .and(
        'contain',
        'Verifique cuidadosamente los datos y confirme la operación.'
      )

    cy.get(this.selectors.swalButton).click()
    return this
  }

  verifyOrderIsCreated() {
    cy.get(this.selectors.swalModal, { timeout: 20000 })
      .should('be.visible')
      .and('contain', 'Orden adjuntada')
    cy.get(this.selectors.swalButton).click()

    return this
  }
  openRecipe() {
    cy.contains('button', 'Receta').click()
    cy.get('.react-switch-bg').click()
    return this
  }

  searchDrug() {
    cy.contains('button', 'Buscar fármaco por genérico').click()

    cy.get(this.selectors.drugInput)
      .should('be.visible')
      .type('paracetamol', { delay: 300 })
      .then(() => {
        cy.get('div[cmdk-item]')
          .first()
          .should('contain', 'paracetamol')
          .click()
      })
    return this
  }

  addDrug() {
    cy.contains('Dosis y presentación').should('be.visible').click()
    cy.get('[placeholder="Pastillas"]', { timeout: 10000 }).should('be.visible')

    cy.get('[role="presentation"]').find('.relative').first().click()

    cy.contains('Cantidad').should('be.visible').click()
    cy.get(this.selectors.quantitySelect).select('1', { force: true })
    return this
  }

  addIndication() {
    cy.get(this.selectors.indicationInput)
      .should('be.visible')
      .type('una cada 8 horas')

    cy.contains('Agregar').click()
    return this
  }

  associateDiagnosis() {
    cy.contains('Diagnóstico asociado').click()
    cy.get(this.selectors.diagnosisInput)
      .type('Fiebre', { delay: 100 })
      .then(() => {
        cy.get('div[cmdk-item]').first().should('contain', 'Fiebre').click()
      })
    return this
  }

  generatePrescription() {
    cy.contains('Generar receta').click()
    return this
  }

  prescriptionShouldBeCreatedSuccessfully() {
    cy.get(this.selectors.swalModal, { timeout: 20000 })
      .should('be.visible')
      .contains('Receta creada')
    return this
  }
}

export default new PrescriptionPage()
