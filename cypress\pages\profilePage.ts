import BasePage from './basePage'

class ProfilePage extends BasePage {
  private selectors = {
    professionalDataCard: '[class^="doctorProfesionalData_"]',
    save: '[class^="doctorProfesionalData_"] button:contains("Guardar")',
    birthDateInput: '#dob',
    genderType: 'button[role="combobox"]',
    genderOption: 'span',
    fullname: '#fullname',
    columnTextValue: '[class*=doctorProfesionalData_midColumn]',
    titleHeader: '[data-cy="header"]',

    loader: '[data-testid="occipital-fullloader"]',
  }

  visitProfile() {
    cy.visit('/profile')
    return this
  }

  shouldBeOnProfilePage() {
    cy.url({ timeout: 10000 }).should('include', '/profile')
    cy.get(this.selectors.loader, { timeout: 10000 }).should('not.exist')
    cy.get(this.selectors.titleHeader)
      .should('be.visible')
      .and('contain', 'Perfil')

    return this
  }

  openProfessionalData() {
    cy.contains('Datos profesionales').click()
    return this
  }

  shouldDisplayProfessionalData() {
    cy.get('.uploadCard').should('be.visible')

    cy.get(this.selectors.professionalDataCard)
      .should('be.visible')
      .and('contain', 'Nombre')
      .and('contain', 'Fecha de nacimiento')
      .and('contain', 'Sexo')
    return this
  }

  updateAndVerifyName(name: string) {
    cy.get(this.selectors.professionalDataCard).find('button').eq(0).click()
    cy.get(this.selectors.fullname).clear().type(name)
    cy.get(this.selectors.save).click()
    cy.get(this.selectors.fullname, { timeout: 10000 }).should('not.exist')
    cy.get(this.selectors.columnTextValue).eq(0).should('include.text', name)

    return this
  }

  updateAndVerifyBirthDate(age: string) {
    cy.get(this.selectors.professionalDataCard).find('button').eq(1).click()

    const [year, month, day] = age.split('/')
    const invertDateFormat = `${day}-${month}-${year}`
    cy.get(this.selectors.birthDateInput).type(invertDateFormat)
    cy.get(this.selectors.save).click()
    cy.get(this.selectors.birthDateInput, { timeout: 10000 }).should(
      'not.exist'
    )

    cy.get(this.selectors.columnTextValue).eq(1).should('include.text', age)

    return this
  }
  updateAndVerifyGender(gender: string) {
    cy.get(this.selectors.professionalDataCard).find('button').eq(2).click()
    cy.get(this.selectors.genderType).should('be.visible').click()
    cy.contains(gender).click()
    cy.get(this.selectors.birthDateInput, { timeout: 10000 }).should(
      'not.be.visible'
    )
    cy.get(this.selectors.genderType, { timeout: 10000 }).should('not.exist')
    cy.get(this.selectors.columnTextValue).eq(2).should('include.text', gender)

    return this
  }
  uploadProfessionalPhoto(file: string) {
    cy.intercept({ method: 'PATCH', path: '/doctor/providers' }).as(
      'updatePhoto'
    )
    cy.get('input[type="file"]').attachFile(file)
    return this
  }

  shouldSuccessfullyUploadProfessionalPhoto() {
    cy.wait('@updatePhoto').then((interception) => {
      expect(interception.response?.statusCode).to.eq(200)
      expect(interception.request.body).to.have.property('photo')
    })
    return this
  }
}

export default new ProfilePage()
