import BasePage from './basePage'

class RecipesPage extends BasePage {
  private selectors = {
    titleHeader: '[data-cy="header"]',
    loader: '[data-testid="occipital-fullloader"]',
  }

  shouldBeOnRecipesPage() {
    cy.url({ timeout: 10000 }).should('include', '/recetas')
    cy.get(this.selectors.loader, { timeout: 10000 }).should('not.exist')
    cy.get(this.selectors.titleHeader)
      .should('be.visible')
      .and('contain', 'Recetas')

    return this
  }
}

export default new RecipesPage()
