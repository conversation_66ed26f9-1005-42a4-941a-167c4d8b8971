import BasePage from './basePage'

class SchedulePage extends BasePage {
  private selectors = {
    titleHeader: '[data-cy="header"]',
    loader: '[data-testid="occipital-fullloader"]',
  }

  shouldBeOnSchedulePage() {
    cy.url({ timeout: 10000 }).should('include', '/schedule')
    cy.get(this.selectors.loader, { timeout: 10000 }).should('not.exist')
    cy.get(this.selectors.titleHeader)
      .should('be.visible')
      .and('contain', 'Agenda')

    return this
  }
}

export default new SchedulePage()
