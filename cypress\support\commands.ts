import homePage from 'cypress/pages/homePage'
import loginPage from 'cypress/pages/loginPage'
import 'cypress-file-upload'

Cypress.Commands.add('login', ({ email, password }) => {
  loginPage.visitLogin()

  loginPage.interceptRequestsLogin()

  cy.log('Attempting to login with provided credentials...')
  loginPage.attemptLogin(email, password).loginShouldBeSuccessful()

  cy.log('Before starting the test, verify that the user is on the homepage.')
  homePage.shouldBeOnHomePage()

  cy.log('Login successful and user is on the homepage. Test ready to start...')
})

Cypress.Commands.add(
  'loginDoctorByService',
  ({ email, password }: { email: string; password: string }) => {
    const API_KEY = ''
    const BASE_URL = 'https://identitytoolkit.googleapis.com/v1/accounts'

    cy.request({
      method: 'POST',
      url: `${BASE_URL}:signInWithPassword?key=${API_KEY}`,
      headers: {
        'Content-Type': 'application/json',
        Origin: Cypress.config('baseUrl') as string,
      },
      body: {
        email: email,
        password: password,
        returnSecureToken: true,
      },
    }).then((response) => {
      expect(response.status).to.eq(200)

      const { token, localId } = response.body

      cy.setCookie('token', token)
      cy.setCookie('uid', localId)

      window.localStorage.setItem('token', token)
      window.localStorage.setItem('uid', localId)

      cy.request({
        method: 'POST',
        url: `${BASE_URL}:lookup?key=${API_KEY}`,
        headers: {
          'Content-Type': 'application/json',
          Origin: Cypress.config('baseUrl') as string,
        },
        body: {
          idToken: token,
        },
      }).then((lookupResponse) => {
        expect(lookupResponse.status).to.eq(200)

        //cy.setCookie('user', JSON.stringify(lookupResponse.body.users))
      })
    })
  }
)

Cypress.Commands.add('loginDoctor', (email: string, password: string) => {
  //doctorAuthToken
})
// cypress/support/commands.ts

Cypress.Commands.add('loginPatient', (email: string, password: string) => {
  //patientAuthToken
})

Cypress.Commands.add('getTokenFromLocalStorage', (user: string) => {
  cy.window().then((win) => {
    const token = win.localStorage.getItem(user)
    return token
  })
})

Cypress.on('uncaught:exception', (err, runnable) => {
  return false
})

Cypress.Commands.add('setOriginHeader', () => {
  const baseUrl = Cypress.config('baseUrl')
  cy.intercept(
    {
      url: 'https://stag.apis.umasalud.com/**',
    },
    (req) => {
      req.headers['Origin'] = baseUrl as string
      req.continue()
    }
  ).as('requestWithOrigin')
})

export {}
