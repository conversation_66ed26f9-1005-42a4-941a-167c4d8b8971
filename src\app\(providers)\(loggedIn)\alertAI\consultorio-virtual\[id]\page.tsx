'use client'
import { useGetOperationById } from "@/services/requests/alertAI/getdata/useGetOperationById";
import TelehealthRoomAlertIAPage from "@/storybook/TelehealthRoom/TelehealthRoomAlertIAPage/TelehealthRoomAlertIAPage";
import { Loader } from "@umahealth/occipital";

type ConsultorioPageProps = {
  params: { id: number };
};

export default function Home({ params }: ConsultorioPageProps) {
  const { id } = params;

  const operation = useGetOperationById(id)

  if (!operation.data){
    return <Loader/>
  }

  return (
    <main className="trenes">
      <TelehealthRoomAlertIAPage operation={operation.data} />
    </main>
  );
}
