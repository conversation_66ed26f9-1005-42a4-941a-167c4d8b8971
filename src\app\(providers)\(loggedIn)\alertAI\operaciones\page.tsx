"use client";
import { useGetPendingOperations } from "@/services/requests/alertAI/getdata/getPendingOperations";
import { Loader, Text } from "@umahealth/occipital";
import OperationCard from "@/components/AlertAI/components/Operations/SingleOperation";
import { useStartOperation } from "@/services/requests/alertAI/mutations/useAnswerCall";
import { useRouter } from "next/navigation";

/**
 * Vista para mostrar la lista de operaciones pendientes.
 *
 */
const PageOperationsList = () => {
  const getPendingOperations = useGetPendingOperations();
  const router = useRouter();
  const startOperation = useStartOperation();

  if (getPendingOperations.isLoading) {
    return (
      <div className="h-full w-full flex justify-center items-center">
        <Loader size="size-7" />
      </div>
    );
  }

  if (getPendingOperations.isSuccess) {
    return (
      <div className="trenes">
        <div className="bg-white sm:p-8 p-4 rounded-3xl my-6 mx-2">
          <Text
            tag="h2"
            color="text-primary-800"
            weight="font-semibold"
            className="mb-6 sm:text-l text-m text-center sm:text-left"
          >
            Listado de evaluaciones ({getPendingOperations.data?.length})
          </Text>
          <div className="bg-[#F5F7F9] py-2 rounded-2xl">
            {getPendingOperations.data?.map((operation) => {
              return (
                <OperationCard
                  onClickRetomarConsulta={(operationId) => {
                    router.replace(`./consultorio-virtual/${operationId}`);
                  }}
                  onClickIniciarConsulta={(operationId) => {
                    startOperation.mutate(
                      {
                        call: true,
                        id: operationId,
                      },
                      {
                        onSuccess: () => {
                          router.replace(
                            `./consultorio-virtual/${operationId}`,
                          );
                        },
                      },
                    );
                  }}
                  onClickRevisarApto={(operationId) => {
                    startOperation.mutate(
                      {
                        call: false,
                        id: operationId,
                      },
                      {
                        onSuccess: () => {
                          router.replace(
                            `./consultorio-virtual/${operationId}`,
                          );
                        },
                      },
                    );
                  }}
                  key={operation.id}
                  operation={operation}
                />
              );
            })}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      Ocurrió un error obteniendo los datos de las operaciones
      {JSON.stringify(getPendingOperations.error)}
    </div>
  );
};

export default PageOperationsList;
