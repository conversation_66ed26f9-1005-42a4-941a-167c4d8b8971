'use server'
import Appointments from '@/components/Appointments/Appointments'
import StatusNav, { ServiceCategory } from '@/components/Appointments/UI/StatusNav/StatusNav'
import MedikitModal from '@/storybook/components/modals/MedikitModal/MedikitModal'
import HeaderSuspense from '@/components/GeneralComponents/Header'
import { getAvailablePermissions } from '@/serverServices/getAvailablePermissions'
import { getFeatures } from '@/serverServices/getFeatures'
import { getLicences } from '@/serverServices/getLicences'
import { getProvider } from '@/serverServices/getProvider'
import CameraMicrophoneModal from '@/storybook/components/modals/CameraMicrophoneModal/CameraAndMicrophoneModal'
import { getCurrentViewCookie, getDefaultView } from '@/cookies/currentView'
import { getPatientTypeFilterCookie } from '@/cookies/patientTypeFilter'
import { PatientType } from '@/storybook/components/PatientTypeSelect/PatientTypeSelect'

export default async function AppointmentsPage({ searchParams } : {searchParams : { currentView? : ServiceCategory, patientType?: PatientType }}) {
  
  const [features, provider, licenses, permissions, patientTypeFilter] = await Promise.all([
    getFeatures(),
    getProvider(),
    getLicences(),
    getAvailablePermissions(),
    getPatientTypeFilterCookie()
  ])

  // Determinar la vista activa con la siguiente prioridad:
  // 1. Parámetro URL searchParams.currentView
  // 2. Cookie de vista guardada anteriormente
  // 3. Vista predeterminada basada en permisos
  const savedView = await getCurrentViewCookie();
  const defaultView = getDefaultView(permissions);
  const currentView = searchParams.currentView || savedView || defaultView;

    const activePatientFilter = (
      searchParams.patientType || 
      patientTypeFilter || 
      'all'
    );

  // filterMissingDocumentsByMandatoryFieldsSelected missing documents
  // const skipDocumentation = liecnsesSettings?.data?.skipDocumentation

  return (
    <div className="px-36 h-full w-full flex flex-col">
      <HeaderSuspense title="Consultorio" />
      {/* Reemplazado con StatusNavNew que contiene toda la lógica de negocio */}
      <StatusNav provider={provider} permissions={permissions} currentView={currentView} patientTypeFilter={activePatientFilter}/>
      <Appointments
        features={features}
        licenses={licenses}
        currentView={currentView}
        patientTypeFilter={activePatientFilter}
      />
      { !provider?.medikit_token && process.env.NEXT_PUBLIC_COUNTRY === 'MX' &&
        <MedikitModal/>
      }
      { Boolean(process.env.NEXT_PUBLIC_ISFARMATODO) && (permissions.online || permissions.guardia) &&
        <CameraMicrophoneModal />
      }
      </div>
  )
}
