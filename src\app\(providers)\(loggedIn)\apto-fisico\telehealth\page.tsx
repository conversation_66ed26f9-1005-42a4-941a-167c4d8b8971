import Header from '@/components/GeneralComponents/Header';
import TelehealthView from '@/components/FitnessCheck/components/TelehealthRoomFitnessCheck/TelehealthViewFitnessCheck'
import RoomWrapperServer from '@/components/Consultorio/Telehealth/TelehealthRoom/RoomWrapperServer';
import { TSpecialties } from '@umahealth/entities';
import { attTypeTelehealthRoom } from '../../doctor/page';


type TDoctorAttSearchParams = { attType: attTypeTelehealthRoom; assignationId: string, specialty: 'bag' | TSpecialties }

interface ITelehealthRoom {
  searchParams: TDoctorAttSearchParams
}


export default function AptoFisicoTelehealth( {searchParams}: ITelehealthRoom) {
  
  return (
    <RoomWrapperServer assignationId={searchParams.assignationId} attType={searchParams.attType} specialty={searchParams.specialty}>
      <Header title='Consultorio' arrowBack />
      <TelehealthView />
    </RoomWrapperServer>
  )
}
