import ConsultaExternaView from "@/components/ElectronicHealthRecordMx/infrastructure/view/ConsultaExternaView"
import { getAppointmentById } from "@/store/actions/appointments/getAppointmentsById"
import { getProviderData } from "@/components/ElectronicHealthRecordMx/infrastructure/repositories/providerRepository"
import cluesDataRaw from "@/components/ElectronicHealthRecordMx/infrastructure/data/establecimiento_salud_catalogo.json"
import { IHealthFacility } from "@/components/ElectronicHealthRecordMx/domain/types/IHealthFacility"

const cluesData = cluesDataRaw as IHealthFacility[]

export default async function ConsultorioVirtualMexico({ searchParams} : { searchParams: {assignationId: string} }) {
  const appointment = await getAppointmentById(searchParams.assignationId, 'bag/MX')

  if(!appointment) throw new Error('Appointment not found')

  const providerData = await getProviderData(appointment.uid as string)
  if(!providerData) throw new Error('Provider not found')

  const cluesInfo = cluesData.find(clues => clues.clues === providerData.clues)
  if(!cluesInfo) throw new Error(`CLUES not found with value: ${providerData.clues}`)

  // eslint-disable-next-line
  // @ts-ignore
  delete appointment.timestamps

  return (
    <main>
      <ConsultaExternaView 
        appointment={appointment}
        providerData={providerData}
        cluesInfo={cluesInfo}
      />
    </main>
  )
}