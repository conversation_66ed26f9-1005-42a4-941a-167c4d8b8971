"use client";

import { But<PERSON> } from "@umahealth/occipital";
import { ErrorModal } from "@/storybook/components/modals/ErrorModal/ErrorModal";

export default function AppointmentsError({
  error,
}: {
  error: Error & { digest?: string };
}) {
  return <ErrorModal
    error={error}
    title='Hubo un problema con la pestaña del paciente'
    subtitle='Nuestro equipo ya esta trabajando en restablecer el equipo'
  >
    <Button type='link' href='/appointments' variant='filled'>
      Volver a la pestaña de turnos
    </Button>
    </ErrorModal>
}
