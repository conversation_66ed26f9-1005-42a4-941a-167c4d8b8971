import React from 'react'
import TelehealthRoom from '@/views/TelehealthRoom/TelehealthRoom'
import { TSpecialties } from '@umahealth/entities'
import { getFeatures } from '@/serverServices/getFeatures'
import FeatureNotAvailable from '@/views/FeatureNotAvailable'
import FarmatodoAtt from '@/views/FarmatodoAtt'
import { getAssignation } from '@/serverServices/getAssignation'
import { getMedicalRecords } from '@/serverServices/getMedicalRecords'
import { getPatientByUid } from '@/serverServices/getPatientByUid'
import { getActiveServicesByCorporate } from '@/serverServices/getActiveServicesByCorporate'
import { getAttCollectionPath } from '@/serverServices/utils/getServiceFromSpecialtyAndAttType'
import { ErrorModal } from '@/storybook/components/modals/ErrorModal/ErrorModal'
import { Button } from '@umahealth/occipital'
import { loadFormDataCookies } from '@/cookies/AssignationFormData'

/** Este AttType difiere de los de entities, no sé por qué pero estos son sus valores segun las urls que vi en logging */
export type attTypeTelehealthRoom = 'consultorio' | 'online' | 'onDemand' | 'chatAtt'

type TDoctorAttSearchParams = {
  attType: attTypeTelehealthRoom
  assignationId: string
  specialty: 'bag' | TSpecialties
}

interface IDoctorAtt {
  searchParams: TDoctorAttSearchParams
}

export default async function DoctorAtt({ searchParams }: IDoctorAtt) {
  const features = await getFeatures()
  const formData = await loadFormDataCookies(searchParams.assignationId)

  if (
    process.env.NEXT_PUBLIC_COUNTRY === 'CO' ||
    process.env.NEXT_PUBLIC_COUNTRY === 'VE'
  ) {
    if (
      features?.consultorio_online === false &&
      searchParams?.attType === 'online'
    ) {
      return <FeatureNotAvailable />
    }
    if (
      features?.consultorio_presencial === false &&
      searchParams?.attType === 'consultorio'
    ) {
      return <FeatureNotAvailable />
    }
    return (
      <FarmatodoAtt
        assignationId={searchParams.assignationId}
        attType={searchParams?.attType}
        specialty={searchParams.specialty}
      />
    )
  }

  if (features?.consultorio_online === false) {
    return <FeatureNotAvailable />
  }

  const assignation = await getAssignation({
    service: getAttCollectionPath(searchParams.attType, searchParams.specialty), // En AR siempre es bag
    assignationId: searchParams.assignationId,
  })

  if (!assignation) {
    throw new Error(
      `No se encontró la asignación de este turno en la base de datos assignationId: ${searchParams.assignationId} attType: ${searchParams.attType} specialty:${searchParams.specialty}`
    )
  }

  const [medicalRecords, patient, activeServices] = await Promise.all([
    getMedicalRecords({
      patientUserUid: assignation.patient.uid,
    }),
    getPatientByUid({
      uid: assignation.patient.uid_dependant
        ? assignation.patient.uid_dependant
        : assignation.patient.uid,
    }),
    getActiveServicesByCorporate({
      corporate: assignation.patient.corporate ?? `UMA ${process.env.NEXT_PUBLIC_COUNTRY}`,
    }),
  ])

  const errorDescription = !!assignation.timestamps?.dt_cancel?._seconds && !!assignation.timestamps.dt_assignation?._seconds ?
  `La consulta fue cancelada por una demora en abrirse de ${(assignation.timestamps.dt_cancel?._seconds - assignation.timestamps.dt_assignation?._seconds) / 60 } minutos`:
  `La consulta fue cancelada por demoras en abrirse`

  if (!patient && assignation?.destino_final === 'DELAY CANCEL') {
    return (
      <ErrorModal
        error={errorDescription}
        title={`La consulta fue cancelada automáticamente por demoras`}
        subtitle={errorDescription}
      >
        <Button type="link" href="/appointments" variant="filled">
          Volver a la pestaña de turnos
        </Button>
      </ErrorModal>
    )
  }

  if (!patient){
    return (
      <ErrorModal
        error={'No logramos obtener la información del paciente de este turno'}
        title={`No logramos obtener la información del paciente de este turno`}
        subtitle={'Nuestro equipo ya esta trabajando en resolverlo'}
      >
        <Button type="link" href="/appointments" variant="filled">
          Volver a la pestaña de turnos
        </Button>
      </ErrorModal>
    )
  }

  if (!activeServices) {
    return (
      <ErrorModal
        error={'Hubo un problema al abrir la consulta médica'}
        title={`Hubo un problema obteniendo los servicios activos para la obra social del cliente `}
        subtitle=" Nuestro equipo ya esta trabajando en arreglar el problema corporate: ${assignation?.patient?.corporate} assignation: ${assignation?.assignation_id}"
      >
        <Button type="link" href="/appointments" variant="filled">
          Volver a la pestaña de turnos
        </Button>
      </ErrorModal>
    )
  }

  return (
    <TelehealthRoom
      assignation={assignation}
      medicalRecords={medicalRecords}
      patient={patient}
      activeServices={activeServices}
      initialFormData={formData}
    />
  )
}
