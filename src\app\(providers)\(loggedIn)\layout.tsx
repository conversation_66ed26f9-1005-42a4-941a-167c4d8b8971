import React  from "react"
import "@/styles/index.scss"
import { getDeviceChangeCookie } from "@/cookies/deviceChange"
import ServerSidebar from "@/components/GeneralComponents/Sidebar/ServerSidebar"
import dynamic from 'next/dynamic'
import sidebarStyles from "@/components/GeneralComponents/Sidebar/Sidebar.module.css"

const DeviceChangePromptImplementation = dynamic(() => import("@/storybook/ChangeDevice/DeviceChangePrompt/DeviceChangePromptImplementation"))

export default async function RootLayout({
  children,
}: {
  children: React.ReactElement
}) {

  const [deviceChange] = await Promise.all([
    getDeviceChangeCookie(),
  ])

  return (
    <div
      className='w-full h-screen flex bg-background'
    >
      {!deviceChange && 
        <DeviceChangePromptImplementation />
      }
      <ServerSidebar/>
      <main className={`${sidebarStyles.scrollbarHide}  bg-[#F5F7F9] flex flex-col h-full w-full overflow-scroll`}>{children}</main>
    </div>
  );
}
