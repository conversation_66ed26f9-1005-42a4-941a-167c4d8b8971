'use client'

import { errorHandler } from "@/config/stackdriver";
import { Button, Image } from "@umahealth/occipital";
import { useEffect, useMemo } from "react";
import { v4 as uuidv4 } from 'uuid'
import ErrorEnchufe from '@/assets/illustrations/errorEnchufe.png'

function generateShortUUID() {
	// Genera un UUID
	const uuid = uuidv4()
  
	// Toma los primeros 6 caracteres del UUID
	const shortUUID = uuid.replace(/-/g, '').slice(0, 6)
  
	// Inserta un guion cada 3 caracteres
	return shortUUID.slice(0, 3) + '-' + shortUUID.slice(3)
}


export default function AppointmentsError({
  error,
}: {
  error: Error & { digest?: string }
}){
    
    const timestamp = new Date().toLocaleString();
    const digest = typeof error === 'object' && error !== null && 'digest' in error && (typeof error?.digest === 'number' || typeof error?.digest === 'string') ? error.digest : false
	const idUnico = useMemo(() => generateShortUUID(), [])

    useEffect(() => {
    if (errorHandler){
            errorHandler.report(`[STATUS]:${digest ? `[DIGEST ${digest}]` : `[ID ${idUnico}]`} Ocurrió un problema renderizando Appointments ${error}`)
        }
    }, [idUnico, errorHandler, digest])

    return (
      <div className="lg:px-24 lg:py-24 md:py-20 md:px-44 px-4 py-24 items-center flex justify-center flex-col-reverse lg:flex-row md:gap-28 gap-16">
        <div className="xl:pt-24 w-full xl:w-1/2 relative pb-12 lg:pb-0">
          <div className="relative">
            <div className="absolute">
              <div className="">
                <h1 className="my-2 text-gray-800 font-bold text-2xl">
                  Ocurrió un problema con las notificaciones
                </h1>
                <p className="my-2 text-gray-800"> más detalles del error</p>
                {error?.message && (
                  <>
                    <p>
                      {" "}
                      error {error.name}: {error.message}
                    </p>
                  </>
                )}
                  <>
                    <p> código de seguimiento {error.digest ?? idUnico} </p>
                  </>
                {<p> fecha del suceso: {timestamp}</p>}
                <Button className="mt-4" type="link" href="/" variant="filled">
                  {" "}
                  Volver al inicio{" "}
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div>
          <Image src={ErrorEnchufe} alt="" />
        </div>
      </div>
    );
}