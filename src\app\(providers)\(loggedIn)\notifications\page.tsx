import HeaderSuspense from '@/components/GeneralComponents/Header';
import { getProvider } from '@/serverServices/getProvider'
import NotificationsView from '@/views/Notifications'

export default async function NotificationsPage() {

    const provider = await getProvider()

    return (
      <main className='px-32'>
        <HeaderSuspense
          title="Notificaciones"
          arrowBack={true}
        />
        <NotificationsView provider={provider} />
      </main>
    );
}
