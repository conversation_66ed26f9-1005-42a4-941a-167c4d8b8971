import React from 'react'
import HeaderSuspense from '@/components/GeneralComponents/Header'
import { FarmacoDetail } from '@/components/MyPatients/presentation/ui/SearchFarmacoPatient/FarmacoDetail'

interface IPropsChildren{
    patientFhirId: string,
    encounterId?: string,
    assignationId?: string,
    patientUid?: string,
    attType?: string
}

export default async function DetailFarmacoPatient ({ params }: { params: { detailIds: string[] }}) {

    const propsToChildren: IPropsChildren = {
        patientFhirId: params.detailIds[0],
        encounterId: params.detailIds[1],
        assignationId: params.detailIds[2],
        patientUid: params.detailIds[3],
        attType: params.detailIds[4]
    }

    return <>
        <HeaderSuspense title='Detalle paciente' />
        <FarmacoDetail {...propsToChildren} />
    </>
}
