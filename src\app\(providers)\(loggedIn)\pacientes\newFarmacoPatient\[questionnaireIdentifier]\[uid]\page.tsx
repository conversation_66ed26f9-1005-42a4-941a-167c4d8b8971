import React from 'react'
import HeaderSuspense from '@/components/GeneralComponents/Header'
import { NewFarmacoPatientView } from '@/components/MyPatients/presentation/views/NewFarmacoPatientForms/PatientData/PatientDataView'
import { QuestionnaireIdentifier } from '@/components/MyPatients/presentation/views/NewFarmacoPatientForms/PatientData/PatientDataForm'

export default async function NewFarmacoPatient ({ params }: { params: { questionnaireIdentifier: QuestionnaireIdentifier, uid: string }}){

    return <>
        <HeaderSuspense title='Nuevo paciente' />
        <NewFarmacoPatientView questionnaireIdentifier={params.questionnaireIdentifier} uid={params.uid} />
    </>
}

