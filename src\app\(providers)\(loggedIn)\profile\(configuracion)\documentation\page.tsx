import { Documentation } from '@/components/User/UI/Profile/Components/Documentation'
import { getMandatoryFields } from '@/serverServices/getMandatoryFields'
import { getProvider } from '@/serverServices/getProvider'

export default async function DocumentationPage() {

		const mandatoryFields = await getMandatoryFields()
		const provider = await getProvider()
	

	return (
			<Documentation mandatoryFields={mandatoryFields} provider={provider}/>
	)
}
