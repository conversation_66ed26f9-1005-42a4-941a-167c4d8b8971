import RoomWrapperServer from '@/components/Consultorio/Telehealth/TelehealthRoom/RoomWrapperServer'
import TelehealthView from '@/components/Consultorio/Telehealth/TelehealthView/TelehealthView'
import { getHasSeenTelehealthGuideTour } from '@/cookies/hasSeenTelehealthGuideTour'
import { loadFormDataCookies } from '@/cookies/AssignationFormData'
import { TSpecialties } from '@umahealth/entities'
import { attTypeTelehealthRoom } from '../doctor/page'
import HeaderSuspense from '@/components/GeneralComponents/Header'
import { getProvider } from '@/serverServices/getProvider'

type TDoctorAttSearchParams = { attType: attTypeTelehealthRoom; assignationId: string, specialty: 'bag' | TSpecialties }

interface ITelehealthRoom {
  searchParams: TDoctorAttSearchParams
}


export default async function Telehealth({ searchParams }: ITelehealthRoom) {
  const hasSeenGuidedTour = await getHasSeenTelehealthGuideTour()
  
  // Cargar datos de las cookies en el servidor
  const formData = await loadFormDataCookies(searchParams.assignationId)
  const provider = await getProvider()

  return (
    <div className="px-[70px] h-full">
      <HeaderSuspense title="Consulta"  />
      <RoomWrapperServer assignationId={searchParams.assignationId} attType={searchParams.attType} specialty={searchParams.specialty}>
        <div className="h-[calc(100vh-90px)] overflow-hidden mt-4"> {/* Ajusta 60px según el tamaño del header */}
          <TelehealthView 
            providerUid={provider.uid}
            hasSeenGuidedTour={hasSeenGuidedTour} 
            initialFormData={formData}
            assignationId={searchParams.assignationId}
          />
        </div>
      </RoomWrapperServer>
    </div>
  )
}
