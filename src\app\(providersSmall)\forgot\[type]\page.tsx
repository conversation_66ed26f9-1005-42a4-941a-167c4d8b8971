import { ForgotEmail } from "@/components/User/ForgotCredentials/ForgotEmail/ForgotEmail";
import { ForgotPassword } from "@/components/User/ForgotCredentials/ForgotPassword/ForgotPassword";
import { LoginPage } from "@/storybook/components";

export default function ForgotTypePage({
  params,
}: {
  params: { type: "email" | "password" };
}) {

  
	if (params.type === 'email'){
		return <LoginPage>
      <ForgotEmail/>
    </LoginPage>
	}

	if (params.type === 'password'){
		return  <LoginPage>
      <ForgotPassword/>
    </LoginPage>
	}

}