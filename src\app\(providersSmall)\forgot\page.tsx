import { Login<PERSON><PERSON>, LogoSelector } from "@/storybook/components";
import { <PERSON>ton, Paragraph } from "@umahealth/occipital";
import { Icon } from "@umahealth/occipital/client";
import Link from "next/link";

export default function RecoveryPage() {
  return (
    <LoginPage>
      <Link
        className="self-start flex items-center text-primary"
        href="/"
      >
        <Icon className="mr-2" name="chevronLeft" />
        Volver
      </Link>
      <LogoSelector />
      <Paragraph className="mt-10 mb-5" size="text-l">
        Encuentra tu cuenta de ÜMA
      </Paragraph>
      <Button
        className="mb-4"
        type="link"
        variant="outlined"
        href="/forgot/email"
      >
        Olvid<PERSON> mi email
      </Button>
      <Button
        className="mb-4"
        type="link"
        variant="outlined"
        href="/forgot/password"
      >
        Olvidé mi contraseña
      </Button>
    </LoginPage>
  );
}
