"use client";
import { ReactNode } from "react";
import { QueryClientProvider } from "react-query";
import { queryClient } from "@/providers/QueryClient";
import { QueryClient, QueryClientProvider as QueryClientProviderNew } from "@tanstack/react-query";
import { NextIntlClientProvider } from "next-intl";
import ReduxProvider from "@/providers/ReduxProvider";
import messagesAr from "@/messages/AR.json";
import messagesMx from "@/messages/MX.json";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

const queryClientNew = new QueryClient()

export default function PublicLayout({ children }: { children: ReactNode }) {

  return (
    <ReduxProvider>
      <QueryClientProviderNew client={queryClientNew}>
        <QueryClientProvider client={queryClient}>
          <NextIntlClientProvider
            locale="es"
            messages={
              process.env.NEXT_PUBLIC_COUNTRY === "AR"
                ? messagesAr
                : messagesMx
            }
          >
            <div>
              {children}
            </div>
          </NextIntlClientProvider>
          <ReactQueryDevtools initialIsOpen={true} client={queryClientNew} buttonPosition='bottom-left'/>
        </QueryClientProvider>
      </QueryClientProviderNew>
    </ReduxProvider>
  );
} 