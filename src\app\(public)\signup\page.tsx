import { 
  SignUpStep, 
  STEP_COMPONENTS, 
  SignUp, 
  isValidStep,
  StepperProvider
} from '@/components/User/SignUp/config';

type SearchParams = {
  step?: string;
};

export default function SignUpPage({
  searchParams,
}: {
  searchParams: SearchParams;
}) {
  // Convertir el paso a número o usar el paso inicial si no hay parámetro
  const stepParam = searchParams.step ? parseInt(searchParams.step, 10) : SignUpStep.INITIAL;
  
  // Si el usuario proporciona un paso inválido, redirigir al paso inicial
  if (!isValidStep(stepParam)) {
    return <SignUp />;
  }
  
  // Renderizar el componente correspondiente al paso actual dentro del StepperProvider
  const StepComponent = STEP_COMPONENTS[stepParam as SignUpStep];
  return (
    <StepperProvider initialStep={stepParam}>
      <StepComponent />
    </StepperProvider>
  );
}