import * as google from '@googleapis/healthcare'
import { NextResponse } from 'next/server'
const parent = process.env.FHIR_STORE_FULL_RESOURCE_NAME

export async function POST(req: Request){
	try {
		const reqBody = await req.json()
		const healthcare = google.healthcare({
			version: 'v1',
			auth: new google.auth.GoogleAuth({
				scopes: ['https://www.googleapis.com/auth/cloud-platform'],
			}),
			headers: { 'Content-Type': 'application/fhir+json' },
		})
		const request = { parent, type: reqBody.resourceType, requestBody: reqBody.resourcePayload }
		const resource = await healthcare.projects.locations.datasets.fhirStores.fhir.create(request)
		return NextResponse.json(resource.headers.location.split(`${reqBody.resourceType}/`)[1]?.split('/')[0])
	} catch (error: any) {
		console.error('ERROR', await (error?.message as Blob).text() )
		return new NextResponse(JSON.stringify({ error: error }), {
			status: 500,
		});
	}
}