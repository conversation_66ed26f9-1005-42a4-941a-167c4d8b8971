import * as google from '@googleapis/healthcare'
import { NextResponse } from 'next/server'
const parent = process.env.FHIR_STORE_FULL_RESOURCE_NAME

export async function DELETE(req: Request){
	try {
		const reqBody = await req.json()
		const healthcare = google.healthcare({
			version: 'v1',
			auth: new google.auth.GoogleAuth({
				scopes: ['https://www.googleapis.com/auth/cloud-platform'],
			}),
			headers: { 'Content-Type': 'application/fhir+json' },
		})
		const name = `${parent}/fhir/${reqBody.resourceType}/${reqBody.resourceId}`
		await healthcare.projects.locations.datasets.fhirStores.fhir.delete({name})
		return NextResponse.json({msg: `Resource ${reqBody.resourceId} deleted successfully`})
	} catch (error) {
		console.error(error)
		return new NextResponse(JSON.stringify({ error: error }), {
			status: 500,
		});
	}
}