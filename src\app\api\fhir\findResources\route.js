import * as google from '@googleapis/healthcare'
import { NextResponse } from 'next/server'
const parent = process.env.FHIR_STORE_FULL_RESOURCE_NAME

export async function POST(req){
	try {
		const reqBody = await req.json()
		const healthcare = google.healthcare({
			version: 'v1',
			auth: new google.auth.GoogleAuth({
				scopes: ['https://www.googleapis.com/auth/cloud-platform'],
			}),
			headers: { 'Content-Type': 'application/fhir+json' },
		})
		const name = `${parent}/fhir/${reqBody.resourceType}?${reqBody.filters}`
		const resource = await healthcare.projects.locations.datasets.fhirStores.fhir.read({name})
		const resourceBlob = resource.data
		const bufferSymbol = Object.getOwnPropertySymbols(resourceBlob).find(sym => sym.toString() === 'Symbol(buffer)') || ''
		const resourceBuffer = resourceBlob[bufferSymbol]
		const jsonObject = JSON.parse(resourceBuffer.toString('utf-8'))
		return NextResponse.json(jsonObject.entry)
	} catch (error) {
		console.error(error)
		return new NextResponse(JSON.stringify({ error: error }), {
			status: 500,
		});
	}
}