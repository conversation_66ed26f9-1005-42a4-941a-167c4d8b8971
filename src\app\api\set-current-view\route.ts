import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { ServiceCategory } from "@/components/Appointments/UI/StatusNav/StatusNav";

const COOKIE_NAME = 'current_view';

// Lista de vistas permitidas
const ALLOWED_VIEWS: ServiceCategory[] = [
  'guardia',
  'online',
  'consultorio',
  'today',
  'special_consultorio',
  'aptofisico'
];

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { value } = body;

    // Validar que el valor es una vista permitida
    if (!value || !ALLOWED_VIEWS.includes(value)) {
      return NextResponse.json(
        { success: false, message: "Invalid view type" },
        { status: 400 }
      );
    }

    // Guardar la vista en cookies
    cookies().set(COOKIE_NAME, value, {
      httpOnly: true,
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 días
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error setting current view cookie:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
