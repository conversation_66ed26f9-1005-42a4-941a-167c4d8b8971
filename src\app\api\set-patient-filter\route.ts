import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { value } = body;

    // Valida que el valor sea uno de los permitidos
    if (!value || !['all', 'adults', 'pediatric'].includes(value)) {
      return NextResponse.json(
        { error: 'Valor no válido para el filtro de pacientes' },
        { status: 400 }
      );
    }

    // Crear la respuesta
    const response = NextResponse.json(
      { success: true, message: 'Preferencia de filtro guardada correctamente' },
      { status: 200 }
    );

    // Establecer la cookie (30 días de duración)
    response.cookies.set({
      name: 'patient_type_filter',
      value,
      path: '/',
      maxAge: 30 * 24 * 60 * 60, // 30 días en segundos
      httpOnly: false, // false para que sea accesible desde JS
      sameSite: 'strict',
    });

    return response;
  } catch (error) {
    console.error('Error al procesar la solicitud:', error);
    return NextResponse.json(
      { error: 'Error al procesar la solicitud' },
      { status: 500 }
    );
  }
}
