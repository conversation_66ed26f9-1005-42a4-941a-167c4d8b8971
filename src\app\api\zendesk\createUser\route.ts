// app/api/zendesk/user/route.ts

import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(req: NextRequest) {
  try {
    const userData = await req.json();

    const subdomain = process.env.ZENDESK_SUBDOMAIN;
    const email = process.env.ZENDESK_EMAIL;
    const apiToken = process.env.ZENDESK_API_TOKEN;

    if (!subdomain || !email || !apiToken) {
      return NextResponse.json(
        { error: 'Faltan las configuraciones necesarias en las variables de entorno.' },
        { status: 500 }
      );
    }

    const response = await axios.post(
      `https://${subdomain}.zendesk.com/api/v2/users.json`,
      userData,
      {
        auth: {
          username: email,
          password: apiToken,
        },
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return NextResponse.json(response.data, { status: 201 });
  } catch (error: any) {
    console.error(
      'Error al crear el usuario en Zendesk:',
      error.response?.data || error.message
    );

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.error || 'No se pudo crear el usuario en Zendesk.';

    return NextResponse.json({ error: message }, { status });
  }
}
