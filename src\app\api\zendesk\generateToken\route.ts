// app/api/generate-zendesk-token/route.ts

import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

// Indica que esta función solo debe ejecutarse en el servidor
export const dynamic = "force-dynamic";

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();

    if (!body.email || !body.uid) {
      return NextResponse.json(
        { error: "Datos de usuario incompletos" },
        { status: 400 }
      );
    }

    // Crear el payload para el token JWT
    const payload = {
      name: body?.name,
      email: body.email,
      email_verified: true,
      external_id: body.uid,
      iat: Math.floor(Date.now() / 1000), // Fecha de emisión
      exp: Math.floor(Date.now() / 1000) + 3600 * 12, // Expira en 12 horas
      user_fields: {
        personal_id: body.uid,
      },
      picture: body.picture,
      scope: "user",
    };

    // Generar el token JWT
    /* la variable de entorno Zendesk_Secret sólo existe en el servidor, por tanto
    TS con Next creen que puede valer undefined, pero route.ts vive sólo en el servidor
    lo cual significa que la variable siempre va a estar definida correctamente. */
    // eslint-disable-next-line
    const token = jwt.sign(payload, process.env.ZENDESK_SECRET!, {
      algorithm: "HS256",
      keyid: "app_6708236840bcec9802b87591"
    });
    /*  header: {
        kid: "app_6708236840bcec9802b87591",
      }, */

    return NextResponse.json({ token });
  } catch (error) {
    console.error("Error al generar el token JWT:", error);
    return NextResponse.json(
      { error: "Error al generar el token JWT" },
      { status: 500 }
    );
  }
}
