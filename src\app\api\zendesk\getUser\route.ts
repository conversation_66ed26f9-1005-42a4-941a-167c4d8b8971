// app/api/zendesk/user/route.ts

import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

interface ZendeskUser {
  id: number;
  name: string;
  email: string;
  external_id: string;
  // otros campos según sea necesario
}

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const externalId = searchParams.get("externalId");

  if (!externalId) {
    return NextResponse.json(
      { error: "El parámetro externalId es requerido." },
      { status: 400 }
    );
  }

  const subdomain = process.env.ZENDESK_SUBDOMAIN;
  const email = process.env.ZENDESK_EMAIL;
  const apiToken = process.env.ZENDESK_API_TOKEN;

  if (!subdomain || !email || !apiToken) {
    return NextResponse.json(
      {
        error:
          "Faltan las configuraciones necesarias en las variables de entorno.",
      },
      { status: 500 }
    );
  }

  try {
    const response = await axios.get<{ users: ZendeskUser[] }>(
      `https://${subdomain}.zendesk.com/api/v2/users/search.json`,
      {
        params: {
          external_id: externalId,
        },
        auth: {
          username: email,
          password: apiToken,
        },
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    const users = response.data.users;

    return NextResponse.json(users, { status: 200 });
  } catch (error: any) {
    console.error(
      "Error al buscar el usuario:",
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    
    if (status === 404){
      return NextResponse.json([], { status: 200 });
    }

    const message =
      error.response?.data?.error ||
      "No se pudo obtener el usuario de Zendesk.";

    return NextResponse.json({ error: message }, { status });
  }
}
