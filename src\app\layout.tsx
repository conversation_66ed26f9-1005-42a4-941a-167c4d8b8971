import { Poppins } from 'next/font/google'
import React from 'react'
import Script from 'next/script';
import '@/styles/index.scss'
import { isProd } from '@/config/isProduction';
import { ServerAuthProvider } from '@/auth/server-auth-provider';
import { PHProvider } from '@/providers/PostHogProvider';
import BrowserSafeProvider from '@/providers/BrowserSafeProvider';

const PoppinsSet = Poppins({ 
	subsets: ['latin'],
	display: 'swap',
	weight: ['200', '300', '400', '600', '700', '800', '900'],
	variable: '--font-poppins-sans',
	fallback: ['system-ui', 'arial'],
	preload: true, 
	adjustFontFallback: true,
})

export default function RootLayout({
	children,
}: {
	children: React.ReactNode
}) {

	return (
    <html lang="es">
      <Script id="ze-snippet" src={process.env.NEXT_PUBLIC_ZENDESK} />
      {isProd && (
        <>
          <Script src="https://www.googletagmanager.com/gtag/js?id=G-K70HDEK1HR" />
          <Script id="google-analytics">
            {`
					if(typeof window !=='undefined') {
						window.dataLayer = window.dataLayer || [];
						function gtag(){dataLayer.push(arguments);}
						gtag('js', new Date());

						gtag('config', 'G-K70HDEK1HR');
					}
					`}
          </Script>
        </>
      )}
      <Script src="https://apis.google.com/js/api.js" />
      <body className={PoppinsSet.className}>
        <ServerAuthProvider>
          <PHProvider>
            <BrowserSafeProvider>
              {children}
            </BrowserSafeProvider>
          </PHProvider>
        </ServerAuthProvider>
      </body>
    </html>
  );
}
