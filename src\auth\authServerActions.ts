'use server'
import { IdTokenResult } from 'firebase/auth'
import { cookies, headers } from 'next/headers'
import { redirect } from 'next/navigation'
import { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

/**
 * Cuando el tiempo definido en la cookie `expirationToken` vence, debemos obtener un nuevo token del cliente.
 * Para ello mandamos a esta página. Si se edita la página de ingresar con email, debe tenerse cuidado con esta función,
 * que espera que la página de ingreso con email tenga la lógica necesaria para actualizar el token.
 *
 * @param {NextRequest} request - El objeto de solicitud de Next.js.
 * @returns {NextResponse} La respuesta de redirección a la página de inicio de sesión con verificación de caché.
 */
export async function redirectToverifyCacheAndLogin(request: NextRequest) {
	const requestHeaders = new Headers(request.headers)
	requestHeaders.set('x-next-pathname', request.nextUrl.pathname)


	const loginURL = new URL('/login', request.url)


	// Construir la URL de redirección con el parámetro verifyCache
	// y mantener los parámetros de consulta originales
	const queryParams = new URLSearchParams(request.nextUrl.search)
	queryParams.delete('redirect')
	queryParams.set('verifyCache', 'true')
	queryParams.set('redirect', request.nextUrl.pathname)
	loginURL.search = queryParams.toString()

	return NextResponse.redirect(loginURL)
}


/**
 * Redirige al usuario a la página de login con los parámetros necesarios.
 */
export async function redirectToverifyCacheAndLoginServer() {
	const headersList = headers();
	const currentPathname = headersList.get("x-next-pathname") || "/";
	const currentSearch = headersList.get("x-next-search") || "";

	if (currentSearch.includes("verifyCache")) {
		console.log("tengo verify Cache así que estoy en el user");
		return;
	}

	const queryParams = new URLSearchParams(currentSearch);
	queryParams.delete("redirect");
	queryParams.set("verifyCache", "true");
	queryParams.set("redirect", currentPathname);

	// Usamos ruta relativa directamente
	const loginURL = `/login?${queryParams.toString()}`;
	redirect(loginURL);
}

/** En caso donde quiere acceder a rutas que no debería, lo redirigo al login */
export async function redirectToLogin(request: NextRequest) {

	const requestHeaders = new Headers(request.headers)
	requestHeaders.set('x-next-pathname', request.nextUrl.pathname)

	// Obtener los parámetros de consulta de la URL original
	const queryParams = request.nextUrl.searchParams

	// Construir la URL de redirección con los parámetros de consulta
	const loginURL = new URL('/login', request.url)
	queryParams.forEach((value, key) => {
		loginURL.searchParams.append(key, value)
	})

	// Añadir el parámetro 'redirect' que indica la ruta original
	loginURL.searchParams.append('redirect', request.nextUrl.pathname)


	// Redirigir a la URL de inicio de sesión con los parámetros de consulta
	return NextResponse.redirect(loginURL)
}

/** Setea el serverToken, el JWT token, es decir el bearer token que necesita por ejemplo el Megalito para funcionar
 * lo guarda en las cookies
 */
export async function setAuthServerToken(token: string, expiration?: string) {
	cookies().set('token', token, {
		sameSite: 'none',
		secure: true
	})
	if (expiration) {
		cookies().set('expirationToken', expiration, {
			sameSite: 'none',
			secure: true
		})
	}
}

/**
 * 
 * @param auth instancia Auth
 * Setea el uid y el user en cookies
 */
export async function saveUserInServer(auth: IdTokenResult) {
	cookies().set('uid', auth.claims?.user_id as string, {
		sameSite: 'none',
		secure: true
	})
	cookies().set('user', JSON.stringify(auth.claims), {
		sameSite: 'none',
		secure: true
	})
}

export async function getTokenFromCookies() {
	const cookieStore = cookies()
	return cookieStore.get("token")?.value;
}

export async function getUidFromCookies() {
	const cookieStore = cookies()
	return cookieStore.get("uid")?.value;
}


/**
 * Ideal para deslogearte del servidor
 */
export async function removeAuthCookies() {
	cookies().delete('token')
	cookies().delete('uid')
	cookies().delete('user')
	cookies().delete('expirationToken')
}
