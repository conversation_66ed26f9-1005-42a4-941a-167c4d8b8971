'use client'
import { auth } from '@/config/firebase'
import { useZendeskAuthentication } from '@/services/zendesk/useZendeskAuthentication'

export default function ZendeskAuth() {
  useZendeskAuthentication({
    email: auth.currentUser?.email??undefined,
    fullname: auth.currentUser?.displayName ?? undefined,
    profile_pic: auth.currentUser?.photoURL ?? undefined,
    uid: auth.currentUser?.uid,
  })

  return null
}
