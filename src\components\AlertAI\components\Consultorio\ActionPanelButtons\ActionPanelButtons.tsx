"use client";
import React from "react";
import { Button } from "@umahealth/occipital";
import EndButtonCall from "../EndButtonCall/EndButtonCall";
import { Icon } from "@umahealth/occipital/client";
import { stringOrDate } from "react-big-calendar";
import { cn } from "@/lib/utils";


/**
 * El componente ActionPanelButtons proporciona un conjunto de botones de acción 
 * para controlar el estado del micrófono y la cámara durante una videollamada, 
 * así como un botón para finalizar la llamada.
 *
 * @component
 * @param {Object} props - Propiedades del componente
 * @param {() => void} props.handleEndCall - Función que se ejecuta cuando se hace click en el botón para finalizar la llamada.
 * @param {() => void} props.onToggleAudio - Función que se ejecuta cuando se hace click en el botón para activar/desactivar el micrófono.
 * @param {() => void} props.onToggleVideo - Función que se ejecuta cuando se hace click en el botón para activar/desactivar la cámara.
 * @param {stringOrDate} [props.className] - Clases de tailwind adicionales para el componente.
 * @param {boolean} props.isMicOn - Indica si el micrófono está activado.
 * @param {boolean} props.isCameraOn - Indica si la cámara está activada.
 */
const ActionPanelButtons = ({ handleEndCall, onToggleAudio, onToggleVideo, className, isMicOn, isCameraOn }: { handleEndCall: () => void; onToggleAudio:() => void; onToggleVideo:() => void, className?: stringOrDate, isMicOn: boolean, isCameraOn: boolean }) => {

  return (
    <div className={cn("flex justify-center 2xl:mt-6 xl:mt-2 mt-0", className)}>
      {/* Preloading images - es para que no tarde en cargar la primera vez que haces click */}
      <div className="hidden">
        <Icon name="microphone" />
        <Icon name="microphoneOff" />
        <Icon name="videocall" />
        <Icon name="videocallOff" />
      </div>
      {/*  */}
      <div className="flex justify-center gap-4 py-2 px-6 bg-white rounded-xl shadow-sm">
        <button
          onClick={() => {
            onToggleAudio();
          }}
          type="button"
          className="rounded-full min-w-[44px] h-[44px] w-[44px] bg-gray-600 hover:bg-gray-800 flex items-center justify-center"
        >
          <Icon
            name={isMicOn ? "microphone" : "microphoneOff"}
            size="size-6"
            color='text-grey-100'
          />
        </button>
        <Button
          onClick={() => {
            onToggleVideo();
          }}
          
          type="button"
          size="small"
          className="rounded-full min-w-[44px] h-[44px] w-[44px] bg-gray-600 outline-none hover:bg-gray-800"
        >
          <Icon
            name={isCameraOn ? "videocall" : "videocallOff"}
            size="size-6"
            className="text-white"
          />
        </Button>
        <EndButtonCall handleEndCall={handleEndCall} />
      </div>
    </div>
  );
};

export default ActionPanelButtons;
