"use client";
import React from "react";
import { Icon } from "@umahealth/occipital/client";
import { IOperation } from "@umahealth/entities";
import { Loader } from "@umahealth/occipital";

/**
 * El componente CallButton gestiona la interacción del doctor con diferentes estados de la operación,
 * permitiendo al usuario retomar, revisar o iniciar una videollamada en función del estado actual.
 *
 * @component
 * @param {Object} props - Propiedades del componente.
 * @param {IOperationResponse} props.operation - La operación actual que se está gestionando.
 * @param {string} [props.status] - El estado actual de la operación.
 */
export default function CallButton({
  operation,
  onClickRevisarApto,
  onClickIniciarConsulta,
  onClickRetomarConsulta,
  isLoading
}: {
  operation: IOperation;
  onClickRevisarApto: (operationId: IOperation["id"]) => void;
  onClickIniciarConsulta: (operationId: IOperation["id"]) => void;
  onClickRetomarConsulta: (operationId: IOperation["id"]) => void;
  isLoading?: boolean
}) {

  /**
   * Maneja el click en el botón para retomar la videollamada.
   */
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    onClickRetomarConsulta(operation.id)
  };

  /**
   * Maneja el click en el botón para iniciar la videollamada y responder a la llamada.
   */
  const handleClickAnswerCall = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    event.stopPropagation();
    onClickIniciarConsulta(operation.id)
  };

  const handleClickClearance = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    onClickRevisarApto(operation.id);
  };

  if (operation.status === "in-review") {
    return (
      <button
        onClick={isLoading ? undefined : handleClick}
        className="flex items-center justify-center h-12 bg-primary-50 border border-primary-600 text-primary-600 rounded-full px-4 hover:bg-primary-700 hover:text-white hover:border-primary-700"
      >
        {isLoading ?
          <Loader />
          : <><Icon
            color="inherit"
            name="videocall"
            size="size-6"
            aria-hidden="true"
          />
            <span className="ml-2">Retomar</span>
          </>
        }

      </button>
    );
  }

  return (
    <div className="flex gap-4">
      {operation.status === "to-apt-review" &&
        operation.questions?.resume?.apto ? (
        <>
          <button
            onClick={handleClickClearance}
            className={`flex items-center justify-center h-12 bg-primary-50 border border-primary-600 text-primary-600 rounded-full px-4 sm:hover:bg-primary-700 sm:hover:text-white sm:hover:border-primary-700"`}
          >
            <Icon
              color="inherit"
              name="description"
              size="size-6"
              aria-hidden="true"
            />
            <span className="ml-2">Revisar Apto</span>
          </button>
          <button
            onClick={handleClickAnswerCall}
            className="flex items-center justify-center w-12 h-12 rounded-full transition-all duration-300 ease-in-out overflow-hidden group bg-primary-600 text-white sm:hover:bg-primary-700 cursor-pointer sm:hover:w-auto sm:hover:px-4"
            disabled={false}
          >
            {isLoading ? <Loader /> : <>
            <Icon
              color="text-grey-25"
              name="videocall"
              size="size-6"
              aria-hidden="true"
            />
              <span className="ml-2 hidden sm:group-hover:inline">
                Iniciar consulta
              </span>
            </>}
          </button>
        </>
      ) : (
        <>
          <button
            onClick={handleClickAnswerCall}
            className="flex items-center justify-center w-12 h-12 rounded-full transition-all duration-300 ease-in-out overflow-hidden group bg-primary-600 text-white sm:hover:bg-primary-700 cursor-pointer sm:hover:w-auto sm:hover:px-4"
            disabled={false}
          >
            {isLoading ? <Loader /> : <><Icon
              color="text-grey-25"
              name="videocall"
              size="size-6"
              aria-hidden="true"
            />
              <span className="ml-2 hidden sm:group-hover:inline">
                Iniciar consulta
              </span></>
            }

          </button>
        </>
      )}
    </div>
  );
}
