import React from "react";
import { But<PERSON> } from "@umahealth/occipital";
import { Icon } from "@umahealth/occipital/client";


/**
 * Componente EndButtonCall
 *
 * Este componente renderiza un botón para finalizar una llamada. 
 * Utiliza el componente `Button` y muestra un icono de finalización de llamada.
 *
 * @component
 * @param {Object} props - Las propiedades del componente.
 * @param {function} props.handleEndCall - Función que se ejecuta al hacer clic en el botón para finalizar la llamada.
 */
const EndButtonCall = ({ handleEndCall }: { handleEndCall: () => void }) => {

  return (
    <Button
      type="button"
      size="small"
      className="rounded-full bg-error h-[44px] w-[76px] hover:bg-error-700"
      onClick={() => {
        handleEndCall()
      }}
    >
      <Icon name="callEnd" size="size-6" className="text-white" />
    </Button>
  );
};

export default EndButtonCall;
