import { Button, Paragraph, Title } from '@umahealth/occipital';
import { DialogClose, DialogContent, DialogDescription, DialogOverlay, DialogPortal, DialogRoot, DialogTitle } from '@umahealth/occipital/client';
import { useEffect, useState } from 'react';

/**
 * Componente `PermissionCheck`
 * 
 * Este componente verifica los permisos de acceso a cámara y micrófono del navegador del usuario.
 * Si el acceso a estos dispositivos no está habilitado, se muestra un modal solicitando al usuario que habilite los permisos necesarios.
 * 
 */
const PermissionCheck = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    checkPermissions();
  }, []);

   /**
   * Verifica los permisos de acceso a cámara y micrófono del navegador.
   * Si no se pueden obtener los flujos de audio o video, se abre un modal solicitando al usuario que habilite los permisos.
   * 
   * @async
   * @function checkPermissions
   * @returns {Promise<void>}
   */
  const checkPermissions = async () => {
    try {
        const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
        const videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
      if(!audioStream.active || !videoStream.active){
        setIsModalOpen(true)
      }
    } catch (err) {
      console.error('Error accessing media devices:', err);
    }
  };

  return (
    <DialogRoot open={isModalOpen} modal={true}>
	<DialogPortal>
		<DialogOverlay className='bg-black/30' />
		<DialogContent className="flex items-center flex-col p-8">
		<DialogTitle asChild>
		<Title
							hierarchy="h1"
							color="text-primary-800"
							size="text-l"
							weight="font-semibold"
							className="mb-4"
						>
                            Permisos de audio y video
						</Title>
		</DialogTitle>
		<DialogDescription asChild>
			<>
			<Paragraph className="text-center pb-6">
					Por favor habilite los permisos de cámara y micrófono
						</Paragraph>

			</>
		</DialogDescription>
		
		<DialogClose asChild>
			<Button
			variant="outlined"
			size="full"
			type="button"
            onClick={() => setIsModalOpen(false)}
			>
			Cerrar
			</Button>
		</DialogClose>
		</DialogContent>
	</DialogPortal>
	</DialogRoot>
  );
};

export default PermissionCheck;