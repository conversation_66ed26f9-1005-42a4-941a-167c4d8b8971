import React, { useRef, useEffect, useState } from 'react';
import { LocalVideo } from '@umahealth/occipital/client';
import { Publisher, Session } from '@opentok/client';


/**
 * Componente `PublisherComponent`
 * 
 * Este componente maneja la publicación de audio y video en una sesión de OpenTok.
 * Utiliza `Publisher` de OpenTok para iniciar la publicación y controla el estado del micrófono y la cámara.
 * 
 * @component
 * @param {Object} props - Propiedades del componente.
 * @param {Session} props.session - Instancia de la sesión de OpenTok a la que se publica el flujo de medios.
 * @param {boolean} props.isMicOn - Estado del micrófono, indica si el micrófono está encendido (`true`) o apagado (`false`).
 * @param {boolean} props.isCameraOn - Estado de la cámara, indica si la cámara está encendida (`true`) o apagada (`false`).
 * 
 */
export const PublisherComponent = ({ session, isMicOn, isCameraOn }:{session:Session, isMicOn:boolean, isCameraOn:boolean }) => {
  const [isConnected, setIsConnected] = useState(false);
  const publisherRef = useRef<Publisher | null>(null);
  const publisherContainerRef = useRef<HTMLDivElement | null>(null);
  
  let publisher: Publisher;
  
  useEffect(() => {
    if (!session) return;
    
    // Escucha eventos de conexión
    const handleConnectionCreated = () => setIsConnected(true);
    const handleConnectionDestroyed = () => setIsConnected(false);
    
    session.on('connectionCreated', handleConnectionCreated);
    session.on('connectionDestroyed', handleConnectionDestroyed);

    if(publisherContainerRef.current && isConnected){
      publisher = OT.initPublisher(publisherContainerRef.current, {
        insertMode: 'append',
        width: '100%',
        height: '100%',
        showControls: false
      });

      publisherRef.current = publisher
    
    session.publish(publisher, (error: any) => {
      if (error) {
        console.error('Error publishing:', error);
      }
    });
  }

    return () => {
      if(publisher) session.unpublish(publisher);
      session.off('connectionCreated', handleConnectionCreated);
      session.off('connectionDestroyed', handleConnectionDestroyed);
    };
  }, [session, isConnected]);

  useEffect(() => {
    if (publisherRef.current) {
      publisherRef.current.publishAudio(isMicOn);
    }
  }, [isMicOn, publisherRef.current]);

  useEffect(() => {
    if (publisherRef.current) {
      publisherRef.current.publishVideo(isCameraOn);
    }
  }, [isCameraOn, publisherRef.current]);

  return <LocalVideo 	
              className="[&>div>div:first-child]:w-[100%] [&>div>div:first-child]:h-[100%] [&>div>div:nth-child(2)]:hidden" 
              cameraOff={false} 
              microphoneStatus='on' 
              name='' 
              ref={publisherContainerRef} 
          />;
};