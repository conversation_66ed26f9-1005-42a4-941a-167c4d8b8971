import React, { useEffect, ReactElement, useCallback } from 'react';
import { RemoteVideo } from '@umahealth/occipital/client';
import { Session } from '@opentok/client';


/**
 * Componente `Subscriber`
 * 
 * Este componente maneja la suscripción y visualización de un flujo de medios remoto en una sesión de OpenTok.
 * Utiliza `RemoteVideo` para mostrar el video del flujo suscrito y maneja la suscripción al flujo en la sesión.
 * 
 * @component
 * @param {Object} props - Propiedades del componente.
 * @param {Session} props.session - Instancia de la sesión de OpenTok a la que se suscribe el flujo de medios.
 * @param {any} props.stream - Flujo de medios que se va a suscribir y mostrar.
 * @param {ReactElement} props.children - Elementos secundarios que se mostrarán encima del video remoto.
 * 
 */
export const Subscriber = ({ session, stream, children } : { session: Session, stream :any, children: ReactElement } ) => {

  const subscriberRef = useCallback((node: HTMLDivElement & { videoClassname?: string } | null) => {
    if (node && session && stream) {
      session.subscribe(stream, node, {
        insertMode: 'append',
        width: '100%',
        height: '100%',
        showControls: false
      });
    }
  }, [session, stream]);

  useEffect(() => {
    if (!session || !stream) return;

    return () => {
      if (session && stream) {
        const subscribers = session.getSubscribersForStream(stream);
        subscribers.forEach(subscriber => session.unsubscribe(subscriber));
      }
    };
  }, [session, stream]);

  return <RemoteVideo 
          className="[&>div]:w-full [&>div]:h-full h-full overflow-hidden [&>div>div>div>button]:w-full" 
          ref={subscriberRef}
         >  
            {children}
         </RemoteVideo>;
};