"use client"
import React, { useState, useEffect } from 'react';
import { Subscriber } from '../Suscriber/Suscriber';
import OT, { Session } from '@opentok/client';
import PermissionCheck from '../PermissionCheck/PersmissionCheck';
import { PublisherComponent } from '../Publisher/Publisher';
import { Loader } from '@umahealth/occipital';


/**
 * Componente `VideoCall`
 * 
 * Este componente maneja una llamada de video utilizando la biblioteca OpenTok. Se encarga de inicializar la sesión de OpenTok, 
 * conectar al usuario, y gestionar los flujos de medios. Muestra el componente `PermissionCheck` para solicitar permisos de 
 * cámara y micrófono, y el componente `Subscriber` para mostrar el video del flujo remoto, así como el componente 
 * `PublisherComponent` para permitir al usuario publicar su video y audio.
 * 
 * @component
 * @param {Object} props - Propiedades del componente.
 * @param {string} [props.sessionId] - ID de la sesión de OpenTok.
 * @param {string} [props.token] - Token de conexión para la sesión de OpenTok.
 * @param {boolean} props.isMicOn - Indica si el micrófono del usuario está activado.
 * @param {boolean} props.isCameraOn - Indica si la cámara del usuario está activada.
 * 
 */
export const VideoCall = ({sessionId, token, isMicOn, isCameraOn} : {sessionId?: string ; token?: string, isCameraOn:boolean, isMicOn: boolean}) => {

  const [session, setSession] = useState<Session | null>(null);
  const [remoteStream, setRemoteStream] = useState(null);

  useEffect(() => {
    if (!sessionId || !token) return;

    const session = OT.initSession(process.env.NEXT_PUBLIC_OPENTOK_APIKEY, sessionId);

    if(!session) return;

    setSession(session)

    session.on('streamCreated', (event: any) => {
      setRemoteStream(event.stream);
    });

    session.on('streamDestroyed', () => {
      setRemoteStream(null);
    });


    session.connect(token, (error) => {
      if (error) {
        console.error('Error connecting to the session:', error);
      }
    });

    return () => {
      if (session) {
				session.disconnect()
			}
    };
  }, [sessionId, token]);
  

  if (!sessionId || !token) {
    return <div className='flex justify-center items-center h-[30%]'><Loader size='size-6' /></div>;
  }

  return (
    <div className='grow'>
      <PermissionCheck />
      {session &&
        <Subscriber session={session} stream={remoteStream}>
          <PublisherComponent isMicOn={isMicOn} isCameraOn={isCameraOn} session={session} />
        </Subscriber>
      }
    </div>
  );
};