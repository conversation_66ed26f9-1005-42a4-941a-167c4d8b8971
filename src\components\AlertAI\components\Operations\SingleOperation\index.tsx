"use client";

import { CardTitle } from "@/storybook/components/UICard/Atoms/CardTitle/CardTitle";
import { CardUserName } from "@/storybook/components/UICard/Atoms/CardUserName/CardUserName";
import { KeyValue } from "@/storybook/components/UICard/Atoms/KeyValue/KeyValue";
import { PrescriptionMR } from "@/storybook/components/UICard/Molecules/PrescriptionMR/PrescriptionMR";
import { Loader, Text } from "@umahealth/occipital";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Icon,
} from "@umahealth/occipital/client";
import CallButton from "../../Consultorio/CallButton/CallButton";
import { useGetOperationById } from "../../../../../services/requests/alertAI/getdata/useGetOperationById";
import { extractSummaryOperationData } from "../../../utils/extractOperationData";
import { getPendingOperationsReturnValue } from "@/services/requests/alertAI/getdata/getPendingOperations";
import { IOperation } from "@umahealth/entities";

/**
 * Componente `SingleOperation`
 *
 * Este componente muestra los detalles de una operación específica en un formato de tarjeta expandible. Utiliza
 * un acordeón para permitir la visualización y ocultación de los resultados de evaluación de la operación. Muestra
 * información relevante sobre el usuario y permite realizar una llamada si la operación está completa.
 *
 * @component
 * @param {Object} props - Propiedades del componente.
 * @param {IOperationResponse} props.operation - Datos de la operación a mostrar.
 *
 */
export default function OperationCard({
  operation,
  onClickRevisarApto,
  onClickIniciarConsulta,
  onClickRetomarConsulta,
  isLoading
}: {
  operation: getPendingOperationsReturnValue[0];
  onClickRevisarApto: (operationId: IOperation["id"]) => void;
  onClickIniciarConsulta: (operationId: IOperation["id"]) => void;
  onClickRetomarConsulta: (operationId: IOperation["id"]) => void;
  isLoading?: boolean
}) {
  const getOperationById = useGetOperationById(operation.id);

  if (getOperationById.isLoading || getOperationById.data === undefined) {
    return (
      <PrescriptionMR className="!border-none drop-shadow-md rounded-2xl sm:px-4 sm:py-6 px-2 py-4">
        <Loader />
      </PrescriptionMR>
    );
  }

  return (
    <PrescriptionMR className="!border-none drop-shadow-md rounded-2xl sm:px-4 sm:py-6 px-2 py-4">
      <Accordion type="single" collapsible>
        <AccordionItem value={`operation-${getOperationById.data?.id}`}>
          <AccordionTrigger className="py-0 px-4">
            <div className="flex justify-between sm:pr-8 pr-2 w-full">
              <div>
                <div className="flex gap-1">
                  {getOperationById.data?.questions?.resume?.apto ? (
                    <>
                      <Icon
                        name="checkFilled"
                        color="text-success"
                        size="size-5"
                      />
                      <Text
                        tag="p"
                        color="text-success-700"
                        weight="font-semibold"
                        className="italic"
                      >
                        Apto
                      </Text>
                    </>
                  ) : (
                    <>
                      <Icon
                        name="cancelFilled"
                        color="text-error-500"
                        size="size-5"
                      />
                      <Text
                        tag="p"
                        color="text-error-700"
                        weight="font-semibold"
                        className="italic"
                      >
                        No Apto
                      </Text>
                    </>
                  )}
                </div>
                <CardUserName
                  className="!text-primary-800"
                  fullname={`${getOperationById.data?.user.firstName} ${getOperationById.data?.user.lastName}`}
                  pronouns={`legajo: ${getOperationById.data?.user.nomina}`}
                />
              </div>
              {operation && (
                <CallButton
                  onClickRevisarApto={onClickRevisarApto}
                  onClickIniciarConsulta={onClickIniciarConsulta}
                  onClickRetomarConsulta={onClickRetomarConsulta}
                  operation={getOperationById.data}
                  isLoading={isLoading}
                />
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent className="pb-0">
            <hr className="mt-4"></hr>
            <div className="mt-4 px-4">
              <CardTitle color="text-primary-800" className=" mb-3 mt-5">
                Resultados de Evaluación
              </CardTitle>
              <div className="flex flex-col gap-1">
                {extractSummaryOperationData(getOperationById.data)?.map(
                  (item) => {
                    return (
                      <KeyValue
                        key={item.key}
                        someKey={item.key}
                        someValue={item.value?.toString()}
                      />
                    );
                  },
                )}
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </PrescriptionMR>
  );
}
