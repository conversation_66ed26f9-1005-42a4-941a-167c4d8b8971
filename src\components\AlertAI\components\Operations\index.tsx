"use client";

import { Text } from "@umahealth/occipital";
import Header from "@/components/GeneralComponents/Header";
import OperationCard from "./SingleOperation";
import { getPendingOperationsReturnValue } from "@/services/requests/alertAI/getdata/getPendingOperations";
import { IOperation } from "@umahealth/entities";

/**
 * Componente `Operaciones`
 *
 * Este componente muestra una lista de solicitudes de evaluación. Cada solicitud se representa como una tarjeta
 * utilizando el componente `SingleOperation`. Incluye un encabezado y un conteo de la cantidad de evaluaciones
 * que se están mostrando.
 *
 * @component
 * @param {Object} props - Propiedades del componente.
 * @param {IOperationResponse[]} props.operations - Lista de operaciones a mostrar.
 *
 */

export default function Operaciones({
  operations,
  onClickRevisarApto,
  onClickIniciarConsulta,
  onClickRetomarConsulta,
}: {
  operations: getPendingOperationsReturnValue;
  onClickRevisarApto: (operationId: IOperation["id"]) => void;
  onClickIniciarConsulta: (operationId: IOperation["id"]) => void;
  onClickRetomarConsulta: (operationId: IOperation["id"]) => void;
}) {
  return (
    <>
      <Header title="Solicitudes" />
      <div className="bg-white sm:p-8 p-4 rounded-3xl my-6 mx-2">
        <Text
          tag="h2"
          color="text-primary-800"
          weight="font-semibold"
          className="mb-6 sm:text-l text-m text-center sm:text-left"
        >
          Listado de evaluaciones ({operations?.length})
        </Text>
        <div className="bg-[#F5F7F9] py-2 rounded-2xl">
          {operations?.map((operation) => {
            return (
              <OperationCard
                onClickRevisarApto={onClickRevisarApto}
                onClickIniciarConsulta={onClickIniciarConsulta}
                onClickRetomarConsulta={onClickRetomarConsulta}
                key={operation.id}
                operation={operation}
              />
            );
          })}
        </div>
      </div>
    </>
  );
}
