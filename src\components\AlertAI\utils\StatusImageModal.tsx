/**
 * Componente para mostrar un ícono basado en el estado.
 *
 * @param {Object} props - Props del componente.
 * @param {"warning" | "success"} props.status - El estado para determinar qué ícono mostrar. Puede ser "warning" (advertencia) o "success" (éxito).
 * @param {string} [props.className] - Clase CSS opcional para aplicar al componente SVG.
 */
export function StatusImageModal({
	status,
	className,
}: {
	status: "warning" | "success";
	className?: string;
}) {
	if (status === "warning") {
		return (
			<svg
				className={className}
				width="100"
				height="101"
				viewBox="0 0 100 101"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path
					d="M93.4099 37.5595L93.0099 36.6755C89.7099 29.2195 82.6579 24.0755 74.5999 23.5455C73.562 23.4771 72.5205 23.4858 71.4839 23.5715C64.0099 24.1935 56.9719 28.8815 53.4439 35.5895C51.3699 39.5335 50.3239 44.2495 46.9739 47.1615C42.6139 50.9515 36.1279 50.2555 30.4619 49.3475C24.7959 48.4395 18.3039 47.7475 13.9599 51.5675C11.2399 53.9535 9.99193 57.6835 9.72193 61.3115C9.00193 70.9115 14.5539 80.3115 22.4659 85.6315C30.3779 90.9515 40.2739 92.6255 49.7679 91.9455C63.0659 90.9975 76.3179 85.4055 85.1519 75.2735C93.9859 65.1415 97.7999 50.2835 93.4099 37.5595Z"
					fill="#E7FBFF"
				/>
				<path
					d="M49.8107 81.4914L22.5027 81.5609C15.8757 81.5609 11.7186 74.6557 15.019 69.1027L28.6165 46.2214L42.214 23.3401C45.5144 17.7872 53.7981 17.7655 57.1247 23.301L70.8352 46.1171L84.5457 68.9332C87.8722 74.4556 83.7456 81.4131 77.1186 81.4131L49.8107 81.4914Z"
					fill="white"
				/>
				<path
					d="M49.8112 81.491H50.3635H51.9898L58.2297 81.4563L68.1093 81.4171L74.2797 81.3954C75.3712 81.3954 76.4974 81.3954 77.641 81.3649C78.8169 81.2964 79.9671 80.9933 81.0241 80.4735C82.1263 79.9161 83.095 79.127 83.8636 78.1602C84.6642 77.1487 85.2194 75.9654 85.4856 74.7032C85.7579 73.3865 85.6845 72.0217 85.2725 70.7418C84.8681 69.4373 84.0202 68.2936 83.3157 67.0587L73.9318 51.4392L63.5696 34.2065C61.7722 31.2148 59.9459 28.1709 58.0906 25.0748L57.4079 23.9268C57.1774 23.5398 56.947 23.1528 56.6904 22.7875C56.1664 22.0539 55.5277 21.4093 54.7989 20.8786C54.0545 20.3485 53.2312 19.9391 52.3594 19.6654C51.4859 19.3967 50.5773 19.2589 49.6634 19.2566C48.7507 19.257 47.8433 19.3948 46.9717 19.6654C45.2333 20.2131 43.7215 21.3141 42.6668 22.8006C42.4146 23.1702 42.1841 23.5572 41.9537 23.9486L41.2623 25.1096C39.4214 28.2057 37.6096 31.2495 35.8267 34.2412L25.5428 51.5262L16.2372 67.1804C15.5371 68.4154 14.6935 69.5634 14.2978 70.8722C13.8928 72.154 13.8269 73.5188 14.1065 74.8336C14.3765 76.0957 14.9363 77.2776 15.7415 78.2863C16.5163 79.2477 17.4892 80.0308 18.594 80.5822C19.6548 81.0925 20.806 81.3881 21.9814 81.4519C23.1251 81.4823 24.2556 81.4519 25.3471 81.4519H31.5131H41.3927H49.259H47.6327L41.3927 81.4823L31.5131 81.5258L25.3471 81.5476C24.2513 81.5476 23.1337 81.5476 21.9771 81.5476C20.7837 81.4921 19.6138 81.1963 18.5375 80.6779C17.4129 80.1228 16.423 79.3288 15.6371 78.3515C14.8204 77.3423 14.2488 76.1575 13.9673 74.8902C13.6775 73.5517 13.742 72.1608 14.1543 70.8548C14.3695 70.1885 14.6614 69.5493 15.024 68.9502L16.1024 67.1282C19.0158 62.2232 22.1249 56.9878 25.3949 51.474L35.6659 34.176C37.4487 31.1756 39.2605 28.1317 41.1014 25.0444L41.7928 23.8833C42.0232 23.492 42.2537 23.1006 42.5146 22.7223C43.5928 21.2029 45.1382 20.0775 46.9152 19.5175C48.7052 18.9522 50.6259 18.9522 52.4159 19.5175C53.2997 19.8004 54.1344 20.2184 54.8902 20.7568C55.6339 21.3018 56.2869 21.9608 56.8252 22.7093C57.0904 23.0832 57.3209 23.4746 57.5514 23.8616C57.7819 24.2486 58.0167 24.6356 58.2471 25.0226L63.7261 34.1543L74.0797 51.3957C77.3801 56.8921 80.5153 62.1102 83.4462 66.9978L84.5376 68.8154C84.9009 69.4142 85.1927 70.0534 85.4073 70.72C85.8295 72.022 85.903 73.4118 85.6204 74.751C85.3512 76.0364 84.7853 77.241 83.968 78.2689C83.1858 79.2492 82.2008 80.0487 81.0806 80.6127C80.0073 81.1395 78.8391 81.4456 77.6454 81.5128C76.4887 81.5519 75.3668 81.5128 74.2754 81.5345H68.105H58.2297H50.3635C50.0156 81.4997 49.8112 81.491 49.8112 81.491Z"
					fill="#263238"
				/>
				<path
					d="M49.8415 23.3751H49.8197C48.7374 23.3696 47.6725 23.6478 46.731 24.1818C45.7896 24.7158 45.0045 25.4872 44.4538 26.419L19.1417 69.2769C18.5958 70.1846 18.3102 71.2251 18.3164 72.2843C18.3225 73.3436 18.6201 74.3807 19.1765 75.282C19.7373 76.2025 20.5283 76.961 21.4715 77.4826C22.4148 78.0041 23.4777 78.2708 24.5555 78.2563L75.671 77.978C76.7543 77.9851 77.8204 77.7077 78.7628 77.1736C79.7053 76.6394 80.4909 75.8672 81.0413 74.9342C81.5863 74.0261 81.8713 72.9857 81.8652 71.9267C81.859 70.8677 81.562 69.8307 81.0065 68.929L55.2335 26.3494C54.6723 25.4264 53.8793 24.6662 52.9334 24.1445C51.9875 23.6227 50.9216 23.3574 49.8415 23.3751Z"
					fill="#F7941E"
				/>
				<path
					d="M52.5771 58.3152L53.5903 39.6257L45.824 39.691L47.2025 58.3587L52.5771 58.3152Z"
					fill="white"
				/>
				<path
					d="M49.8192 61.6154H49.7801C48.5879 61.6057 47.4364 62.0486 46.5579 62.8547C46.1509 63.2391 45.8282 63.704 45.6104 64.2197C45.3926 64.7355 45.2843 65.2909 45.2925 65.8507C45.2925 66.413 45.4083 66.9692 45.6327 67.4846C45.8572 68.0001 46.1855 68.4638 46.5971 68.8468C47.4746 69.6425 48.6218 70.0747 49.8062 70.0556H49.8453C51.0256 70.0609 52.1614 69.6053 53.011 68.7859C53.4068 68.3905 53.7193 67.9196 53.9299 67.4013C54.1405 66.8829 54.245 66.3276 54.2372 65.7681C54.2285 63.3895 52.3283 61.6067 49.8192 61.6154Z"
					fill="white"
				/>
				<g opacity="0.3">
					<path
						d="M62.4856 32.3245L22.3976 56.6799L17.3274 65.4419L65.2686 37.1164L62.4856 32.3245Z"
						fill="white"
					/>
				</g>
				<g opacity="0.3">
					<path
						d="M68.7149 43.0256L14.3816 75.717C14.5928 76.5173 14.9807 77.2602 15.5165 77.8912L69.6194 44.6041L68.7149 43.0256Z"
						fill="white"
					/>
				</g>
			</svg>
		);
	}

	return (
		<svg
			width="100"
			height="101"
			viewBox="0 0 100 101"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			className={className}
		>
			<path
				d="M93.4099 37.5595L93.0099 36.6755C89.7099 29.2195 82.6579 24.0755 74.5999 23.5455C73.562 23.4771 72.5205 23.4858 71.4839 23.5715C64.0099 24.1935 56.9719 28.8815 53.4439 35.5895C51.3699 39.5335 50.3239 44.2495 46.9739 47.1615C42.6139 50.9515 36.1279 50.2555 30.4619 49.3475C24.7959 48.4395 18.3039 47.7475 13.9599 51.5675C11.2399 53.9535 9.99193 57.6835 9.72193 61.3115C9.00193 70.9115 14.5539 80.3115 22.4659 85.6315C30.3779 90.9515 40.2739 92.6255 49.7679 91.9455C63.0659 90.9975 76.3179 85.4055 85.1519 75.2735C93.9859 65.1415 97.7999 50.2835 93.4099 37.5595Z"
				fill="#E7FBFF"
			/>
			<g clipPath="url(#clip0_1269_40855)">
				<path
					d="M72.0292 18.6296H28.1024C27.0356 18.6296 26.1708 19.4945 26.1708 20.5613V84.5302C26.1708 85.597 27.0356 86.4618 28.1024 86.4618H72.0292C73.096 86.4618 73.9608 85.597 73.9608 84.5302V20.5613C73.9608 19.4945 73.096 18.6296 72.0292 18.6296Z"
					fill="#F9FAFB"
				/>
				<path
					d="M72.0307 86.4624L72.1791 86.4519C72.321 86.4385 72.4611 86.4105 72.5972 86.3683C72.8716 86.2811 73.123 86.1335 73.3329 85.9364C73.5427 85.7392 73.7057 85.4974 73.8097 85.2289C73.8819 85.0416 73.9242 84.8441 73.9351 84.6436C73.9456 84.4345 73.9351 84.2088 73.9351 83.9746C73.9351 83.5063 73.9351 82.9941 73.9351 82.4401C73.9351 81.3301 73.9351 80.0486 73.9351 78.6082C73.9351 72.8403 73.9351 64.4948 73.9205 54.1842C73.9205 49.0289 73.9205 43.3802 73.9205 37.3176C73.9205 34.2863 73.9205 31.1547 73.9205 27.9227C73.9205 26.3074 73.9205 24.6691 73.9205 23.0078C73.9205 22.1716 73.9205 21.3353 73.9205 20.4991C73.9086 20.082 73.7584 19.6806 73.4935 19.3582C73.2286 19.0358 72.864 18.8105 72.4571 18.718C72.2509 18.6757 72.0402 18.6595 71.83 18.6699H28.106C27.6213 18.6703 27.1551 18.8566 26.8036 19.1904C26.4539 19.5238 26.2458 19.9789 26.2224 20.4615C26.2224 21.4357 26.2224 22.4141 26.2224 23.3883V40.4953C26.2224 47.9147 26.2224 55.0727 26.2224 61.89C26.2224 68.7073 26.2224 75.1859 26.2224 81.2485C26.2224 82.0067 26.2224 82.7579 26.2224 83.5022C26.2224 83.8743 26.2224 84.2485 26.2224 84.6143C26.2366 84.9763 26.3558 85.3264 26.5653 85.622C26.7761 85.9167 27.066 86.1457 27.4015 86.2826C27.5658 86.3484 27.738 86.3927 27.9137 86.4143C28.0893 86.4289 28.2691 86.4247 28.4489 86.4268H38.6529L63.0747 86.4415L69.7143 86.4519L63.0747 86.4624L38.6529 86.477H28.428C28.2503 86.477 28.0705 86.477 27.8844 86.477C27.7014 86.455 27.5221 86.4086 27.3513 86.339C27.0045 86.1964 26.7057 85.9573 26.4905 85.6502C26.2752 85.3431 26.1525 84.9807 26.1367 84.606C26.1367 84.2338 26.1367 83.8659 26.1367 83.4917V81.2485C26.1367 75.1859 26.1367 68.7052 26.1367 61.89C26.1367 55.0748 26.1367 47.9147 26.1367 40.4953C26.1367 36.7852 26.1367 33.0132 26.1367 29.1791V23.3904C26.1367 22.4183 26.1367 21.4461 26.1367 20.4636C26.1619 19.9561 26.3813 19.4777 26.7494 19.1275C27.1176 18.7773 27.6063 18.582 28.1144 18.5821H71.8216C72.0381 18.5739 72.2549 18.5907 72.4676 18.6323C72.8932 18.7285 73.2747 18.9636 73.5521 19.3004C73.8295 19.6372 73.9871 20.0567 74 20.4929C74 21.3291 74 22.1653 74 23.0015V27.9227C74 31.1505 74 34.2821 74 37.3176C74 43.3802 74 49.0247 74 54.1842C74 64.4948 74 72.8403 73.9853 78.6082C73.9853 80.0486 73.9853 81.3259 73.9853 82.4401C73.9853 82.9941 73.9853 83.5063 73.9853 83.9746C73.9853 84.2108 73.9853 84.4324 73.9853 84.6457C73.9741 84.8479 73.9304 85.047 73.8557 85.2352C73.7321 85.5488 73.5297 85.8253 73.2683 86.038C73.0806 86.1872 72.8684 86.3025 72.6411 86.3787C72.4999 86.4198 72.3549 86.4464 72.2083 86.4582L72.0307 86.4624Z"
					fill="#3B3E45"
				/>
				<g opacity="0.3">
					<path
						d="M30.3418 84.93H70.4512L70.0101 76.5866V72.1462L30.3418 78.3447V84.93Z"
						fill="#00151D"
					/>
				</g>
				<g opacity="0.3">
					<path
						d="M62.1256 18.7681L63.0371 21.4732V22.7966H62.0085L62.1256 18.7681Z"
						fill="#00151D"
					/>
				</g>
				<path
					d="M28.303 21.3477V83.6587H60.809H71.8304V21.3477H28.303Z"
					fill="#FCFCFC"
				/>
				<path
					d="M71.8293 72.516C71.8293 72.516 71.8293 72.4365 71.8293 72.2776C71.8293 72.1188 71.8293 71.8804 71.8293 71.5689C71.8293 70.9418 71.8293 70.0177 71.8293 68.8115C71.8293 66.3948 71.8293 62.8597 71.8168 58.3713C71.8168 49.3819 71.8168 36.5982 71.8001 21.3476L71.8419 21.3894H28.3145L28.3605 21.3434V21.3998C28.3605 45.3534 28.3605 67.2269 28.3479 83.6544L28.3145 83.6189L52.0778 83.6356L28.3145 83.6503H28.2789V83.6168C28.2789 67.1892 28.2789 45.3158 28.2789 21.3622V21.2598H28.3249H71.8523H71.8921V21.3455C71.8921 36.6065 71.8795 49.3882 71.8753 58.3692C71.8753 62.8576 71.8753 66.3927 71.8649 68.8094C71.8649 70.0157 71.8649 70.9418 71.8649 71.5669C71.8649 71.8783 71.8649 72.1146 71.8649 72.2756C71.8649 72.4365 71.8293 72.516 71.8293 72.516Z"
					fill="#3B3E45"
				/>
				<path
					d="M41.4228 16.9412H60.8022C61.1532 16.9412 61.4898 17.0806 61.738 17.3288C61.9861 17.5769 62.1255 17.9135 62.1255 18.2645V23.3299H40.1016V18.2645C40.1016 17.9139 40.2407 17.5776 40.4884 17.3295C40.7361 17.0814 41.0722 16.9417 41.4228 16.9412Z"
					fill="#3B3E45"
				/>
				<path
					d="M53.9165 14.5388H48.3118L48.9661 19.3429H53.189L53.9165 14.5388Z"
					fill="#3B3E45"
				/>
				<path
					d="M60.7099 16.941C60.7099 16.9661 56.4117 16.987 51.1121 16.987C45.8126 16.987 41.5165 16.9661 41.5165 16.941C41.5165 16.9159 45.8126 16.895 51.1121 16.895C56.4117 16.895 60.7099 16.9159 60.7099 16.941Z"
					fill="#666973"
				/>
				<path
					d="M40.5948 22.2986C40.5677 22.2986 40.5468 21.3955 40.5468 20.2791C40.5468 19.1627 40.5677 18.2617 40.5948 18.2617C40.622 18.2617 40.6408 19.1648 40.6408 20.2791C40.6408 21.3934 40.6178 22.2986 40.5948 22.2986Z"
					fill="#666973"
				/>
				<path
					d="M61.4129 21.8203C61.3659 21.211 61.3506 20.5998 61.3669 19.9889C61.3506 19.3774 61.3659 18.7655 61.4129 18.1555C61.438 18.1555 61.4589 18.9771 61.4589 19.9889C61.4589 21.0008 61.438 21.8203 61.4129 21.8203Z"
					fill="#666973"
				/>
				<path
					d="M60.3508 22.6709C60.3508 22.696 55.9397 22.7169 50.5001 22.7169C45.0605 22.7169 40.6473 22.696 40.6473 22.6709C40.6473 22.6458 45.0563 22.6228 50.5001 22.6228C55.9439 22.6228 60.3508 22.6437 60.3508 22.6709Z"
					fill="#666973"
				/>
				<path
					d="M66.3199 33.3057H42.2806V34.7837H66.3199V33.3057Z"
					fill="#F9FAFB"
				/>
				<path
					d="M53.1871 36.3247H42.2806V37.7923H53.1871V36.3247Z"
					fill="#F9FAFB"
				/>
				<path
					d="M39.0293 32.676H35.0844C34.6388 32.676 34.2775 33.0373 34.2775 33.483V37.4279C34.2775 37.8735 34.6388 38.2348 35.0844 38.2348H39.0293C39.475 38.2348 39.8362 37.8735 39.8362 37.4279V33.483C39.8362 33.0373 39.475 32.676 39.0293 32.676Z"
					fill="#F7941E"
				/>
				<path
					d="M36.9762 36.778L35.4187 35.5759L35.8389 35.0324L36.8821 35.8393L38.3664 34.1313L38.8848 34.5808L36.9762 36.778Z"
					fill="#FCFCFC"
				/>
				<path
					d="M66.3199 42.4563H42.2806V43.9343H66.3199V42.4563Z"
					fill="#F9FAFB"
				/>
				<path
					d="M53.1871 45.4727H42.2806V46.9402H53.1871V45.4727Z"
					fill="#F9FAFB"
				/>
				<path
					d="M39.0293 41.8247H35.0844C34.6388 41.8247 34.2775 42.186 34.2775 42.6317V46.5765C34.2775 47.0222 34.6388 47.3835 35.0844 47.3835H39.0293C39.475 47.3835 39.8362 47.0222 39.8362 46.5765V42.6317C39.8362 42.186 39.475 41.8247 39.0293 41.8247Z"
					fill="#F7941E"
				/>
				<path
					d="M36.9762 45.9259L35.4187 44.7239L35.8389 44.1803L36.8821 44.9873L38.3664 43.2793L38.8848 43.7288L36.9762 45.9259Z"
					fill="#FCFCFC"
				/>
				<path
					d="M66.3199 52.2588H42.2806V53.7368H66.3199V52.2588Z"
					fill="#F9FAFB"
				/>
				<path
					d="M53.1871 55.2756H42.2806V56.7432H53.1871V55.2756Z"
					fill="#F9FAFB"
				/>
				<path
					d="M66.3199 61.3188H42.2806V62.7969H66.3199V61.3188Z"
					fill="#F9FAFB"
				/>
				<path
					d="M53.1871 64.3379H42.2806V65.8055H53.1871V64.3379Z"
					fill="#F9FAFB"
				/>
				<path
					d="M39.0293 51.6272H35.0844C34.6388 51.6272 34.2775 51.9885 34.2775 52.4342V56.379C34.2775 56.8247 34.6388 57.186 35.0844 57.186H39.0293C39.475 57.186 39.8362 56.8247 39.8362 56.379V52.4342C39.8362 51.9885 39.475 51.6272 39.0293 51.6272Z"
					fill="#F7941E"
				/>
				<path
					d="M36.9762 55.7292L35.4187 54.5271L35.8389 53.9835L36.8821 54.7905L38.3664 53.0825L38.8848 53.532L36.9762 55.7292Z"
					fill="#FCFCFC"
				/>
				<path
					d="M39.1064 66.5979H35.0047C34.7866 66.5979 34.5774 66.5114 34.423 66.3574C34.2686 66.2033 34.1816 65.9944 34.181 65.7763V61.6746C34.1816 61.4565 34.2686 61.2476 34.423 61.0935C34.5774 60.9395 34.7866 60.853 35.0047 60.853H39.1064C39.2143 60.853 39.3211 60.8743 39.4208 60.9156C39.5205 60.9569 39.611 61.0174 39.6873 61.0937C39.7636 61.17 39.8241 61.2605 39.8654 61.3602C39.9067 61.4599 39.928 61.5667 39.928 61.6746V65.7763C39.928 65.9942 39.8414 66.2032 39.6873 66.3572C39.5332 66.5113 39.3243 66.5979 39.1064 66.5979ZM35.0047 61.0412C34.8384 61.0412 34.6789 61.1073 34.5612 61.2249C34.4436 61.3425 34.3775 61.502 34.3775 61.6683V65.77C34.3775 65.9363 34.4436 66.0959 34.5612 66.2135C34.6789 66.3311 34.8384 66.3972 35.0047 66.3972H39.1064C39.2727 66.3972 39.4322 66.3311 39.5498 66.2135C39.6675 66.0959 39.7335 65.9363 39.7335 65.77V61.6746C39.7335 61.5083 39.6675 61.3488 39.5498 61.2311C39.4322 61.1135 39.2727 61.0475 39.1064 61.0475L35.0047 61.0412Z"
					fill="#F7941E"
				/>
				<path
					d="M66.3199 71.686H42.2806V73.1641H66.3199V71.686Z"
					fill="#F9FAFB"
				/>
				<path
					d="M53.1871 74.7051H42.2806V76.1727H53.1871V74.7051Z"
					fill="#F9FAFB"
				/>
				<path
					d="M39.1064 76.9652H35.0047C34.7876 76.962 34.5803 76.8744 34.4266 76.7211C34.2729 76.5678 34.1848 76.3607 34.181 76.1436V72.0419C34.1843 71.8245 34.2721 71.6169 34.4259 71.4631C34.5796 71.3093 34.7873 71.2215 35.0047 71.2183H39.1064C39.3245 71.2188 39.5334 71.3058 39.6875 71.4602C39.8415 71.6147 39.928 71.8238 39.928 72.0419V76.1436C39.9274 76.3613 39.8407 76.57 39.6867 76.724C39.5328 76.8779 39.3241 76.9646 39.1064 76.9652ZM35.0047 71.4064C34.8384 71.4064 34.6789 71.4725 34.5612 71.5901C34.4436 71.7077 34.3775 71.8672 34.3775 72.0336V76.1352C34.3775 76.3016 34.4436 76.4611 34.5612 76.5787C34.6789 76.6963 34.8384 76.7624 35.0047 76.7624H39.1064C39.2727 76.7624 39.4322 76.6963 39.5498 76.5787C39.6675 76.4611 39.7335 76.3016 39.7335 76.1352V72.0419C39.7335 71.8756 39.6675 71.7161 39.5498 71.5985C39.4322 71.4809 39.2727 71.4148 39.1064 71.4148L35.0047 71.4064Z"
					fill="#F7941E"
				/>
				<path
					d="M63.3531 27.5857H39.387V29.6386H63.3531V27.5857Z"
					fill="#F7941E"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1269_40855">
					<rect
						width="72"
						height="72"
						fill="white"
						transform="translate(14 14.5)"
					/>
				</clipPath>
			</defs>
		</svg>
	);
}