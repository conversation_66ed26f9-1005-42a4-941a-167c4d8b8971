import { IOperation } from "@umahealth/entities";


  function isAlcoholized(
    alcoholResult: number | "Negativo" | "Positivo" | undefined,
  ) {
    if (alcoholResult === "Negativo" || Number(alcoholResult) === 0) {
      return 'Negativo';
    }
    if (alcoholResult === "Positivo") {
      return alcoholResult;
    }
    if (Number(alcoholResult) > 0) {
      return `Positivo (${Number(alcoholResult)})`;
    }

    return 'No se realiza';
  }

  function resultTestClock( testClock : boolean | undefined | null){
    if (testClock === true){
      return 'Satisfactorio'
    } 

    if (testClock === false){
      return 'Insatisfactorio'
    }

    if (testClock === null){
      return 'Se negó a realizarse el test'
    }

    return 'No hay información'
  }

/** Funciones helpers para tranformar los datos a mostrar en el formulario que va a ver el médico durante la consulta.
 * Hay casos donde si no esta el dato no lo muestra y otros donde informa que no fue tomado el estudio
 * Por como estan armados los datos, null suele significar que no se tomó el dato y false que no fue satisfactorio
 * @param {IOperationResponse | undefined} operationSummary - Resumen de la operación.
 * @returns {Array<{ key: string; value: string | boolean | number | null }>} - Datos transformados para mostrar.
 */
export function extractSummaryOperationData(
  operationSummary: IOperation | undefined,
): { key: string; value: string | boolean | number | null }[] {
  if (!operationSummary) {
    return [];
  }

  if (!operationSummary.questions) {
    return [];
  }

  const substance_exam = operationSummary.questions.substance_exam;
  const health_exam = operationSummary.questions.health_exam;
  const observation_exam = operationSummary.questions.observations_exam;

  return [
    {
      key: "Alcoholemia",
      value: isAlcoholized(substance_exam?.breathalyzer_result),
    },
    {
      key: "Alcoholemia segunda toma",
      value: isAlcoholized(substance_exam?.breathalyzer_result_second_attempt as number | "Negativo" | "Positivo" )
    },
    {
      key: "Frecuencia Cardíaca",
      value: health_exam?.heart_rate
        ? `${health_exam.heart_rate} lpm`
        : "Sin información",
    },
    {
      key: "Frecuencia Cardíaca - segunda toma",
      value: health_exam?.heart_rate_secondAttemp
        ? `${health_exam.heart_rate} lpm`
        : "No se realiza",
    },
    {
      key: "Medicación",
      value: observation_exam?.takes_medication ? "Sí" : "No",
    },
    {
      key: "Oximetría",
      value: health_exam?.oximetry
        ? `${health_exam.oximetry}%`
        : "Sin información",
    },
    {
      key: "Prueba de Sustancias Antígenas",
      value: substance_exam?.perform_substance_test
        ? `${substance_exam?.substance_test_result} (${substance_exam?.positive_substances_second_attempt ?? substance_exam?.positive_substances})`
        : "No se realiza",
    },
    {
      key: "Prueba de sustancias antígenas, segunda toma",
      value: substance_exam?.perform_substance_test_second_attempt
        ? `${substance_exam?.substance_test_result_second_attempt} (${substance_exam?.positive_substances_second_attempt})`
        : "No se realiza",
    },
    {
      key: "Test del reloj",
      value: resultTestClock(
        health_exam?.test_result_secondAttemp ?? health_exam?.test_result,
      ),
    },
  ];
}

/**
 * Extrae los datos generales de observación de la operación.
 * Si el dato no está disponible, muestra "Sin información".
 *
 * @param {IOperationResponse | undefined} operationSummary - Resumen de la operación.
 * @returns {Array<{ key: string; value: string | boolean | number | null }>} - Datos generales de observación transformados para mostrar.
 */
export function extractGeneralObservationData(
  operationSummary: IOperation | undefined,
): { key: string; value: string | boolean | number | null }[] {
  if (!operationSummary) return [];

  const answers = [];

  if (
    !isNullUndefined(operationSummary?.questions?.observations_exam?.oriented)
  ) {
    answers.push({
      key: "Orientado en tiempo y espacio",
      value: operationSummary?.questions?.observations_exam?.oriented
        ? "Sí"
        : "No",
    });
  } else {
    answers.push({
      key: "Orientado en tiempo y espacio",
      value: "Sin información",
    });
  }

  if (
    !isNullUndefined(
      operationSummary?.questions?.observations_exam?.gait_alteration,
    )
  ) {
    answers.push({
      key: "Alteración de la marcha",
      value: operationSummary?.questions?.observations_exam?.gait_alteration
        ? "Sí"
        : "No",
    });
  } else {
    answers.push({ key: "Alteración de la marcha", value: "Sin información" });
  }

  if (
    !isNullUndefined(
      operationSummary?.questions?.observations_exam?.takes_medication,
    )
  ) {
    answers.push({
      key: "Medicación",
      value: operationSummary?.questions?.observations_exam?.takes_medication
        ? "Sí"
        : "No",
    });
  } else {
    answers.push({ key: "Medicación", value: "Sin información" });
  }

  return answers;
}

/**
 * Extrae los datos del examen de salud de la operación.
 * Si el dato no está disponible, muestra "Sin información".
 *
 * @param {IOperationResponse | undefined} operationSummary - Resumen de la operación.
 * @returns {Array<{ key: string; value: string | boolean | number | null }>} - Datos del examen de salud transformados para mostrar.
 */
export function extractHealthExamData(
  operationSummary: IOperation | undefined,
): { key: string; value: string | boolean | number | null }[] {
  if (!operationSummary) {
    return [];
  }
  if (!operationSummary.questions) {
    return [];
  }
  const answers = [];

    answers.push({
      key: "Test del reloj",
      value: resultTestClock(
        operationSummary?.questions?.health_exam?.test_result_secondAttemp ??
          operationSummary?.questions?.health_exam?.test_result
      ),
    });

  if (!isNullUndefined(operationSummary?.questions?.health_exam?.heart_rate)) {
    answers.push({
      key: "Frecuencia Cardíaca",
      value: `${operationSummary?.questions?.health_exam?.heart_rate} lpm`,
    });
  } else {
    answers.push({ key: "Frecuencia Cardíaca", value: "Sin información" });
  }

  if (
    !isNullUndefined(
      operationSummary?.questions?.health_exam?.heart_rate_secondAttemp,
    )
  ) {
    answers.push({
      key: "Frecuencia Cardíaca 2da toma",
      value: `${operationSummary?.questions?.health_exam?.heart_rate_secondAttemp} lpm`,
    });
  }

  if (!isNullUndefined(operationSummary?.questions?.health_exam?.oximetry)) {
    answers.push({
      key: "Oximetría",
      value: `${operationSummary?.questions?.health_exam?.oximetry} %`,
    });
  } else {
    answers.push({ key: "Oximetría", value: "Sin información" });
  }

  return answers;
}

/**
 * Extrae los datos del examen de sustancias de la operación.
 * Si el dato no está disponible, muestra "Sin información".
 *
 * @param {IOperationResponse | undefined} operationSummary - Resumen de la operación.
 * @returns {Array<{ key: string; value: string | boolean | number | null }>} - Datos del examen de sustancias transformados para mostrar.
 */
export function extractSubstanceExamData(
  operationSummary: IOperation | undefined,
): { key: string; value: string | boolean | number | null }[] {

  if (!operationSummary) return [];

  const answers: { key: string; value: string | boolean | number | null }[] =
    [];


  answers.push({
    key: "Alcoholemia",
    value: isAlcoholized(operationSummary?.questions?.substance_exam
      ?.breathalyzer_result)
  });


  if (
    !isNullUndefined(
      operationSummary?.questions?.substance_exam
        ?.substance_test_result_second_attempt,
    )
  ) {
    // No puede ser null pero TS parece no poder darse cuenta
    answers.push({
      key: "Alcoholemia segunda toma",
      value:isAlcoholized(operationSummary?.questions?.substance_exam
          ?.breathalyzer_result_second_attempt as number | "Negativo" | "Positivo" | undefined)
    });
  }

  if (
    !isNullUndefined(
      operationSummary?.questions?.substance_exam?.perform_substance_test,
    )
  ) {
    answers.push({
      key: "Sustancias Antígenas",
      value: operationSummary?.questions?.substance_exam?.perform_substance_test
        ? `${operationSummary?.questions?.substance_exam?.substance_test_result} (${operationSummary?.questions?.substance_exam?.positive_substances})`
        : "No se realiza",
    });
  }


  if (
    !isNullUndefined(
      operationSummary?.questions?.substance_exam?.perform_substance_test,
    )
  ) {
    answers.push({
      key: "Sustancias Antígenas segunda toma",
      value: operationSummary?.questions?.substance_exam?.perform_substance_test_second_attempt
        ? `${operationSummary?.questions?.substance_exam?.substance_test_result_second_attempt} (${operationSummary?.questions?.substance_exam?.positive_substances_second_attempt})`
        : "No se realiza",
    });
  }

  return answers;
}

function isNullUndefined(value: any): value is null | undefined {
  return value === null || value === undefined;
}
