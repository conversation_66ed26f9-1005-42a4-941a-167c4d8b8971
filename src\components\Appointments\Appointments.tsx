'use client'
import React, {
  useEffect,
  useState,
} from 'react'
import '../../components/TermsConditions/styles/TermsCamera.module.css'
import 'react-phone-input-2/lib/style.css'
import {
  usePhoneValidationModal,
} from './hooks'
import useGetProviderRatingStatus from '../Rating/hooks/useProviderRatingStatus'
import { isFarmatodo } from '@/config/endpoints'
import { getPrescriptionStatus } from './UI/Modals/services/getPrescriptionStatus'
import '../../components/TermsConditions/styles/TermsCamera.module.css'
import { IFeatures, ILicense } from '@umahealth/entities'
import {
  RawTimestamp,
} from './utils/filterMandatoryFields'
import dynamic from 'next/dynamic'
import { useCurrentAppointments } from './hooks/useCurrentAppointments'
import { ServiceCategory } from './UI/StatusNav/StatusNav'
import AssignedAppointments from './UI/AssignedAppointments'
// Lazy imports
const PhoneValidation = dynamic(
  () => import('./UI/Modals/PhoneValidation/PhoneValidation')
)
const PrescriptionSuspended = dynamic(
  () => import('./UI/Modals/PrescriptionModal/PrescriptionSuspended')
)
const PrescriptionRestablished = dynamic(
  () => import('./UI/Modals/PrescriptionModal/PrescriptionRestablished')
)
const Rating = dynamic(() => import('../Rating'))

/**
 * Pestaña que da inicio a la de turnos de appointments
 * @returns
 */
export default function Appointments({
  features,
  licenses,
  currentView,
  patientTypeFilter
}: {
  features: IFeatures
  licenses: ILicense<RawTimestamp>[]
  currentView: ServiceCategory
  patientTypeFilter: 'all' | 'adults' | 'pediatric'
}) {
  const [phoneModal, setPhoneModal] = useState(false)
  const [showPrescriptionSuspendedModal, setShowPrescriptionSuspendedModal] =
    useState<boolean>(false)
  const [
    showPrescriptionReestablishedModal,
    setShowPrescriptionReestablishedModal,
  ] = useState<boolean>(false)
  const [skipSuspended, setSkipSuspended] = useState<null | string>(null)
  const [skipReestablished, setSkipReestablished] = useState<null | string>(
    null
  )
  const isTimeToShowRatingModal = useGetProviderRatingStatus()
  const showRatingModal = !isFarmatodo && isTimeToShowRatingModal.data

  useEffect(() => {
    const fetchStatus = async () => {
      const prescriptionStatus = await getPrescriptionStatus()
      const prescriptionSuspendedStatus =
        prescriptionStatus.prescription_suspended
      const prescriptionReestablishedStatus =
        await prescriptionStatus.prescription_reestablished

      setShowPrescriptionSuspendedModal(prescriptionSuspendedStatus)
      setShowPrescriptionReestablishedModal(prescriptionReestablishedStatus)

      if (!prescriptionReestablishedStatus) {
        localStorage.removeItem('skipPrescriptionReestablishedModal')
      }
      if (!prescriptionSuspendedStatus) {
        localStorage.removeItem('skipPrescriptionSuspendedModal')
      }
    }
    fetchStatus()
  }, [])

  useEffect(() => {
    if (showPrescriptionSuspendedModal) {
      const isModalSkipped = localStorage.getItem(
        'skipPrescriptionSuspendedModal'
      )
      setSkipSuspended(isModalSkipped)
    }
    if (showPrescriptionReestablishedModal) {
      const isModalSkipped = localStorage.getItem(
        'skipPrescriptionReestablishedModal'
      )
      setSkipReestablished(isModalSkipped)
    }
  }, [showPrescriptionSuspendedModal, showPrescriptionReestablishedModal])

  usePhoneValidationModal(setPhoneModal)

  useCurrentAppointments({
    features: features,
    licenses: licenses,
    patientTypeFilter: patientTypeFilter
  })

  return (
    <>
        {showRatingModal && <Rating />}
        {process.env.NEXT_PUBLIC_COUNTRY === 'AR' &&
          showPrescriptionSuspendedModal &&
          !skipSuspended && (
            <PrescriptionSuspended
              setShowPrescriptionSuspendedModal={
                setShowPrescriptionSuspendedModal
              }
            />
          )}
        {process.env.NEXT_PUBLIC_COUNTRY === 'AR' &&
          showPrescriptionReestablishedModal &&
          !skipReestablished && (
            <PrescriptionRestablished
              setShowPrescriptionReestablishedModal={
                setShowPrescriptionReestablishedModal
              }
            />
          )}
        <AssignedAppointments
          currentView={currentView}
          patientTypeFilter={patientTypeFilter}
        />
      {!isFarmatodo && phoneModal && (
        <PhoneValidation setPhoneModal={setPhoneModal} />
      )}
    </>
  )
}
