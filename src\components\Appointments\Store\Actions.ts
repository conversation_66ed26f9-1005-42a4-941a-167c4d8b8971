import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath'
export type Views = 'online' | 'guardia' | 'consultorio' | 'today' | 'pediatric' | 'all' | 'special_consultorio' | ''

type Action_DONT_SHOW_PEDIATRIC_APPOINTMENTS = {
	type: 'DONT_SHOW_PEDIATRIC_APPOINTMENTS';
	payload: boolean;
}
type Action_SET_MODAL_APPOINTMENTS = {
	type: 'SET_MODAL_APPOINTMENTS';
	payload: string;
}
type Action_SET_ASSIGNED_APPOINTS = {
	type: 'SET_ASSIGNED_APPOINTS';
	payload: IAppointmentWithPath[];
}
type Action_SET_BAG_APPOINTS = {
	type: 'SET_BAG_APPOINTS';
	payload: IAppointmentWithPath[];
}
type Action_SET_CONSULTORIO_APPOINTMENTS = {
	type: 'SET_CONSULTORIO_APPOINTMENTS';
	payload: IAppointmentWithPath[];
}

type Action_SET_SPECIAL_CONSULTORIO_APPOINTMENTS = {
	type: 'SET_SPECIAL_CONSULTORIO_APPOINTMENTS';
	payload: IAppointmentWithPath[];
}

type Action_SET_DONE_APPOINTS = {
	type: 'SET_DONE_APPOINTS';
	payload: IAppointmentWithPath[];
}
type Action_SET_FILTERED_APPOINTS = {
	type: 'SET_FILTERED_APPOINTS';
	payload: IAppointmentWithPath[];
}
type Action_FILTER_DONE_APPOINTMENTS_DATE = {
	type: 'FILTER_DONE_APPOINTMENTS_DATE';
	payload: string;
}
type Action_FILTER_DONE_APPOINTMENTS_MONTH = {
	type: 'FILTER_DONE_APPOINTMENTS_MONTH';
	payload: number;
}
type Action_FILTER_DONE_APPOINTMENTS_YEAR = {
	type: 'FILTER_DONE_APPOINTMENTS_YEAR';
	payload: number;
}
type Action_FILTER_ASSIGN_APPOINTMENTS_DATE = {
	type: 'FILTER_ASSIGN_APPOINTMENTS_DATE';
	payload: number;
}
type Action_FILTER_ASSIGN_APPOINTMENTS_MONTH = {
	type: 'FILTER_ASSIGN_APPOINTMENTS_MONTH';
	payload: number;
}
type Action_FILTER_ASSIGN_APPOINTMENTS_YEAR = {
	type: 'FILTER_ASSIGN_APPOINTMENTS_YEAR';
	payload: number;
}
type Action_FILTER_DONE_PATIENT = {
	type: 'FILTER_DONE_PATIENT';
	payload: string;
}
type Action_SET_ATT_APPOINTMENTS = {
	type: 'SET_ATT_APPOINTMENTS';
	payload: IAppointmentWithPath[];
}
type Action_FILTER_PATIENT_DNI = {
	type: 'FILTER_PATIENT_DNI';
	payload: string;
}

type Action_FILTER_PATIENT_CREDENTIAL = {
	type: 'FILTER_PATIENT_CREDENTIAL';
	payload: string;
}

type Action_CLEAN_FILTERS = {
	type: 'CLEAN_FILTERS';
}

type Action_FILTER_DONE_APPOINTMENTS_ONSITE = {
	type: 'FILTER_DONE_APPOINTMENTS_ONSITE';
	payload: boolean;
}
type Action_FILTER_DONE_APPOINTMENTS_ONLINE = {
	type: 'FILTER_DONE_APPOINTMENTS_ONLINE';
	payload: boolean;
}
type Action_SET_SPECIALIST_ATT_APPOINTMENTS = {
	type: 'SET_SPECIALIST_ATT_APPOINTMENTS';
	payload: IAppointmentWithPath[];
}
type Action_SET_CONSULTORIO_ATT_APPOINTMENTS = {
	type: 'SET_CONSULTORIO_ATT_APPOINTMENTS';
	payload: IAppointmentWithPath[];
}

type Action_SET_PEDIATRIC_APPOINTS = {
	type: 'SET_PEDIATRIC_APPOINTS';
	payload: IAppointmentWithPath[];
}

export type ActionAppointments =
|Action_SET_MODAL_APPOINTMENTS
|Action_SET_ASSIGNED_APPOINTS
|Action_SET_BAG_APPOINTS
|Action_SET_CONSULTORIO_APPOINTMENTS
|Action_SET_SPECIAL_CONSULTORIO_APPOINTMENTS
|Action_SET_DONE_APPOINTS
|Action_SET_FILTERED_APPOINTS
|Action_FILTER_DONE_APPOINTMENTS_DATE
|Action_FILTER_DONE_APPOINTMENTS_MONTH
|Action_FILTER_DONE_APPOINTMENTS_YEAR
|Action_FILTER_ASSIGN_APPOINTMENTS_DATE
|Action_FILTER_ASSIGN_APPOINTMENTS_MONTH
|Action_FILTER_ASSIGN_APPOINTMENTS_YEAR
|Action_FILTER_DONE_PATIENT
|Action_SET_ATT_APPOINTMENTS
|Action_FILTER_PATIENT_DNI
|Action_FILTER_PATIENT_CREDENTIAL
|Action_CLEAN_FILTERS
|Action_DONT_SHOW_PEDIATRIC_APPOINTMENTS
|Action_FILTER_DONE_APPOINTMENTS_ONSITE
|Action_FILTER_DONE_APPOINTMENTS_ONLINE
|Action_SET_SPECIALIST_ATT_APPOINTMENTS
|Action_SET_CONSULTORIO_ATT_APPOINTMENTS
|Action_SET_PEDIATRIC_APPOINTS
