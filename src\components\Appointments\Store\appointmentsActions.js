import { collection, query, where, getDocs } from '@/config/firebase'
import {errorHandler} from '@/config/stackdriver'
import {firestore} from '@/config/firebase'
import { store } from '@/store/configStore'

const { dispatch, getState } = store

export const getDoneAppointsForBag = async () => {
	const state = getState()
	const doctor = state.user.profile
	dispatch({ type: 'SET_LOADING', payload: true })

	if(!doctor?.cuit) throw new Error('Field cuit not found in this provider')

	let doneAppointmentsQuery 
	if (process.env.NEXT_PUBLIC_COUNTRY !== 'AR') {
		doneAppointmentsQuery = query(
		collection(firestore, `assignations/bag/${process.env.NEXT_PUBLIC_COUNTRY}`),
		where('cuit', '==', doctor.cuit)
		);
	} else {
		doneAppointmentsQuery = query(
		collection(firestore, 'assignations/online_clinica_medica/bag'),
		where('cuit', '==', doctor.cuit)
		);
	}

	try {
		const snap = await getDocs(doneAppointmentsQuery)
		let done = []
		snap.forEach((subDoc) => {
			let data = { ...subDoc.data(), path: subDoc.ref.path }
			data['service'] = 'guardia_online'
			done.push(data)
		})
		dispatch({ type: 'SET_LOADING', payload: false })
		return done
	} catch (error) {
		console.error(error)
		dispatch({ type: 'SET_LOADING', payload: false })
		return []
	}
}

export const getDoneAppointsForOnsite = async () => {
	const { dispatch, getState } = store
	const state = getState()
	const doctor = state.user.profile

	dispatch({ type: 'SET_LOADING', payload: true })

	try {
		const appointsRef = collection(firestore, 'assignations/consultorio/AR')
		const q = query(appointsRef,
			where('state', '==', 'DONE'),
			where('cuit', '==', doctor?.cuit)
		)
		const querySnapshot = await getDocs(q)
		let done = []
		querySnapshot.forEach((subDoc) => {
			let data = { ...subDoc.data(), path: subDoc.ref.path }
			data['service'] = 'consultorio'
			done.push(data)
		})

		dispatch({ type: 'SET_LOADING', payload: false })
		return done
	} catch (error) {
		console.error(error)
		dispatch({ type: 'SET_LOADING', payload: false })
		return []
	}
}

export const getDoneAppointsForSpecialist = async () => {
	const { dispatch, getState } = store
	const state = getState()
	const doctor = state.user.profile

	dispatch({ type: 'SET_LOADING', payload: true })

	try {
		const specialistRef = collection(firestore, `assignations/online/${process.env.NEXT_PUBLIC_COUNTRY}`)
		const q = query(specialistRef,
			where('uid', '==', doctor?.uid),
			where('state', '==', 'DONE')
		)
		const querySnapshot = await getDocs(q)
		let att = []
		querySnapshot.forEach((subDoc) => {
			let data = { ...subDoc.data(), path: subDoc.ref.path }
			data['service'] = 'especialista_online'
			att.push(data)
		})

		dispatch({ type: 'SET_LOADING', payload: false })
		return att
	} catch (error) {
		errorHandler.report(error)
		dispatch({ type: 'SET_LOADING', payload: false })
		return []
	}
}
export const getDoneAppointsForChat = async () => {
	const { dispatch, getState } = store
	const state = getState()
	const doctor = state.user.profile

	dispatch({ type: 'SET_LOADING', payload: true })

	try {
		const chatRef = collection(firestore, `assignations/chatAtt/${process.env.NEXT_PUBLIC_COUNTRY}`)
		const q = query(chatRef,
			where('uid', '==', doctor?.uid),
			where('state', '==', 'DONE')
		)
		const querySnapshot = await getDocs(q)
		let att = []
		querySnapshot.forEach((subDoc) => {
			let data = { ...subDoc.data(), path: subDoc.ref.path }
			data['service'] = 'chat'
			att.push(data)
		})

		dispatch({ type: 'SET_LOADING', payload: false })
		return att
	} catch (error) {
		errorHandler.report(error)
		dispatch({ type: 'SET_LOADING', payload: false })
		return []
	}
}
