import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath'
import { ActionAppointments, Views } from './Actions'
import { getLocalStorageItem } from '../utils/manageLocalStorage'
export interface IAppointmentsReducerState {
	current_view: Views,
	modal_appointments: string,
	assign_appoints: Array<IAppointmentWithPath>,
	done_appoints: Array<IAppointmentWithPath>,
	bag_appoints: Array<IAppointmentWithPath>,
	filtered_appoints: Array<IAppointmentWithPath>,
	consultorio_appointments: Array<IAppointmentWithPath>,
	special_consultorio_appointments: Array<IAppointmentWithPath>,
	att_appointments: Array<IAppointmentWithPath>,
	filter_date: string,
	filter_month: number,
	filter_year: number,
	filter_patient: string,
	filter_assign_date: number,
	filter_assign_month: number,
	filter_assign_year: number,
	filter_patient_dni: string,
	filter_patient_credential: string,
	dont_show_pediatric_appointments: boolean,
	filter_onsite: boolean,
	filter_online: boolean,
	specialist_att_appointments: Array<IAppointmentWithPath>,
	consultorio_att_appointments: Array<IAppointmentWithPath>,
	pediatric_appoints: Array<IAppointmentWithPath>,
}

const filterValuesDefaults = {
	filter_date: 'Todos',
	filter_month: new Date().getMonth() + 1,
	filter_year: new Date().getFullYear(),
	filter_patient: '',
	filter_assign_date: new Date().getDate(),
	filter_assign_month: new Date().getMonth() + 1,
	filter_assign_year: new Date().getFullYear(),
	filter_patient_dni: '',
	filter_patient_credential: '',
	filter_onsite: false,
	filter_online: false,
}

export const initialAppointmentReducerState : IAppointmentsReducerState = {
	current_view: '',
	modal_appointments: 'invitation',
	assign_appoints: [],
	done_appoints: [],
	bag_appoints: [],
	filtered_appoints: [],
	consultorio_appointments: [],
	special_consultorio_appointments: [],
	att_appointments: [],
	dont_show_pediatric_appointments: !!getLocalStorageItem('dontShowPediatricAppointments'),
	specialist_att_appointments: [],
	consultorio_att_appointments: [],
  pediatric_appoints: [],
	...filterValuesDefaults
}

export const enum appointmentsTypes  {
	SET_MODAL_APPOINTMENTS = 'modal_appointments',
	SET_ASSIGNED_APPOINTS = 'assign_appoints',
	SET_BAG_APPOINTS = 'bag_appoints',
	SET_CONSULTORIO_APPOINTMENTS = 'consultorio_appointments',
	SET_SPECIAL_CONSULTORIO_APPOINTMENTS = 'special_consultorio_appointments',
	SET_DONE_APPOINTS = 'done_appoints',
	SET_FILTERED_APPOINTS = 'filtered_appoints',
	FILTER_DONE_APPOINTMENTS_DATE = 'filter_date',
	FILTER_DONE_APPOINTMENTS_MONTH = 'filter_month',
	FILTER_DONE_PATIENT = 'filter_patient',
	FILTER_DONE_APPOINTMENTS_YEAR = 'filter_year',
	FILTER_ASSIGN_APPOINTMENTS_DATE = 'filter_assign_date',
	FILTER_ASSIGN_APPOINTMENTS_MONTH = 'filter_assign_month',
	FILTER_ASSIGN_APPOINTMENTS_YEAR = 'filter_assign_year',
	SET_ATT_APPOINTMENTS = 'att_appointments',
	FILTER_PATIENT_DNI = 'filter_patient_dni',
	FILTER_PATIENT_CREDENTIAL = 'filter_patient_credential',
	FILTER_DONE_APPOINTMENTS_ONSITE = 'filter_onsite',
	FILTER_DONE_APPOINTMENTS_ONLINE = 'filter_online',
	SET_SPECIALIST_ATT_APPOINTMENTS = 'specialist_att_appointments',
	SET_CONSULTORIO_ATT_APPOINTMENTS = 'consultorio_att_appointments',
  SET_PEDIATRIC_APPOINTS = 'pediatric_appoints',
}

/**
 * Son todas las actions que pueden haber
 */
export type appointmentsTypesStrings = keyof typeof appointmentsTypes;

export type ActionAppointment = {
	type: appointmentsTypes,
	payload: IAppointmentsReducerState[appointmentsTypes]
}

export default function appointmentsReducers(
	state : IAppointmentsReducerState = initialAppointmentReducerState,
	action: ActionAppointments): IAppointmentsReducerState {
	switch (action.type) {
		case 'SET_MODAL_APPOINTMENTS':
			return { ...state, modal_appointments: action.payload }
		case 'SET_ASSIGNED_APPOINTS':
			return { ...state, assign_appoints: action.payload }
		case 'SET_BAG_APPOINTS':
			return { ...state, bag_appoints: action.payload }
		case 'SET_CONSULTORIO_APPOINTMENTS':
			return { ...state, consultorio_appointments: action.payload }
		case 'SET_SPECIAL_CONSULTORIO_APPOINTMENTS':
			return { ...state, special_consultorio_appointments: action.payload }
		case 'SET_DONE_APPOINTS':
			return { ...state, done_appoints: action.payload }
		case 'SET_FILTERED_APPOINTS':
			return { ...state, filtered_appoints: action.payload }
		case 'FILTER_DONE_APPOINTMENTS_DATE':
			return {...state, filter_date: action.payload}
		case 'FILTER_DONE_APPOINTMENTS_MONTH':
			return {...state, filter_month: action.payload}
		case 'FILTER_DONE_APPOINTMENTS_YEAR':
			return {...state, filter_year: action.payload}
		case 'FILTER_ASSIGN_APPOINTMENTS_DATE':
			return {...state, filter_assign_date: action.payload}
		case 'FILTER_ASSIGN_APPOINTMENTS_MONTH':
			return {...state, filter_assign_month: action.payload}
		case 'FILTER_ASSIGN_APPOINTMENTS_YEAR':
			return {...state, filter_assign_year: action.payload}
		case 'FILTER_DONE_PATIENT':
			return {...state, filter_patient: action.payload}
		case 'FILTER_PATIENT_DNI':
			return {...state, filter_patient_dni: action.payload}
		case 'FILTER_PATIENT_CREDENTIAL':
			return {...state, filter_patient_credential: action.payload}	
		case 'SET_ATT_APPOINTMENTS':
			return {...state, att_appointments: action.payload}
		case 'CLEAN_FILTERS':
			return { ...state, ...filterValuesDefaults }
		case 'DONT_SHOW_PEDIATRIC_APPOINTMENTS':
			return { ...state, dont_show_pediatric_appointments: action.payload }
		case 'FILTER_DONE_APPOINTMENTS_ONSITE':
			return { ...state, filter_onsite: action.payload }
		case 'FILTER_DONE_APPOINTMENTS_ONLINE':
			return { ...state, filter_online: action.payload }
		case 'SET_SPECIALIST_ATT_APPOINTMENTS':
			return { ...state, specialist_att_appointments: action.payload }
		case 'SET_CONSULTORIO_ATT_APPOINTMENTS':
			return { ...state, consultorio_att_appointments: action.payload }
		case 'SET_PEDIATRIC_APPOINTS':
			return { ...state, pediatric_appoints: action.payload }
		default:
			return state
	}
}
