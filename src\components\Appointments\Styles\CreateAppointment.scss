@use '@/styles/global/Vars.scss';

.createAppointment__container{
	padding: 0 10px 10px 10px;
	h3{
		margin: 10px 0 15px 0;
		padding: 0;
		color: Vars.$uma-primary;
	}
	.createAppointment__inputContainer{
		margin-top: 12px;
		display: flex;
		align-items: center;
		label{
			margin-right: 5px;
			font-size: 1rem;
			color: Vars.$uma-text;
			width: 150px;
		}
		label:nth-child(3){
			width: 50px;
		}
		.createAppointment__input{
			padding: 6px;
			border: 1px solid Vars.$data-grey !important;
			border-radius: 6px;
			width: 350px;
			color: Vars.$uma-text;
			background-color: #f6f6f6 !important
		}
		.createAppointment__input:focus{
			outline: 1px solid rgba(10, 109, 215, 0.4);
			border: 1px solid rgba(10, 109, 215, 0.4);
		}
		form{
			width: 80px;
		}
	}
}