@use '../../../styles/index.scss';
@use '@/styles/global/Vars.scss';

.filter__display {
	min-height: 30vh;
	padding: 0 10px;
	text-align: center;

	.filter__summary {
		position: relative;
		padding: 10px;
		margin: 10px 0;
		outline: none;
		font-weight: bold;
		font-size: 1.1rem;
		cursor: pointer;
		text-align: left;
		width: 100%;
	}

	summary::-webkit-details-marker {
		color: Vars.$uma-primary;
	}
}

.filter__with__btn {
	display: flex;
	flex-direction: column;
	padding: 6px;
	margin: 15px 0;
	gap: 8px;
	.filter__input {
		width: 100%;
		padding: 8px;
		text-align: left;
		border: 1px solid Vars.$data-grey !important;
		border-radius: 5px;
	}
	
}

.filter__unit {
	padding: 6px;
	margin: 15px 0;
	display: grid;
	align-items: center;
	grid-template-columns: 30px 1fr;
	gap: 10px;
	width: 100%;

	.filter__select {
		padding: 8px;
		border: 1px solid Vars.$data-grey !important;
		text-align: center;
		background-color: Vars.$white-color;
		border-radius: 5px;
	}

	.filter__input {
		width: 100%;
		padding: 8px;
		text-align: left;
		border: 1px solid Vars.$data-grey !important;
		display: block;
		grid-column: 1/-1;
		border-radius: 5px;
	}
}

.history_nav_fichas {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding: 15px;

	svg {
		font-size: 2rem;
	}

	button>span>svg {
		position: absolute;
		right: 10px;
		top: 5px;
	}
}

.containerButtons {
	padding: 6px;
}