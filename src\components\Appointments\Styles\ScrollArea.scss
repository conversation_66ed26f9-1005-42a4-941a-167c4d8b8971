.ScrollAreaRoot {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.ScrollAreaViewport {
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.ScrollAreaScrollbar {
  display: flex;
  user-select: none;
  touch-action: none;
  padding: 2px;
  background: transparent;
  transition: background 160ms ease-out;
}

.ScrollAreaScrollbar[data-orientation="vertical"] {
  width: 12px;
}

.ScrollAreaScrollbar[data-orientation="horizontal"] {
  flex-direction: column;
  height: 12px;
}

.ScrollAreaThumb {
  flex: 1;
  background: var(--neutral-100, #CFD8DC);
  border-radius: 100px;
  width: 7px;
  position: relative;
}

.ScrollAreaCorner {
  background: transparent;
}
