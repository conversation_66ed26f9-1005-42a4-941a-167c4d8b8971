import React, { useEffect, useRef } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAppSelector } from '@/store/hooks'
import Modal from '@/components/GeneralComponents/Modal'
import Attached from '@/components/Consultorio/Attached/'
import * as Scroll<PERSON>rea from '@radix-ui/react-scroll-area';
import AppointmentsList from './PatientCard/AppointmentsList'
import { ServiceCategory } from './StatusNav/StatusNav'
import logoUma from '@/assets/logo.png'
import { PatientType, PatientTypeSelect } from '@/storybook/components/PatientTypeSelect/PatientTypeSelect'

const AssignedAppointments = ({ isLoading, currentView, patientTypeFilter} : {
	isLoading?: boolean
	currentView: ServiceCategory
	patientTypeFilter?: 'all' | 'adults' | 'pediatric'
}) => {
	const { 
		assign_appoints, 
		att_appointments,
		bag_appoints, 
		consultorio_appointments,
		special_consultorio_appointments,
		specialist_att_appointments,
	} = useAppSelector(state => state.appointments)
	const { attached } = useAppSelector((state) => state.front)
	const hasAtt = (assign_appoints?.length + att_appointments?.length +
		bag_appoints?.length + consultorio_appointments?.length) + special_consultorio_appointments?.length + specialist_att_appointments?.length > 0
	const isMx = process.env.NEXT_PUBLIC_COUNTRY === 'MX'
	const audioRef = useRef<HTMLAudioElement | null>(null)

	// Usar directamente los query params, con fallback a cookie o valor por defecto
	const searchParams = useSearchParams();
	
	// Obtener el valor actual del tipo de paciente con prioridad: URL > Cookie > Default
	const activePatientFilter = (
		searchParams.get('patientType') as PatientType || 
		patientTypeFilter || 
		'all'
	);

	function spawnNotification(theBody : string, theTitle : string) {
	
		new Notification(theTitle, {
			body: theBody,
			icon: logoUma.src
		})
	}

	useEffect(() => {
		if(hasAtt && isMx){
			if (audioRef.current) audioRef.current?.play()
			document.title = `(${bag_appoints?.length}) ÜMA Salud`
			spawnNotification('Puedes atenderlo ingresando a ÜMA', '¡Tienes un nuevo turno!')
		} else {
			document.title = 'ÜMA Salud'
		}
	}, [hasAtt])

	if (isLoading) {
		return (
			<div className="w-full h-[calc(100%-48px)] flex flex-col rounded-3xl mb-12 bg-white">
				{  currentView === 'guardia' &&
				<div className="px-6 pt-6 pb-2 rounded-t-3xl">
					<PatientTypeSelect
						className="max-w-[256px]"
						initialValue={activePatientFilter}
					/>
				</div>
				}
				<div className="w-full flex-1 overflow-hidden">
				<ScrollArea.Root className="w-full h-full relative pr-6" type='always'>
					<ScrollArea.Viewport className="w-full h-full pl-4 pr-6">
						<div className="pb-6 rounded-b-3xl">
							{/* Skeleton para AppointmentsList basado en diseño real */}
							<div className="px-4 py-2 space-y-2">
								{Array.from({ length: 4 }).map((_, index) => (
									<div key={index} className="animate-pulse rounded-xl bg-white p-4 shadow-sm">
										{/* Cabecera - Nombre del servicio y botón */}
										<div className="flex justify-between items-center mb-4">
											<div className="h-5 w-32 rounded bg-gray-200"></div>
											<div className="h-7 w-24 rounded-full bg-gray-200"></div>
										</div>

										{/* Información del paciente - Similar a la tarjeta real */}
										<div className="mb-4">
											<div className="h-5 w-40 mb-3 rounded bg-gray-200"></div>
											<div className="flex gap-6 mb-1">
												<div>
													<div className="h-4 w-32 mb-1 rounded bg-gray-200"></div>
													<div className="h-4 w-24 rounded bg-gray-200"></div>
												</div>
												<div>
													<div className="h-4 w-32 mb-1 rounded bg-gray-200"></div>
													<div className="h-4 w-24 rounded bg-gray-200"></div>
												</div>
											</div>
										</div>

										{/* Sección de botones similar a la vista de consulta real */}
										<div className="flex justify-between items-center pt-2 border-t border-gray-100">
											<div className="flex">
												<div className="h-8 w-8 rounded-full bg-gray-200 mr-2"></div>
												<div className="h-8 w-8 rounded-full bg-gray-200"></div>
											</div>
											<div className="flex gap-2">
												<div className="h-8 w-24 rounded-md bg-gray-200"></div>
												<div className="h-8 w-24 rounded-md bg-gray-200"></div>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					</ScrollArea.Viewport>
					<ScrollArea.Scrollbar 
						className="flex touch-none pt-4 select-none transition-colors duration-[160ms] ease-out data-[orientation=vertical]:w-[7px] mr-6 absolute right-0 top-0 bottom-0" 
						orientation="vertical"
					>
						<ScrollArea.Thumb className="relative flex-1 w-[7px] rounded-[100px] bg-[#CFD8DC]" />
					</ScrollArea.Scrollbar>
					<ScrollArea.Corner className="bg-transparent" />
				</ScrollArea.Root>
			</div>
			</div>
		)
	}

	return (
		<div className="w-full h-[calc(100%-100px)] flex flex-col bg-white rounded-3xl mb-12">
			{hasAtt && isMx && <audio ref={audioRef} id="audio" src="/assets/audio/ring.mp3" />}
			{attached?.show && <Modal><Attached/></Modal>}
			
			{/* Selector de tipo de paciente fijo (fuera del área de scroll) */}
			{  currentView === 'guardia' &&
			<div className="px-6 pt-6 pb-2 rounded-t-3xl">
				<PatientTypeSelect 
					className="max-w-[256px]"
					initialValue={activePatientFilter}
				/>
			</div>
			}

			{/* Contenedor con altura fija que contiene el área de scroll */}
			<div className={`w-full flex-1 overflow-hidden pb-8 ${currentView === 'guardia' ? '' : 'pt-20'}`}>
				<ScrollArea.Root className="w-full h-full relative pr-6" type='always'>
					<ScrollArea.Viewport className="w-full h-full pl-4 pr-6">
						<div className="rounded-b-3xl">
							<AppointmentsList
								currentView={currentView}
								activeFilter={activePatientFilter}
							/>
						</div>
					</ScrollArea.Viewport>
					<ScrollArea.Scrollbar 
						className="flex touch-none pt-4 select-none transition-colors duration-[160ms] ease-out data-[orientation=vertical]:w-[7px] mr-6 absolute right-0 top-0 bottom-0" 
						orientation="vertical"
					>
						<ScrollArea.Thumb className="relative flex-1 w-[7px] rounded-[100px] bg-[#CFD8DC]" />
					</ScrollArea.Scrollbar>
					<ScrollArea.Corner className="bg-transparent" />
				</ScrollArea.Root>
			</div>
		</div>
	)
}

export default AssignedAppointments
