import React, {useState} from 'react'
import { Button } from '@umahealth/occipital-ui'
import TimePicker from '@/components/GeneralComponents/Timepicker/TimePicker'
import {doctorCreateAppointment} from '@/config/endpoints'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import axios from 'axios'
import swal from 'sweetalert'
import moment from 'moment'
import '../Styles/CreateAppointment.scss'

const CreateAppointmentForm = () => {
	const [fullname, setFullname] = useState('')
	const [document, setDocument] = useState('')
	const [date, setDate] = useState(moment().format('DD/MM/YYYY'))
	const [fromHour, setFromHour] = useState('00:00')
	const [ws, setWs] = useState('+52')
	const [motivo, setMotivo] = useState('')
	const doctor = useAppSelector(state => state.user.profile)
	const dispatch = useAppDispatch()

	const createConsulta = async (e) => {
		e.preventDefault()
		const headers = { headers: { 'Content-Type': 'application/json' } }
		const patient = {
			fullname: fullname,
			dni: document,
			ws: ws.trim()
		}
		const data = {
			attentionDate: moment(date).format('DD-MM-YYYY'),
			attentionTime: fromHour,
			lat: '',
			lon: '',
			motivos_de_consulta: motivo, 
			patient: patient, 
			doctorUid: doctor?.uid
		}

		try {
			await axios.post(doctorCreateAppointment, data, headers)
			dispatch({ type: 'SET_MODAL', payload: false })
			swal({
				title: '¡Consulta creada!',
				description: 'Su turno ya esta en agenda.',
				icon: 'success',
			})
		} catch (error) {
			swal({
				title: 'Ocurrió un error',
				description: 'Vuelva a intentarlo',
				icon: 'warning',
			})
		}
	}

	return (
		<form onSubmit={createConsulta} className="createAppointment__container">
			<h3>Crea una consulta para un paciente.</h3>
			<div className="createAppointment__inputContainer">
				<label>Nombre completo:</label>
				<input
					className="createAppointment__input" 
					type="text" 
					name="fullname" 
					onChange={(e) => setFullname(e.target.value)} 
					value={fullname}/>
			</div>
			<div className="createAppointment__inputContainer">
				<label>Documento:</label>
				<input 
					className="createAppointment__input" 
					type="number" 
					name="dni" 
					onChange={(e) => setDocument(e.target.value)} 
					value={document} />
			</div>
			<div className="createAppointment__inputContainer">
				<label>Fecha de la consulta:</label>
				<input 
					className="createAppointment__input" 
					type="date" 
					name="date" 
					format='dd-mm-yyyy'
					onChange={(e) =>setDate(e.target.value)} 
					value={date} />
			</div>
			<div className="createAppointment__inputContainer">
				<label>Horario: </label>
				<TimePicker
					dataTime={fromHour}
					onChangeTime={(value) => setFromHour(value)} />
			</div>
			<div className="createAppointment__inputContainer">
				<label>Whatsapp:</label>
				<input 
					className="createAppointment__input" 
					type="text" 
					name="ws" 
					onChange={(e) => setWs(e.target.value)} 
					value={ws} />
			</div>
			<div className="createAppointment__inputContainer">
				<label>Motivo de la cita:</label>
				<input 
					className="createAppointment__input" 
					type="text" 
					name="motivo" 
					onChange={(e) => setMotivo(e.target.value)} 
					value={motivo} />
			</div>
			<Button>Crear consulta</Button>
		</form>
	)
}

export default CreateAppointmentForm