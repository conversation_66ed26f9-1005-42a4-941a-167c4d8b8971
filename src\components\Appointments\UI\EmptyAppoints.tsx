import React, { useState, useContext } from 'react'
import { useAppDispatch } from '@/store/hooks'
import { Icon } from '@umahealth/occipital/client'
import { AvailablePermissionsContext } from '@/components/User/AvailablePermissionsProvider'
import ImageEmptyComponent from '@/assets/ImageEmptyComponent.png'
import InvitePatientWs from '@/components/GeneralComponents/Share&Invite/InvitePatients/InvitePatientWs'
import Modal from 'src/storybook/components/shared/Modal/index'
import EmptyState from '@/components/GeneralComponents/EmptyState/EmptyState'
import { isFarmatodo } from '@/config/endpoints'
import { Button } from '@umahealth/occipital'

const EmptyAppoints = () => {
  const dispatch = useAppDispatch()
  const [homeModal, setHomeModal] = useState<string | ''>('')
  const availablePermissions = useContext(AvailablePermissionsContext)
  const atiendeSoloGuardia =
    !availablePermissions?.online && availablePermissions?.guardia

  const renderInModal = () => {
    switch (homeModal) {
      case 'invitePatientWs':
        return <InvitePatientWs />
      default:
        return
    }
  }

  const handleInvitePatientsWs = () => {
    setHomeModal('invitePatientWs')
    dispatch({ type: 'SET_MODAL', payload: true })
  }

  const handleShareProfile = () => {
    setHomeModal('shareProfile')
    dispatch({ type: 'SET_MODAL', payload: true })
  }

  const handleCloseModal = () => {
    setHomeModal('')
    dispatch({ type: 'SET_MODAL', payload: false })
  }

  const generalSubtitles = [
    `Aquí aparecerán las consultas tomadas pero, primero, hágale saber a sus pacientes que se encuentra en ÜMA.`,
    <span
      className="pt-4 text-neutral-600 font-semibold"
      key={'invite-links-subtitle'}
    >
      Invítelos a través de estos medios:
    </span>,
    <div
      className="flex gap-8 my-4 justify-center items-center"
      key={'invite-links'}
    >
      <Button
        type="button"
        variant="text"
        size="small"
        onClick={handleInvitePatientsWs}
      >
        <Icon
          name="whatsApp"
          size="size-11"
          color="text-success-500"
          aria-hidden="true"
          className="cursor-pointer"
        />
      </Button>
      <Icon
        name="share"
        size="size-10"
        color="text-primary"
        aria-hidden="true"
        className="cursor-pointer"
        onClick={handleShareProfile}
      />
    </div>,
  ]

  const farmatodoSubtitles = [
    `Aquí aparecerán las consultas tomadas pero, primero, hágale saber a sus pacientes que se encuentra en ÜMA.`,
    'Comparte tu perfil: ',
  ]

  const subtitlesGuardia = ['Aquí aparecerán las consultas tomadas.']

  const subtitles = isFarmatodo ? farmatodoSubtitles : generalSubtitles

  return (
    <div className="flex items-center justify-center min-h-[70vh] w-full">
      <div className="px-10 py-12 max-w-xl w-full flex flex-col items-center">
        <EmptyState
          image={ImageEmptyComponent}
          title={'Aún no tiene consultas'}
          subtitles={atiendeSoloGuardia ? subtitlesGuardia : subtitles}
          startFunction={atiendeSoloGuardia ? undefined : handleShareProfile}
        />
        {homeModal && (
          <Modal
            isOpen={!!homeModal}
            title={
              homeModal === 'invitePatientWs'
                ? 'Invitar por WhatsApp'
                : homeModal === 'shareProfile'
                ? 'Compartir perfil'
                : ''
            }
            onClose={handleCloseModal}
          >
            {renderInModal()}
          </Modal>
        )}
      </div>
    </div>
  )
}

export default EmptyAppoints
