import React from 'react'

interface EmptyServiceProps {
  title?: string;
  message?: string;
  className?: string;
}

const EmptyService: React.FC<EmptyServiceProps> = ({ 
  title = 'No hay consultas disponibles', 
  message = 'No tienes consultas pendientes para este servicio',
  className = ''
}) => {

  return (
    <div className={`flex flex-col items-center justify-center p-8 rounded-xl bg-white my-6 ${className}`}>
      <h3 className="text-lg font-medium text-grey-900 mb-2">{title}</h3>
      <p className="text-grey-500 text-center max-w-md">{message}</p>
    </div>
  )
}

export default EmptyService
