import React, { useState } from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import { <PERSON><PERSON>, Spacer } from 'occipital-new'
import { isFarmatodo } from '@/config/endpoints'
import { Checkbox } from '@umahealth/occipital/client'

const FilterAppointments = () => {
  const dispatch = useAppDispatch()
  const { filter_date, filter_month, filter_year, filter_patient, filter_patient_dni, filter_online, filter_onsite } = useAppSelector(state => state.appointments)
  const days = Array.from({ length: new Date(filter_year, filter_month, 0).getDate() }, (_, i) => i + 1)
  const filterDays = ['Todos', ...days]
  const [patientCredential, setPatientCredential] = useState('')

  const FilterAppointmentsDate = (selectedDate) => {
    dispatch({ type: 'FILTER_DONE_APPOINTMENTS_DATE', payload: selectedDate })
  }

  const FilterAppointmentsMonth = (selectedMonth) => {
    dispatch({ type: 'FILTER_DONE_APPOINTMENTS_MONTH', payload: selectedMonth })
  }

  const FilterAppointmentsYear = (selectedYear) => {
    dispatch({ type: 'FILTER_DONE_APPOINTMENTS_YEAR', payload: selectedYear })
  }


  const filterDonePatient = (selectedPatient) => {
    dispatch({ type: 'FILTER_DONE_PATIENT', payload: selectedPatient })
  }

  const filterPatientDni = (selectedDni) => {
    dispatch({ type: 'FILTER_PATIENT_DNI', payload: selectedDni })
  }

  const filterPatientCredential = (selectedCredential) => {
    dispatch({ type: 'FILTER_PATIENT_CREDENTIAL', payload: selectedCredential })
  }

  const cleanFilters = () => {
    dispatch({ type: 'CLEAN_FILTERS' })
    setPatientCredential('')
  }

  const filterOnsiteAppointments = (value) => {
    dispatch({ type: 'FILTER_DONE_APPOINTMENTS_ONSITE', payload: value })
  }

  const filterOnlineAppointments = (value) => {
    dispatch({ type: 'FILTER_DONE_APPOINTMENTS_ONLINE', payload: value })
  }

  const isCleanFilters = !(
    filter_date !== 'Todos' ||
    filter_month !== new Date().getMonth() + 1 ||
    filter_year !== new Date().getFullYear() ||
    filter_patient?.length ||
    filter_patient_dni?.length ||
    filterPatientCredential?.length
  )

  return (
    <div className="filter__display">
      <details>
        <summary className="filter__summary">Por fecha</summary>
        <div className="filter__unit">
          <span className="filter__label">Día</span>
          <select
            onChange={(e) => FilterAppointmentsDate(e.target.value)}
            defaultValue={filter_date}
            value={filter_date}
            className="filter__select"
          >
            {filterDays.map(d => {
              return (
                <option key={d} value={d}>
                  {d}
                </option>
              )
            })}
          </select>
        </div>
        <div className="filter__unit">
          <span className="filter__label">Mes</span>
          <select
            onChange={(e) => FilterAppointmentsMonth(e.target.value)}
            defaultValue={filter_month}
            value={filter_month}
            className="filter__select"
          >
            {Array.from({ length: 12 }, (_, i) => i).map(d => {
              return (
                <option key={d} value={d + 1}>
                  {d + 1}
                </option>
              )
            })}
          </select>
        </div>
        <div className="filter__unit">
          <span className="filter__label">Año</span>
          <select
            onChange={(e) => FilterAppointmentsYear(e.target.value)}
            defaultValue={filter_year}
            value={filter_year}
            className="filter__select"
          >
            <option value={new Date().getFullYear()}>
              {new Date().getFullYear()}
            </option>
            <option value={new Date().getFullYear() - 1}>
              {new Date().getFullYear() - 1}
            </option>
            <option value={new Date().getFullYear() - 2}>
              {new Date().getFullYear() - 2}
            </option>
          </select>
        </div>
      </details>
      {!isFarmatodo && (

        <details>
          <summary className="filter__summary">
            Por paciente
          </summary>
          <div className="filter__unit">
            <input
              type="text"
              name="filter__patient"
              onChange={e =>
                filterDonePatient(e.target.value)
              }
              defaultValue={filter_patient}
              value={filter_patient}
              placeholder="Nombre y/o apellido"
              className="filter__input"
            />
          </div>
        </details>
      )}
      <details>
        <summary className="filter__summary">{isFarmatodo ? 'Por cedula' : 'Por DNI'}</summary>
        <div className="filter__with__btn">
          <input
            type="text"
            name="filter__patient"
            onChange={e => {
              isFarmatodo ? setPatientCredential(e.target.value) : filterPatientDni(e.target.value)
            }}
            defaultValue={!isFarmatodo ? filter_patient_dni : patientCredential}
            value={!isFarmatodo ? filter_patient_dni : patientCredential}
            placeholder={!isFarmatodo ? "DNI" : "Cedula"}
            className="filter__input"
          />
          <Button
            disabled={isCleanFilters}
            occ_type="filled"
            size="full"
            type="button"
            action={() => filterPatientCredential(patientCredential)}
          >
            Filtrar por cedula
          </Button>
        </div>
      </details>
      {isFarmatodo && (
        <details>
          <summary className="filter__summary">
            Por tipo de encuentro
          </summary>
          <div className='flex flex-col gap-2 items-start pl-4 mb-3'>
            <Checkbox
              side='right'
              variant='filled'
              onCheckedChange={(value) => filterOnsiteAppointments(value)}
              checked={filter_onsite}
            >
              Encuentro presencial
            </Checkbox>
            <Checkbox
              side='right'
              variant='filled'
              onCheckedChange={(value) => filterOnlineAppointments(value)}
              checked={filter_online}
            >
              Encuentro online
            </Checkbox>
          </div>
        </details>
      )}
      <div className="containerButtons">
        <Button
          disabled={isCleanFilters}
          occ_type="outlined"
          size="full"
          type="button"
          action={() => cleanFilters()}
        >
          Limpiar filtros
        </Button>
        <Spacer direction="vertical" value="16px" />
        <Button
          occ_type="filled"
          size="full"
          type="button"
          action={() =>
            dispatch({ type: 'SET_SIDEBAR_MODAL', payload: false })
          }
        >
          Cerrar
        </Button>
      </div>
    </div>
  )
}

export default FilterAppointments
