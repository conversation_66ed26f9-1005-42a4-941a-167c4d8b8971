'use client'
import React, { useEffect, useContext, useMemo, useState } from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import empty_img from '@/assets/empty.png'
import { sortArrayByDate } from '@/utils/arrayUtils'
import { BsFilterRight } from 'react-icons/bs'
import { Button } from '@umahealth/occipital-ui'
import { AvailablePermissionsContext } from '@/components/User/AvailablePermissionsProvider'
import SideBarModal from '@/components/GeneralComponents/SideBarModal'
import DoneNav from './DoneNav'
import EmptyState from '@/components/GeneralComponents/EmptyState/EmptyState'
import PatientCard from '../PatientCard/PatientCard'
import FilterAppointments from '../FilterAppointments'
import moment from 'moment'
import '../../Styles/Filters.scss'
import { isFarmatodo } from '@/config/endpoints'
import { getResourceByFilters } from '@/components/MyPatients/infraestructure/services/getResourceByFilters'
import { getDoneAppointments } from './utils/doneAppointments/getDoneAppointmentsConfig'
import { useClient } from '@/providers/ClientProvider'

const DoneAppointments = () => {
  const dispatch = useAppDispatch();
  const { sidebar_modal } = useAppSelector((state) => state.front);
  const doctor = useAppSelector((state) => state.user.profile);
  const {
    done_appoints,
    filter_month,
    filter_year,
    filter_patient,
    filter_patient_dni,
    filter_date,
    filter_patient_credential,
    filter_online,
    filter_onsite,
  } = useAppSelector((state) => state.appointments);
  const [farmacoPatient, setFarmacoPatient] = useState('')
  const [applyFilters, setApplyFilters] = useState(false); 
  const availablePermissions = useContext(AvailablePermissionsContext);
  const atiendeSoloGuardia = !availablePermissions?.online && availablePermissions?.guardia;
  const client = useClient()
  

  useEffect(() => {
    if (doctor?.uid) {
      getDoneAppointments(client, dispatch, availablePermissions);
    }
  }, [availablePermissions]);

  useMemo(() => {
    const getPatient = async () =>{
      if(filter_patient_credential?.length){
        const patient = await getResourceByFilters('Patient', `identifier=${filter_patient_credential}`)
        if(patient.data?.[0]?.resource){
          setFarmacoPatient(patient.data?.[0]?.resource?.id)
        }
      }else{
        setFarmacoPatient('')
      }
    }
    getPatient()
  }, [filter_patient_credential])

  const doneFilterAppointments = useMemo(() => {
    if (!applyFilters) {
      return done_appoints
    }

    let allDoneAppointments = done_appoints

    allDoneAppointments = allDoneAppointments?.filter((item) => {
      const dateFilter = filter_date === 'Todos' ? 'YYYY-MM':'YYYY-MM-DD'
      const date_one = isFarmatodo ? moment(item.period?.start, 'YYYY-MM-DD')?.format(dateFilter) : moment(item.date, 'YYYY-MM-DD').format(dateFilter)

      const date_two = moment(
        `${filter_year}-${filter_month}-${filter_date}`,
        "YYYY-MM-DD"
        ).format(filter_date === "Todos" ? "YYYY-MM" : "YYYY-MM-DD");
      return date_two === date_one;
    });
    if(farmacoPatient){
      allDoneAppointments = allDoneAppointments?.filter((appointment) => appointment?.subject?.reference === `Patient/${farmacoPatient}`)
    }

    if (filter_patient_dni?.length) {
      allDoneAppointments = allDoneAppointments?.filter((appointment) => appointment?.patient?.dni?.includes(filter_patient_dni));
    }

    if (filter_patient?.length) {
      allDoneAppointments = allDoneAppointments?.filter((appointment) => appointment?.patient?.fullname.toLowerCase()?.includes(filter_patient.toLowerCase()));
    }

    // Para farmatodo
    if (filter_onsite && !filter_online) {
      allDoneAppointments = allDoneAppointments?.filter((appointment) => appointment?.identifier?.[0]?.type?.coding?.[0]?.code === 'onsite');
    }
    if (!filter_onsite && filter_online) {
      allDoneAppointments = allDoneAppointments?.filter((appointment) => appointment?.identifier?.[0]?.type?.coding?.[0]?.code === 'online');
    }

    return sortArrayByDate(allDoneAppointments);
  }, [
    done_appoints,
    filter_date,
    filter_month,
    filter_year,
    filter_patient,
    filter_patient_dni,
    farmacoPatient,
    applyFilters,
    filter_onsite,
    filter_online,
  ])

  return (
    <div className="pb-6">
      <div className="flex justify-between items-center mb-4">
        {/* Navegación de estado - siempre visible */}
        <DoneNav filteredAppointments={doneFilterAppointments} />
        
        {/* Botón de filtros */}
        <Button
          action={() => {
            dispatch({ type: "SET_SIDEBAR_MODAL", payload: true });
            setApplyFilters(true);
          }}
          color="secondary"
          size="large"
          className="shadow-sm"
        >
          <BsFilterRight aria-hidden="true" className="mr-1" />
          <b>Modificar filtros</b>
        </Button>
      </div>
      {sidebar_modal && (
        <SideBarModal title="Filtrar historial">
          <FilterAppointments />
        </SideBarModal>
      )}
      {doneFilterAppointments?.length ? (
        <div className="space-y-2">
          {doneFilterAppointments.map((appointment, index) => (
            <PatientCard
              key={`${index.toString()}`}
              appointment={appointment}
              isHistoryView={true}
            />
          ))}
        </div>
      ) : (
        <div className="mt-6 flex justify-center">
          <EmptyState
            image={empty_img}
            title={"No hay atenciones realizadas."}
            subtitles={
              atiendeSoloGuardia
                ? ["Aquí aparecerán las atenciones realizadas"]
                : ["No hay atenciones que cumplan con el criterio de búsqueda. Puede modificar los mismos en 'Modificar filtros'."]
            }
          />
        </div>
      )}
    </div>
  );
};

export default DoneAppointments;
