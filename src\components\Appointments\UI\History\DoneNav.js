import React, { useMemo } from 'react'
import { AppointmentTypeButton } from '@/storybook/components/buttons/AppointmentTypeButton/AppointmentTypeButton'
import { AppointmentTypeButtonContainer } from '@/storybook/components/buttons/AppointmentTypeButton/AppointmentTypeButtonContainer'

const DoneNav = ({filteredAppointments}) => {
	// Utilizando useMemo para evitar recálculos innecesarios
	const appointmentsCount = useMemo(() => {
		return filteredAppointments?.length || 0;
	}, [filteredAppointments]);

	return (
		<AppointmentTypeButtonContainer className="mb-4">
			<AppointmentTypeButton
				isActive={true}
				label="Consultas realizadas"
				count={appointmentsCount}
				countBgColor="bg-[#7353BA]"
				max={999}
			/>
		</AppointmentTypeButtonContainer>
	)
}

export default DoneNav