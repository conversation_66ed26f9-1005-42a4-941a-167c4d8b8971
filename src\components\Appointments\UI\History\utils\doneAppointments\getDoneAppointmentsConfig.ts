import { ClientsNames, TClientsNames } from "@/config/clients";
import { Action } from "@/store/reducers";
import { Dispatch } from "redux";
import { ObjectPermissions } from "@/serverServices/getAvailablePermissions";
import { getDoneAppointmentsFarma } from "./getDoneAppointmentsFarma";
import { getDoneAppointmentsUma } from "./getDoneAppointmentsUma";

export const getDoneAppointments = (client: TClientsNames, dispatch: Dispatch<Action<string, unknown>>, availablePermissions: ObjectPermissions | null) => {
  if (client === ClientsNames.FARMATODO) {
    return getDoneAppointmentsFarma(dispatch)
  }
  return getDoneAppointmentsUma(availablePermissions, dispatch)
}