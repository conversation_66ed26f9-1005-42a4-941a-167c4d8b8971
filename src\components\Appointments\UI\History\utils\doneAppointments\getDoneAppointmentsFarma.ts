import { getResourceByFilters } from "@/components/MyPatients/infraestructure/services/getResourceByFilters"
import { Action } from "@/store/reducers"
import { IEncounter } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IEncounter"
import { Dispatch } from "redux"

export const getDoneAppointmentsFarma = async (dispatch: Dispatch<Action<string, unknown>>) => {
  const encounters = await getResourceByFilters<IEncounter>('Encounter', 'status=finished')
  const dataEncounters = encounters?.data?.map(encounter => encounter.resource)
  dispatch({ type: 'SET_DONE_APPOINTS', payload: dataEncounters})
}