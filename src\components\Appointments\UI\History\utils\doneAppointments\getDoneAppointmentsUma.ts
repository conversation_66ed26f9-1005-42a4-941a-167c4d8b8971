import { getDoneAppointsForBag, getDoneAppointsForChat, getDoneAppointsForOnsite, getDoneAppointsForSpecialist } from "@/components/Appointments/Store/appointmentsActions";
import { ObjectPermissions } from "@/serverServices/getAvailablePermissions";
import { Action } from "@/store/reducers";
import { Dispatch } from "redux";

export const getDoneAppointmentsUma = async (availablePermissions: ObjectPermissions | null, dispatch: Dispatch<Action<string, unknown>>) => {
  let specialistAppointments = [];
  let bagAppointments = [];
  let onsiteAppointments = [];
  let chatAppointments = [];

  if (availablePermissions?.online) {
    specialistAppointments = await getDoneAppointsForSpecialist();
  }
  if (availablePermissions?.guardia) {
    bagAppointments = await getDoneAppointsForBag();
  }
  if (availablePermissions?.consultorio) {
    onsiteAppointments = await getDoneAppointsForOnsite();
  }
  if (availablePermissions?.chatAtt) {
    chatAppointments = await getDoneAppointsForChat();
  }

  dispatch({
    type: "SET_DONE_APPOINTS",
    payload: [
      ...specialistAppointments,
      ...bagAppointments,
      ...onsiteAppointments,
      ...chatAppointments,
    ],
  });
};