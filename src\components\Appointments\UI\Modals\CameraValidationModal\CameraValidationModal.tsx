import React, { useContext } from "react";
import {Mo<PERSON>} from "occipital-new";
import { Button } from "@umahealth/occipital";
import { Paragraph, Spacer, Title } from "@umahealth/occipital-ui";
import styles from "./cameraValidation.module.scss";
import { useRouter } from "next/navigation";
import moment from "moment";
import { AvailablePermissionsContext } from "@/components/User/AvailablePermissionsProvider";

const CameraValidationModal = ({setCameraPermissions}: {setCameraPermissions: React.Dispatch<React.SetStateAction<boolean>>}) => {
  const onClose = ()  => {
    const now = moment().toISOString();
    localStorage.setItem('cameraValidationDismissed', now)
    setCameraPermissions(true)
  }
  const availablePermissions = useContext(AvailablePermissionsContext)
  const router = useRouter()
  return (
    <Modal
      noCloseBtn={!availablePermissions?.consultorio}
      onClose={onClose}
      width="min-width"
    >
      <div className={styles["camera-validation__container"]}>
      <Title
        text="Permisos de video y audio requeridos"
        color="grey-2"
        size="s"
        weight="bold"
      />
      <Spacer direction="vertical" value="10px" />
      <Paragraph
        text="Para comenzar las atenciones médicas de una forma eficaz, es obligatorio habilitar los permisos de acceso a la cámara y micrófono en su navegador (Chrome)."
        align="center"
      />
      <Spacer direction="vertical" value="20px" />
      <video style={{width: '70%'}} src="https://storage.googleapis.com/uma-v2.appspot.com/Habilitar%20permisos%20video%20y%20audio%20-%20Guia%20Tutorial.mp4" autoPlay muted loop></video>
      <Spacer direction="vertical" value="32px" />
      <Button
            type="button"
            size="extended"
            variant="filled"
            action={() => router.push("/")}
          >
            Volver al inicio
          </Button>
    </div>
    </Modal>
  );
};

export default CameraValidationModal;
