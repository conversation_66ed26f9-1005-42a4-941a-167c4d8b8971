import React from "react";
import { Paragraph, Spacer, Title } from "@umahealth/occipital-ui";
import { Button } from "@umahealth/occipital";
import { useGetValidationCode } from "@/hooks/useGetPhoneValidationCode";
import { useAppSelector } from "@/store/hooks";
import styles from "./phoneValidation.module.scss";
import { InputPhone } from "@umahealth/occipital/client";
import '@umahealth/occipital/styles'
import "./pinfield.scss";
import {
  Control,
  Controller,
  FieldValues,
  Path,
  SubmitHandler,
  UseFormRegister,
  useForm,
} from "react-hook-form";

interface IWsInput<T extends FieldValues> {
  control: Control<T>;
  register: UseFormRegister<T>;
  classNameContainer?: string;
  error?: string;
  disabled?: boolean;
}

interface IWSInputData {
  ws: string;
}

function InputWs<T extends FieldValues>({
  error,
  control,
  classNameContainer,
  register,
  disabled,
}: IWsInput<T>) {
  function wsValidLength(value: string) {
    if (typeof value !== "string") return;
    return value?.length >= 7 && value?.length <= 15;
  }
  function checkValidWs(value: string) {
    const wsRegex = /^[0-9]+$/i;
    if (typeof value !== "string" || !value) return;
    if (!wsRegex.test(value)) return "Solo se permiten números";
    if (!wsValidLength(value))
      return "Debe tener entre 7 y 15 dígitos, incluyendo prefijo internacional y código de área";
  }

  const { ref, name } = register("ws" as Path<T>, {
    validate: checkValidWs,
  });
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <InputPhone
          ref={ref}
          onChange={field.onChange}
          value={field.value}
          classNameContainer={classNameContainer}
          error={error}
          disabled={disabled}
          disableDropdown={disabled}
        />
      )}
    />
  );
}
export const GenerateCode = ({
  setInputValue,
  setStep,
}: {
  setInputValue: React.Dispatch<React.SetStateAction<string>>;
  setStep: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const doctor = useAppSelector((state: any) => state.user.profile);
  const getValidationCode = useGetValidationCode({
    onSuccess: (data) => {
      if (data?.sent) {
        return setStep(1)
      }
      setError("ws", { type: "custom", message: "No se pudo enviar el código" })
    },
    onError: () => {
      setError("ws", { type: "custom", message: "No se pudo enviar el código" })
    }
  });
  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    setError,
  } = useForm<IWSInputData>();
  const onSubmit: SubmitHandler<IWSInputData> = (data) => {
    setInputValue(data.ws);
    getValidationCode.mutate({phoneNumber: data.ws, providerUid: doctor?.uid})
  }

  return (
    <div className={styles["phone-validation__container"]}>
      <Title
        text="Verifiquemos tu número de teléfono"
        color="default"
        size="l"
        weight="bold"
      />
      <Spacer direction="vertical" value="10px" />
      <Paragraph
        text="Te enviaremos un código de verificación por SMS, eso nos ayudará a mantener tu cuenta segura. No aplicarán tarifas de mensajería"
        align="center"
      />
      <Spacer direction="vertical" value="32px" />
      <form onSubmit={handleSubmit(async data => await onSubmit(data))} className='h-full w-full flex flex-col items-center px-10'>
        <InputWs
          control={control}
          register={register}
          error={errors?.ws?.message as string}
        />
        <Spacer direction="vertical" value="32px" />
        <div className={styles["phone-validation__button-container"]}>
          <Button
            type="submit"
            loading={getValidationCode.isLoading}
            size="full"
            variant="filled"
          >
            Enviar código de verificación
          </Button>
        </div>
      </form>
    </div>
  );
};
