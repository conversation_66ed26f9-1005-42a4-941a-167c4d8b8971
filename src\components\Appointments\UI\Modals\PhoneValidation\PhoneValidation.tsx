import React, { useState } from "react";
import {Modal} from "occipital-new";
import { GenerateCode } from "./GenerateCode";
import { ValidateCode } from "./ValidateCode";
import { VerificationSuccess } from "./VerificationSuccess";
import moment from "moment";



const PhoneValidation = ({
  setPhoneModal,
}: {
  setPhoneModal: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [inputValue, setInputValue] = useState("");
  const [step, setStep] = useState(0);
  const onClose = ()  => {
    const now = moment().toISOString();
    localStorage.setItem('phoneValidationDismissed', now)
    setPhoneModal(false)
  }

  return (
    <Modal
      onClose={() => onClose()}
      width="max-width"
    >
      {step === 0 && (
        <GenerateCode
          setInputValue={setInputValue}
          setStep={setStep}
        />
      )}
      {step === 1 && <ValidateCode inputValue={inputValue} setStep={setStep} />}
      {step === 2 && <VerificationSuccess setPhoneModal={setPhoneModal} />}
    </Modal>
  );
};

export default PhoneValidation;
