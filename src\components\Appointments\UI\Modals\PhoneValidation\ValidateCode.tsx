import React, { useEffect, useState } from "react";
import { Paragraph, Spacer, Title } from "@umahealth/occipital-ui";
import <PERSON>n<PERSON><PERSON> from "react-pin-field";
import { Button } from "@umahealth/occipital";

import "react-phone-input-2/lib/style.css";

import { useAppSelector } from "@/store/hooks";
import "./pinfield.scss";
import { useValidatePhoneCode } from "@/hooks/useValidatePhoneCode";
import styles from "./phoneValidation.module.scss";

const Countdown = ({
  setStep,
}: {
  setStep: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const [timeLeft, setTimeLeft] = useState<number>(120);
  useEffect(() => {
    if (timeLeft === 0) return;

    const intervalId = setInterval(() => {
      setTimeLeft((prevTime) => prevTime - 1);
    }, 1000);

    return () => clearInterval(intervalId);
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
  };

  return (
    <div className={styles["phone-validation__countdown-button"]}>
      <Button
        disabled={timeLeft > 0}
        type="button"
        action={() => {
          setStep(0);
        }}
        size="full"
        variant="text"
      >
        No recibí el código {timeLeft > 0 && formatTime(timeLeft)}
      </Button>
    </div>
  );
};

export const ValidateCode = ({
  inputValue,
  setStep,
}: {
  inputValue: string;
  setStep: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const [code, setCode] = useState<string>("");
  const doctor = useAppSelector((state: any) => state.user.profile);
  const validatePhone = useValidatePhoneCode(inputValue, code, doctor.uid);

  function maskString(str: string) {
    if (str?.length <= 3) {
      return str;
    }
    const maskedPart = "*".repeat(str?.length - 3);
    const visiblePart = str.slice(-3);
    return maskedPart + visiblePart;
  }

  useEffect(() => {
    if (validatePhone.isSuccess && validatePhone.data?.data?.validated) {
      setStep(2);
    }
  }, [validatePhone.isSuccess, validatePhone.data]);

  return (
    <div className={styles["phone-validation__container"]}>
      <Title
        text="Ingresá el código de 6 dígitos"
        color="default"
        size="l"
        weight="bold"
      />
      <Spacer direction="vertical" value="10px" />
      <Paragraph
        text={`Enviamos el código a ${maskString(inputValue)}. Revisá en la bandeja de entrada de SMS`}
        align="center"
      />
      <Spacer direction="vertical" value="32px" />
      <div>
        <PinField
          className="pin-field"
          length={6}
          onChange={(value) => setCode(value)}
        />
        {validatePhone.isError ||
          (validatePhone.data?.data && !validatePhone.data?.data?.validated && (
            <div className={styles["phone-validation__pin-field-error"]}>
              El código de verificación ingresado es incorrecto
            </div>
          ))}
      </div>
      <Spacer direction="vertical" value="32px" />
      <Button
        action={() => validatePhone.mutate()}
        disabled={code?.length !== 6}
        type="button"
        size="extended"
        variant="filled"
      >
        Verificar mí teléfono
      </Button>
      <Spacer direction="vertical" value="20px" />
      <Countdown setStep={setStep} />
    </div>
  );
};