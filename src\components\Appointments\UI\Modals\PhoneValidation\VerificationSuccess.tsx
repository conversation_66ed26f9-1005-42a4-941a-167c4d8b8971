import React from "react";
import { Paragraph, Spacer, Title } from "@umahealth/occipital-ui";
import { Button, Image } from "@umahealth/occipital";
import "react-phone-input-2/lib/style.css";
import styles from './phoneValidation.module.scss'
import "./pinfield.scss";

import SuccessCheck from '@/assets/illustrations/check.png'
export const VerificationSuccess = ({
  setPhoneModal,
}: {
  setPhoneModal: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  return (
    <div className={styles["phone-validation__container"]}>
      <div className={styles["phone-validation__image"]}>
        <Image src={SuccessCheck} alt="Imágen con un tilde verde" ></Image>
      </div>
      <Spacer direction="vertical" value="30px" />
      <Title
        text="Número de teléfono verificado"
        color="default"
        size="l"
        weight="bold"
      />
      
      <Spacer direction="vertical" value="20px" />
      <Paragraph
        text="¡Felicitaciones! Verificaste tu número de teléfono exitosamente"
        align="center"
      />
      <Spacer direction="vertical" value="20px" />
      <Button
        type="button"
        action={() => {
          setPhoneModal(false);
        }}
        size="extended"
        variant="filled"
      >
        Entendido
      </Button>
    </div>
  );
};