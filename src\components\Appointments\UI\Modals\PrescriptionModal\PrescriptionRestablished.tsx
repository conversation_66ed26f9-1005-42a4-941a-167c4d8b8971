import { Modal } from "@/storybook/components/modals/Modal/Modal";
import ServerReestablished from "@/assets/reestablishedServer.png";
import React, { Dispatch, SetStateAction } from "react";

interface PrescriptionRestablishedProps {
    setShowPrescriptionReestablishedModal: Dispatch<SetStateAction<boolean>>;
}

export default function PrescriptionRestablished({ setShowPrescriptionReestablishedModal } : PrescriptionRestablishedProps) {

    const content = {
        image: ServerReestablished,
        alternativeText: 'Servicio reestablecido',
        title: 'Servicio de Validación de Recetas operando con normalidad.',
        description: 'El servicio de validación de recetas ha sido restablecido. Puede volver a generar recetas electrónicas normalmente.',
        primaryButtonText: 'Entendido',
        primaryAction: () => { 
            localStorage.setItem('skipPrescriptionReestablishedModal', 'true');
            setShowPrescriptionReestablishedModal(false);
        }
    };

    return (
        <Modal
            image={content.image}
            alternativeText={content.alternativeText}
            title={content.title}
            description={content.description}
            primaryButtonText={content.primaryButtonText}
            primaryAction={content.primaryAction}
        />
    );
}
