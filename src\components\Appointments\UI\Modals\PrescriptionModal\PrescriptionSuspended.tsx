import { Modal } from "@/storybook/components/modals/Modal/Modal";
import ServerError from "@/assets/errorServer.png";
import { Dispatch, SetStateAction } from "react";

interface PrescriptionSuspendedProps {
    setShowPrescriptionSuspendedModal: Dispatch<SetStateAction<boolean>>;
}

export default function PrescriptionSuspended ({ setShowPrescriptionSuspendedModal } : PrescriptionSuspendedProps) {

    const content = {
        image: ServerError,
        alternativeText: 'Servicio intermitente',
        title: 'Posibles Inconvenientes en la Generación de Recetas',
        description: 'La generación de recetas puede presentar inconvenientes según la cobertura del paciente. El resto de las funciones de la videollamada operarán con normalidad.',
        primaryButtonText: 'Entendido',
        primaryAction: () => { 
            setShowPrescriptionSuspendedModal(false);
            localStorage.setItem('skipPrescriptionSuspendedModal', 'true');
        }
    };

    return (
        <Modal
            image={content.image}
            alternativeText={content.alternativeText}
            title={content.title}
            description={content.description}
            primaryButtonText={content.primaryButtonText}
            primaryAction={content.primaryAction}
        />
    );
}
