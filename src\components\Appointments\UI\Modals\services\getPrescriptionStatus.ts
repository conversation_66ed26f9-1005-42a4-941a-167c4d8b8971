import { pop_up_prescription} from '@/config/endpoints';
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import axios from 'axios';

export const getPrescriptionStatus = async () => {
  try {
    const token = await getFirebaseIdToken();
    
    // Si no hay token, el usuario no está autenticado
    if (!token) {
      console.warn('Usuario no autenticado al verificar estado de prescripción');
      return {
        prescription_suspended: false,
        prescription_reestablished: false
      };
    }
    
    const response = await axios.get(pop_up_prescription, {
      headers: { 
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      },
    });
    const status = response.data
    return status
  } catch (error) {
    console.error("Error fetching prescription suspended status:", error);
    // Retornar valores predeterminados en caso de error
    return {
      prescription_suspended: false,
      prescription_reestablished: false
    };
  }
};
