import React, { useCallback } from "react";
import { useAppDispatch } from "@/store/hooks";
import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";
import { IfhirR4 } from "@smile-cdr/fhirts";
import StartAction from "./StartAction";
import { useClient } from "@/providers/ClientProvider";
import { isAppointmentWithPath } from "../utils/checkAppointmentType";
import { getPatientUid } from "./utils/getPatientUid";
import { openFicha } from "./utils/openFicha";
import DetailsButton from "../components/DetailsButton";

export default function AppointmentAction({
  appointment,
  isHistoryView,
}: {
  appointment: IAppointmentWithPath & { status?: IfhirR4.IEncounter["status"] };
  isHistoryView?: boolean;
}) {
  const client = useClient();
  const dispatch = useAppDispatch();
  /** son los archivos adjuntos */
  // const hasDocumentReferences =
  //   appointment?.documentReferences &&
  //   appointment?.documentReferences?.length > 0;
  const handleOpenFicha = useCallback(() => {
    const uid = getPatientUid(appointment, client)
    openFicha({
      appointment,
      client,
      dispatch,
      uid
    })
  }, [client, appointment])

  // const openAttached = async (appointment: IAppointmentWithPath) => {
  //   dispatch({ type: "GET_CURRENT_APPOINTMENT", payload: appointment });
  //   dispatch({
  //     type: "SET_SHOW_ATTACHED",
  //     payload: { show: true, confirm: false },
  //   });
  // };

  return (
    <div className="flex space-x-4">
      {/*Deshabilitamos las preview de archivos adjuntos fuera la consulta de manera provisoria,
        actualmente los archivos adjuntos de pueden visualizar dentro de la consulta correctamente.*/}

      {/* {hasDocumentReferences && (
        <button
          className="flex flex-col items-center"
          onClick={() => openAttached(appointment)}
        >
          {" "}
          <div
            className={`mb-4 'bg-success-500'} w-10 h-10 rounded-full flex justify-center items-center`}
          >
            <Icon
              color="text-grey-1"
              name={"labProfile"}
              size="size-6"
              aria-hidden="true"
            />
          </div>
          <p className="triggerAtt">{t("appointment-motivos_label")}</p>
        </button>
      )} */}

      <DetailsButton
        onClick={handleOpenFicha}
        isHistoryView={isHistoryView}
      />
      
      {isAppointmentWithPath(appointment) && !isHistoryView && (
        <StartAction
          appointment={appointment}
          client={client}
          uidPatient={appointment.patient.uid}
        />
      )}
    </div>
  );
}
