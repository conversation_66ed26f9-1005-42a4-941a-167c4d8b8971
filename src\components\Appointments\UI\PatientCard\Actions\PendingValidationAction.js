import React from 'react'
import { useAppDispatch } from '@/store/hooks'
import { storage } from '@/config/firebase'
import { AiOutlineFileExclamation } from 'react-icons/ai'
import { ref, listAll, getDownloadURL } from 'firebase/storage'

export default function PendingValidationAction({ appointment }) {
	const dispatch = useAppDispatch()

	const getComprobante = async () => {
		dispatch({ type: 'GET_CURRENT_APPOINTMENT', payload: appointment })

		const storageRef = ref(storage, `${appointment?.patient?.uid}/attached/${appointment?.path?.split('/')[3]}`)
		const res = await listAll(storageRef)
		const files = await Promise.all(res.items.map(async item => await getDownloadURL(item)))

		dispatch({ type: 'SET_SHOW_ATTACHED', payload: { appointment: appointment, files: files, show: true, confirm: true } })
	}

	return (
		<div className="actionIcon-container" onClick={getComprobante}>
			<div className={'actionIcon pending-validation'}>
				<AiOutlineFileExclamation aria-hidden='true' />
			</div>
			<p className="triggerAtt">
				PENDIENTE VALIDACIÓN
			</p>
		</div>
	)
}
