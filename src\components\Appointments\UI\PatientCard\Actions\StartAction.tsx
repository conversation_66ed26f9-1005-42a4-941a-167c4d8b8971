import { ClientsNames, TClientsNames } from "@/config/clients"
import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath"
import NewStartCallAction from "@/components/Appointments/presentation/views/NewStartCallAction"
import StartFarmatodoEncounterAction from "../components/StartFarmatodoEncounterAction"

interface IStartAction {
  appointment: IAppointmentWithPath,
  client: TClientsNames,
  uidPatient: string,
  disabled?: boolean
  disabledReason?: string
  onLoadingChange?: (isLoading: boolean) => void
}

function StartAction({
  appointment,
  client,
  uidPatient,
  disabled,
  disabledReason,
  onLoadingChange
}: Readonly<IStartAction>) {
  if (client === ClientsNames.FARMATODO) {
    const encounterType = appointment.path?.includes("consultorio") ? 'onsite' : 'online'
    return (
      <StartFarmatodoEncounterAction
        assignationId={appointment?.assignation_id}
        patientUid={uidPatient}
        isRedesignActive={true}
        type={encounterType}
      />
    )
  }

  return (
    <NewStartCallAction
      appointment={appointment}
      disabled={disabled}
      disabledReason={disabledReason}
      onLoadingChange={onLoadingChange}
    />
  )
}

export default StartAction