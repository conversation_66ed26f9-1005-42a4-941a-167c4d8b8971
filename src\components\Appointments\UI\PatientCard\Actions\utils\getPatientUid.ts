import { ClientsNames, TClientsNames } from "@/config/clients";
import { AppointmentType } from "../../UI/AppointmentInfo/AppointmentInfo";
import { isAppointmentWithPath, isEncounter } from "../../utils/checkAppointmentType";

function getPatientUidFromEncounter(appointment: AppointmentType) {
  if (isEncounter(appointment)) {
    return appointment.subject?.reference ?? '-'
  }
  return '-'
}
function getPatientUidFromAppointment(appointment: AppointmentType) {
  if (isAppointmentWithPath(appointment)) {
    return appointment?.patient?.uid ?? '-'
  }
  return '-'
}

export function getPatientUid(appointment: AppointmentType, client: TClientsNames) {
  if (client === ClientsNames.FARMATODO) {
    return getPatientUidFromEncounter(appointment)
  }
  return getPatientUidFromAppointment(appointment)
}