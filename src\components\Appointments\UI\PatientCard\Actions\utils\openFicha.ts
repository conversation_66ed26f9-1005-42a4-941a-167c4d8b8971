import { ClientsNames, TClientsNames } from "@/config/clients";
import { Action } from "@/store/reducers";
import { Dispatch } from "react";
import { AppointmentType } from "../../UI/AppointmentInfo/AppointmentInfo";
import { isAppointmentWithPath, isEncounter } from "../../utils/checkAppointmentType";

interface IOpenFichaProps {
  appointment: AppointmentType,
  client: TClientsNames,
  dispatch: Dispatch<Action<string, unknown>>,
  uid: string,
}

function openFichaUma({ appointment, dispatch, uid }: IOpenFichaProps) {
  if (isAppointmentWithPath(appointment)) {
    dispatch({
      type: "GET_PATIENT",
      payload: appointment?.patient,
    })
    dispatch({
      type: "OPEN_FICHA",
      payload: { uid },
    })
  }
}

function openFichaFarmatodo({ appointment, dispatch, uid }: IOpenFichaProps) {
  if (isEncounter(appointment)) {
    dispatch({
      type: "GET_PATIENT",
      payload: appointment?.subject?.reference,
    })
    dispatch({
      type: "OPEN_FICHA",
      payload: { uid: `${uid}/-/${appointment.id}` },
    })
  }
}

export function openFicha(props: IOpenFichaProps) {
  if (props.client === ClientsNames.FARMATODO) {
    return openFichaFarmatodo(props)
  }
  return openFichaUma(props)
}