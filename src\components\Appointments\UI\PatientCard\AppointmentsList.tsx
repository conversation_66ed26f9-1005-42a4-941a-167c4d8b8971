import React, { useMemo } from "react";
import { useAppSelector } from "@/store/hooks";
import moment from "moment-timezone";
import EmptyService from "../EmptyService";
import { isFarmatodo } from "@/config/endpoints";
import { ServiceCategory } from "../StatusNav/StatusNav";
import {
  GuardiaView,
  ConsultorioView,
  OnlineView,
  SpecialConsultorioView
} from "./views";
import { LoadingAppointmentProvider } from "../../context/LoadingAppointmentContext";

export default function AppointmentsList({
  currentView,
  activeFilter = 'all'
}: {
  currentView: ServiceCategory
  activeFilter?: 'all' | 'adults' | 'pediatric'
}) {
  // Obtener los datos del estado global
  const {
    assign_appoints: assign_specialist_appoints,
    bag_appoints,
    att_appointments,
    consultorio_appointments,
    consultorio_att_appointments,
    special_consultorio_appointments
  } = useAppSelector((state) => state.appointments);

  // Constantes de fechas - memoizada para evitar recálculos
  const today = useMemo(() => moment(Date.now()).format("YYYY-MM-DD"), []);

  // Usar el filtro que viene desde el componente padre
  const currentFilter = activeFilter;

  const consultorioAppointments = useMemo(() => [
    ...consultorio_appointments,
    ...consultorio_att_appointments,
    ...special_consultorio_appointments,
  ], [consultorio_appointments, consultorio_att_appointments, special_consultorio_appointments]);

  // Verificar si hay consultas en curso - memoizado
  const hasAppointmentsInAtt = useMemo(() =>
    (typeof att_appointments.length === 'number' && att_appointments.length > 0),
    [att_appointments.length]
  );

  // Determinar si las otras consultas deben estar deshabilitadas - memoizado
  const disableOtherAppointments = useMemo(() =>
    hasAppointmentsInAtt && !isFarmatodo,
    [hasAppointmentsInAtt]
  );

  // La clasificación de citas de consultorio se movió al componente ConsultorioView

  // La lógica de clasificación de citas de especialista se movió a OnlineView

  // Renderizar el componente apropiado según la vista actual
  return (
    <LoadingAppointmentProvider>
      <div className="ml-2 mr-1 -mt-2 mb-10 lg:mb-0">
        {currentView === "guardia" && (
          <GuardiaView
            att_appointments={att_appointments}
            guardiaAppointmentsOutAtt={bag_appoints}
            disableOtherAppointments={disableOtherAppointments}
            currentFilter={currentFilter}
          />
        )}

        {currentView === "online" && (
          <OnlineView
            specialistAppointments={assign_specialist_appoints}
            today={today}
          />
        )}

        {currentView === "consultorio" && (
          <ConsultorioView
            consultorioAppointments={consultorioAppointments}
          />
        )}
        {currentView === 'special_consultorio' && (
          <SpecialConsultorioView
            specialConsultorioAppointments={special_consultorio_appointments}
          />
        )}

        {currentView !== "guardia" && currentView !== "consultorio" && currentView !== "online" && currentView !== "special_consultorio" && (
          <EmptyService
            title="Servicio no disponible"
            message="Este tipo de servicio no tiene citas disponibles o no es compatible actualmente"
          />
        )}
      </div>
    </LoadingAppointmentProvider>
  );
}
