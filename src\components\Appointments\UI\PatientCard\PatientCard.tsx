import React  from "react";
import { useAppSelector } from "@/store/hooks";
import moment from "moment-timezone";
import useUser from "@/services/reactQuery/useUser";
import AppointmentInfo from "./UI/AppointmentInfo/AppointmentInfo";
import PatientInfo from "./UI/PatientInfo/PatientInfo";
import { Loader } from "occipital-new";
import AppointmentActions from "./Actions/AppointmentActions";
import Ficha from "@/components/GeneralComponents/Ficha";
import { isFarmatodo } from "@/config/endpoints";
import { DetailEncounterFhir } from "./UI/DetailEncounterFhir/DetailEncounterFhir";
import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";
import { appointmentPatient } from "@umahealth/entities";
import { cn } from "@/lib/utils";
import { IfhirR4 } from "@smile-cdr/fhirts";
import '../../Styles/Appointments.scss'

export function isPediatric(dob: string) {
  return moment().diff(dob, "years") <= 16 && moment().diff(dob, "years") >= 0
    ? "pediatric"
    : false;
}

function PatientCard({
  appointment,
  isHistoryView,
}: {
  appointment: IAppointmentWithPath & { status?: IfhirR4.IEncounter['status'], destino_final?: string },
  isHistoryView?: boolean,
}) {
  const { ficha } = useAppSelector((state) => state.front);
  const pediatric = isPediatric(appointment?.patient?.dob);
  const uidDependant = appointment?.patient?.uid_dependant;
  const uidPatient = appointment?.patient?.uid;
  const user = useUser(uidPatient ?? "NO", uidDependant || undefined);
  
  // Determine if consultation was effective based on destino_final
  if (user.isLoading) {
    return <Loader size={16} color="grey-1" />;
  }

  return (
    <>
      <div
        className={cn(
          "bg-white z-0 rounded-2xl shadow-sm border border-gray-100",
          pediatric
        )}
      >
        <div className="p-4 w-full">
          <AppointmentInfo
            isHistoryView={isHistoryView}
            appointment={appointment}
            patient={user.data}
          />
          {!isHistoryView && !!appointment?.patient && (
            <PatientInfo
              practiceCode={appointment?.practices?.[0]?.cod}
              motivos={appointment?.appointment_data?.motivos_de_consulta}
              patient={appointment.patient as appointmentPatient}
            />
          )}
          <AppointmentActions
            isHistoryView={isHistoryView}
            appointment={appointment}
          />
        </div>
      </div>
      { ficha?.state &&
        isHistoryView &&
        appointment.id === ficha.uid?.split("/-/")[1] && (
          <DetailEncounterFhir
            encounter={appointment as any /* TODO: Revisar esto  */}
            reference={ficha.uid?.split("/-/")[0]}
          />
        )}
      {ficha?.state && !isFarmatodo && <Ficha assignationId={ficha.assignation_id}/>}
    </>
  );
}

export default PatientCard;
