@use '../../../../../../styles/global/Vars.scss';
/* Variables */
$primary-color: #0A6DD7;
$uma-secondary: #ae0ad7;
$white-color: #fff;

.appointmentData__container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  min-width: 150px;
  width: 100%;

  /* Nombre del paciente */
  .patient_name {
    font-size: 18px;
    font-weight: 600;
    width: 100%;
    text-align: start;
    word-wrap: break-word;
    overflow-wrap: break-word;
    margin-bottom: 8px;
    color: #1a1a1a;
  }

  .att-priored {
    width: fit-content;
    border-radius: 5px;
    padding: 5px;
    color: white;
    align-items: flex-start;

    &.especialista_online {
      background: $uma-secondary;
    }
  }

  /* <PERSON>ra de Atención */
  .dateContainer {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4B5563;
    font-size: 14px;

    .att_date {
      font-weight: 500;
    }

    .att_hour {
      font-weight: 500;
    }
  }
}
