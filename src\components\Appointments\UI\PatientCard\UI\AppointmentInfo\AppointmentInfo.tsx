import React, { useMemo } from 'react'
import { IPatient } from '@umahealth/entities'
import { Timestamp } from '@/config/firebase'
import 'moment/locale/es'
import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath'
import styles from './AppointmentInfo.module.scss'
import { IEncounter } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IEncounter'
import { useClient } from '@/providers/ClientProvider'
import { isAppointmentWithPath } from '../../utils/checkAppointmentType'
import { getAppointmentType } from './utils/getAppointmentType'
import { getPatientName } from './utils/getPatientName'
import { getShowTag } from './utils/getShowTag'
import { setDateConfig } from './utils/date/setDateConfig'
import AttTag from '@/components/Appointments/components/AttTag'

export type AppointmentType = IAppointmentWithPath | IEncounter
interface IAppointmentInfo {
	appointment: AppointmentType,
	patient: IPatient<Timestamp> | undefined,
	isHistoryView?: boolean
}

type patientsType = 'normal'

export default function AppointmentInfo({ appointment, patient, isHistoryView }: Readonly<IAppointmentInfo>) {
	const client = useClient()
	const patientType: patientsType = 'normal'
	const { date, hour } = useMemo(() => {
		const config = setDateConfig(client, appointment, isHistoryView)
		return {
			date: config.date,
			hour: config.hour?.replace('hs.', '').trim()
		}
	}, [ appointment, client ])

	const showWaitingTime = isAppointmentWithPath(appointment)
		? !appointment.path?.includes('bag')
		: process.env.NEXT_PUBLIC_COUNTRY !== 'AR'

	const showAppointmentHour = isAppointmentWithPath(appointment)
		? !appointment.path?.includes('bag')
		: process.env.NEXT_PUBLIC_COUNTRY !== 'AR'

	const showAttTag = getShowTag(client)

	const patientName = useMemo(() => {
		return getPatientName(appointment, patient)
	}, [ appointment, patient ])

	const isPresent = isAppointmentWithPath(appointment) && appointment.state === 'PRESENTE'
	const isOverbooked = isAppointmentWithPath(appointment) && appointment.overbooked

	const encounterType = getAppointmentType(appointment)

	return (
		<div className={`${styles.appointmentData__container} ${styles[ encounterType ]}`}>
			<p className={`${styles.patient_name} ${styles[ patientType ]}`}>
				{`${patientName} ${isPresent ? '- Presente' : ''}`}
			</p>
			{showAttTag && (
				<AttTag appointmentService={encounterType} />
			)}
			{showWaitingTime && (
				<div className={styles.dateContainer}>
					{isOverbooked && <p>Sobreturno</p>}
					{showWaitingTime && <p className={styles.att_date}>{date}</p>}
					{showAppointmentHour && <>
						<span>•</span>
						<p className={styles.att_hour}>{hour}</p>
					</>}
				</div>
			)}
		</div>
	)
}
