import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";
import { useClient } from "@/providers/ClientProvider";
import { useMemo } from "react";
import { setDateConfig } from "./utils/date/setDateConfig";

interface IAppointmentInfo {
  appointment: IAppointmentWithPath | any;
  isHistoryView?: boolean;
}

export default function NewAppointmentInfo({
  appointment,
  isHistoryView,
}: IAppointmentInfo) {
  const client = useClient()
  const { date, hour } = useMemo(() => {
    return setDateConfig(client, appointment, isHistoryView)
  }, [ appointment, client ])

  const showWaitingTime =
    !appointment.path?.includes("bag") ||
    process.env.NEXT_PUBLIC_COUNTRY !== "AR";
  const showAppointmentHour =
    !appointment.path?.includes("bag") ||
    process.env.NEXT_PUBLIC_COUNTRY !== "AR";

  return (
    <>
      {showWaitingTime && (
        <span className='text-sm text-neutral-600'>
          {showWaitingTime && <span className="">{date}</span>}
          {showAppointmentHour && <span className=""> - {hour} hs.</span>}
        </span>
      )}
    </>
  );
}
