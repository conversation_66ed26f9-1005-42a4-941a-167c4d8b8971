import { ClientsNames, TClientsNames } from "@/config/clients";
import { setFarmatodoDate } from "./setFarmatodoDate";
import { setUmaDate } from "./setUmaDate";
import { AppointmentType } from "../../AppointmentInfo";

export const setDateConfig = (client: TClientsNames, appointment: AppointmentType, isHistoryView?: boolean) => {
  if (client === ClientsNames.FARMATODO && isHistoryView) {
    return setFarmatodoDate(appointment)
  }

  return setUmaDate(appointment)
}