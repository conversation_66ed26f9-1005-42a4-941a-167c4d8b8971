import { format } from "date-fns"
import { AppointmentType } from "../../AppointmentInfo"
import { es } from "date-fns/locale"
import { isEncounter } from "../../../../utils/checkAppointmentType"

export function setFarmatodoDate(appointment: AppointmentType) {
  let dateValue = ''
  let hourValue = ''
  if (isEncounter(appointment)) {
    // Lógica para Farmatodo
    const periodStart = appointment.period?.start
    dateValue = periodStart
      ? format(new Date(periodStart), "dd 'de' MMMM", { locale: es })
      : ''
    hourValue = periodStart
      ? format(new Date(periodStart), 'HH:mm')
      : ''
  }

  return { date: dateValue, hour: hourValue }
}