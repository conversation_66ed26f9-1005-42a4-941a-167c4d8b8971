import { Timestamp } from "firebase/firestore";
import { AppointmentType } from "../../AppointmentInfo";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { isAppointmentWithPath } from "../../../../utils/checkAppointmentType";

export function setUmaDate(appointment: AppointmentType) {
  let dateValue = ''
  let hourValue = ''

  if (isAppointmentWithPath(appointment)) {
    if (appointment.especialidad !== '' || appointment.service === 'especialista_online') {
      // Si es especialista
      if (appointment.timestamps?.dt_assignation instanceof Timestamp) {
        const date = appointment.timestamps.dt_assignation.toDate()
        dateValue = format(date, "dd 'de' MMMM", { locale: es })
        hourValue = format(date, 'HH:mm')
      }
    } else if (appointment.timestamps?.dt_unsuspend instanceof Timestamp) {
      // Si es de guardia sin suspender
      const date = appointment.timestamps.dt_unsuspend.toDate()
      dateValue = formatDistanceToNow(date, { addSuffix: true, locale: es })
      hourValue = format(date, 'HH:mm')
    } else if (appointment.timestamps?.dt_create instanceof Timestamp) {
      // Si es guardia
      const date = appointment.timestamps.dt_create.toDate()
      dateValue = formatDistanceToNow(date, { addSuffix: true, locale: es })
      hourValue = format(date, 'HH:mm')
    }
  }
  return { date: dateValue, hour: hourValue }
}