import { isAppointmentWithPath, isEncounter } from "../../../utils/checkAppointmentType"
import { AppointmentType } from "../AppointmentInfo"

export function getAppointmentType(appointment: AppointmentType) {
  if (isEncounter(appointment)) {
    return appointment.identifier?.[0]?.type?.coding?.[0]?.code === 'online' 
      ? 'especialista_online' 
      : 'consultorio'
  }

  if (isAppointmentWithPath(appointment)) {
    return appointment.service ?? ''
  }

  return ''
}