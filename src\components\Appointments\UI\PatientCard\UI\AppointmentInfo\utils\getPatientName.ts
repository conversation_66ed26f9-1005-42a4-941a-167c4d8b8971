import { IPatient } from "@umahealth/entities"
import { AppointmentType } from "../AppointmentInfo"
import { isAppointmentWithPath, isEncounter } from "../../../utils/checkAppointmentType"

export function getPatientName(appointment: AppointmentType, patient: IPatient | undefined) {
  if (isEncounter(appointment)) {
    return appointment.subject?.display ?? '-'
  }

  if (patient?.chosenName) {
    return patient.chosenName
  }

  return isAppointmentWithPath(appointment) && appointment.patient?.fullname
    ? appointment.patient.fullname
    : '-'
}