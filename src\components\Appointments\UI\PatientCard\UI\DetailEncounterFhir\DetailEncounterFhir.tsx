import React, { useEffect, useState } from 'react'
import Modal from '@/components/GeneralComponents/Modal'
import { getFhirPatient } from '@/components/MyPatients/infraestructure/services/getFhirPatient'
import { IPatient } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPatient'
import { errorHandler } from '@/config/stackdriver'
import { Loader, Title, Text } from '@umahealth/occipital'
import { IEncounter } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IEncounter'
import style from '../../styles/detailEncounterFhir.module.scss'
import moment from 'moment-timezone'
import { MdExpandMore, MdExpandLess } from 'react-icons/md'
import { fhirR4 } from '@smile-cdr/fhirts'
import { ObservationEncounter } from './ObservationEncounter'
import { appointmentServices } from '@umahealth/entities'
import { useGetAssignation } from '@/services/reactQuery/useGetAssignation'
import { parseEncounterType } from '@/components/Appointments/utils/parseEncounterType'
import { parseFarmaAttReason } from '@/components/Appointments/utils/parseFarmaAttReason'

interface IProps {
    reference: string | undefined,
    encounter: IEncounter
}

export const DetailEncounterFhir = ({ reference, encounter }: IProps) => {
  const [patient, setPatient] = useState<IPatient>()
  const [observationReference, setObervationReference] = useState('')
  const assignationId = encounter.identifier?.[0].value as string
  const encounterType = encounter.identifier?.[0].type?.coding?.[0]?.code as string

  const assignation = useGetAssignation({
    service: parseEncounterType(encounterType) as appointmentServices,
    assignationId,
    options: {
      enabled: !!assignationId && !!encounterType,
      onError: (error) => {
        errorHandler?.report(`[ DetailEncounterFhir ] => Error getting assignation ${assignationId} - type: ${encounterType} - Error: ${JSON.stringify(error)}`)
      }
    }
  })

  useEffect(() => {
    if (reference) {
      (async () => {
        try {
          const patient = await getFhirPatient(reference.split('/')[1])
          setPatient(patient)
        } catch (error) {
          errorHandler?.report(`${JSON.stringify(error)}`)
        }
      })()
    }
  }, [reference])

  const gender = {
    male: 'M',
    female: 'F',
    other: 'Otro',
    unknown: 'No especifica'
  }

  const nameObservations = {
    'LAB_OBSERVATION': 'Laboratorio',
    'PRH_OBSERVATION': 'Problemas relacionados con habitos',
    'PRM_OBSERVATION': 'Problemas relacionados con medicamentos',
    'TREATMENT_OBSERVATION': 'Tratamiento',
    'PARAMETERS_OBSERVATION': 'Parametros',
    'MEDICINE_HOUR_OBSERVATION': 'Medicamentos',
    'PHARMACIST': 'Farmaceutico',
    'Blood Pressure': 'Presión arterial',
    'Glucose': 'Glucosa',
  }

  const handleOpenObservation = (observation: fhirR4.Reference) =>{
    if (observationReference === observation.reference) {
      setObervationReference('')
      return
    }
    setObervationReference(observation.reference ?? '')
  }

  const getBar = (observation: fhirR4.Reference) => {
    const identifierName = observation.display as keyof typeof nameObservations
    return (
      <>
        <div className={style.bar} onClick={() => handleOpenObservation(observation)}>
          <Text color='text-primary' size='text-s' tag='span' weight='font-regular'>
            {nameObservations[identifierName]}  
          </Text>
          <div>
            {observationReference !== observation.reference ? (
              <MdExpandMore />
            ) : (
              <MdExpandLess />
            )}
          </div>
        </div>
        {observationReference === observation.reference && <ObservationEncounter observationReference={observation.reference?.split('/')[1] || ''} />}
      </>
    )
  }

  return (
    <Modal height='80vh'>
      <Title hierarchy='h2' weight='font-bold' size='text-m' color='text-primary'>
        Detalle de encuentro
      </Title>
      <div className='flex flex-col gap-3 mt-4'>
        {!patient || assignation.isLoading ? (
            <Loader size='size-7' color='stroke-primary' />
        ) : (
          <>
            {patient.name && (
              <Text color='text-primary' size='text-s' tag='span' weight='font-regular'>
                <span className='font-semibold'>Nombre:</span> {patient.name[0].text}
              </Text>
            )}
            {patient.gender && (
              <Text color='text-primary' size='text-s' tag='span' weight='font-regular'>
                <span className='font-semibold'>Género:</span> {gender[patient.gender]}
              </Text>
            )}
            {encounter.location && (
              <Text color='text-primary' size='text-s' tag='span' weight='font-regular'>
                <span className='font-semibold'>Lugar:</span> {encounter.location[0]?.physicalType?.text ?? 'No especifica'}
              </Text>
            )}
            {encounter.meta && (
              <Text color='text-primary' size='text-s' tag='span' weight='font-regular'>
                <span className='font-semibold'>Fecha:</span> {moment(encounter.meta.lastUpdated).format('DD/MM/yyyy') || 'No especifica'}
              </Text>
            )}
            {assignation.data?.patient?.ws && (
              <Text color='text-primary' size='text-s' tag='span' weight='font-regular'>
                <span className='font-semibold'>Teléfono:</span> {assignation.data?.patient?.ws}
              </Text>
            )}
            {assignation.data?.appointment_data?.motivos_de_consulta && (
              <Text color='text-primary' size='text-s' tag='span' weight='font-regular'>
                <span className='font-semibold'>Motivo de consulta:</span> {parseFarmaAttReason(assignation.data?.appointment_data?.motivos_de_consulta)}
              </Text>
            )}
            {encounter.reasonReference && encounter.reasonReference?.length > 0 ? <>
              <Text color='text-primary' size='text-s' tag='span' weight='font-semibold'>
                Observaciones creadas durante el encuentro:
              </Text>
              {encounter.reasonReference.map(observation => getBar(observation))}
            </>
              : null}
          </>
        )}
      </div>
    </Modal>
  )
}
