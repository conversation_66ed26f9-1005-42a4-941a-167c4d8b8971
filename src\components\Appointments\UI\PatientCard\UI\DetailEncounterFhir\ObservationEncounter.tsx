import { IfhirR4 } from "@smile-cdr/fhirts";
import React, { useEffect, useState } from "react";
import { getResourceByFilters } from "@/components/MyPatients/infraestructure/services/getResourceByFilters";
import { Text } from "occipital-new";
import style from "../../styles/observationEncounter.module.scss";
import {
  getFieldsToRender,
  getGlucoseFieldsToRender,
} from "../../utils/getFieldsToRender";
import { ObservationsType } from "../../utils/getFieldsToRender";
import { IObservation } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation";

interface IProps {
  observationReference: string;
}

export const ObservationEncounter = ({ observationReference }: IProps) => {
  const [loading, setLoading] = useState(false);
  const [observationView, setObservationView] = useState<{
    data: IfhirR4.IObservation | null;
    error: null | true;
  }>();

  useEffect(() => {
    const getQuestionnaire = async () => {
      try {
        setLoading(true);
        const response = await getResourceByFilters(
          "Observation",
          `_id=${observationReference}`
        );
        setObservationView({
          data: response?.data?.[0]?.resource as IObservation,
          error: null,
        });
      } catch (error) {
        setObservationView({ data: null, error: true });
      } finally {
        setLoading(false);
      }
    };
    observationReference && getQuestionnaire();
  }, [observationReference]);

  if (loading && !observationView?.data) return <>Cargando...</>;

  if (!loading && !observationView?.data)
    return (
      <>No hemos podido obtener la observacion, pro favor intente nuevamente</>
    );

  if (observationView?.data?.code?.text === "Glucose") {
    const fields = getGlucoseFieldsToRender(observationView?.data);
    return (
      <div className={style.containerObservation}>
        {fields.map((field) => (
          <Text
            key={field.key}
            color="text-primary"
            size="s"
            tag="span"
            weight="regular"
          >
            {field.key} : {field.value}
          </Text>
        ))}
      </div>
    );
  }

  const fieldsExceptGlucose = getFieldsToRender(
    observationView?.data?.code?.text as ObservationsType,
    observationView?.data?.component
  );
  return (
    <div className={style.containerObservation}>
      {fieldsExceptGlucose.map((field) => (
        <Text
          key={field.key}
          color="text-primary"
          size="s"
          tag="span"
          weight="regular"
        >
          {field.key} : {field.value}
        </Text>
      ))}
    </div>
  );
};
