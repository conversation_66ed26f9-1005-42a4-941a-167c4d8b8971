import { useClient } from "@/providers/ClientProvider";
import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";
import { CardTitle } from "@/storybook/components/UICard/Atoms/CardTitle/CardTitle";
import { IfhirR4 } from "@smile-cdr/fhirts";
import { gender } from "@umahealth/entities";
import { getKeyValues } from "./utils/getKeyValues";

export const NewPatientInfo = ({
  appointment,
}: {
  appointment: IAppointmentWithPath & { status?: IfhirR4.IEncounter["status"] };
}) => {
  const client = useClient()
  const patientSex = appointment?.patient?.sex as gender
  return (
    <div className="flex-col px-4 grow">
      <CardTitle className="font-semibold text-secondary-600 text-base">
        Información general
      </CardTitle>
      <div className="flex-col flex">
        {getKeyValues(client, {
          patientSex,
          dob: appointment?.patient?.dob,
          ws: appointment?.patient?.ws,
        })}
      </div>
    </div>
  );
};
