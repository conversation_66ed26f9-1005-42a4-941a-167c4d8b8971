@use '../../../../../../styles/global/Vars.scss';

.patientInfo__container {
  display: flex;
  height: 100%;
  padding: 0 8px;

  @media (max-width: 830px) {
    display: none;
  }

  .patientInfo__avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 12px;

    .patientInfo__picture {
      border: 3px solid Vars.$white-color;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }

      svg {
        font-size: 1.5rem;
      }
    }
  }

  .patientInfo__info {
    display: flex;
    flex-direction: column;
    line-height: 1.2rem;
    width: 250px;
    overflow: hidden;
    justify-content: space-between;

    @media (max-width: 1024px) {
      font-size: 1rem;
    }

    .patient_corporate {
      width: 250px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  .patientInfo__motive {
    flex-direction: row;
    text-overflow: ellipsis;
    @media (max-width: 1024px) {
      display: none;
    }
  
    .motivos {
      overflow: -moz-scrollbars-vertical;
      font-size: 16px;
      font-weight: 700;
      overflow-y: auto;
      width: 250px;
      text-wrap: balance;
      max-height: 100px;

      &::-webkit-scrollbar-thumb {
        background-color: Vars.$primary-background;
        border-radius: 5px;
      }

      &::-webkit-scrollbar {
        width: 4px;
        background-color: transparent;
      }
    }

    &.pediatric {
      color: Vars.$pediatric-marker
    }

    @media (max-width: 1024px) {
      font-size: 1rem;
    }
  }
}
