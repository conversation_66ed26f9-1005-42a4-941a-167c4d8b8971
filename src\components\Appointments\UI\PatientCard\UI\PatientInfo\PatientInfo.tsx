import React from 'react'
import moment from 'moment-timezone'
import { appointmentPatient } from '@umahealth/entities'
import styles from './PatientInfo.module.scss'

interface PatientInfo {
	motivos: string | undefined,
	patient: appointmentPatient
	practiceCode?: string
}

export default function PatientInfo({motivos, patient, practiceCode} : PatientInfo) {
	const arrayMotivos = motivos ? [...new Set(motivos.split('.'))] : ['']

	const handleDob = () => {
		if (moment().diff(patient?.dob, 'years') < 0 ||
			moment().diff(patient?.dob, 'years') > 100){
			return 'No informada'
		}else{
			return moment().diff(patient?.dob, 'years')
		}
	}
	return (
		<div className={styles.patientInfo__container}>
			<div className={styles.patientInfo__info}>
				<p><b>Sexo: </b> {patient?.sex || '-'} </p>
				{!isNaN(moment().diff(patient?.dob, 'years')) && <p><b>Edad: </b> { handleDob() }</p>}
				{process.env.NEXT_PUBLIC_COUNTRY === 'AR' && <p><b>DNI: </b> {patient?.dni || '-'} </p>}
				{process.env.NEXT_PUBLIC_COUNTRY === 'MX' && <p><b>Email: </b> {patient?.email || '-'} </p>}
			</div>
			<div className={styles.patientInfo__motive}>
				<p> Motivos de la consulta: </p>
				{arrayMotivos?.length > 1 &&
					<ul className={styles.motivos}>
						{arrayMotivos.map((causa, index) => causa !== '' ? <li key={`${index}-${causa}`}>{causa}</li> : 'No informado')}
					</ul>
				}
				{ arrayMotivos?.length === 1 &&
					<p className={styles.motivos}>
						{arrayMotivos.map(causa => causa !== '' ? causa: 'No informado')}
					</p>
				}
				{practiceCode && <div className='my-2'>
					<p> Código de práctica: </p>
					<ul className={styles.motivos}>
						<li>{practiceCode}</li>
					</ul>
				</div>}
			</div>
		</div>
	)
}
