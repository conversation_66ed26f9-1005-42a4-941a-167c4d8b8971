import { ClientsNames, TClientsNames } from "@/config/clients";
import { KeyValue } from "@/storybook/components/UICard/Atoms/KeyValue/KeyValue";
import { gender } from "@umahealth/entities";
import { getAgeKeyValue, getPhoneKeyValue, getSexKeyValue, TConfig } from "./keyValues"

interface IData {
  dob: string,
  patientSex: gender,
  ws: string,
}

function getUmakeyValues({ patientSex, dob }: IData) {
  return [
    getSexKeyValue(patientSex),
    getAgeKeyValue(dob),
  ]
}

function getFarmatodokeyValues({ patientSex, dob, ws }: IData) {
  return [
    getSexKeyValue(patientSex),
    getAgeKeyValue(dob),
    getPhoneKeyValue(ws),
  ]
}

const keyValuesConfig: Record<TClientsNames, (data: IData) => TConfig[]> = {
  [ClientsNames.UMA]: getUmakeyValues,
  [ClientsNames.FARMATODO]: getFarmatodokeyValues,
}

export function getKeyValues(client: TClientsNames, data: IData) {
  return keyValuesConfig[client](data)?.map(({ key, value }) => (
    <KeyValue key={`${key} ${value}`} someKey={key} someValue={value} />
  ))
}