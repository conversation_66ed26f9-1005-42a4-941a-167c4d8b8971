import { calculateAge } from "@/utils/calculateAge"
import { gender } from "@umahealth/entities"
import { differenceInYears } from "date-fns"

export type TConfig = {
  key: string,
  value: string
}

const sexValues: Record<gender, string> = {
  F: 'Femenino',
  M: 'Masculino',
  X: 'No especifica',
}

export function getSexKeyValue(patientSex: gender): TConfig {
  return {
    key: 'Sexo',
    value: sexValues[patientSex]
  }
}

export function getAgeKeyValue(dob: string): TConfig {
  const isNumber = !isNaN(differenceInYears(new Date(), new Date(dob)))
  return {
    key: 'Edad',
    value: isNumber ? `${calculateAge(dob)} años` : '-'
  }
}

export function getPhoneKeyValue(ws: string): TConfig {
  return {
    key: 'Teléfono',
    value: ws
  }
}