import { ClientsNames, TClientsNames } from '@/config/clients'
import { useClient } from '@/providers/ClientProvider'
import { Icon } from '@umahealth/occipital/client'
import { useTranslations } from 'next-intl'
import React from 'react'

interface IDetailsButtonProps {
  onClick: () => void
  isHistoryView?: boolean
}

function DetailsButton({ onClick, isHistoryView }: Readonly<IDetailsButtonProps>) {
  const t = useTranslations("consultas")
  const client = useClient()

  function getContent(client: TClientsNames) {
    let spanContent = ''

    if (client === ClientsNames.FARMATODO) {
      spanContent = 'Detalle'
      return <p>{spanContent}</p>
    }

    spanContent = t("appointment-history_label")
    return <p>{spanContent}</p>
  }

  const button = (
    <button
      className="flex flex-col items-center"
      onClick={onClick}
    >
      <div
        className='mb-4 bg-white w-10 h-10 rounded-lg flex justify-center items-center border-solid border'
      >
        <Icon
          color="text-grey-1"
          name="history"
          size="size-6"
          aria-hidden="true"
        />
      </div>
      {getContent(client)}
    </button>
  )

  if (client === ClientsNames.FARMATODO && !isHistoryView) {
    return null
  }

  return button
}

export default DetailsButton