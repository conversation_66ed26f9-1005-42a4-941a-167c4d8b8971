import React from "react";
import { Icon } from "@umahealth/occipital/client";

interface IStartCallButton {
  action?: (() => void) | (() => Promise<void>),
  ausent?: boolean,
  disabled?: boolean,
  resume?:boolean,
}

/**
 * 
 * @param disabled si esta desactivado o no
 * @param resume si la consulta esta en espera o no
 * @param ausent Si el paciente esta ausente, se usa en consultorios presenciales 
 * @returns 
 */
export default function StartCallButton({
  disabled,
  resume,
  ausent,
  action
}: IStartCallButton) {
  const IconName = resume ? 'play' : 'videocall'
  const onlineLabel = resume ? 'Retomar consulta' : 'Iniciar consulta'
  const label = ausent ? 'El paciente no está presente' : onlineLabel
  const isDisabled = disabled || ausent
  const onClickAction = isDisabled === true ? undefined : action

  return (
    <button
      className="flex flex-col items-center"
      onClick={onClickAction}
    >
      <div
        className={`mb-4 ${isDisabled ? 'bg-grey-5' : 'bg-success-500'} w-10 h-10 rounded-full flex justify-center items-center`}
      >
        <Icon
          color="text-grey-6"
          name={IconName}
          size="size-5"
          aria-hidden="true"
        />
      </div>
      <p className="triggerAtt">{label}</p>
    </button>
  );
}


