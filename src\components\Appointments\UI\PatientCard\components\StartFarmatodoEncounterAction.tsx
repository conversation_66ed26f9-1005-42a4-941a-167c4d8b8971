/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { useAppSelector } from '@/store/hooks'
import { useRouter } from 'next/navigation'
import { Icon } from '@umahealth/occipital/client'
import { Spacer } from '@umahealth/occipital-ui'
import { getResourceByFilters } from '@/components/MyPatients/infraestructure/services/getResourceByFilters'
import { IPatient } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPatient'
import { farmatodoCountry } from '@/utils/farmatodoCountry'
import swal from 'sweetalert'
import { useStartEncounter } from '@/services/reactQuery/useStartEncounter'

interface IStartFarmatodoActionProps {
  assignationId: string
  isRedesignActive?: boolean
  patientUid: string
  type: 'online' | 'onsite'
}

export default function StartFarmatodoEncounterAction({
  assignationId,
  isRedesignActive,
  patientUid,
  type,
}: Readonly<IStartFarmatodoActionProps>) {
  const { profile } = useAppSelector((state) => state.user)
  const router = useRouter()
  const startEncounter = useStartEncounter(router, type)

  const startEncounterFromAction = async (patientUid: string) => {
    const patientData = await getResourceByFilters<IPatient>(
      'Patient',
      `identifier=${patientUid}`
    )
    if (patientData?.error) {
      return swal({
        title: 'Error',
        text: 'Ha ocurrido un error al obtener la información del paciente',
        icon: 'error',
      })
    }
    const fhirPatient = patientData?.data?.[0]?.resource
    return startEncounter.mutate({
      patientUid,
      fhirPatientUid: fhirPatient?.id ?? '',
      fhirPatientName: fhirPatient?.name?.find((name) => name?.use === 'official')?.text ?? '',
      providerUid: profile.uid,
      providerFhirId: profile.farmatodoId!,
      providerCountry: farmatodoCountry(profile.country),
      assignationId,
    })
  }

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation()
    startEncounterFromAction(patientUid)
  }

  if (isRedesignActive) {
    return (
      <button
        onClick={(event) => handleClick(event)}
        className={`flex items-center justify-center w-12 h-12 rounded-full transition-all duration-300 ease-in-out overflow-hidden group hover:w-auto hover:px-4 hover:bg-success-700 bg-success-500 text-white`}
      >
        <Icon
          color="inherit"
          name="videocall"
          size="size-6"
          aria-hidden="true"
        />
        <span className="ml-2 hidden group-hover:inline">
          Iniciar encuentro
        </span>
      </button>
    )
  }

  return (
    <button
      type='button'
      className="m-0 p-0 flex justify-center items-center flex-col"
      onClick={() => startEncounterFromAction(patientUid)}
    >
      <div className="mb-4 bg-success-500 w-10 h-10 rounded-full flex justify-center items-center hover:bg-success-700 hover:cursor-pointer">
        <Icon
          color="text-grey-6"
          name="play"
          size="size-5"
          aria-hidden="true"
        />
      </div>
      <Spacer />
      <p className="triggerAtt">Iniciar encuentro</p>
    </button>
  )
}