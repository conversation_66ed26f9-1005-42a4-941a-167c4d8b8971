@use '@/styles/global/Vars.scss';
.detailEncounterContainer{
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.bar{
    background-color: Vars.$color-grey-6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 12px;
    &:hover{
        background-color: Vars.$color-grey-5;
        cursor: pointer;
    }
}