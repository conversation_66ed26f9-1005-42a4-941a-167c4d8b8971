
import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";
import { AppointmentType } from "../UI/AppointmentInfo/AppointmentInfo";
import { IEncounter } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IEncounter";

export function isAppointmentWithPath(appointment: AppointmentType): appointment is IAppointmentWithPath {
  return 'path' in appointment && 'service' in appointment;
}

export function isEncounter(appointment: AppointmentType): appointment is IEncounter {
  return 'period' in appointment && 'identifier' in appointment;
}