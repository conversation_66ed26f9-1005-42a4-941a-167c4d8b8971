import { ObservationComponent } from "@smile-cdr/fhirts/dist/FHIR-R4/classes/observationComponent";
import { IObservation } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation";
import { getValue } from "./getValue";

export type ObservationsType =
  | "LAB_OBSERVATION"
  | "PRH_OBSERVATION"
  | "PRM_OBSERVATION"
  | "TREATMENT_OBSERVATION"
  | "PARAMETERS_OBSERVATION"
  | "MEDICINE_HOUR_OBSERVATION"
  | "PHARMACIST"
  | "Blood Pressure"
  | "Glucose";

const medicineKeys = {
  drug: "Medicamento",
  hour: "Hora de medicina",
};

const bloodPressureKeys = {
  "Systolic Blood Pressure": "Presión arterial sistólica",
  "Diastolic Blood Pressure": "Presión arterial diastólica",
  "Cardio frequency": "Frecuencia cardiaca",
  "Location of Blood Pressure Measurement": "Brazo",
  "Blood Pressure Result": "Resultado",
  "Causes that can alter the result": "Causas que pueden alterar el resultado",
};

/**
 * Obtiene un mensaje sobre actividades que pueden alterar el resultado de la presión arterial.
 * 
 * @param input - Cadena JSON que contiene información sobre actividades.
 * @returns {string} - Mensaje sobre las actividades registradas o un mensaje predeterminado.
 */
const getAlterResultBloodPressure = (input: string): string => {
  if (!input) return "No hay actividades registradas";
  const data = JSON.parse(input);
  let msg = "";

  if (data.discussion) {
    msg += "Discutió";
  }
  if (data.food) {
    msg += (msg ? ", " : "") + "Comió recientemente";
  }
  if (data.exercise) {
    msg += (msg ? ", " : "") + "Hizo ejercicio";
  }

  return msg || "No hay actividades registradas";
};

/**
 * Obtiene los campos a renderizar según el tipo de observación y los valores proporcionados.
 * 
 * @param type - Tipo de observación según el estándar FHIR.
 * @param values - Lista de componentes de observación.
 * @returns {Array<{ key: string, value: string }>} - Lista de campos a renderizar.
 */
export const getFieldsToRender = (
  type: ObservationsType,
  values: ObservationComponent[] | undefined
) => {
  if (!values) return [];

  if (type === "MEDICINE_HOUR_OBSERVATION") {
    return (
      values
        .filter(
          (value) => value.code.text === "drug" || value.code.text === "hour"
        )
        ?.map((value) => {
          return {
            key: medicineKeys[value.code.text as "drug" | "hour"],
            value: getValue(value),
          };
        }) || []
    );
  }

  if (type === "Blood Pressure") {
    return (
      values
        .filter(
          (value) =>
            value.code.text === "Systolic Blood Pressure" ||
            value.code.text === "Diastolic Blood Pressure" ||
            value.code.text === "Cardio frequency" ||
            value.code.text === "Location of Blood Pressure Measurement" ||
            value.code.text === "Blood Pressure Result" ||
            value.code.text === "Causes that can alter the result"
        )
        ?.map((value) => {
          return {
            key: bloodPressureKeys[
              value.code.text as
                | "Systolic Blood Pressure"
                | "Diastolic Blood Pressure"
                | "Cardio frequency"
                | "Location of Blood Pressure Measurement"
                | "Blood Pressure Result"
                | "Causes that can alter the result"
            ],
            value:
              value.code.text === "Causes that can alter the result"
                ? getAlterResultBloodPressure(value.valueString || "")
                : getValue(value),
          };
        }) || []
    );
  }

  return (
    values.map((value) => {
      return {
        key: value.code.text,
        value: getValue(value),
      };
    }) || []
  );
};


/**
 * Obtiene los campos a renderizar para la observación de glucosa.
 * 
 * @param glucoseObservation - Observación de glucosa según el estándar FHIR.
 * @returns {Array<{ key: string, value: string }>} - Lista de campos a renderizar para la glucosa.
 */
export const getGlucoseFieldsToRender = (glucoseObservation: IObservation) => {
  return [
    {
      key: "Medición",
      value:
        glucoseObservation.extension?.find((ext) => ext.id === "mealTime")
          ?.valueCodeableConcept?.coding?.[0]?.display || "No especifica",
    },
    {
      key: "Estado",
      value:
        glucoseObservation.extension?.find((ext) => ext.id === "mood")
          ?.valueCodeableConcept?.coding?.[0]?.display || "No especifica",
    },
    {
      key: "Valor",
      value: glucoseObservation.valueQuantity?.value || "No especifica",
    },
  ];
};
