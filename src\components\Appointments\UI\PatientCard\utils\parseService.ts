import { Views } from "@/components/Appointments/Store/Actions";
import { appointmentServices } from "@umahealth/entities";

/** Devuelve el appointmentService correspondiente para el filtro del statusNav seleccionado */
export const parseViewToService = (view: Views): appointmentServices => {
  switch (view) {
    case 'guardia':
      return 'bag'
    case 'online':
      return 'online'
    case 'consultorio':
      return 'consultorio'
    case 'today':
      return 'consultorio'
    case 'pediatric':
      return 'bag'
    case 'special_consultorio':
      return 'consultorio'
    default:
      return 'bag'
  }
}

/** Devuelve el service parseado para representar correctamente las etiquetas de las patientCards segun el appointmentService recibido */
export const parseService = (service: appointmentServices) => {
  switch (service) {
    case 'bag':
      return 'guardia'
    case 'online':
      return 'especialist_online'
    case 'consultorio':
      return 'consultorio'
    default:
      return service
  }
}