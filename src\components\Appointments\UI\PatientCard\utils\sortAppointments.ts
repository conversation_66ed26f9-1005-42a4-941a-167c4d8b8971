import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";

export const sortAppointments = (appointments: IAppointmentWithPath[]): IAppointmentWithPath[] => {
  if (appointments && appointments?.toSorted){
  return appointments?.toSorted((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateA.getTime() - dateB.getTime();
  })
} else return []
}