import React, { useMemo } from 'react';
import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath';
import NewPatientCard from '../../../presentation/views/NewPatientCard';
import { sortAppointments } from '../utils/sortAppointments';
import { SectionDivider } from '@/storybook/components';
import EmptyService from '../../EmptyService';
import moment from 'moment-timezone';

interface ConsultorioViewProps {
  consultorioAppointments: IAppointmentWithPath[];
}

export const ConsultorioView: React.FC<ConsultorioViewProps> = ({
  consultorioAppointments
}) => {
  // Constante para la fecha de hoy
  const today = useMemo(() => moment(Date.now()).format("YYYY-MM-DD"), []);
  
  // Clasificar citas de consultorio por fecha - memoizado
  const { consultorioTodayAppointments, consultorioNotTodayAppointments } = useMemo(() => {
    const todayAppts: IAppointmentWithPath[] = [];
    const notTodayAppts: IAppointmentWithPath[] = [];
    
    consultorioAppointments?.forEach((appoint) => {
      if (appoint.date === today) {
        todayAppts.push(appoint);
      } else {
        notTodayAppts.push(appoint);
      }
    });
    
    return { 
      consultorioTodayAppointments: todayAppts, 
      consultorioNotTodayAppointments: notTodayAppts 
    };
  }, [consultorioAppointments, today]);
  // Memoizar las consultas de hoy ordenadas
  const sortedTodayAppointments = useMemo(() =>
    sortAppointments(consultorioTodayAppointments),
    [consultorioTodayAppointments]
  );

  // Memoizar las consultas futuras ordenadas
  const sortedFutureAppointments = useMemo(() =>
    sortAppointments(consultorioNotTodayAppointments),
    [consultorioNotTodayAppointments]
  );

  // Memoizar los componentes de tarjetas para citas de hoy
  const todayAppointmentsCards = useMemo(() =>
    sortedTodayAppointments?.map((appoint) => (
      <NewPatientCard
        key={appoint.assignation_id}
        appointment={appoint}
      />
    )),
    [sortedTodayAppointments]
  );

  // Memoizar los componentes de tarjetas para citas futuras
  const futureAppointmentsCards = useMemo(() =>
    sortedFutureAppointments?.map((appoint) => (
      <NewPatientCard
        key={appoint.assignation_id}
        appointment={appoint}
      />
    )),
    [sortedFutureAppointments]
  );
  // Si no hay consultas, mostrar mensaje de servicio vacío
  if (consultorioTodayAppointments.length === 0 && consultorioNotTodayAppointments.length === 0) {
    return (
      <EmptyService 
        title="No se encuentran consultas" 
        message="Este tipo de servicio no tiene citas disponibles o no es compatible actualmente" 
      />
    );
  }

  return (
    <>
      {/* Consultas para hoy */}
      {consultorioTodayAppointments?.length > 0 && (
        <>
          {todayAppointmentsCards}
        </>
      )}
      {/* Consultas para próximos días */}
      {consultorioNotTodayAppointments?.length > 0 && (
        <>
          <SectionDivider
            label="Próximos"
            count={consultorioNotTodayAppointments.length}
          />          
          {futureAppointmentsCards}
        </>
      )}
    </>
  );
};
