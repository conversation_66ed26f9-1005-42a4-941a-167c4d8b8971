import React, { useMemo } from 'react';
import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath';
import NewPatientCard from '../../../presentation/views/NewPatientCard';
import { SectionDivider } from '@/storybook/components';
import { useGetPendingPediatricAppointments } from '../../../infraestructure/getPediatricAppointments';
import { isPediatric } from '../PatientCard';
import { useLoadingAppointment } from '../../../context/LoadingAppointmentContext';
import EmptyService from '../../EmptyService';

interface GuardiaViewProps {
  att_appointments: IAppointmentWithPath[];
  guardiaAppointmentsOutAtt: IAppointmentWithPath[];
  disableOtherAppointments: boolean;
  currentFilter: 'all' | 'pediatric' | 'adults';
}

export const GuardiaView: React.FC<GuardiaViewProps> = ({
  att_appointments,
  guardiaAppointmentsOutAtt,
  disableOtherAppointments,
  currentFilter,
}) => {
  // Usamos el contexto para obtener la función de deshabilitación
  const { shouldDisableAppointment } = useLoadingAppointment();
  // Mantenemos el estado local del filtro seleccionado

  // Para filtrar consultas pediátricas cuando se selecciona ese filtro
  const pediatricAppointments = useGetPendingPediatricAppointments(process.env.NEXT_PUBLIC_COUNTRY, {
    enabled: currentFilter === 'pediatric'
  });


  // Memoizamos el listado de consultas filtradas según el tipo de paciente seleccionado
  const filteredAppointments = useMemo(() => {

    // Para el filtro pediátrico, usamos la API específica
    if (currentFilter === 'pediatric') {
      return pediatricAppointments?.data || [];
    }
    // Para adultos, filtramos los que NO son pediátricos
    else if (currentFilter === 'adults') {
      return guardiaAppointmentsOutAtt?.filter(appoint => !isPediatric(appoint.patient?.dob)) || [];
    }
    // Para todos, no filtramos
    else {
      return guardiaAppointmentsOutAtt || [];
    }
  }, [currentFilter, pediatricAppointments?.data, guardiaAppointmentsOutAtt]);

  // Memoizamos el listado de consultas en curso para evitar re-renders innecesarios
  const inProgressAppointments = useMemo(() =>
    att_appointments.map((appoint) => (
      <NewPatientCard
        key={appoint.assignation_id}
        appointment={appoint}
      />
    )),
    [att_appointments]
  );

  // Calculamos si cada cita debe estar deshabilitada y con qué mensaje
  const getDisabledState = useMemo(() => (appointmentId: string) => {
    // Si hay citas en ATT y no es Farmatodo, siempre deshabilitar otras citas
    if (disableOtherAppointments) {
      return {
        disabled: true,
        reason: "Podrás iniciar nuevas consultas cuando finalices tu consulta en curso"
      };
    }
    
    // Si hay una cita cargando o estamos en transición de estado
    if (shouldDisableAppointment(appointmentId)) {
      return {
        disabled: true,
        reason: "Espera a que termine de cargar la consulta actual"
      };
    }
    
    // No hay razón para deshabilitar
    return { disabled: false, reason: undefined };
  }, [disableOtherAppointments, shouldDisableAppointment]);

  // Memoizamos el listado de componentes de tarjetas de citas basado en las citas ya filtradas
  const upcomingAppointments = useMemo(() =>
    filteredAppointments.map((appoint) => {
      const { disabled, reason } = getDisabledState(appoint.assignation_id);
      return (
        <NewPatientCard
          key={appoint.assignation_id}
          appointment={appoint}
          disabled={disabled}
          disabledReason={reason}
        />
      );
    }),
    [filteredAppointments, getDisabledState]
  );

  // Verificar si no hay citas disponibles según el filtro seleccionado
  const shouldShowEmptyService = useMemo(() => {
    if (currentFilter === 'all' && att_appointments.length === 0 && filteredAppointments.length === 0) {
      return true;
    }
    
    if (currentFilter === 'pediatric' && 
        att_appointments.length === 0 && 
        filteredAppointments.filter(appoint => appoint.patient?.pediatric).length === 0) {
      return true;
    }
    
    if (currentFilter === 'adults' && 
        att_appointments.length === 0 && 
        filteredAppointments.filter(appoint => !appoint.patient?.pediatric).length === 0) {
      return true;
    }
    
    return false;
  }, [currentFilter, att_appointments.length, filteredAppointments]);

  // Mostrar mensaje de servicio vacío si no hay citas disponibles
  if (shouldShowEmptyService) {
    return (
      <EmptyService 
        title="La guardia online está tranquila, no hay pacientes esperando" 
        message="Es un buen momento para revisar tu agenda o prepararte para la próxima consulta" 
      />
    );
  }
  
  // Si no hay consultas en curso, mostramos solo las consultas pendientes
  if (att_appointments.length === 0) {
    return (
      <div>
        {upcomingAppointments}
      </div>
    );
  }

  return (
    <>
      {/* Consultas en curso */}
      {att_appointments.length > 0 && (
        <div>
          {inProgressAppointments}
        </div>
      )}

      {/* Consultas pendientes (filtradas según el tipo seleccionado) */}
      {filteredAppointments.length > 0 && (
        <>
          <SectionDivider
            label="Próximos"
            count={filteredAppointments.length}
          />
          {upcomingAppointments}
        </>
      )}
    </>
  );
};
