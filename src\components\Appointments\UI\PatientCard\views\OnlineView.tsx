import React, { useMemo } from 'react'
import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath'
import NewPatientCard from '../../../presentation/views/NewPatientCard'
import EmptyService from '../../EmptyService'
import { SectionDivider } from '@/storybook/components'

interface OnlineViewProps {
  specialistAppointments: IAppointmentWithPath[]
  today: string
}

export const OnlineView: React.FC<OnlineViewProps> = ({
  specialistAppointments,
  today,
}) => {
  // Verificar si hay al menos una cita en ATT
  const hasAttAppointments = useMemo(() => {
    return specialistAppointments.some(appt => appt.state === 'ATT');
  }, [specialistAppointments]);

  // Si hay citas en ATT, deshabilitar todas las demás
  const shouldDisable = useMemo(() => {
    return hasAttAppointments;
  }, [hasAttAppointments]);

  
  // Clasificar consultas de especialista - memoizado
  const { specialistTodayAppointments, specialistNotTodayAppointments } = useMemo(() => {
    const todayAppts: IAppointmentWithPath[] = [];
    const notTodayAppts: IAppointmentWithPath[] = [];
    
    specialistAppointments.forEach((appoint) => {
      if (appoint.date === today && appoint.especialidad !== 'aptofisico') {
        todayAppts.push(appoint);
      } else if (appoint.date !== today && appoint.especialidad !== 'aptofisico') {
        notTodayAppts.push(appoint);
      }
    });

    return { specialistTodayAppointments: todayAppts, specialistNotTodayAppointments: notTodayAppts };
  }, [specialistAppointments, today]);

  // 1. Memoizar todas las citas en atención (ATT), independientemente de la fecha
  const attSpecialistAppointments = useMemo(
    () => specialistAppointments
      .filter((appoint) => appoint.state === 'ATT')
      .map((appoint) => (
        <NewPatientCard
          key={appoint.assignation_id}
          appointment={appoint}
        />
      )),
    [specialistAppointments]
  );

  // 2. Memoizar las citas asignadas (ASSIGN) de hoy
  const todayAssignSpecialistAppointments = useMemo(
    () => specialistTodayAppointments
      .filter((appoint) => appoint.state === 'ASSIGN')
      .map((appoint) => (
        <NewPatientCard
          key={appoint.assignation_id}
          appointment={appoint}
          disabled={shouldDisable}
          disabledReason='Podrás iniciar nuevas consultas cuando finalices tus consultas en curso'
        />
      )),
    [specialistTodayAppointments, shouldDisable]
  );

  // 3. Memoizar las citas asignadas (ASSIGN) de otros días
  const futureAssignSpecialistAppointments = useMemo(
    () => specialistNotTodayAppointments
      .filter((appoint) => appoint.state === 'ASSIGN')
      .map((appoint) => (
        <NewPatientCard
          key={appoint.assignation_id}
          appointment={appoint}
          disabled={shouldDisable}
          disabledReason='Podrás iniciar nuevas consultas cuando finalices tus consultas en curso'
        />
      )),
    [specialistNotTodayAppointments, shouldDisable]
  );

  // Si no hay consultas o no quedan citas después de filtrar, mostrar el mensaje de servicio vacío
  if (
    !specialistAppointments?.length ||
    (!attSpecialistAppointments.length &&
     !todayAssignSpecialistAppointments.length &&
     !futureAssignSpecialistAppointments.length)
  ) {
    return (
      <EmptyService 
        title="Tu agenda está tranquila, no hay turnos cercanos" 
        message="Ampliar tu disponibilidad en agenda permite que más pacientes te elijan" 
      />
    );
  }

  return (
    <>
      {/* 1. Primero, mostrar todas las citas en ATT */}
      {attSpecialistAppointments.length > 0 && (
          <div className="grid gap-2">
            {attSpecialistAppointments}
          </div>
      )}

      {/* 2. Luego, mostrar las citas ASSIGN de hoy */}
      {todayAssignSpecialistAppointments.length > 0 && (
        <>
          <SectionDivider
            label="Hoy"
            count={todayAssignSpecialistAppointments.length}
          />
          <div className="grid gap-2">
            {todayAssignSpecialistAppointments}
          </div>
        </>
      )}

      {/* 3. Finalmente, mostrar las citas ASSIGN de otros días */}
      {futureAssignSpecialistAppointments.length > 0 && (
        <>
          <SectionDivider
            label="Próximos"
            count={futureAssignSpecialistAppointments.length}
          />
          <div className="grid gap-2 mt-2">
            {futureAssignSpecialistAppointments}
          </div>
        </>
      )}
    </>
  )
}
