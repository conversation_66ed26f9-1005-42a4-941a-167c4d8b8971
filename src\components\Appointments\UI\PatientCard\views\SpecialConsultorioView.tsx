import React, { useMemo } from 'react';
import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath';
import NewPatientCard from '../../../presentation/views/NewPatientCard';
import { sortAppointments } from '../utils/sortAppointments';
import EmptyService from '../../EmptyService';

interface SpecialConsultorioViewProps {
  specialConsultorioAppointments: IAppointmentWithPath[];
}

export const SpecialConsultorioView: React.FC<SpecialConsultorioViewProps> = ({
  specialConsultorioAppointments
}) => {
  // Memoizar las consultas ordenadas
  const sortedAppointments = useMemo(() =>
    sortAppointments(specialConsultorioAppointments),
    [specialConsultorioAppointments]
  );

  // Memoizar los componentes de tarjetas para las citas especiales
  const appointmentsCards = useMemo(() =>
    sortedAppointments?.map((appoint) => (
      <NewPatientCard
        key={appoint.assignation_id}
        appointment={appoint}
      />
    )),
    [sortedAppointments]
  );

  // Si no hay citas especiales, mostrar mensaje de servicio vacío
  if (!specialConsultorioAppointments?.length) {
    return (
      <EmptyService 
        title="No hay consultas especiales" 
        message="No hay consultas especiales programadas actualmente" 
      />
    );
  }

  return (
    <>
      {appointmentsCards}
    </>
  );
};
