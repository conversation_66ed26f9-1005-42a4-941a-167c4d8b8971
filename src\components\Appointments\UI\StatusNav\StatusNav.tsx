'use client'

import React from 'react'
import { isFarmatodo } from '@/config/endpoints'
import useHandleAppointmentsLength from '@/hooks/useHandleAppointmentsLength'
import { useRouter } from 'next/navigation'
import { AppointmentTypeButton } from '@/storybook/components/buttons/AppointmentTypeButton/AppointmentTypeButton'
import { AppointmentTypeButtonContainer } from '@/storybook/components/buttons/AppointmentTypeButton/AppointmentTypeButtonContainer'
import { IProviderExtendedWithProfileMediaPaths } from '@/serverServices/getProvider'
import { ObjectPermissions } from '@/serverServices/getAvailablePermissions'
import { PatientType } from '@/storybook/components/PatientTypeSelect/PatientTypeSelect'
import { bagAppointmentsLimit } from '@/store/actions/appointments/guardAppointments/useBagAppointments'

/**
 * Tipos de servicios disponibles en el sistema de consultas.
 * Cada servicio corresponde a una categoría diferente de citas o especialidades.
 */
export type ServiceCategory =
  | 'guardia'     // Guardia Online (urgencias)
  | 'online'      // Especialista Online 
  | 'consultorio' // Consultorio presencial
  | 'today'       // Consultas del día
  | 'special_consultorio'  // Prácticas especiales en consultorio
  | 'aptofisico'  // Certificados de aptitud física

/**
 * Props para el componente StatusNavNew.
 */
interface StatusNavNewProps {
  /** Datos del proveedor médico actual */
  provider: IProviderExtendedWithProfileMediaPaths;
  /** Permisos disponibles para el usuario */
  permissions: ObjectPermissions;
  currentView: ServiceCategory;
  patientTypeFilter: PatientType;
}

/**
 * Mapea las categorías de servicio a sus respectivos colores de fondo para los contadores.
 * Esta función asigna un color consistente según el tipo de servicio para mantener
 * coherencia visual en la interfaz.
 * 
 * @param service - La categoría de servicio
 * @returns El nombre de clase CSS para el color de fondo
 */
const getCountBgColor = (service: ServiceCategory): string => {
  switch (service) {
    case 'guardia':
      return 'bg-primary'   // Color primario 
    case 'online':
      return 'bg-[#7353BA]' // Púrpura
    case 'consultorio':
    case 'special_consultorio':
      return 'bg-secondary'  // Color secundario 
    case 'today':
    case 'aptofisico':
      return 'bg-[#0ABF8F]'  // Verde
  }
}

/**
 * Obtiene el nombre localizado/humanizado para mostrar en la UI según la categoría de servicio.
 * Adapta los nombres según el contexto (ej: Farmatodo tiene nombres específicos).
 * 
 * @param service - La categoría de servicio
 * @returns El nombre legible para humanos
 */
const getServiceDisplayName = (service: ServiceCategory): string => {
  switch (service) {
    case 'guardia':
      return 'Guardia Online'
    case 'online':
      return isFarmatodo ? 'Encuentros Online' : 'Especialista Online'
    case 'consultorio':
      return isFarmatodo ? 'Encuentros Presenciales' : 'Consultorio'
    case 'special_consultorio':
      return 'Prácticas especiales'
    case 'today':
      return 'Consultas de hoy'
    case 'aptofisico':
      return 'Aptos Físico'
    default:
      return ''
  }
}

/**
 * Componente de navegación para filtrar los tipos de consultas médicas.
 * 
 * Reglas de negocio:
 * - Para Farmatodo, se muestran solo: "all", "consultorio" y "online" con nombres específicos.
 * - Los permisos determinan qué servicios pueden verse (excepto "all" que siempre está disponible).
 * - La especialidad pediátrica solo aparece si el médico es pediatra.
 * - "Aptos Físico" solo aparece para médicos con esa especialidad específica.
 * - Se muestra un contador con el número de citas en cada categoría.
 * - El servicio "pediatric" tiene un checkbox adicional para filtrar citas pediátricas.
 * - El servicio "aptofisico" redirige a una página específica en lugar de filtrar.
 *
 * @param props - Las propiedades del componente
 */
const StatusNavNew = ({ provider, permissions, currentView, patientTypeFilter }: StatusNavNewProps) => {
  const router = useRouter()
  
  // Define los servicios disponibles según el contexto (Farmatodo o normal)
  const availableServices: ServiceCategory[] = isFarmatodo
    ? ['consultorio', 'online']
    : [
        'guardia',
        'online',
        'consultorio',
        'special_consultorio',
        'aptofisico',
      ]
  
  const { handleAppointsLength } = useHandleAppointmentsLength(patientTypeFilter)

  

  /**
   * Manejador para el cambio de servicios
   * @param newView Nueva vista seleccionada
   */
  async function handleViewChange(newView: ServiceCategory) {
    // Guardar vista seleccionada en cookies
    try {
      await fetch('/api/set-current-view', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ value: newView })
      });
    } catch (error) {
      console.error('Error al guardar la vista seleccionada:', error);
    }

    // Redirección a la vista seleccionada
    router.push(`/appointments?currentView=${newView}`);
    
  }

  /**
   * Renderiza los botones de tipo de cita basados en los servicios disponibles y permisos.
   */
  const renderAppointmentButtons = () => {
    return availableServices.map((service) => {

      // Verifica si el servicio debe mostrarse según permisos y especialidad
      const shouldDisplay = 
        permissions?.[service] 
      
      if (!shouldDisplay) return null;
      
      const count = handleAppointsLength(service);
      
      return (
        <React.Fragment key={service}>
          <AppointmentTypeButton 
            isActive={currentView === service}
            label={getServiceDisplayName(service)}
            count={count} 
            countBgColor={getCountBgColor(service)}
            onClick={() => handleViewChange(service)}
            max={bagAppointmentsLimit}
          />
        </React.Fragment>
      );
    });
  };

  /**
   * Renderiza casos especiales para consultorio cuando no es Farmatodo.
   * Incluye "Consultas de hoy" y "Prácticas especiales".
   */
  const renderSpecialCases = () => {
    if (!permissions?.consultorio || isFarmatodo) return null;
    
    return (
      <>
        <AppointmentTypeButton 
          isActive={currentView === 'special_consultorio'}
          label={getServiceDisplayName('special_consultorio')}
          count={handleAppointsLength('special_consultorio')} 
          countBgColor={getCountBgColor('special_consultorio')}
          onClick={() => handleViewChange('special_consultorio')}
          max={bagAppointmentsLimit}
        />
      </>
    );
  };

  /**
   * Renderiza el botón para Aptos Físico si corresponde a la especialidad del médico.
   */
  const renderAptoFisico = () => {
    if (provider?.matricula_especialidad !== 'aptofisico') return null;
    
    return (
      <AppointmentTypeButton 
        isActive={false}
        label={getServiceDisplayName('aptofisico')}
        count={handleAppointsLength('aptofisico')} 
        countBgColor={getCountBgColor('aptofisico')}
        onClick={() => handleViewChange('aptofisico')}
        max={bagAppointmentsLimit}
      />
    );
  };

  /**
   * Obtiene todos los botones que se deben mostrar en un solo array para renderizado declarativo
   */
  const getAllButtons = () => {
    // Obtener los botones principales
    const mainButtons = renderAppointmentButtons();
    
    // Obtener los botones de casos especiales (si existen)
    const specialButtons = renderSpecialCases();
    
    // Obtener el botón de apto físico (si existe)
    const aptoFisicoButton = renderAptoFisico();
    
    // Combinar todos los botones en un solo array eliminando los nulls
    return [
      ...Array.isArray(mainButtons) ? mainButtons : [mainButtons],
      specialButtons,
      aptoFisicoButton
    ].filter(Boolean);
  };
  
  return (
    <AppointmentTypeButtonContainer className='mb-4'>
      {getAllButtons()}
    </AppointmentTypeButtonContainer>
  );
};

export default StatusNavNew;
