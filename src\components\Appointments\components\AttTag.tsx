import { ClientsNames, TClientsNames } from '@/config/clients'
import { useClient } from '@/providers/ClientProvider'
import { Tag } from '@umahealth/occipital/client'
import React from 'react'

interface ITagProps {
  text: string,
  className?: string,
  color?: string
}

type ITagConfig = Record<TClientsNames, Record<string, ITagProps>>

const tagPropsConfig: ITagConfig = {
  [ClientsNames.FARMATODO]: {
    'especialista_online': {
      text: "Encuentro Online",
      className: "text-accent-400 mr-2 mt-1",
    },
    'consultorio': {
      text: "Encuentro presencial",
      className: "mr-2 mt-1",
      color: 'blue',
    },
  },
  [ClientsNames.UMA]: {
    'especialista_online': {
      text: 'Especialista',
      className: 'bg-transparent text-accent-400 pl-0 mr-2',
    },
    'consultorio': {
      text: 'Consultorio',
      className: 'h-5 mr-2',
      color: 'blue',
    },
    'guardia': {
      text: 'Guardia Online',
      className: 'bg-transparent text-primary-500 pl-0 mr-2',
    },
  }
}

const getTagProps = (client: TClientsNames, service: string) => {
  return tagPropsConfig[client][service]
}

const AttTag = ({ appointmentService }: { appointmentService: string }) => {
  const client = useClient()
  const tagProps = getTagProps(client, appointmentService)

  return <Tag {...tagProps} />
}

export default AttTag