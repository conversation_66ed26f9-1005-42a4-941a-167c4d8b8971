import React, { useState } from 'react';
import Draggable, { DraggableData, DraggableEvent } from 'react-draggable';

interface DraggableContainerProps {
  children: React.ReactNode;
}

const DraggableContainer: React.FC<DraggableContainerProps> = ({ children }) => {
  const [controlledPosition, setControlledPosition] = useState({ x: -240, y: -250 });
  const handleDrag = (e: DraggableEvent, data: DraggableData) => {
    setControlledPosition({ x: data.x, y: data.y });
  };
  return (
    <Draggable 
      position={controlledPosition} 
      onDrag={handleDrag}
      handle=".handle"
      >
      <div
      style={{
        borderRadius: '8px',
        cursor: 'grab',
        position: 'fixed',
        bottom: 0,
        right: 0,
      }}
      className='handle'
      >
        {children}
      </div>
    </Draggable>
  );
};
export default DraggableContainer;