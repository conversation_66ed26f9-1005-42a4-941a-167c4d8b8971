'use client'
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface LoadingAppointmentContextType {
  loadingAppointmentId: string | null;
  setLoadingAppointment: (appointmentId: string | null) => void;
  isAppointmentLoading: (appointmentId: string) => boolean;
  shouldDisableAppointment: (appointmentId: string) => boolean;
}

const LoadingAppointmentContext = createContext<LoadingAppointmentContextType | undefined>(undefined);

export function LoadingAppointmentProvider({ children }: { children: ReactNode }) {
  // Estado para rastrear qué cita está cargando
  const [loadingAppointmentId, setLoadingAppointmentId] = useState<string | null>(null);
  // Estado para rastrear si estamos en un estado intermedio
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Limpiar estado de transición después de un tiempo
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    
    if (isTransitioning) {
      // Asegurar que después de un tiempo prudencial, el estado de transición se limpie
      timeoutId = setTimeout(() => {
        setIsTransitioning(false);
      }, 5000); // 5 segundos debería ser suficiente para cualquier transición
    }
    
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isTransitioning]);

  // Función mejorada para establecer estado de carga
  const setLoadingAppointment = (appointmentId: string | null) => {
    if (appointmentId) {
      // Si estamos estableciendo un ID (inicio de carga), marcamos transición
      setIsTransitioning(true);
    } else if (loadingAppointmentId) {
      // Si estamos limpiando un ID (fin de carga), mantenemos transición brevemente
      // Esto evita que las citas se habiliten temporalmente durante cambios de estado
      setIsTransitioning(true);
    }
    
    setLoadingAppointmentId(appointmentId);
  };

  const isAppointmentLoading = (appointmentId: string) => {
    return loadingAppointmentId === appointmentId;
  };
  
  // Nueva función para determinar si una cita debe estar deshabilitada
  const shouldDisableAppointment = (appointmentId: string) => {
    // Si hay una cita cargando y no es esta, o estamos en transición
    return (!!loadingAppointmentId && loadingAppointmentId !== appointmentId) || 
           (isTransitioning && loadingAppointmentId !== appointmentId);
  };

  return (
    <LoadingAppointmentContext.Provider
      value={{
        loadingAppointmentId,
        setLoadingAppointment,
        isAppointmentLoading,
        shouldDisableAppointment,
      }}
    >
      {children}
    </LoadingAppointmentContext.Provider>
  );
}

export function useLoadingAppointment() {
  const context = useContext(LoadingAppointmentContext);
  if (context === undefined) {
    throw new Error('useLoadingAppointment must be used within a LoadingAppointmentProvider');
  }
  return context;
}
