import { resume_att_guard } from "@/config/endpoints";
import axios from "axios";
import {
  IProvider,
} from "@umahealth/entities";
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
export interface reserveAppointmentResponse {
  reservation: boolean;
  error: boolean
}


export const resumeGuardiaAttReq = async (
  doctor: IProvider,
  assignation_id: string,
): Promise<reserveAppointmentResponse> => {
  const token = await getFirebaseIdToken();
  const headers = {
    "content-type": "application/json",
    authorization: `Bearer ${token}`,
    "uid": doctor?.uid
  };

  const body = {
    country: process.env.NEXT_PUBLIC_COUNTRY,
    appointmentId: assignation_id,
  };

  const { data } =
    await axios.post<reserveAppointmentResponse>(resume_att_guard, body, {
      headers
    });

  return data

};
