import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { reserve_att_guard } from "@/config/endpoints";
import axios from "axios";

import {
  IProvider,
} from "@umahealth/entities";
export interface resumeAppointmentResponse {
  reservation: boolean;
  error: boolean
}


export const reserveGuardiaAttReq = async (
  doctor: IProvider,
  assignation_id: string,
): Promise<resumeAppointmentResponse> => {
  const token = await getFirebaseIdToken();
  const headers = {
    "content-type": "application/json",
    authorization: `Bearer ${token}`,
    "uid": doctor?.uid
  };

  const body = {
    country: process.env.NEXT_PUBLIC_COUNTRY,
    appointmentId: assignation_id,
  };

  const { data } =
    await axios.post<resumeAppointmentResponse>(reserve_att_guard, body, {
      headers
    });

  return data

};
