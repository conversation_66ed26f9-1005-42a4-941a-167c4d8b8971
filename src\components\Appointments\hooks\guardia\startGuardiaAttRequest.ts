'use client';
import { start_att_guard } from "@/config/endpoints";
import axios from "axios";
import {
  attCategories,
  countries,
  TSpecialties,
} from "@umahealth/entities";
import { getTokenFromCookies, getUidFromCookies } from '@/auth/authServerActions';
import format from 'date-fns/format';
import { toZonedTime } from 'date-fns-tz/toZonedTime';
import { getLastGuardRequest, setLastGuardRequest } from './lastGuardRequestCookies';

interface startChatRequestResponse {
  patientUid: string;
  assignationId: string;
  dependantUid: string;
  specialty: TSpecialties;
  attType: attCategories;
  attCountry: countries;
  cuit: string;
  providerUid: string;
}

export interface startChatRequestResponseExtended extends startChatRequestResponse {
  corporate: string;
}

export const startGuardiaAttReq = async (
  {
    uid_dependant,
    uid,
    path,
    assignation_id,
    corporate,
  }: {
    uid_dependant?: string | false;
    uid: string;
    path?: string;
    assignation_id: string;
    corporate: string;
  }
): Promise<startChatRequestResponseExtended> => {
  const lastExecutionCookie = await getLastGuardRequest()
  const now = Date.now();
  const nowString = now.toString()

  if (lastExecutionCookie) {
    const lastExecution = parseInt(lastExecutionCookie, 10);
    if (now - lastExecution < 5000) {
      console.warn("Se ejecutó hace menos de 5 segundo. Esperando 5 segundos...");
      await new Promise((res) => setTimeout(res, 5000));
    }
  }

  // Guardamos el nuevo timestamp
  await setLastGuardRequest(nowString)

  const token = await getTokenFromCookies();
  const doctorUid = await getUidFromCookies();
  const activeUid: Omit<string, ''> = uid_dependant ? uid_dependant : uid;
  const dependant = Boolean(uid_dependant);

  // Usamos date-fns-tz para obtener fecha en Argentina
  const zonedDate = toZonedTime(new Date(), 'America/Argentina/Buenos_Aires');
  const dt = format(zonedDate, 'yyyy-MM-dd HH:mm:ss');
  const { data: startChatResponse } = await axios.post<startChatRequestResponse>(
    start_att_guard,
    {
      activeUid: activeUid,
      country: process.env.NEXT_PUBLIC_COUNTRY,
      uid: uid,
      appointmentPath: path,
      eventPath: `events/requests/online/${assignation_id}`,
      dependant: uid_dependant,
      cuit: doctorUid,
      isDependant: dependant,
      date: dt,
      assignationId: assignation_id,
      type: "bag",
      appointmentType: "bag",
      providerUid: doctorUid
    },
    { headers:  {
        "content-type": "application/json",
        authorization: `Bearer ${token}`
      }
     }
  );

  if (startChatResponse?.providerUid === doctorUid) {
    return {
      ...startChatResponse,
      corporate,
    };
  } else {
    throw new Error(
      `No se pudo iniciar esta consulta porque hubo un error al obtener el cuit o el providerUid, la response fue: ${JSON.stringify(startChatResponse)}`
    );
  }
};
