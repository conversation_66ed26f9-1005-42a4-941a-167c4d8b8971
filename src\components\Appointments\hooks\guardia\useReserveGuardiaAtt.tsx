import { IProvider } from '@umahealth/entities';
import { reserveAppointmentResponse, resumeGuardiaAttReq } from './reserveGuardiaAttRequest';
import { useMutation } from 'react-query';
import { reserveGuardiaAttReq } from './resumeGuardiaAttRequest';

const useReserveGuardiaAtt = () => {
  return useMutation<reserveAppointmentResponse, Error, { doctor: IProvider; assignation_id: string, action: 'RESERVE' | 'RESUME' }>(
    async ({ doctor, assignation_id, action }) => {
      if(action === 'RESERVE') {
        return await reserveGuardiaAttReq(doctor, assignation_id);
      } else {
        return await resumeGuardiaAttReq(doctor, assignation_id);
      }
    }
  );
};

export default useReserveGuardiaAtt