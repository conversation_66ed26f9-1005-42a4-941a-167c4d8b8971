import { useMutation } from "@tanstack/react-query";
import { errorHandler } from "@/config/stackdriver";
import { useRouter } from "next/navigation";
import { startGuardiaAttReq, startChatRequestResponseExtended } from "./startGuardiaAttRequest";
import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";
import { useAppSelector } from "@/store/hooks";
import usePostLogs from "@/services/reactQuery/Logs/usePostLogs";
import { redirectToAtt } from "../../utils/redirectToAtt";
import { useDispatch } from "react-redux";
import { useQueryClient } from "@tanstack/react-query";
import { useTransition } from "react";
import { useLoadingAppointment } from "../../context/LoadingAppointmentContext";

export interface IAppointmentMetric {
  index: number;
  listQuantity?: number;
  appointmentsListQ?: number;
  isPediatric?: boolean;
}

export const useStartGuardiaAtt = (appointment: IAppointmentWithPath) => {
  const doctor = useAppSelector(state => state.user.profile);
  const router = useRouter();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const [isPending, startTransition] = useTransition();
  const { mutate: logEvent } = usePostLogs(
    doctor.uid,
    appointment.assignation_id,
  );

  // Obtenemos el contexto de carga de citas
  const { setLoadingAppointment } = useLoadingAppointment();

  const mutation = useMutation({
    // mutationFn define la función asíncrona que realiza la operación
    mutationFn: async () => {
      // Establecer que esta cita está cargando
      setLoadingAppointment(appointment.assignation_id);
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Timeout: La operación tardó demasiado')), 30000)
      );

      // Creamos primero la promesa antes de usarla en Promise.race
      const dataPromise = startGuardiaAttReq({
        assignation_id: appointment.assignation_id,
        corporate: appointment.patient.corporate,
        path: appointment.path,
        uid: appointment.patient.uid,
        uid_dependant: appointment.patient.uid_dependant
      });

      return Promise.race([dataPromise, timeoutPromise]) as Promise<startChatRequestResponseExtended>;
    },
    // onSuccess se ejecuta cuando la mutación se completa con éxito
    onSuccess: async (data) => {
      // Limpiar el estado de carga
      setLoadingAppointment(null);
      
      // Aseguramos que se invaliden las consultas para refrescar el estado
      queryClient.invalidateQueries({ queryKey: ['noIomaBagAppointments'] });
      queryClient.invalidateQueries({ queryKey: ['BagAppointments'] });
      if (!data) return;

      // Actualizamos el estado global
      dispatch({ type: "SET_CITA_IN_DETAIL", payload: {} });
      dispatch({ type: "RESET_ATT" });
      dispatch({ type: "RESET_FICHA" });
      dispatch({ type: "RESET_MEDIKIT" });
      dispatch({ type: "RESET_MY_PATIENT" });
      dispatch({ type: "CLEAN_CALL" });
      
      // Registramos el evento
      logEvent({ events: "joinRoom" });

      // Redireccionamos al usuario con transición suave
      const redirectPath = await redirectToAtt(data);
      startTransition(() => {
        router.push(redirectPath);
      });
    },
    // onError maneja errores de forma centralizada
    onError: (error) => {
      // Limpiar el estado de carga en caso de error
      setLoadingAppointment(null);
      
      // Forzamos una actualización de estado
      console.error('Error al iniciar atención de guardia:', error);
      logEvent({ events: "failedToJoinRoom" });
      
      // Refrescamos las listas de citas
      queryClient.invalidateQueries({ queryKey: ['noIomaBagAppointments'] });
      queryClient.invalidateQueries({ queryKey: ['BagAppointments'] });

      if (error instanceof Error && errorHandler) {
        errorHandler.report(error);
      }
    }
  });

  // Devolvemos la mutación con estado de carga mejorado
  return {
    ...mutation,
    // Combinamos el estado de carga de la mutación con el estado de transición
    isLoading: mutation.isPending || isPending
  };
};
