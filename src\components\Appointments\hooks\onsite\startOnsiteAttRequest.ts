import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { start_chat_node } from "@/config/endpoints";
import moment from 'moment-timezone';
import axios from "axios";
import { IAppointment } from "@umahealth/entities/src/entities/appointments/interfaces/IAppointment";
import {
  attCategories,
  countries,
  IProvider,
  TSpecialties,
} from "@umahealth/entities";

interface startChatRequestResponse {
  patientUid: string;
  assignationId: string;
  dependantUid: string;
  specialty: TSpecialties;
  attType: attCategories;
  attCountry: countries;
  cuit: string;
  providerUid: string;
}

export interface startChatRequestResponseExtended {
  patientUid: string;
  assignationId: string;
  dependantUid: string;
  specialty: TSpecialties;
  attType: attCategories;
  attCountry: countries;
  cuit: string;
  providerUid: string;
  corporate: string;
}

export const startOnsiteAttReq = async (
  appointment: IAppointment,
  doctor: IProvider,
) => {
  const dt = moment().tz('America/Argentina/Buenos_Aires').format('YYYY-MM-DD HH:mm:ss');
  const token = await getFirebaseIdToken();
  const { patient, path, assignation_id } = appointment;
  const activeUid = patient.uid_dependant ? patient.uid_dependant : patient.uid;
  const dependant = patient.uid_dependant ? true : false;
  const startChatHeaders = {
    "content-type": "application/json",
    authorization: `Bearer ${token}`,
  };

  const startChatBody = {
    activeUid: activeUid,
    country: process.env.NEXT_PUBLIC_COUNTRY,
    uid: patient.uid,
    appointmentPath: path,
    eventPath: `events/requests/onsite/${assignation_id}`,
    dependant: patient.uid_dependant,
    cuit: doctor?.core_id || doctor?.uid,
    isDependant: dependant,
    assignationId: assignation_id,
    type: "onsite",
    appointmentType: "onsite",
    date: dt,
    providerUid: doctor?.uid,
  };

  const { data: startChatResponse } =
    await axios.post<startChatRequestResponse>(start_chat_node, startChatBody, {
      headers: startChatHeaders,
    });
    
  if(startChatResponse?.cuit === doctor?.cuit || startChatResponse?.cuit === doctor?.uid || startChatResponse?.providerUid === doctor?.uid) {
      return {
        ...startChatResponse, 
        corporate: patient?.corporate
      } satisfies startChatRequestResponseExtended
  } else {
        throw new Error(`No se pudo iniciar esta consulta porque hubo un error al obtener el cuit o el providerUid, la response fue: ${JSON.stringify(startChatResponse)}`)
  }
};
