import { useAppSelector } from "@/store/hooks";
import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";

export function useOpenSpecialistAppointments() {
  const appointments = useAppSelector((state) => state.appointments.assign_appoints);

  const openSpecialistAppointment = appointments.find(
    (appointment: IAppointmentWithPath) =>
      appointment.service === "especialista_online" && appointment.state === "ATT"
  );

  return {
    isOpen: !!openSpecialistAppointment,
    appointment: openSpecialistAppointment,
  };
}