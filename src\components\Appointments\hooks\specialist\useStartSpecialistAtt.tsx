import { errorHand<PERSON> } from "@/config/stackdriver";
import { IAppointment } from "@umahealth/entities/src/entities/appointments/interfaces/IAppointment";
import { useMutation } from "@tanstack/react-query";
import { useAppSelector } from "@/store/hooks";
import { redirectToAtt } from "../../utils/redirectToAtt";
import { useRouter } from "next/navigation";
import usePostLogs from "@/services/reactQuery/Logs/usePostLogs";
import { startChatRequestResponseExtended, startSpecialistAttReq } from "./startSpecialistAttRequest";
import { useDispatch } from "react-redux";
import { useTransition } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useLoadingAppointment } from "../../context/LoadingAppointmentContext";

export interface IAppointmentMetric {
  index: number;
  listQuantity?: number;
  appointmentsListQ?: number;
  isPediatric?: boolean;
}

export const useStartSpecialistAtt = (appointment: IAppointment) => {
  const doctor = useAppSelector(state => state.user.profile);
  const router = useRouter();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const [isPending, startTransition] = useTransition();
  const { mutate: logEvent } = usePostLogs(
    doctor.uid,
    appointment.assignation_id,
  );

  // Obtenemos el contexto de carga de citas
  const { setLoadingAppointment } = useLoadingAppointment();

  const mutation = useMutation({
    mutationFn: async (): Promise<startChatRequestResponseExtended | undefined> => {
      // Establecer que esta cita está cargando
      setLoadingAppointment(appointment.assignation_id);
      // Implementamos un timeout para la operación
      const timeoutPromise = new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('Timeout: La operación tardó demasiado')), 30000)
      );
      
      // Ejecutamos la petición
      const dataPromise = startSpecialistAttReq(appointment, doctor);
      
      // Retornamos el primero que termine
      return Promise.race([dataPromise, timeoutPromise]) as Promise<startChatRequestResponseExtended>;
    },
    onSuccess: async(data) => {
      // Limpiar el estado de carga
      setLoadingAppointment(null);
      
      // Aseguramos que se invaliden las consultas para refrescar el estado
      queryClient.invalidateQueries({ queryKey: ['SpecialistAppointments'] });
      if (!data) return;

      // Actualizamos el estado global
      dispatch({ type: "SET_CITA_IN_DETAIL", payload: {} });
      dispatch({ type: "RESET_ATT" });
      dispatch({ type: "RESET_FICHA" });
      dispatch({ type: "RESET_MEDIKIT" });
      dispatch({ type: "RESET_MY_PATIENT" });
      dispatch({ type: "CLEAN_CALL" });
      
      // Registramos el evento
      logEvent({ events: "joinRoom" });

      // Redireccionamos con transición suave
      const redirectPath = await redirectToAtt(data);
      startTransition(() => {
        router.push(redirectPath);
      });
    },
    onError: (error) => {
      // Limpiar el estado de carga en caso de error
      setLoadingAppointment(null);
      
      // Forzamos una actualización de estado
      console.error('Error al iniciar atención de especialista:', error);
      logEvent({ events: "failedToJoinRoom" });
      
      // Invalidamos queries para refrescar datos
      queryClient.invalidateQueries({ queryKey: ['SpecialistAppointments'] });
      
      if (error instanceof Error && errorHandler) {
        errorHandler.report(error);
      }
    }
  });

  // Devolvemos la mutación con estado de carga mejorado
  return {
    ...mutation,
    // Combinamos el estado de carga de la mutación con el de transición
    isLoading: mutation.isPending || isPending
  };
};