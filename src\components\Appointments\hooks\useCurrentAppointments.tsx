import { useContext, useEffect, useMemo } from 'react'
import useSpecialistAppointments from '@/store/actions/appointments/useSpecialistAppointments'
import useConsultorioAppointments from '@/store/actions/appointments/onsiteAppointments/useListenConsultorioAppointments'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import { AvailablePermissionsContext } from '@/components/User/AvailablePermissionsProvider'
import { useGetInstrumentalPractices } from '@/services/reactQuery/useGetInstrumentalPractices'
import sortSpecialistAppointment from '@/store/actions/appointments/sorts/sortSpecialistAppointments'
import useBagAppointmentsATTOrReserved from '@/store/actions/appointments/guardAppointments/useBagAppointmentsATT'
import useBagAppointments from '@/store/actions/appointments/guardAppointments/useBagAppointments'
import useConsultorioSpecialAppointments from '@/store/actions/appointments/onsiteAppointments/useListenConsultorioSpecialAppointments'
import { IFeatures } from '@/serverServices/getFeatures'
import { isEqual } from 'lodash' // Importar isEqual de lodash para comparación profunda
import { ILicense } from '@umahealth/entities'
import { RawTimestamp } from '../utils/filterMandatoryFields'
import usePediatricAppointments from '@/store/actions/appointments/guardAppointments/usePediatricAppointments'
import { PatientType } from '@/storybook/components/PatientTypeSelect/PatientTypeSelect'

interface UseCurrentAppointmentsProps {
  features: IFeatures
  licenses: ILicense<RawTimestamp>[]
  patientTypeFilter: PatientType
}

export function useCurrentAppointments({
  features,
  licenses,
  patientTypeFilter
}: UseCurrentAppointmentsProps) {
  const doctor = useAppSelector((state) => state.user.profile)
  const availablePermissions = useContext(AvailablePermissionsContext)
  const instrumentalPractices = useGetInstrumentalPractices('EMERGENCIAS')

  const specialistAppointment = useSpecialistAppointments({
    userUid: doctor.uid,
    enabled: !!availablePermissions?.online && !!features?.consultorio_online,
  })

  const bagAppointmentsAtt = useBagAppointmentsATTOrReserved({
    userUid: doctor.uid,
    enabled: !!availablePermissions?.guardia && !!features?.guardia,
  })


  const {
    assign_appoints,
    consultorio_appointments,
    special_consultorio_appointments,
    att_appointments,
    bag_appoints,
    pediatric_appoints,
  } = useAppSelector((state) => state.appointments)

    // Check if any license jurisdiction is inside the allowed list
    const hasAllowedLicenseForIOMA = licenses.some(
      (license) =>
        license.jurisdiction &&
        ['M.N.', 'Buenos Aires', 'CABA'].includes(license.jurisdiction))

  const sortedData = useMemo(() => {
    if (
      specialistAppointment.data &&
      process.env.NEXT_PUBLIC_COUNTRY !== 'MX'
    ) {
      return sortSpecialistAppointment(specialistAppointment.data)
    }
    return specialistAppointment.data
  }, [specialistAppointment.data])

  const bagAppointments = useBagAppointments({
    enabled: !!availablePermissions?.guardia && !!features?.guardia,
    hasAllowedLicenseForIOMA: hasAllowedLicenseForIOMA
  })

  const pediatricAppointments = usePediatricAppointments({
    enabled: !!availablePermissions?.guardia && (patientTypeFilter === 'pediatric'),
    hasAllowedLicenseForIOMA: hasAllowedLicenseForIOMA
  })

  const consultorioAppointments = useConsultorioAppointments({
    enabled:
      !!features?.consultorio_presencial && !!availablePermissions?.consultorio && !!instrumentalPractices.data,
    instrumentalPractices: instrumentalPractices.data,
  })


  const consultorioSpecialAppointments = useConsultorioSpecialAppointments({
    enabled:
      !!features?.consultorio_presencial && !!availablePermissions?.consultorio && !!instrumentalPractices.data,
    instrumentalPractices: instrumentalPractices.data,
  })

  const dispatch = useAppDispatch()

  useEffect(() => {
    const newData =
      process.env.NEXT_PUBLIC_COUNTRY === 'MX'
        ? specialistAppointment.data
        : sortedData

    if (newData && !isEqual(assign_appoints, newData)) {
      dispatch({ type: 'SET_ASSIGNED_APPOINTS', payload: newData })
    }
  }, [dispatch, specialistAppointment.data, sortedData, assign_appoints])

  useEffect(() => {
    if (
      bagAppointmentsAtt.data &&
      !isEqual(att_appointments, bagAppointmentsAtt.data)
    ) {
      dispatch({
        type: 'SET_ATT_APPOINTMENTS',
        payload: bagAppointmentsAtt.data,
      })
    }
  }, [dispatch, bagAppointmentsAtt.data, att_appointments])

  useEffect(() => {
    if (bagAppointments.data && !isEqual(bag_appoints, bagAppointments.data)) {
      dispatch({ type: 'SET_BAG_APPOINTS', payload: bagAppointments.data })
    }
  }, [dispatch, bagAppointments.data, bag_appoints])


  useEffect(() => {
    if (
      consultorioAppointments.data &&
      !isEqual(consultorio_appointments, consultorioAppointments.data)
    ) {
      dispatch({
        type: 'SET_CONSULTORIO_APPOINTMENTS',
        payload: consultorioAppointments.data,
      })
    }
  }, [dispatch, consultorioAppointments.data, consultorio_appointments])

  useEffect(() => {
    if (
      consultorioSpecialAppointments.data &&
      !isEqual(
        special_consultorio_appointments,
        consultorioSpecialAppointments.data
      )
    ) {
      dispatch({
        type: 'SET_SPECIAL_CONSULTORIO_APPOINTMENTS',
        payload: consultorioSpecialAppointments.data,
      })
    }
  }, [
    dispatch,
    consultorioSpecialAppointments.data,
    special_consultorio_appointments,
  ])

  useEffect(() => {
    if (
      pediatricAppointments.data &&
      !isEqual(
        pediatric_appoints,
        pediatricAppointments.data
      )
    ) {
      dispatch({
        type: 'SET_PEDIATRIC_APPOINTS',
        payload: pediatricAppointments.data,
      })
    }
  }, [pediatricAppointments.data, pediatric_appoints])
}
