import { useEffect } from 'react';
import moment from 'moment';
import { useValidatePhoneFlag } from '@/services/reactQuery/useValidatePhoneFlag';
import { useValidatePhoneStatus } from '@/hooks/useGetPhoneValidationCode';
import { useAppSelector } from '@/store/hooks';

export function usePhoneValidationModal(setPhoneModal: (open: boolean) => void) {
    const doctor = useAppSelector((state) => state.user.profile)
    const phoneValidationFlag = useValidatePhoneFlag()
    const validationStatus = useValidatePhoneStatus(doctor.uid);
    useEffect(() => {
        const phoneValidationDismissedDate = localStorage.getItem('phoneValidationDismissed');
        const hoursDifference = phoneValidationDismissedDate ? moment().diff(phoneValidationDismissedDate, 'hours') : '';

        if (
            phoneValidationFlag.data?.phoneCode &&
            (hoursDifference === "" || hoursDifference > 24) &&
            validationStatus.isSuccess &&
            validationStatus.data !== 'code'
        ) {
            setPhoneModal(true);
        }
    }, [validationStatus.isSuccess, validationStatus.data, phoneValidationFlag.data?.phoneCode, setPhoneModal]);
}
