import { useContext, useEffect } from "react"
import { getLocalStorageItem, setLocalStorageItem } from "../utils/manageLocalStorage"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { RootState } from "@/store/configStore"
import { AvailablePermissionsContext } from "@/components/User/AvailablePermissionsProvider"

export function usePediatricFilterPreference() {
  const dispatch = useAppDispatch()
  const { dont_show_pediatric_appointments } = useAppSelector((state: RootState) => state.appointments)
  const availablePermissions = useContext(AvailablePermissionsContext)

  const handleDontShowPediatric = (value: boolean) => {
    setLocalStorageItem('dontShowPediatricAppointments', !value)
    dispatch({
      type: 'DONT_SHOW_PEDIATRIC_APPOINTMENTS',
      payload: !value,
    })
  }

  useEffect(() => {
    let finalValue = true
  
    const storedPreference = getLocalStorageItem('dontShowPediatricAppointments')
    if (storedPreference !== null && storedPreference !== undefined) {
      finalValue = typeof storedPreference === 'boolean'
        ? storedPreference
        : storedPreference === 'true'
    }
    
    if (typeof availablePermissions?.pediatric === 'boolean') {
      finalValue = availablePermissions.pediatric
  }
  
  handleDontShowPediatric(finalValue)
  }, [])
  return { dont_show_pediatric_appointments, handleDontShowPediatric }
}