import { batch } from 'react-redux';
import { useDispatch } from 'react-redux';

export const useResetFicha = () => {
    const dispatch = useDispatch();
    /**
     * Ejecuta una serie de acciones de limpieza en el estado global de Redux relacionadas con la atención médica.
     * 
     * #### Detalle de las acciones:
     * - Limpia los detalles de la cita (`SET_CITA_IN_DETAIL`).
     * - Resetea el estado de la atención (`RESET_ATT`).
     * - Limpia la ficha médica (`RESET_FICHA`).
     * - Limpia los datos relacionados con recetas médicas (`RESET_MEDIKIT`).
     * - Limpia los datos del paciente (`RESET_MY_PATIENT`).
     * - Limpia el estado de la llamada en curso (`CLEAN_CALL`).
     * 
     *#### Por qué usamos batch:
     * - Agrupa múltiples dispatches en una sola actualización de estado
     * - <PERSON>vita re-renders innecesarios (sin batch, cada dispatch causaría un re-render)
     * - Mejora el performance al reducir el número de actualizaciones
     * - Previene estados intermedios que podrían causar problemas en la UI
     * - Es la forma recomendada por Redux para despachar múltiples acciones juntas

     * 
     * #### Uso:
     * ```javascript
     * resetFicha(); // Ejecuta todas las acciones de limpieza
     * ```
     */
    const resetFicha = () => {
        batch(() => {
            dispatch({ type: "SET_CITA_IN_DETAIL", payload: {} });
            dispatch({ type: "RESET_ATT" });
            dispatch({ type: "RESET_FICHA" });
            dispatch({ type: "RESET_MEDIKIT" });
            dispatch({ type: "RESET_MY_PATIENT" });
            dispatch({ type: "CLEAN_CALL" });
        });
    }

    return { resetFicha }
}

