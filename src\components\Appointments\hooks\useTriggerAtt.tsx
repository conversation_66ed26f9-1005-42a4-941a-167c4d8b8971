import { useRouter } from "next/navigation";
import { redirectToAtt } from "../utils/redirectToAtt";
import { errorHandler } from "@/config/stackdriver";
import { verifyCameraAndMicrophone } from "../utils/verifyCameraAndMicrophone";
import swal from "sweetalert";
import { useStartChatRequest } from "@/services/reactQuery/useStartChatRequest";
import { IAppointment } from "@umahealth/entities/src/entities/appointments/interfaces/IAppointment";
import { useDispatch } from "react-redux";
import { startChatErrorSelector } from "./utils/startChatErrors";
import { useMutation } from "react-query";

export interface IAppointmentMetric {
  index: number;
  listQuantity?: number;
  appointmentsListQ?: number;
  isPediatric?: boolean;
}

export const useStartAppointment = () => {
  const router = useRouter();
  const startChatRequest = useStartChatRequest();
  const dispatch = useDispatch();

  return useMutation(async (appointment: IAppointment): Promise<void> => {
    try {
      const isGuardia = appointment.path?.includes("bag");
      const isSpecialist = appointment.path?.includes("online");
      // Por ahora verificamos sólo para docs de guardia que tengan cámara y mic activados
      if (isGuardia || isSpecialist) {
        await verifyCameraAndMicrophone();
      }
    } catch (err) {
      errorHandler && errorHandler.report(`error verificando cámara y micrófono: ${err}`);
      const timestamp = new Date().toLocaleString();
      await swal(
        "Hubo un error obteniendo los permisos de tu cámara y microfono",
        `Prueba que ningún otro programa este usando tu microfono o cámara, 
			detalle: ${timestamp} error: ${err}`,
        "warning",
      );
    }
    startChatRequest.mutate(appointment, {
      onSuccess: async (startChatRequestResponse) => {
        if (startChatRequestResponse.patientUid)
        dispatch({ type: "SET_CITA_IN_DETAIL", payload: {} });
        dispatch({ type: "RESET_ATT" });
        dispatch({ type: "RESET_FICHA" });
        dispatch({ type: "RESET_MEDIKIT" });
        dispatch({ type: "RESET_MY_PATIENT" });
        dispatch({ type: "CLEAN_CALL" }); // Limpiamos el estado de la consulta por si había otra
        const redirectPath = await redirectToAtt({
          ...startChatRequestResponse,
        });
        router.push(redirectPath);
      },
      onError: async (error) => {
        console.error(error);
        if (error instanceof Error && errorHandler) {
          errorHandler.report(error);
        }
        const clickedButton = await startChatErrorSelector(error);
        if (
          clickedButton === "cameraPermissions" &&
          typeof window !== "undefined"
        ) {
          window.open(
            "https://support.google.com/chrome/answer/2693767?hl=es-419&co=GENIE.Platform%3DDesktop&oco=0",
            "_blank",
          );
        }
      },
    });
  });
};
