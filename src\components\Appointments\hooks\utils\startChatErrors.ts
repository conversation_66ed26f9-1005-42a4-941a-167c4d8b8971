import swal from "sweetalert";

export const genericSwalError = async (
  error: any,
  title: string,
  text: string,
) => {
  const timestamp = new Date().toLocaleString();
  const clickedButton = await swal({
    title: error.title || `${title}`,
    text:
      error.text ||
      `${text}. Si el error persiste, comuníquese con Soporte. Detalle: ${timestamp}`,
    icon: error.icon || "warning",
    buttons: error.buttons || { cancel: "Salir" },
    dangerMode: error.dangerMode || false,
  });
  return clickedButton
};

export const startChatErrorSelector = async (error: any) => {
  const genericTitle = "No se pudo iniciar esta consulta"
  if (error?.response?.data?.msg?.includes("locking")) {
    return await genericSwalError(error, genericTitle, "La consulta se encuentra en proceso de asignación con otro médico.")
  }
  if (error?.response?.data?.msg?.includes("taken")) {
    return await genericSwalError(error, genericTitle, "La consulta está siendo atendida por otro medico, intente eligiendo otro paciente. ")
  }
  if (error?.response?.data?.msg?.includes("finished")) {
    return await genericSwalError(error, genericTitle, "La consulta acaba de ser finalizada por otro médico, intente eligiendo otro paciente.")
  }
  if (error?.response?.data?.msg?.includes("locking")) {
    return await genericSwalError(error, genericTitle, "La consulta se encuentra en proceso de asignación con otro médico.")
  }
  return await genericSwalError(error, genericTitle, "No se pudo iniciar esta consulta por un error desconocido. intente eligiendo otro paciente.")
};
