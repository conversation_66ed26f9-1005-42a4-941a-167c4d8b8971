import { firestore, getDocs, collection, QuerySnapshot, DocumentData, CollectionReference, query, where, Query, orderBy, limit} from '@/config/firebase'
import { countries } from '@umahealth/entities'

type CountryPath = Record<countries, string>

async function getAmountOfPendingAppointments(country : countries) : Promise<number> {
	const path : CountryPath = {
		'AR': '/assignations/online_clinica_medica/bag',
		'MX': '/assignations/bag/MX',
		'VE': '/assignations/bag/VE',
		'CO': '/assignations/bag/CO',
	}
	let appointments : QuerySnapshot<DocumentData>
	try {
		const appointmentRef : CollectionReference<DocumentData> = collection(firestore, path[country])
		const appointmentQuery : Query<DocumentData> = query(
			appointmentRef, 
			where('state', '==', 'ASSIGN'),
			orderBy('timestamps.dt_create', 'desc'),
			limit(50)
		)
		appointments = await getDocs(appointmentQuery)
	} catch (err) {
		throw new Error(`db error ${err}`)
	}
	if (appointments.empty) {
		return 0
	} else {
		return appointments.size
	}
}

export default getAmountOfPendingAppointments