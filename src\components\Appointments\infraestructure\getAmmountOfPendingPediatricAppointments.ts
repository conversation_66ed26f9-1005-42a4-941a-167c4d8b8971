import { firestore, getDocs, collection, QuerySnapshot, CollectionReference, query, where, Query, limit} from '@/config/firebase'
import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath'
import { useQuery } from 'react-query'

type countries = 'AR' | 'MX' | 'VE' | 'CO'
type CountryPath = Record<countries, string>

async function getAmountOfPendingPediatricAppointments(country : countries) : Promise<number> {
    
	const path : CountryPath = {
		'AR': '/assignations/online_clinica_medica/bag',
		'MX': '/assignations/bag/MX',
		'VE': '/assignations/bag/VE',
		'CO': '/assignations/bag/CO',
	}
	let appointments : QuerySnapshot<IAppointmentWithPath>
	try {
		const appointmentRef = collection(firestore, path[country]) as CollectionReference<IAppointmentWithPath>
		const appointmentQuery : Query<IAppointmentWithPath> = query(
			appointmentRef, 
			where('state', '==', 'ASSIGN'),
			where('patient.pediatric', '==', true),
			limit(50)
		)
		appointments = await getDocs(appointmentQuery)
	} catch (err) {
		throw new Error(`Db error in getAmountOfPediatricAppointments: ${err}`)
	}
	if (appointments.empty) {
		return 0
	}
	return appointments.size
		
}

export function useGetAmountOfPendingPediatricAppointments(country : countries = 'AR', {enabled} : { enabled : boolean }){
	return useQuery({
		queryKey: ['getPendingPediatricAppointmentsLength', country],
		queryFn: () => getAmountOfPendingPediatricAppointments(country),
		enabled: enabled
	}
	)	
}

export default getAmountOfPendingPediatricAppointments
