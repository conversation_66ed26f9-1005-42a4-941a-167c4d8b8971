import { firestore, getDocs, collection, QuerySnapshot, CollectionReference, query, where, Query, orderBy, limit} from '@/config/firebase'
import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath'
import { useQuery } from 'react-query'

type countries = 'AR' | 'MX' | 'VE' | 'CO'
type CountryPath = Record<countries, string>

async function getPendingPediatricAppointments(country : countries = 'AR') : Promise<IAppointmentWithPath[]> { 
	const path : CountryPath = {
		'AR': '/assignations/online_clinica_medica/bag',
		'MX': '/assignations/bag/MX',
		'VE': '/assignations/bag/VE',
		'CO': '/assignations/bag/CO',
	}
	let appointments : QuerySnapshot<IAppointmentWithPath>
	try {
		const appointmentRef = collection(firestore, path[country]) as CollectionReference<IAppointmentWithPath>
		const appointmentQuery : Query<IAppointmentWithPath> = query(
			appointmentRef, 
			where('state', '==', 'ASSIGN'),
			where('patient.pediatric', '==', true),
			orderBy('score', 'desc'),
			orderBy('timestamps.dt_create', 'asc'),
			limit(5)
		)
		appointments = await getDocs(appointmentQuery)
	} catch (err) {
		throw new Error(`db error in getPendingPediatricAppointments ${err}`)
	}

	if (appointments.empty) {
		return []
	}
	const appointmentDataFiltered : IAppointmentWithPath[] = []

	const appointmentsFiltered = appointments.docs

	appointmentsFiltered.forEach(appointment => appointmentDataFiltered.push({
		...appointment.data(),
		service: 'guardia',
		path: appointment.ref.path,
	}))

	return appointmentDataFiltered.slice(0, 6)
}

export function useGetPendingPediatricAppointments(country : countries, {enabled} : { enabled : boolean }){
	return useQuery({
		queryKey: ['getPendingPediatricAppointments', country],
		queryFn: () => getPendingPediatricAppointments(country),
		enabled: enabled
	}
	)	
}


export default getPendingPediatricAppointments
