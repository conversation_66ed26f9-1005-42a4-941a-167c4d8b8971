import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";
import { useAppDispatch } from "@/store/hooks";
import { CardTitle } from "@/storybook/components/UICard/Atoms/CardTitle/CardTitle";
import { IfhirR4 } from "@smile-cdr/fhirts";
import Symptoms from "./Symptoms";
import { useClient } from "@/providers/ClientProvider";
import { useCallback } from "react";
import { getPatientUid } from "../../UI/PatientCard/Actions/utils/getPatientUid";
import { openFicha } from "../../UI/PatientCard/Actions/utils/openFicha";
import HistoriaClinicaTooltip from "./utils/HistoriaClinicaTooltip";

export default function NewAppointmentActions({
  appointment,
  isHistoryView,
}: {
  appointment: IAppointmentWithPath & { status?: IfhirR4.IEncounter["status"] };
  isHistoryView?: boolean;
}) {
  const dispatch = useAppDispatch();
  const arrayMotivos = appointment?.appointment_data?.motivos_de_consulta
    ? [...new Set(appointment.appointment_data.motivos_de_consulta.split('.'))]
    : [''];
  const client = useClient()
  const handleOpenFicha = useCallback(() => {
    const uid = getPatientUid(appointment, client)
    openFicha({
      appointment,
      client,
      dispatch,
      uid
    })
  }, [client, appointment])

  return (
    <>
      <div className="flex-col px-4 grow">
        <div className="flex-col justify-start ">
          <CardTitle className='font-semibold mb-3.5 text-base text-secondary-600'>Datos de Consulta</CardTitle>
        </div>
        {/*** Síntomas *** */}
        {arrayMotivos && <Symptoms motivos={arrayMotivos} />}
      </div>
      <HistoriaClinicaTooltip
        onClick={handleOpenFicha}
        isHistoryView={isHistoryView}
      />
    </>
  );
}
