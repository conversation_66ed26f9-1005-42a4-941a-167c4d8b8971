import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";
import { CardUserName } from "@/storybook/components/UICard/Atoms/CardUserName/CardUserName";
import { CardSection } from "@/storybook/components/UICard/Organisms/CardSubSection/CardSection";
import NewAppointmentActions from "./NewAppointmentActions";
import { Loader } from "occipital-new";
import { isPediatric } from "../../UI/PatientCard/PatientCard";
import { NewPatientInfo } from "../../UI/PatientCard/UI/PatientInfo/NewPatientInfo";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Tag,
} from "@umahealth/occipital/client";
import { useAppSelector } from "@/store/hooks";
import useUser from "@/services/reactQuery/useUser";
import NewAppointmentInfo from "../../UI/PatientCard/UI/AppointmentInfo/NewAppointmentInfo";
import { isFarmatodo } from "@/config/endpoints";
import Ficha from "@/components/GeneralComponents/Ficha";
import { useClient } from "@/providers/ClientProvider";
import AttTag from "../../components/AttTag";
import StartAction from "../../UI/PatientCard/Actions/StartAction";

export default function NewPatientCard({
  appointment,
  isHistoryView,
  disabled,
  disabledReason
}: {
  appointment: IAppointmentWithPath;
  isHistoryView?: boolean;
  disabled?: boolean
  disabledReason?: string
}) {
  const { ficha } = useAppSelector((state) => state.front);
  const pediatric = isPediatric(appointment?.patient?.dob);
  const uidDependant = appointment?.patient?.uid_dependant;
  const uidPatient = appointment?.patient?.uid;
  const user = useUser(uidPatient ?? "NO", uidDependant || undefined);
  const client = useClient()

  if (user.isLoading) {
    return <Loader size={16} color="grey-1" />;
  }

  return (
    <div className="w-full">
      <Accordion
        className="hover:shadow-accordion w-full rounded-2xl bg-[#F5F7F9] px-4 py-0 mt-4 mb-5"
        type="single"
        defaultValue={`item-${appointment.assignation_id}`}
        collapsible
      >
        <AccordionItem key={appointment.assignation_id} value={`item-${appointment.assignation_id}`}>
          <AccordionTrigger className="w-96 pt-0">
            <div className="mt-6 ml-6 mr-6 flex flex-col w-full">
              <div className="flex justify-between w-full">
                <span className="flex h-5 mt-2">
                  <AttTag appointmentService={appointment?.service as string} />
                  {pediatric && (
                    <Tag className="h-5 mr-2" color="blue" text="Pediatría" />
                  )}
                  {appointment.overbooked && <Tag className="text-accent-600 bg-transparent" text="Sobreturno"/>}
                  <NewAppointmentInfo
                    appointment={appointment}
                    isHistoryView={isHistoryView}
                  />
                </span>
                {appointment?.state !== "DONE" && (
                  <StartAction
                    appointment={appointment}
                    client={client}
                    disabledReason={disabledReason}
                    uidPatient={uidPatient}
                    disabled={disabled}
                  />
                )}
              </div>
              <div className="flex w-full mt-0">
                <CardUserName
                  fullname={appointment.patient.fullname}
                  pronouns=""
                />
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <hr className="border-gray-200 mb-4" />
            <div className="ml-4">
              <CardSection>
                <NewPatientInfo appointment={appointment} />
                <NewAppointmentActions
                  appointment={appointment}
                  isHistoryView={isHistoryView}
                />
                {ficha?.state && !isFarmatodo && <Ficha assignationId={ficha.assignation_id}/>}
              </CardSection>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
