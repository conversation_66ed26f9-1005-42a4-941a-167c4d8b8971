import React, { useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, Too<PERSON><PERSON>Provider, TooltipTrigger } from "@umahealth/occipital/client";
import { Loader } from "@umahealth/occipital";
import {
  getAppointmentStartMessage,
  specialistAppointmentIsOnTime,
} from "@/components/Appointments/utils/activeStartCall";
import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath";
import swal from "sweetalert";
import { useTranslations } from "next-intl";
import { useStartGuardiaAtt } from "../../hooks/guardia/useStartGuardiaAtt";
import { useStartSpecialistAtt } from "../../hooks/specialist/useStartSpecialistAtt";
import { useStartOnsiteAtt } from "../../hooks/onsite/useStartOnsiteAtt";
import { useOpenSpecialistAppointments } from "../../hooks/specialist/useOpenSpecialistAppointment";
import axios from "axios";

export default function NewStartCallAction({
  appointment,
  disabled,
  disabledReason,
  onLoadingChange,
}: {
  appointment: IAppointmentWithPath;
  disabled?: boolean
  disabledReason?: string
  onLoadingChange?: (isLoading: boolean) => void
}) {
  const t = useTranslations();
  const isSpecialistAppointmentOnTime = specialistAppointmentIsOnTime(appointment);
  

  const isGuardia = appointment.path?.includes("bag");
  const isSpecialist = appointment.path?.includes("online");
  const isOnsite = appointment.path?.includes("consultorio");

  const startGuardiaAppointment = useStartGuardiaAtt(appointment);
  const startSpecialistAppointment = useStartSpecialistAtt(appointment);
  const startOnsiteAppointment = useStartOnsiteAtt(appointment);

  const loading = startGuardiaAppointment.isLoading || 
                  startSpecialistAppointment.isLoading || 
                  startOnsiteAppointment.isLoading

  const { isOpen: isSpecialistAppointmentOpen, appointment: openAppointment } =
    useOpenSpecialistAppointments();

    useEffect(() => {
    let timeoutId: NodeJS.Timeout

    if (startGuardiaAppointment.isLoading || 
        startSpecialistAppointment.isLoading || 
        startOnsiteAppointment.isLoading) {
      timeoutId = setTimeout(() => {
        // Este timeout es para cancelar operaciones que tarden demasiado
        // Se limpia al desmontar el componente
      }, 10000) // 10 segundos de timeout
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [startGuardiaAppointment.isLoading, startSpecialistAppointment.isLoading, startOnsiteAppointment.isLoading])

  // Efecto para notificar cambios en el estado de carga
  useEffect(() => {
    // Notificar al componente padre sobre cambios en el estado de carga
    if (onLoadingChange) {
      onLoadingChange(loading);
    }
  }, [loading, onLoadingChange])


  const startAppointmentOnClickFunction = async (
    event: React.MouseEvent<HTMLDivElement>
  ) => {
    event.stopPropagation();

    try {
      if (isGuardia) {
        const result = await startGuardiaAppointment.mutateAsync()
            
        if (!result) {
          throw new Error('La consulta ya no está disponible')
        }
        return result
      }

      if (isSpecialist) {
        if (
          isSpecialistAppointmentOpen &&
          openAppointment?.assignation_id !== appointment.assignation_id
        ) {
          return swal({
            title: "Tienes una consulta abierta",
            text: `Parece que tienes una consulta en curso con el paciente ${openAppointment?.patient?.fullname}. Finalizala para tomar una nueva.`,
            icon: "warning",
          });
        }
        const result = await startSpecialistAppointment.mutateAsync()
        if (!result) {
          throw new Error("La consulta ya no está disponible")
        }
        return result
      }

      if (isOnsite) {
        const result = await startOnsiteAppointment.mutateAsync()
        if (!result) {
          throw new Error("La consulta ya no está disponible")
        }
        return result
      }

      throw new Error("No se pudo identificar el tipo de consulta")

    } catch (error) {
      let axiosErrorMessage = '';
      
      if(axios.isAxiosError(error)) {
        axiosErrorMessage = error.response?.data?.exception?.message || 'Ocurrió un error al iniciar la consulta';
        
        if (axiosErrorMessage.includes('ALREADYTAKENAPPOINTMENTMIDDLEWARE')) {
          axiosErrorMessage = 'Esta consulta ya fue iniciada por otro profesional';
        }
      }

      const errorMessage = axiosErrorMessage ? axiosErrorMessage : error instanceof Error && error.message === "La consulta ya no está disponible"
        ? "Esta consulta ya fue iniciada por otro profesional"
        : error instanceof Error
          ? error.message
          : "Ocurrió un error al iniciar la consulta"

      return swal({
        title: "Error al iniciar la consulta",
        text: errorMessage,
        icon: "warning",
      });
    }
  };

  // Keyboard handler for accessibility
  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    // Execute click function on Enter or Space
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      startAppointmentOnClickFunction(event as unknown as React.MouseEvent<HTMLDivElement>);
    }
  };

  const isDisabled =
    (appointment?.service === "consultorio" && appointment?.state !== "PRESENT") || disabled || isSpecialistAppointmentOnTime === false
  const buttonText =
    appointment.state === "ATT"
      ? "Retomar"
      : t("consultas.appointment-start_label");

  // Retomar consulta
  if (appointment.state === "ATT") {
    if (isDisabled) {
      return (
        <TooltipProvider>
          <Tooltip >
            <TooltipTrigger asChild>
              <div
                role="button"
                aria-disabled={true}
                aria-label={buttonText}
                className="flex items-center justify-center h-12 rounded-full px-4 bg-gray-300 text-gray-500 cursor-not-allowed"
              >
                <Icon
                  color="inherit"
                  name="videocall"
                  size="size-6"
                  aria-hidden="true"
                />
                <span className="ml-2">{buttonText}</span>
              </div>
            </TooltipTrigger>
            <TooltipContent
              className="max-w-96 flex items-center justify-center p-2"
              align="start"
              side="top"
              alignOffset={12}
            >
              <span>
                {disabledReason || (!isSpecialistAppointmentOnTime && getAppointmentStartMessage(appointment.timestamps?.dt_assignation)) || "Esta consulta no está disponible actualmente"}
              </span>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return (
      <div
        onClick={startAppointmentOnClickFunction}
        onKeyDown={handleKeyDown}
        role="button"
        tabIndex={0}
        aria-disabled={loading || false}
        aria-label={buttonText}
        className={`flex items-center justify-center h-12 rounded-full px-4 bg-success-50 border border-success-600 text-success-600 hover:bg-success-700 ${loading ? 'bg-success-700' : ''} hover:text-white hover:border-success-700 cursor-pointer'}`}
      >
        {loading ? (
          <>
            <Loader size="size-5" color="stroke-grey-50" />
          </>
        ) : (
          <>
            <Icon
              color="inherit"
              name="videocall"
              size="size-6"
              aria-hidden="true"
            />
            <span className="ml-2">{buttonText}</span>
          </>
        )}
      </div>
    );
  }

  // En consultorio si el paciente no se encuentra presente no se puede iniciar la atención.
  if (
    appointment?.service === "consultorio" &&
    appointment?.state !== "PRESENT"
  ) {
    return (
      <TooltipProvider>
        <Tooltip >
          <TooltipTrigger asChild>
            <div
              role="button"
              aria-disabled="true"
              aria-label="Paciente ausente"
              tabIndex={-1}
              className="flex items-center justify-center p-2 h-12 rounded-full bg-gray-300 text-gray-500 cursor-not-allowed"
            >
              <Icon
                color="inherit"
                name="videocall"
                size="size-6"
                aria-hidden="true"
              />
              <span className="ml-2">Paciente ausente</span>
            </div>
          </TooltipTrigger>
          <TooltipContent
            className="max-w-96 flex items-center justify-center p-2"
            align="start"
            side="top"
            alignOffset={12}
          >
            <span>
              El paciente no se encuentra presente en la sala de espera
            </span>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

    if (isDisabled) {
      return (
        <TooltipProvider>
          <Tooltip >
            <TooltipTrigger asChild>
              <div
                role="button"
                aria-disabled={true}
                aria-label={buttonText}
                tabIndex={-1}
                className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-300 text-gray-500 cursor-not-allowed"
              >
                <Icon
                  color="inherit"
                  name="videocall"
                  size="size-6"
                  aria-hidden="true"
                />
                <span className="ml-2 hidden">{buttonText}</span>
              </div>
            </TooltipTrigger>
            <TooltipContent
              className="max-w-96 flex items-center justify-center p-2"
              align="start"
              side="top"
              alignOffset={12}
            >
              <span>
                {disabledReason || (!isSpecialistAppointmentOnTime && getAppointmentStartMessage(appointment.timestamps?.dt_assignation)) || "Esta consulta no está disponible actualmente"}
              </span>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    
    return (
      <div
        onClick={loading ? undefined : startAppointmentOnClickFunction}
        onKeyDown={loading ? undefined : handleKeyDown}
        role="button"
        tabIndex={loading ? -1 : 0}
        aria-disabled={loading || false}
        aria-label={loading ? "Cargando" : buttonText}
        className={`flex items-center justify-center ${loading ? 'w-auto px-4' : 'w-12'} h-12 rounded-full transition-all duration-300 ease-in-out overflow-hidden group ${loading ? 'bg-success-300 text-white cursor-wait' : 'hover:w-auto hover:px-4 hover:bg-success-600 active:bg-success-700 bg-success-500 text-white cursor-pointer'}`}
      >
        {loading ? (
          <>
            <Loader size="size-5" color="stroke-success-600" />
            <span className="ml-2">Cargando...</span>
          </>
        ) : (
          <>
            <Icon
              color="inherit"
              name="videocall"
              size="size-6"
              aria-hidden="true"
            />
            <span className="ml-2 hidden group-hover:inline">{buttonText}</span>
          </>
        )}
      </div>
    );

}