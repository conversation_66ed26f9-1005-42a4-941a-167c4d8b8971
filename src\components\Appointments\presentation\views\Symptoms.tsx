import ActionIconButton from "@/storybook/components/ActionIconButton/ActionIconButton";
import React from "react";
import { DialogContent, DialogOverlay, DialogPortal, DialogRoot, DialogTitle, DialogTrigger, Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@umahealth/occipital/client";
import { Title } from "@umahealth/occipital";
import DraggableContainer from "../../components/Draggable";
import "../../Styles/SymptomsDialog.scss"
import { parseFarmaAttReason } from "../../utils/parseFarmaAttReason";


interface SymptomsProps {
  motivos: string[]
}
export default function Symptoms({ motivos }: SymptomsProps) {


  return (
      <DialogRoot>
        <TooltipProvider>
          <Tooltip disableHoverableContent={true}>
            <DialogTrigger asChild>
              <TooltipTrigger asChild>
                <ActionIconButton
                  className='mt-2'
                  aria-label="lista de síntomas"
                  icon="listUl"
                  type="button"
                />
              </TooltipTrigger>
            </DialogTrigger>
            <DialogPortal>
              <DraggableContainer>
                <DialogOverlay className='bg-black/30' />
                <DialogContent className="bg-dialogDraggableBg/90 rounded-xl" closeButton>
                  <DialogTitle>
                    <Title
                      hierarchy="h3"
                      size="text-s"
                      weight="font-regular"
                      className="text-white mb-6"
                    >
                      Datos de la consulta | Síntomas
                    </Title>
                  </DialogTitle>
                  {
                    <div className="max-h-96 overflow-y-auto thin-scrollbar rounded-xl py-2 px-5 bg-white" >
                      <Title
                        hierarchy="h3"
                        size="text-s"
                        weight="font-bold"
                        className="text-neutral-600 font-semibold"
                      >
                        Motivos de consulta
                      </Title>
                      <ul className="mt-2 text-neutral-500 text-sm">
                        {motivos.map((causa, index) => causa !== '' ? <li key={`${index}-${causa}`}>{parseFarmaAttReason(causa)}</li> : 'No informado')}
                      </ul>
                    </div>
                  }
                </DialogContent>
              </DraggableContainer>
            </DialogPortal>
            <TooltipContent hideWhenDetached={true} className='max-w-40' side="bottom" sideOffset={4}>
              <span className='font-semibold mb-2'>Síntomas</span>
              <p className='font-normal'>Respuestas brindadas por el paciente previo a la consulta y diagnóstico asociado.</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </DialogRoot>
  )
}
