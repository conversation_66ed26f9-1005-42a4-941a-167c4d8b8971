import { ClientsNames, TClientsNames } from "@/config/clients";
import { useClient } from "@/providers/ClientProvider";
import ActionIconButton from "@/storybook/components/ActionIconButton/ActionIconButton";
import { CardTitle } from "@/storybook/components/UICard/Atoms/CardTitle/CardTitle";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@umahealth/occipital/client";
import { useTranslations } from "next-intl";

interface IHistoriaClinicaProps {
  onClick: () => void
  isHistoryView?: boolean
}

function setTooltipContent (spanContent: string, pContent: string) {
  return (
    <>
      <span className='font-semibold'>
        {spanContent}
      </span>
      <p className='font-normal'>
        {pContent}
      </p>
    </>
  )
}

function HistoriaClinicaTooltip ({ onClick, isHistoryView }: Readonly<IHistoriaClinicaProps>) {
  const t = useTranslations("consultas")
  const client = useClient()

  function getTooltipContent (client: TClientsNames) {
    const pContent = 'Anotaciones durante consulta propias o de otros profesionales.'
    let spanContent = ''

    if (client === ClientsNames.FARMATODO) {
      spanContent = 'Detalle'
      return setTooltipContent(spanContent, pContent)
    }

    spanContent = t("appointment-history_label")
    return setTooltipContent(spanContent, pContent)
  }

  const tooltip = (
    <div className="flex-col grow">
      <CardTitle className='font-semibold text-base text-secondary-600'>
        Historia Clínica
      </CardTitle>
      <div className="flex justify-start gap-6 ">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <ActionIconButton
                className='mt-2'
                aria-label="resumen"
                icon="labProfile"
                type="button"
                onClick={onClick}
              />
            </TooltipTrigger>
            <TooltipContent className='max-w-40 mb-2' side="bottom" sideOffset={4}>
              {getTooltipContent(client)}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  )

  if (client === ClientsNames.FARMATODO && !isHistoryView) {
    return null
  }

  return tooltip
}

export default HistoriaClinicaTooltip