import { Timestamp } from '@/config/firebase'
import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath'
import { format, subMinutes,isAfter, isBefore } from "date-fns";
import es from "date-fns/locale/es";

/**
 * Determina si la consulta con especialista puede iniciarse, 
 * basada en el tipo de servicio y la cercanía al horario de asignación.
 * 
 * Útil para bloquear consultas anticipadas en el módulo de especialistas.
 *
 * @param {IAppointmentWithPath} appointment - Objeto de turno con tipo de servicio y timestamps.
 * @returns {boolean} - Retorna true si la llamada puede iniciarse, sino false.
 */
export function specialistAppointmentIsOnTime(appointment: IAppointmentWithPath): boolean {
  const { service, timestamps } = appointment;

  if (service !== 'especialista_online' || !(timestamps?.dt_assignation instanceof Timestamp)) {
    return true;
  }

  const now = new Date();
  const appointmentTime = timestamps.dt_assignation.toDate();
  const specialistCallTime = Number(process.env.NEXT_PUBLIC_ATTEND_SPECIALIST_CALL_TIME ?? 5);

  const allowedStartTime = subMinutes(appointmentTime, specialistCallTime);

  // Retorna true si el tiempo actual es igual o posterior al tiempo permitido
  return isAfter(now, allowedStartTime) || now.getTime() === allowedStartTime.getTime();
}

/**
 * Genera un mensaje indicando cuándo se puede iniciar la consulta.
 *
 * @param {Timestamp} dtAssignation - Fecha y hora asignada de la consulta.
 * @returns {string | null} - Retorna el mensaje si no se puede iniciar aún, o null si ya se puede.
 */
export function getAppointmentStartMessage(dtAssignation?: Timestamp): string | null {
  if (!dtAssignation) return null;

  const now = new Date();
  const appointmentTime = dtAssignation.toDate();
  const specialistCallTime = Number(process.env.NEXT_PUBLIC_ATTEND_SPECIALIST_CALL_TIME ?? 5);
  const allowedStartTime = subMinutes(appointmentTime, specialistCallTime);

  // Si ya se puede iniciar (incluyendo después del turno), retornar null
  if (isBefore(allowedStartTime, now)) return null;

  const appointmentTimeStr = format(appointmentTime, "HH:mm", { locale: es });

  return `Sólo puede iniciar la llamada ${specialistCallTime} minutos antes de la consulta (a las ${appointmentTimeStr}).`;
}
