import { appointmentPatient, ILicense } from '@umahealth/entities'
import { Timestamp } from '@/config/firebase'
import { UseQueryResult } from '@tanstack/react-query'

const checkLicenceForIOMA = (
	licences: UseQueryResult<ILicense<Timestamp>[], Error>,
	patient: appointmentPatient,
	service?: string
): boolean | undefined => {
	if(service === 'consultorio') return true
	if (licences.isSuccess) {
		const allowedJurisdictions = ['M.N.', 'Buenos Aires', 'CABA']
		const hasAllowedLicense = licences.data.some((licence) => 
			allowedJurisdictions.includes(licence.jurisdiction as string)
		)
		const isIOMA: boolean = patient.corporate.toUpperCase() === 'IOMA' || patient.corporate.toUpperCase() === 'IOMA-APP'
		// Si es paciente de IOMA, solo permitir si tiene una licencia permitida
		if (isIOMA) {
			return hasAllowedLicense
		}

		// Para otros pacientes, permitir siempre
		return true
	}
	
	return false
}

export default checkLicenceForIOMA
