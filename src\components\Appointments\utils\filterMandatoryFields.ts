import { formatDate } from './../../User/utils/getInitialDate'
import moment from 'moment-timezone'
import { IProvider } from '@umahealth/entities'
import { TSpecialties } from '@umahealth/entities'
import {
  IMandatoryFieldsAR,
  IMandatoryFieldsMX,
  mandatoryFieldsKeys,
  IEachMandatoryField,
} from './../../../services/reactQuery/useMandatoryFields'
import { isFarmatodo } from '@/config/endpoints'
import { deserializeTimestamp } from '@/lib/utils'
import { Timestamp } from "@/config/firebase";

interface IDocument extends IEachMandatoryField {
  path: string
}
interface IMandatoryFields<T> {
  data?: T[]
}

export interface RawTimestamp {
  _seconds: number
  _nanoseconds: number
}

type ExpirationFields = 'path_dni' | 'path_insurance'

const expirationFields: readonly ExpirationFields[] = [
  'path_dni',
  'path_insurance',
]


export interface ISelect extends IEachMandatoryField {
	path: string;
	expiration: Timestamp | null;
}

/**
 * Filtra los campos obligatorios que debe completar un doctor según su cobertura, especialidad y vencimiento.
 *
 * @template T - Tipo que extiende de IDocument (ej. IMandatoryField).
 * @param {IMandatoryFields<T> | undefined} fields - Lista de campos obligatorios a evaluar.
 * @param {IProvider} doctor - Proveedor médico con datos de cobertura y especialidad.
 * @param {boolean} [skipDocumentation] - Si se activa, saltea todos los chequeos (como si ningún documento fuera obligatorio).
 * @returns {T[] | undefined} Lista de campos filtrados que el doctor debe completar, o `undefined` si no hay campos.
 */
export const filterMandatoryFields = <T extends IDocument>(
  fields: IMandatoryFields<T> | undefined,
  doctor: IProvider,
  skipDocumentation?: boolean
): T[] | undefined => {
  return fields?.data?.filter((field) => {
    // Casos en los que se omite directamente el campo
    if (isFarmatodo || skipDocumentation) return false

    // Si el profesional está exceptuado por cobertura o especialidad
    const isExceptedByCoverage = field.exceptedCoverages?.some((os) =>
      doctor?.social_work?.includes(os)
    )
    const isExceptedBySpecialty = field.exceptedSpecialties?.includes(
      doctor?.matricula_especialidad
    )

    if (isExceptedByCoverage || isExceptedBySpecialty) return false

    // Si el campo no expira, se requiere solo si no hay `path`
    if (!field.expires) {
      return field.required && !field.path
    }

    // Si el campo expira, se requiere si no hay `path` o está vencido
    return (
      (field.required && !field.path) ||
      validateExpirationDate(
        {
			expireDni: doctor.expireDni,
			expireInsurance: doctor.expireInsurance,
			matricula_especialidad: doctor.matricula_especialidad
		},
        field.type as 'dni' | 'insurance',
        field.exceptedSpecialties
      )
    )
  })
}

export const validateExpirationDate = (
  {
    expireDni,
    matricula_especialidad,
    expireInsurance,
  }: {
    expireDni?: Timestamp | null
    matricula_especialidad: TSpecialties
    expireInsurance?: Timestamp | null
  },
  type: 'dni' | 'insurance',
  exceptedSpecialties: TSpecialties[]
) => {
  const expirations = {
    dni: expireDni,
    insurance: !exceptedSpecialties.includes(matricula_especialidad)
      ? expireInsurance
      : moment().add(10, 'd').format('DD/MM/YYYY'),
  } as const
  const expirationDocument = expirations[type]
  /** Si no existe debería estar desactivado */
  if (!expirationDocument) return true
  const dateExpiration = formatDate(expirationDocument)
  if (!dateExpiration) return true
  const today = moment().toDate()
  return dateExpiration < today
}

export const selectMandatoryFields = <T extends IEachMandatoryField>(
  doc: IMandatoryFieldsAR | IMandatoryFieldsMX | undefined,
  doctor: IProvider
): T[] => {
  const docsMapped: T[] = []
  const expirationDni = doctor.expireDni
  const expirationInsurance = doctor.expireInsurance
  const expireMatricula = doctor.expireMatricula
  const expiration = {
    path_dni: expirationDni,
    path_insurance: expirationInsurance,
    path_matricula: expireMatricula,
  }
  for (const key in doc) {
    const objMapped: T = {
      ...(doc[key as mandatoryFieldsKeys] as T),
      path: doctor[key as mandatoryFieldsKeys] as string,
      expiration: expirationFields.some((field) => field === key)
        ? expiration[key as ExpirationFields]
        : null,
    }
    docsMapped.push(objMapped)
  }
  return docsMapped
}

export const selectMandatoryFieldsServer =  (
  doc: IMandatoryFieldsAR | IMandatoryFieldsMX | undefined,
  doctor: IProvider<RawTimestamp>
): ISelect[] => {
  const docsMapped: ISelect[] = []

  const expirationDni = deserializeTimestamp(doctor.expireDni)
  const expirationInsurance = deserializeTimestamp(doctor.expireInsurance)

  const expiration = {
    path_dni: expirationDni,
    path_insurance: expirationInsurance,
  }

  for (const key in doc) {
    const objMapped: ISelect = {
      ...(doc[key as mandatoryFieldsKeys] as ISelect),
      path: doctor[key as mandatoryFieldsKeys] as string,
      expiration: expirationFields.some((field) => field === key)
        ? expiration[key as ExpirationFields]
        : null,
    }
    docsMapped.push(objMapped)
  }

  return docsMapped
}

/**
 * Filtra los campos obligatorios que debe completar un doctor según su cobertura, especialidad y vencimiento.
 *
 * @template T - Tipo que extiende de IDocument (ej. IMandatoryField).
 * @param {IMandatoryFields<T> | undefined} fields - Lista de campos obligatorios a evaluar.
 * @param {IProvider} doctor - Proveedor médico con datos de cobertura y especialidad.
 * @param {boolean} [skipDocumentation] - Si se activa, saltea todos los chequeos (como si ningún documento fuera obligatorio).
 * @returns {T[] | undefined} Lista de campos filtrados que el doctor debe completar, o `undefined` si no hay campos.
 */
export const filterMandatoryFieldsSelected = (
  fields: ISelect[],
  doctor: IProvider<RawTimestamp>,
  skipDocumentation?: boolean
) => {
  return fields.filter((field) => {
    // Casos en los que se omite directamente el campo
    if (isFarmatodo || skipDocumentation) return false

    // Si el profesional está exceptuado por cobertura o especialidad
    const isExceptedByCoverage = field.exceptedCoverages?.some((os) =>
      doctor?.social_work?.includes(os)
    )
    const isExceptedBySpecialty = field.exceptedSpecialties?.includes(
      doctor?.matricula_especialidad
    )

    if (isExceptedByCoverage || isExceptedBySpecialty) return false

    // Si el campo no expira, se requiere solo si no hay `path`
    if (!field.expires) {
      return field.required && !field.path
    }

    // Si el campo expira, se requiere si no hay `path` o está vencido
    return (
      (field.required && !field.path) ||
      validateExpirationDate(
        {
			matricula_especialidad: doctor.matricula_especialidad,
			expireDni: deserializeTimestamp(doctor.expireDni),
			expireInsurance: deserializeTimestamp(doctor.expireInsurance)
		},
        field.type as 'dni' | 'insurance',
        field.exceptedSpecialties
      )
    )
  })
}
