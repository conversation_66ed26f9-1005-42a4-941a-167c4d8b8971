import { getLocalStorage as getBrowserLocalStorage, setLocalStorage as setBrowserLocalStorage } from '@/utils/validations/browserUtils'

const definedKeys = ["dontShowPediatricAppointments"] as const
type definedKeys = (typeof definedKeys)[number]
/**
 * Stores a boolean value in the browser's localStorage
 * @param {string} key the key to store the value under
 * @param {boolean | string} value the boolean or string value to store
 */
function setLocalStorageItem(key: definedKeys, value: boolean | string | number) {
  if (!definedKeys.includes(key)) {
    console.error(`Invalid key. Defined keys are: ${definedKeys.join(", ")}`)
  }
  setBrowserLocalStorage(key, JSON.stringify(value))
}


/**
 * Retrieves a boolean value from the browser's localStorage
 * @param {string} key the key to retrieve the value from
 * @returns {boolean} the stored value or false if no value is found
 */
function getLocalStorageItem(key: definedKeys): boolean | string | number | null {
  const storedValue = getBrowserLocalStorage(key)
  return storedValue ? JSON.parse(storedValue) : null
}

export { setLocalStorageItem, getLocalStorageItem }