import { getIsNewTelehealthRoomActive } from "@/modules/consultorio/application/hooks/useGetTelehealthRoomActive";
import { getProvider } from "@/serverServices/getProvider";
import { startChatRequestResponseExtended } from "@/services/reactQuery/useStartChatRequest"
import { TSpecialties } from "@umahealth/entities";

/** Te redirigue a la instancia de la videollamada */
export async function redirectToAtt({ rol, patientUid, assignationId, dependantUid, specialty, attType, attCountry, corporate }: startChatRequestResponseExtended & { rol?: string }) {
    
    const provider = await getProvider()
    const isNewTelehealthRoomActive = await getIsNewTelehealthRoomActive(provider.uid)
    
    let providerModule = isNewTelehealthRoomActive ? 'telehealth' : 'doctor';

    /** El 'as string' esta mal pero es mejor que dejar todo doctor como any sólo porque el rol no puede ser PSICOLOGIA. Hay que añadir psicologia al listado. */
    if (rol === 'PSICOLOGIA') {
        providerModule = 'psychologist'
    }

    if (specialty === 'aptofisico' as TSpecialties) {
        providerModule = 'apto-fisico/telehealth'
    }

    if (process.env.NEXT_PUBLIC_COUNTRY === 'MX') {
        return `/consultorio-virtual/mexico?patientUid=${patientUid}&assignationId=${assignationId}&dependant=${dependantUid}&specialty=${specialty}&attType=${attType}`
    }
    return (`/${providerModule}?patientUid=${patientUid}&assignationId=${assignationId}&dependant=${dependantUid}&specialty=${specialty}&attType=${attType}&attCountry=${attCountry || process.env.NEXT_PUBLIC_COUNTRY}&corporate=${corporate}`) as const
}
