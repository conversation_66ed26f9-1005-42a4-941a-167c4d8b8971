import { start_att_guard, start_att_onsite, start_att_specialist } from "@/config/endpoints"
import { useAppSelector } from "@/store/hooks"
import { IAppointment } from "@umahealth/entities"
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken"
import axios from "axios"
import moment from 'moment-timezone'
import { useTranslations } from "next-intl"


export const useStartAtt = () => {
        const t = useTranslations()
        const doctor = useAppSelector(state => state.user.profile)

        const endpointSelector = (appointmentType: 'onsite' | 'online' | 'bag' | string): string => {
            switch (appointmentType) {
                case 'onsite':
                    return start_att_onsite
                case 'online':
                    return start_att_specialist
                default:
                    return start_att_guard
            }
        }
        
        const startAtt = async (appointment: IAppointment | any) => {
            const {patient, path, assignation_id} = appointment
            const dt = moment().tz(t('time.zone')).format('YYYY-MM-DD HH:mm:ss')
            const dependant = patient.uid_dependant ? true : false
            const activeUid = patient.uid_dependant ? patient.uid_dependant : patient.uid
            const requestType = path.includes('consultorio') ? 'onsite' : 'online'
            let appointmentType = ''
            if (requestType === 'onsite') {
                appointmentType = 'onsite'
            } else {
                if (appointment.cuit === 'bag') {
                    appointmentType = 'bag'
                } else {
                    appointmentType = 'online'
                }
            }
            const token = await getFirebaseIdToken()
            const data = {
                activeUid: activeUid,
                country: process.env.NEXT_PUBLIC_COUNTRY,
                uid: patient.uid,
                appointmentPath: path,
                eventPath: `events/requests/${requestType}/${assignation_id}`,
                dependant: patient.uid_dependant,
                cuit: doctor?.core_id || doctor?.uid,
                isDependant: dependant,
                date: dt,
                timezone: t('time.zone'),
                assignationId: assignation_id,
                video: true,
                type: appointment.cuit === 'bag' ? doctor.matricula_especialidad : 'online',
                appointmentType: appointmentType,
                providerUid: doctor?.uid,
            }

            const headers = {
                'content-type': 'application/json',
                'authorization': `Bearer ${token}`
            }
            const {data: startAttResponse} = await axios.post(endpointSelector(appointmentType), data, { headers })

            if(startAttResponse?.cuit === doctor?.cuit
                || startAttResponse?.cuit === doctor?.uid
                || startAttResponse?.providerUid === doctor?.uid) {
                    return {...startAttResponse, corporate: patient?.corporate}
            } else {
                throw new Error(`No se pudo iniciar esta consulta ${JSON.stringify(startAttResponse)}`)
            }
        }
        
        return {startAtt}
    }
    
    