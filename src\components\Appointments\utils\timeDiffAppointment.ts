import { IAppointmentWithPath } from "@/store/actions/appointments/utils/IAppointmentWithPath"
import moment from "moment"

// esta funcion se encarga de validar si la hora del turno es menor o igual a 5 minutos para habilitar el boton de iniciar consulta 

export function timeDiffAppointment(appointment:IAppointmentWithPath) : boolean {
	const now = moment()
	//formateo la fecha del turno para compararla con la actual
	const timestampAssign = moment.unix(appointment?.timestamps?.dt_assignation?.seconds??0)
	// Calcular la diferencia en minutos entre las dos fechas
	const timeDiff = now.diff(timestampAssign, 'minutes')

    return -timeDiff <= 5
}