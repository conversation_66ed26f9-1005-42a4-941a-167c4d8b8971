export async function verifyCameraAndMicrophone() {
	try {
		const stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: true });
		// Si necesitas hacer algo con el stream, puedes hacerlo aquí
		stream.getTracks().forEach(track => track.stop()); // Detener el stream si no lo necesitas
	} catch (error) {
		const errorMessage = {
			msg: 'Error obteniendo los permisos de cámara y micrófono',
			title: 'Debes activar los permisos de cámara y micrófono',
			text: 'Para realizar la consulta debes activar los permisos de cámara y micrófono. Si no sabes cómo hacerlo puedes contactar a soporte o haz clic en el botón de "Ver más".',
			icon: 'warning',
			error: error,
			buttons: {
				cameraPermissions: 'Ver más',
				cancel: 'Continuar'
			},
			dangerMode: false
		};

		if ((error as Error).name === 'NotAllowedError') {
			errorMessage.msg = 'Permisos de cámara y micrófono denegados.';
		} else if ((error as Error).name === 'NotFoundError') {
			errorMessage.msg = 'No se encontró ningún dispositivo de cámara o micrófono.';
		}

		throw errorMessage;
	}
}