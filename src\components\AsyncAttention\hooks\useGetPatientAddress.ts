import { useContext } from 'react'
import { useAppSelector } from '@/store/hooks'
import { MrContext } from '..'
import { final_destination_derivation } from '@/config/finalDestinations'

export const useGetPatientAddress = () => {
	const mr = useContext(MrContext)
	const patientAddress = useAppSelector((state) => state.ficha.patientAddress)
	const address = `Dirección: ${patientAddress?.destination?.user_address || ''} - Piso:  ${patientAddress.destination.user_floor  || ''} - Departamento:  ${patientAddress.destination.user_number  || ''} - Observaciones:  ${patientAddress.destination.user_obs  || ''} - Teléfono:  ${patientAddress.destination.user_ws  || ''}`
	if(mr?.mrInView?.mr?.destino_final && final_destination_derivation.includes(mr?.mrInView?.mr?.destino_final)) return address
	return null
}
