'use client'
import React, { createContext, useState } from "react";
import { RightColumn } from "./ui/RightColumn/RightColumn";
import { CenterColumn } from "./ui/CenterColumn/CenterColumn";
import { LeftColumn } from "./ui/LeftColumn/LeftColumn";
import { Timestamp } from "@/config/firebase";
import { IChatAttAppointment, IMedicalRecord } from "@umahealth/entities";
import { EmptySelectedAtt } from "./ui/emptyColumns/EmptySelectedAtt";
import CameraAgreementModal  from "../TermsConditions/CameraAgreementModal"; 
import { isFarmatodo } from '@/config/endpoints'
import useGetProviderRatingStatus from "../Rating/hooks/useProviderRatingStatus";
import Rating from "../Rating";
import style from "./styles/asyncAttention.module.scss";

export const AttContext = createContext<
  | {
      attInView: IChatAttAppointment<Timestamp> | undefined;
      setAttInView: React.Dispatch<
        React.SetStateAction<IChatAttAppointment<Timestamp> | undefined>
      >;
    }
  | undefined
>(undefined);
export const MrContext = createContext<
  | {
      mrInView: IMedicalRecord<Timestamp> | undefined;
      setMrInView: React.Dispatch<
        React.SetStateAction<IMedicalRecord<Timestamp> | undefined>
      >;
    }
  | undefined
>(undefined);

export default function AsyncAttention() {
  const [attInView, setAttInView] = useState<
    IChatAttAppointment<Timestamp> | undefined
  >(undefined);
  const [mrInView, setMrInView] = useState<
    IMedicalRecord<Timestamp> | undefined
  >(undefined);

  const isTimeToShowRatingModal = useGetProviderRatingStatus()
  const showRatingModal = !isFarmatodo && isTimeToShowRatingModal.data

  return (
    <div className={style.asyncAttentionContainer}>
      <AttContext.Provider value={{ attInView, setAttInView }}>
        <MrContext.Provider value={{ mrInView, setMrInView }}>
          <LeftColumn />
          <CameraAgreementModal/>
          {showRatingModal && <Rating/>}
          {attInView ? (
            <>
              <CenterColumn />
              <RightColumn />
            </>
          ) : (
            <EmptySelectedAtt />
          )}
        </MrContext.Provider>
      </AttContext.Provider>
    </div>
  );
}
