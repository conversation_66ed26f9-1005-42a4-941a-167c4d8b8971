import React from 'react'
import { useAppSelector } from '@/store/hooks'
import { AttsList } from './components/AttsList'
import { NewAtt } from './components/NewAtt'
import { EmptyListAtt } from '../emptyColumns/EmptyListAtt'
import style from './leftColumn.module.scss'
import CloseSession from './components/CloseSession'
import { useOnSnapshot } from '@/hooks/useOnSnapshot'
import { where } from '@/config/firebase'
import { IChatAttAppointment } from '@umahealth/entities'

export const LeftColumn = () => {
	const { currentUser } = useAppSelector(state => state.user)
	const appointments = useOnSnapshot<IChatAttAppointment>(`assignations/chatAtt/${process.env.NEXT_PUBLIC_COUNTRY}`, [where('state', 'in', ['ATT', 'PENDING_DONE']), where('uid', '==', currentUser.uid)], !!currentUser.uid, 'LeftColumn')

	return (<div className={style.leftColumnContainer}>
		{appointments?.length === 0 ? <EmptyListAtt /> : <AttsList appointments={appointments} />}
		<NewAtt appointments={appointments}/>
		<CloseSession/>
	</div>)
}
