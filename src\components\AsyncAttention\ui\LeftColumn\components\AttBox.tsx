import React, { useContext } from "react";
//import { IChatAttAppointment } from '@umahealth/entities' TODO: agregar unreadMessagesDoctor a la interface
import { AttContext, MrContext } from "@/components/AsyncAttention";
import { usePatchMedicalRecord } from "@/services/reactQuery/usePatchMedicalRecord";
import { useAppSelector } from "@/store/hooks";
import style from "../styles/attBox.module.scss";
import { queryClient } from "@/providers/QueryClient";

export const AttBox = ({ appointment }: { appointment: any }) => {
  const { currentUser } = useAppSelector((state: any) => state.user);
  const attContext = useContext(AttContext);
  const mrContext = useContext(MrContext);
  const patchMedicalRecord = usePatchMedicalRecord(
    mrContext?.mrInView,
    currentUser.uid
  );

  const handleClickAtt = () => {
    mrContext?.mrInView && patchMedicalRecord.mutate();
    attContext?.setAttInView(appointment);
    queryClient.removeQueries("medicalRecord");
  };

  const notifyMessageBuilder = (n: number) => {
    if (n > 1) return `${n} Mensajes nuevos`;
    return `${n} Mensaje nuevo`;
  };

  return (
    <div
      className={`${style.attBoxContainer} ${
        attContext?.attInView?.assignation_id === appointment?.assignation_id &&
        style.selected
      }`}
      onClick={handleClickAtt}
    >
      {appointment?.patient?.fullname || "Nombre no especificado"}
      {appointment.unreadMessagesDoctor > 0 && (
        <div className={style.notify}>
          {notifyMessageBuilder(appointment.unreadMessagesDoctor)}
        </div>
      )}
    </div>
  );
};
