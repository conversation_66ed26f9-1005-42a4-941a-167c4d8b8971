import React, { useMemo } from "react";
import { IChatAttAppointment } from "@umahealth/entities";
import { Text, Spacer } from "occipital-new";
import { AttBox } from "./AttBox";
import style from "../styles/attList.module.scss";

export const AttsList = ({
  appointments,
}: {
  appointments: IChatAttAppointment[] | undefined;
}) => {
  const pendingAppointments = useMemo(
    () =>
      appointments?.filter(
        (appointment) => appointment.state === "PENDING_DONE"
      ),
    [appointments]
  );
  const attAppointments = useMemo(
    () => appointments?.filter((appointment) => appointment.state === "ATT"),
    [appointments]
  );

  return (
    <div className={style.attListContainer}>
      <Text tag="span" weight="regular" size="m" color="text-primary">
        En curso:
      </Text>
      <div className={style.attListPicked}>
        {attAppointments?.length
          ? attAppointments.map((appointment) => (
              <AttBox
                key={appointment.assignation_id}
                appointment={appointment}
              />
            ))
          : "No tienes consultas tomadas"}
      </div>
      <Spacer value="horizontal" />
      <Text tag="span" weight="regular" size="m" color="text-primary">
        Pendientes de cierre:
      </Text>
      <div className={style.attListPicked}>
        {pendingAppointments?.length
          ? pendingAppointments.map((appointment) => (
              <AttBox
                key={appointment.assignation_id}
                appointment={appointment}
              />
            ))
          : "No tienes consultas pendientes de cierre"}
      </div>
      <Spacer value="horizontal" />
      <Text tag="span" weight="regular" size="m" color="text-primary">
        {" "}
        En simultáneo:
      </Text>
      <div className={style.attListPicked}>
        {attAppointments?.length
          ? attAppointments?.length
          : "No tienes consultas tomadas."}
      </div>
    </div>
  );
};
