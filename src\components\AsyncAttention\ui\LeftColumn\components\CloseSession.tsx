import { But<PERSON> } from 'occipital-new'
import React from 'react'
import { useRouter } from 'next/navigation'
import { useCloseAsyncAttSession } from '@/services/reactQuery/useCloseAsyncAttSession'

//import { useCloseAsyncAtt } from '@/services/reactQuery/closeAsyncAttSession'
import { useAppSelector } from '@/store/hooks'
import swal from 'sweetalert'
import { ButtonList } from 'sweetalert/typings/modules/options/buttons'


const CloseSession = () => {
	const { currentUser } = useAppSelector(state => state.user)
	const closeSession = useCloseAsyncAttSession(currentUser.uid)
	const router = useRouter()
	const buttonList : ButtonList = { 
		cancel: {
			text: 'Cancelar',
			value: false,
			visible: true
		},
		catch: {
			text: 'Aceptar',
			value: true,
		}
	}
	const handleCloseSession = async () =>{
		const response = await swal({
			title: 'Desconectarme del chat',
			text: 'Si te desconectas del chat, todas tus consultas por chat actuales serán delegadas a otro médico.',
			icon: 'warning',
			buttons: buttonList
		})
		if(response) {
			closeSession.mutate()
			router.push('/')
		}
	}


	return (
		<div>
			<Button loading={closeSession.isLoading} occ_type='outlined' size='full' type='button' action={() => handleCloseSession()}>Desconectarme del chat</Button>
		</div>
		
	)
}

export default CloseSession