import React, { useEffect, useContext } from 'react'
import { <PERSON><PERSON>, Spacer, Paragraph } from 'occipital-new'
import { IAppointment } from '@umahealth/entities'
import { AttContext } from '@/components/AsyncAttention'
import { useAppSelector } from '@/store/hooks'
import { useNewAsyncAtt } from '@/services/reactQuery/useNewAsyncAtt'
import { useOnSnapshot } from '@/hooks/useOnSnapshot'
import { where } from '@/config/firebase'
import { getUidFromResponse } from '../utils/getUidFromResponse'
import { getChatAttAppointment } from '@/services/requests/getChatAttAppointment'

export const NewAtt = ({ appointments } : { appointments: IAppointment[] | undefined }) => {
	const asyncAtt = useContext(AttContext)
	const { currentUser, profile } = useAppSelector(state => state.user)
	const newAsyncAtt = useNewAsyncAtt(currentUser.uid)
	const isPediatric = profile?.matricula_especialidad === 'pediatria'
	const amountAsyncAppointments = useOnSnapshot('assignations/chatAtt/AR', [where('state', 'in', ['ASSIGN','PENDING']), where('pediatric', '==', isPediatric)], true, 'NewAtt')
	
	const handleNewAtt = async () =>{
		newAsyncAtt.mutate()
	}

	useEffect(() => {
		localStorage.setItem('amountAsyncAppointments', amountAsyncAppointments?.length.toString())
	}, [amountAsyncAppointments?.length])
	
	if(newAsyncAtt.isSuccess && newAsyncAtt.data){
		const uidNewAtt = getUidFromResponse(newAsyncAtt.data)
		if(uidNewAtt){
			getChatAttAppointment(uidNewAtt)
				.then(newAtt =>{
					asyncAtt?.setAttInView && asyncAtt?.setAttInView(newAtt||undefined)
				})
		}
		newAsyncAtt.reset()
	}

	return (
		<div>
			{typeof amountAsyncAppointments?.length === 'number' && <Paragraph weight='regular' size='s' color='text-primary'>{amountAsyncAppointments?.length === 0 ? 'No hay pacientes esperando' : amountAsyncAppointments?.length > 0 && `${amountAsyncAppointments?.length} pacientes esperando`} </Paragraph>}
			<Spacer value='8px' direction='vertical'/>	
			<Button disabled={amountAsyncAppointments?.length === 0 || Number(appointments?.length) >= 20} loading={newAsyncAtt.isLoading} action={handleNewAtt} occ_type='filled' size='full' type='button'>Atender otro</Button>
		</div>	
	)
}
