@use '@/styles/global/Vars.scss';

.attBoxContainer{
    border: 1px solid Vars.$color-grey-5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    gap: 12px;
    &:hover{
        cursor: pointer;
        border-left: 3px solid Vars.$uma-primary;
        background-color: Vars.$color-grey-6;
    }
}

.selected{
    border-left: 3px solid Vars.$uma-primary;
    background-color: Vars.$color-grey-6;
}

.statusAtt{
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.notify{
    background-color: #E57373;
    border-radius: 10px;
    font-size: 12px;
    color: white;
    padding: 2px 4px;
    font-weight: bold;
    white-space: nowrap;

}