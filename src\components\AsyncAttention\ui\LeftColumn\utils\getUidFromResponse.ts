import { AxiosResponse } from 'axios'

export const getUidFromResponse = (response: AxiosResponse) => {
    if(!response.data?.message) return null
    if(!/successfully started appointment/i.test(response.data.message)) return null
    const regexUid = /(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}-\d{6}_\w+)/
    const match = response.data.message?.match(regexUid)
    if(!match) return null
    return match[0] as string||null
}

