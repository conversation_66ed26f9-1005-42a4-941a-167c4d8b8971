import React, { useContext, useMemo, useState } from 'react'
import { Loader } from 'occipital-new'
import { AttContext, MrContext } from '@/components/AsyncAttention'
import { MedicalRecord } from './components/medicalRecord/MedicalRecord'
import { CloseAtt } from './components/CloseAtt'
import { StartMedicalRecord } from './components/StartMedicalRecord'
import { Prescription } from './components/prescription/Prescription'
import useMedicalRecord from '@/services/reactQuery/Patient/useMedicalRecord'
import style from './rightColumn.module.scss'
import { AttMotive } from './components/attMotive/AttMotive'
import { CloseEmptyAppointment } from './components/CloseEmptyAppointment'
import StudiesOrder from '@/components/Consultorio/Prescriptions/Studie'
import { SectionBar } from './components/SectionBar'


export const RightColumn = () => {
	const [currentView, setCurrentView] = useState()
	const [view, setView] = useState(false)
	const appointmentInView = useContext(AttContext)
	const mrInView = useContext(MrContext)
	const medicalRecordSelected = useMedicalRecord(appointmentInView?.attInView?.patient?.uid??'NO', appointmentInView?.attInView?.assignation_id??'NO', undefined, { enabled: !!appointmentInView?.attInView, refetchOnWindowFocus: false })
	useMemo(() => medicalRecordSelected.data && mrInView?.setMrInView(medicalRecordSelected.data), [medicalRecordSelected.data]) 
	const attMotive:any = appointmentInView?.attInView?.appointment_data
	if(medicalRecordSelected.isLoading) return <div className={style.rightColumnContainer}>
		<div className={style.loaderContainer}><Loader size={34} color='primary'/></div>
	</div>
	if(!medicalRecordSelected.data){
		return <div className={style.rightColumnContainerEmpty}>
			<AttMotive motive={attMotive.motivos_de_consulta || 'Sin Motivo de consulta'} />
			<div className={style.rightColumnFooter}>
				<StartMedicalRecord />
				<CloseEmptyAppointment />
			</div>
		</div>
	}
	
	return (
		<div className={style.rightColumnContainer}>
			<div className={style.contentActions}>
				<div className={style.attActions}>
					<AttMotive motive={attMotive.motivos_de_consulta|| 'Sin Motivo de consulta'} />
					<MedicalRecord medicalRecord={medicalRecordSelected} />
					<Prescription />
					<div>
						<SectionBar title='Ordenes' action={() => setView(!view)}/>
						{
							(view || currentView ) && <StudiesOrder setCurrentView={setCurrentView}/>
						}
					</div>
					
				</div>
			</div>
			<CloseAtt />
		</div>
	)
}
