import React, { useContext } from 'react'
import { AttContext, MrContext } from '@/components/AsyncAttention'
import { useCloseAsyncAtt } from '@/services/reactQuery/useCloseAsyncAtt'
import swal from 'sweetalert'
import { errorHandler } from '@/config/stackdriver'
import { validateCloseAtt } from '@/components/AsyncAttention/utils/validateCloseAtt'
import { useAttentionParameters } from '@/services/reactQuery/useAttentionParameters'
import { Button } from 'occipital-new'
import { useGetPatientAddress } from '@/components/AsyncAttention/hooks/useGetPatientAddress'
import { queryClient } from '@/providers/QueryClient'
import { EndButton } from '@/modules/consultorio/presentation/components/EndButton/EndButton'
import { useSearchParams } from 'next/navigation'

export const CloseAtt = () => {
	const mrContext = useContext(MrContext)
	const attContext = useContext(AttContext)
	const address = useGetPatientAddress()
	const closeAsyncAtt = useCloseAsyncAtt(attContext?.attInView, mrContext?.mrInView, address)
	const attention = useAttentionParameters()
	const dt_start = attContext?.attInView?.timestamps?.dt_start

	const destinoFinal = mrContext?.mrInView?.mr?.destino_final
  const router = useSearchParams()
  const attType = router.get('attType')
	const isConsultorio = ['onsite', 'consultorio'].includes(attType as string) 
	const isCloseAttWithTimer = !isConsultorio && !attention.isLoading && destinoFinal === 'Anula el paciente'

	if(closeAsyncAtt.isError){
		const timestamp = new Date().toLocaleString();
		errorHandler?.report(closeAsyncAtt.error as any)
		swal('No hemos podido cerrar la consulta, por favor intente nuevamente', `${closeAsyncAtt.error}`, `Detalle: ${timestamp}` ,'warning')
	}

	if(closeAsyncAtt.isSuccess){
		mrContext?.setMrInView(undefined)
		attContext?.setAttInView(undefined)
	}
	
	const handleCloseAtt = async () =>{
      const validations = await validateCloseAtt(mrContext?.mrInView);
      validations && closeAsyncAtt.mutate();
      queryClient.clear();
	}
	
	return isCloseAttWithTimer ? (
    <EndButton
      onClick={handleCloseAtt}
      timeout={attention}
      dtStartChatAtt={dt_start}
      timerEnabled={true}
    >
      Finalizar consulta
    </EndButton>
  ) : (
    <Button
      loading={closeAsyncAtt.isLoading}
      action={handleCloseAtt}
      type="button"
      occ_type="filled"
      size="full"
    >
      Finalizar consulta
    </Button>
  );
}
