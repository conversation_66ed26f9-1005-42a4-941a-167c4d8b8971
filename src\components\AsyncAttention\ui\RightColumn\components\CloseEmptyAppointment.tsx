import React, { useContext } from 'react'
import { errorHandler } from '@/config/stackdriver'
import { Button } from 'occipital-new'
import { AttContext } from '@/components/AsyncAttention'
import { useCloseAsyncEmptyAtt } from '@/services/reactQuery/useCloseAsyncEmptyAtt'
import { useAppSelector } from '@/store/hooks'
import swal from 'sweetalert'
import { useAttentionParameters } from '@/services/reactQuery/useAttentionParameters'
import { useTranslations } from 'next-intl'
import { EndButton } from '@/modules/consultorio/presentation/components/EndButton/EndButton'


export const CloseEmptyAppointment = () => {
	const { currentUser } = useAppSelector(state => state.user)
	const attContext = useContext(AttContext)
	const closeAsyncEmptyAtt = useCloseAsyncEmptyAtt(currentUser.uid, attContext?.attInView)
	const attention = useAttentionParameters()
	const dt_start = attContext?.attInView?.timestamps?.dt_start
	const t = useTranslations('attention')

	if(closeAsyncEmptyAtt.isError){
		const timestamp = new Date().toLocaleString();
		errorHandler?.report(closeAsyncEmptyAtt.error as any)
		swal('No hemos podido cerrar la consulta, por favor intente nuevamente', `${closeAsyncEmptyAtt.error}`, `Detalle: ${timestamp}`, 'warning')
	}

	if(closeAsyncEmptyAtt.isSuccess){
		attContext?.setAttInView(undefined)
	}

	return !attention.isLoading ? (
    <EndButton
      onClick={() => closeAsyncEmptyAtt.mutate()}
      timeout={attention}
      dtStartChatAtt={dt_start}
      timerEnabled={true}
    >
      Finalizar consulta
    </EndButton>
  ) : (
    <Button
      loading={closeAsyncEmptyAtt.isLoading || attention.isLoading}
      action={() => closeAsyncEmptyAtt.mutate()}
      type="button"
      occ_type="filled"
      size="full"
    >
      {t("closeattention-btn") as string}
    </Button>
  );
}
