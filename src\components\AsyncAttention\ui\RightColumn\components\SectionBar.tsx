import React, { useState } from 'react'
import { MdExpandMore, MdExpandLess } from 'react-icons/md'
import style from '../styles/sectionBar.module.scss'

interface IProps{
	title: string,
	action: (...args : any[]) => any
}

export const SectionBar = ({ title, action }: IProps) => {
	const [ view, setView ] = useState(false)

	const handleAction = () => {
		action()
		setView(!view)
	}

	return (
		<div onClick={handleAction} className={style.sectionBarContainer}>
			<div>{title}</div>
			{view ? <MdExpandLess /> : <MdExpandMore/> }
		</div>
	)
}
