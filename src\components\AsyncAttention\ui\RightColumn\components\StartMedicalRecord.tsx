import React, { useContext } from 'react'
import { Button } from 'occipital-new'
import { useCreateAsyncMr } from '@/services/reactQuery/useCreateAsyncMr'
import { useAppSelector } from '@/store/hooks'
import { AttContext } from '@/components/AsyncAttention'
import { queryClient } from "@/providers/QueryClient";

export const StartMedicalRecord = () => {
	const { currentUser } = useAppSelector(state => state.user)
	const asyncAtt = useContext(AttContext)
	const createAsyncMr = useCreateAsyncMr(currentUser.uid, asyncAtt?.attInView?.assignation_id??'', asyncAtt?.attInView?.patient.uid??'', asyncAtt?.attInView?.patient.uid_dependant??'')

	const handleCreateAsyncMr = () =>{
		createAsyncMr.mutate()
	}

	if(createAsyncMr.isSuccess){
		queryClient.refetchQueries('medicalRecord')
	}

	return (
		<Button action={handleCreateAsyncMr} loading={createAsyncMr.isLoading} occ_type='outlined' type='button' size='full'>Comenzar registro medico</Button>
	)
}
