import { Paragraph, Column, Spacer } from 'occipital-new'
import React from 'react'
import { SectionBar } from '../SectionBar'

interface IProps{
	motive: string
}

export const AttMotive = ({ motive }:  IProps) => {
	const [view, setView] = React.useState(false)
	function parseJsonMotive(motiveQuestions : string) {
		try {
			const questionsAndAnswers = JSON.parse(motiveQuestions)
			return Object.keys(questionsAndAnswers).map((key) => [key, questionsAndAnswers[key]])
		} catch (e) {
			console.error(e)
			return []
		}
		return []
	}

	return (
		<div>
			<SectionBar title='Motivo de consulta' action={() => setView(!view)} />
			{view && <>
				<Spacer value='4px' direction='vertical'/>
				<Paragraph color='primary' size='xs' weight='bold'>Motivo referido por el paciente</Paragraph>
				<Paragraph color='grey-1' size='xxs' weight='regular'>{motive.split('Interrogatorio: ')[0]}</Paragraph>
				<Paragraph color='primary' size='xs' weight='bold'>Interrogatorio</Paragraph>
				{parseJsonMotive(motive.split('Interrogatorio: ')[1])?.map((question, index) =>{
					return <Column key={index}>
						<Paragraph color='primary' size='xxs' weight='regular'>{question[0]}</Paragraph>
						<Paragraph color='grey-2' size='xxs' weight='regular'>{question[1]}</Paragraph>
						<Spacer value='4px' direction='vertical'/>
					</Column>
				})}
				<Spacer value='4px' direction='vertical'/>
			</>}
		</div>
	)
}
