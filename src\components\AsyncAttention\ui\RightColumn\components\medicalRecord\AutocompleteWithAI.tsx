
import React from 'react'
import axios from 'axios'
import {  UseFormWatch, UseFormSetValue } from 'react-hook-form'
import { IFormData } from './MedicalRecord'
import { Button, Column, Paragraph } from 'occipital-new'
import { useAppSelector } from '@/store/hooks/useAppSelector'
import { medical_qa } from '@/config/endpoints'

interface IProps{
	motive: string,
	setValue: UseFormSetValue<IFormData>
	watch: UseFormWatch<IFormData>
}

export default function AutocompleteWithAI({ motive, setValue, watch } : IProps) {
	const [loading, setLoading] = React.useState(false)
	const [warning, setWarning] = React.useState(false)
	const { dataChat } = useAppSelector(state => state.call)

	const autocompleteWithAI = React.useCallback(async () => {
		try {
			setLoading(true)
			setWarning(false)
	
			let motivePlusChat = motive
			dataChat?.forEach((message: any) => {
				motivePlusChat += `${message?.rol}: ${message?.msg}`
			})
			const requestMedicalQa = {
				text: `Transcripción: ${motivePlusChat}`,
				step: 'second'
			}
			const aiDiagnosisOutput = await axios.post(medical_qa, requestMedicalQa)
	
			let epicrisis = ''
			if(aiDiagnosisOutput.data?.output['nota_medica_pase_guardia']) {
				epicrisis += aiDiagnosisOutput.data.output['nota_medica_pase_guardia']
			}
			if(aiDiagnosisOutput.data?.output['pautas_de_alarma']) {
				epicrisis += ' Pautas de alarma para el paciente: '
				aiDiagnosisOutput.data.output['pautas_de_alarma']?.forEach((alarm: string, index: number) => {
					epicrisis += `${alarm}`
					if(index !== aiDiagnosisOutput.data.output['pautas_de_alarma']?.length - 1) epicrisis += ', '
				})
			}
			if(epicrisis === '') {
				setWarning(true)
			}
			setValue('epicrisis', epicrisis)
			watch('epicrisis')
			setLoading(false)
		} catch (err) {
			setWarning(true)
			setLoading(false)
		}
	}, [dataChat, motive, setValue])

	return (
		<Column alignment='center' spacing='center'>
			{warning && <Paragraph color='state-error' weight='regular' size='xxs'>No se ha podido autocompletar la epicrisis. La información es insuficiente o el modelode AI no pudo procesarla</Paragraph>}
			<Button 
				action={() => autocompleteWithAI()} 
				type="button"
				size='extended'
				loading={loading}
				occ_type='text'>Autocompletar epicrisis</Button>
		</Column>
	)
}