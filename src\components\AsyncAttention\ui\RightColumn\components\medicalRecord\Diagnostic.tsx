// import { Combobox, ComboboxList, ComboboxOption } from 'occipital-new'
import React, { useContext, useMemo } from 'react'
import { UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form'
import { Combobox } from '@/components/Shadcn/Combobox'
import { IFormData } from './MedicalRecord'
import { useDiagnosticOptions } from '@/services/reactQuery/useDiagnosticOptions'
import { useDebounce } from '@/hooks/useDebounce'
import { MrContext } from '@/components/AsyncAttention'

interface IProps{
	watch: UseFormWatch<IFormData>,
	register: UseFormRegister<IFormData>,
	setValue: UseFormSetValue<IFormData>,
}

export const Diagnostic = ({ watch, setValue }: IProps) => {
	const asyncMr = useContext(MrContext)
	const debouncedValue = useDebounce<string>(watch('diagnostic'))
	const diagnosticOptions = useDiagnosticOptions()

	useMemo(() => debouncedValue && diagnosticOptions.mutate(debouncedValue) ,[debouncedValue])
	
	return (
		<>
			{/* <Combobox 
				inputmode='text'
				type='text'
				label='Diagnóstico'
				register={register('diagnostic')}
				hasValue={!!watch('diagnostic')}
				loading={diagnosticOptions.isLoading}
			>
				<ComboboxList>
					{diagnosticOptions.data?.length ? diagnosticOptions.data.map((op : {value: string, label: string}) => <ComboboxOption 
						onClick={() => {
							setValue('diagnostic', op.value)
							asyncMr?.mrInView && asyncMr?.setMrInView({...asyncMr.mrInView, mr: { ...asyncMr.mrInView.mr, diagnostico: op.value}})
						}}
						key={op.value}
						value={op.value}>
						{op.label}
					</ComboboxOption>) : 
						debouncedValue && <ComboboxOption 
							onClick={() => {
								setValue('diagnostic', debouncedValue.toUpperCase())
								asyncMr?.mrInView && asyncMr?.setMrInView({...asyncMr.mrInView, mr: { ...asyncMr.mrInView.mr, diagnostico: debouncedValue.toUpperCase()}})
							}}
							key={debouncedValue.toUpperCase()}
							value={debouncedValue.toUpperCase()}>
							{debouncedValue.toUpperCase()}
						</ComboboxOption>
					}
				</ComboboxList>
			</Combobox> */}
			{<Combobox 
				options={diagnosticOptions.data?.length ? diagnosticOptions.data : []} 
				onChange={op => {
					setValue('diagnostic', op)
					asyncMr?.mrInView && asyncMr?.setMrInView({...asyncMr.mrInView, mr: { ...asyncMr?.mrInView?.mr, diagnostico: op}})
				}}
				label='Diagnóstico'
				placeholder='Ingrese un diagnóstico'
				emptyPlaceHolder= { debouncedValue?.length < 6 ? 'escribe más para que podamos buscar un diagnóstico' : 'No hemos encontrado ningun diagnóstico'}
				isLoading={diagnosticOptions.isLoading}
				shouldFilter={false}
			/>}
		</>
	)
}
