import { Select } from '@/components/Shadcn/Select'
import React from 'react'
import { Control, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form'
import { useActiveServicesFromCorporate } from '@/services/reactQuery/useCoverageServices'
import { getFinalDestinations } from '@/utils/arrayUtils'
import { IFormData } from './MedicalRecord'
import { final_destination_derivation } from '@/config/finalDestinations'
import useSpecialties from '@/services/reactQuery/useSpecialties'
import DomicilioPatient from '@/components/Consultorio/AttFile/components/DomicilioPatient'
import { finalDestinations } from '@umahealth/entities'

interface IProps{
	corporate?: string,
	watch: UseFormWatch<IFormData>,
	control: Control<IFormData>,
	register: UseFormRegister<IFormData>,
	setValue: UseFormSetValue<IFormData>
}

export const FinalDestination = ({ corporate, watch, setValue }: IProps) => {
	const coverageServices = useActiveServicesFromCorporate(corporate)
	const specialistiesArray = useSpecialties().data?.specialties_list
	const finalDestinations = getFinalDestinations({
		attType: 'chatAtt',
		coverageServices: coverageServices.data,
		corporate
	})

	return (<>
		<Select 
			defaultValue={watch('finalDestination')??undefined}
			options={finalDestinations}
			onChangeFn={finalDestination => setValue('finalDestination', finalDestination as finalDestinations)}
			placeHolder='Destino final'
		/>
			{final_destination_derivation.includes(watch('finalDestination')??'') && <DomicilioPatient />}	
		{watch('finalDestination') === 'Indico seguimiento con especialista' && 
		<Select options={specialistiesArray??[]} placeHolder='Especialidad' onChangeFn={specialistReferral => setValue('specialist_referral', specialistReferral)} />}
	
	</>
	)
}
