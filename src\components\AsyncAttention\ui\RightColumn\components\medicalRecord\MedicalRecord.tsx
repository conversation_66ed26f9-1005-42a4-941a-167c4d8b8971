import React, { useContext, useMemo, useState } from 'react'
import { finalDestinations, IMedicalRecord } from '@umahealth/entities'
import { FirestoreError, Timestamp } from '@/config/firebase'
import { useForm } from 'react-hook-form'
import { <PERSON><PERSON>nos<PERSON> } from './Diagnostic'
import { TextAreaFields } from './TextAreaFields'
import { FinalDestination } from './FinalDestination'
import { AttContext, MrContext } from '@/components/AsyncAttention'
import { Paragraph } from 'occipital-new'
import ValidateService from '@/utils/validations/ValidateServices'
import { Repose } from './Repose'
import { SectionBar } from '../SectionBar'
import { updateAsyncMr } from '@/components/AsyncAttention/utils/updateAsyncMr'
import AutocompleteWithAI from './AutocompleteWithAI'
import style from '../../styles/medicalRecord.module.scss'
import { UseQueryResult } from 'react-query'
import swal from 'sweetalert'
import { Disclaimer } from '@/components/GeneralComponents/Disclaimer/Disclaimer'

export interface IFormData {
	tratamiento: string,
	epicrisis: string,
	diagnostic: string,
	finalDestination: finalDestinations,
	motivo_de_consulta: string,
	specialist_referral?: string,
	repose?: string,
	reposeStart?: Date,
	reposeEnd?: Date,
}

export const MedicalRecord = ({ medicalRecord }: { medicalRecord: UseQueryResult<IMedicalRecord<Timestamp>, FirestoreError> }) => {
	const asyncAtt = useContext(AttContext)
	const asyncMr = useContext(MrContext)
	const [view, setView] = useState(false)
	const { watch, setValue, register, control } = useForm<IFormData>({
		defaultValues: {
			diagnostic: medicalRecord?.data?.mr?.diagnostico || '',
			finalDestination: medicalRecord?.data?.mr?.destino_final as finalDestinations,
			epicrisis: medicalRecord?.data?.mr?.epicrisis || '',
			tratamiento: medicalRecord?.data?.mr?.tratamiento || ''
		}
	})
	const finalDestination = watch('finalDestination')
	const repose = watch('repose')
	const reposeStart = watch('reposeStart')
	const reposeEnd = watch('reposeEnd')
	const specialistReferral = watch('specialist_referral')
	const epicrisis = watch('epicrisis')
	useMemo(() => {
		if (
			finalDestination
			|| repose
			|| reposeStart
			|| reposeEnd
			|| specialistReferral
			|| epicrisis
		) {
			updateAsyncMr({
				asyncMr,
				finalDestination: finalDestination,
				repose: repose,
				reposeStart: reposeStart,
				reposeEnd: reposeEnd,
				specialist_referral: specialistReferral,
				epicrisis: epicrisis
			})
		}
	}, [
		finalDestination,
		repose,
		reposeStart,
		reposeEnd,
		specialistReferral,
		epicrisis,
	])

	if (medicalRecord.isError) {
		const timestamp = new Date().toLocaleString();
		swal('No hemos podido obtener el registro medico', 'Por favor, intente nuevamente', `Detalle: ${timestamp}`, 'warning')
			.then(() => {
				medicalRecord.remove()
				return
			})
	}

	return (
		<div className={style.medicalRecordContainer}>
			<SectionBar title='Ficha medica' action={() => setView(!view)} />
			{view && <div className={style.medicalRecordContent}>
				<AutocompleteWithAI
					watch={watch}
					setValue={setValue}
					motive={asyncAtt?.attInView?.appointment_data?.motivos_de_consulta || 'Ninguno'} />
				<TextAreaFields watch={watch} setValue={setValue} />
				<Diagnostic watch={watch} register={register} setValue={setValue} />
				<FinalDestination setValue={setValue} register={register} corporate={asyncAtt?.attInView?.patient?.corporate} watch={watch} control={control} />
				<ValidateService
					corporate={asyncAtt?.attInView?.patient?.corporate || 'SIN OBRA SOCIAL (UMA)'}
					fieldToValidate='rest'
					defaultValue={true}
					defaultComponent={<Paragraph weight='regular' size='m' color='state-error'>Esta obra social no cuenta con constancias de reposo habilitadas</Paragraph>}>
					<Repose setValue={setValue} register={register} watch={watch} control={control} />
				</ValidateService>
				<Disclaimer title='Importante' text='Por favor, completa los campos de la forma más detallada posible ya que una vez finalizada la consulta tu paciente pierde acceso al historial de chat.' />
			</div>}
		</div>
	)

}
