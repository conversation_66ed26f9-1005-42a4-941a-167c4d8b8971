import React from 'react'
// import { Select } from 'occipital-new'
import { Select } from '@/components/Shadcn/Select'
import { reposeOptionsDefault, reposeOptionsOccupationalAr, reposeOptionsOccupationalMx } from '@/components/AsyncAttention/utils/reposeOptions'
import { useAppSelector } from '@/store/hooks'
import { Control, UseFormRegister, UseFormWatch, UseFormSetValue } from 'react-hook-form'
import { IFormData } from './MedicalRecord'
import { ReposeDates } from './ReposeDates'
import { attType } from '@/services/reactQuery/useCurrentAppointment'
import { useSearchParams } from 'next/navigation'

interface IProps{
	control: Control<IFormData>,
	watch: UseFormWatch<IFormData>,
	register: UseFormRegister<IFormData>,
	setValue: UseFormSetValue<IFormData>
}

export const Repose = ({ watch, register, setValue }: IProps) => {
	const { profile } = useAppSelector((state) => state.user)
	const searchParams = useSearchParams();
	const attTypeParams = searchParams.get('attType') as attType
	const isOccupationalDoctor = ['medicinalaboral', 'psiquiatria'].includes(profile?.matricula_especialidad) && attTypeParams !== 'bag'

	const optionsReposeOccupational = {
		AR: reposeOptionsOccupationalAr,
		MX: reposeOptionsOccupationalMx
	}

	if(process.env.NEXT_PUBLIC_COUNTRY === 'MX' &&  profile.matricula_especialidad !== 'medicinalaboral') {
		return <></>
	}

	return (<>
		<Select 
			placeHolder='Reposo' 
			options={isOccupationalDoctor 
					? optionsReposeOccupational[process.env.NEXT_PUBLIC_COUNTRY as 'AR' | 'MX'] 
					: reposeOptionsDefault} 
			onChangeFn={repose => setValue('repose', repose)}/>
		{/* <Select label='Reposo' name='repose' options={isOccupationalDoctor ? optionsReposeOccupational[process.env.NEXT_PUBLIC_COUNTRY as 'AR' | 'MX'] : reposeOptionsDefault} control={control} hasValue={!!watch('repose')}/> */}
		{watch('repose') === 'justificado' && 
			<div>
				<ReposeDates watch={watch} register={register}/>
			</div>}
	</>
	)
}
