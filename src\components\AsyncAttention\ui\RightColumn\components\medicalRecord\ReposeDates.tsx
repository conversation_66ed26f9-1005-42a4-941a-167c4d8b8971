import { Input, Text } from 'occipital-new'
import React from 'react'
import { UseFormRegister, UseFormWatch } from 'react-hook-form'
import { IFormData } from './MedicalRecord'
import moment from 'moment'

interface IProps{
	watch: UseFormWatch<IFormData>,
	register: UseFormRegister<IFormData>,
}

export const ReposeDates = ({ watch, register }: IProps) => {
	return (<div>
		{(watch('reposeStart') && watch('reposeEnd')) && <Text tag='p' weight='regular' size='m' color='state-sucess'>Tiempo de reposo {moment.duration(moment(watch('reposeStart'))?.diff(moment(watch('reposeEnd'))?.add(1, 'day')?.format('YYYY-MM-DD')))?.humanize()}
		</Text>}
		<Input size='small' type='date' label='Desde' inputmode='text' hasValue={!!watch('reposeStart')} register={register('reposeStart')}/>
		<Input size='small' type='date' label='Hasta' inputmode='text' hasValue={!!watch('reposeEnd')} register={register('reposeEnd')}/>
	</div>
	)
}
