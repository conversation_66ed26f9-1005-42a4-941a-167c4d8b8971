import React, { useContext } from 'react'
import { useTranslations } from 'next-intl'
import { UseFormSetValue, UseFormWatch } from 'react-hook-form'
import { TextArea } from '@/components/GeneralComponents/Input/Inputs'
import { IFormData } from './MedicalRecord'
import { MrContext } from '@/components/AsyncAttention'
import { Text } from 'occipital-new'

interface IProps{
	watch: UseFormWatch<IFormData>,
	setValue: UseFormSetValue<IFormData>,
}

export const TextAreaFields = ({ watch, setValue }: IProps) => {
	const t = useTranslations('attention')
	const mrContext = useContext(MrContext)
	const handleUpdateProvider = (fieldUpdated: 'tratamiento' | 'epicrisis' | 'motivo_de_consulta', value: string) =>{
		setValue(fieldUpdated, value)
		if(!mrContext?.mrInView) return null
		const mr = mrContext.mrInView
		mrContext.setMrInView({ ...mr,  mr: { ...mr?.mr, [fieldUpdated] : value }})
	}

	return (
		<>
			<Text tag='label' weight='regular' size='s' color='text-primary'>{t('textarea-motivo_de_consulta')}:</Text>
			<TextArea
				id='motivo'
				placeholder={t('textarea-motivo_de_consulta')}
				setTextareaValue={(value: string) => handleUpdateProvider('motivo_de_consulta' , value)}
				textareaValue={watch('motivo_de_consulta')}
				style={{width: '100%'}}
			/>
			<Text tag='label' weight='regular' size='s' color='text-primary'>{t('textarea-epicrisis_label')}</Text>
			<TextArea
				id='epicrisis'
				placeholder={t('textarea-epicrisis_label')}
				setTextareaValue={(value: string) => handleUpdateProvider('epicrisis' , value)}
				textareaValue={watch('epicrisis')}
				style={{width: '100%'}}
			/>
			<Text tag='label' weight='regular' size='s' color='text-primary'>{t('textarea-treatment_label')}</Text>
			<TextArea
				id='treatment'
				placeholder={t('textarea-treatment_label')}
				setTextareaValue={(value: string) => handleUpdateProvider('tratamiento' , value)}
				textareaValue={watch('tratamiento')}
				style={{width: '100%'}}
			/>
		</>
	)
}
