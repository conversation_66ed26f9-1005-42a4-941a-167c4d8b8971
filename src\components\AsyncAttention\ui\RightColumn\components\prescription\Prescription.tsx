import RecipeMX from '@/components/Consultorio/Prescriptions/Recipe/RecipeMX'
import RecipeAR from '@/components/Consultorio/Prescriptions/Recipe/RecipeAR'
import React, { useContext, useState } from 'react'
import { SectionBar } from '../SectionBar'
import { Paragraph } from 'occipital-new'
import style from '../../styles/prescription.module.scss'
import { MrContext } from '@/components/AsyncAttention'

export type TCurrentView = 'prescriptionList' | 'newRecipe' | 'newStudie' | null

export const Prescription = () => {
	const [currentView, setCurrentView] = useState<TCurrentView>(null)
	const mrContext = useContext(MrContext)
	const prescriptionsLength = mrContext?.mrInView?.mr?.prescriptions?.length
	const router = () => {
		switch (process.env.NEXT_PUBLIC_COUNTRY) {
			case 'MX':
				return <RecipeMX goTo={setCurrentView} />
			case 'AR':
				return <RecipeAR goTo={setCurrentView} />
			default:
				return <Paragraph weight='regular' size='m' color='grey-1'>No se pudo determinar el país de la receta</Paragraph>
		}
	}
    
	return (
		<div className={currentView ? style.prescriptionContainer : ''}>
			<SectionBar title={(prescriptionsLength && prescriptionsLength > 0) ? `Recetas: (${prescriptionsLength})` : 'Recetas'} action={() => setCurrentView(currentView ? null : 'newRecipe')}/>
			<div>
				{currentView ? router() : null}
			</div>
		</div>
	)
}
