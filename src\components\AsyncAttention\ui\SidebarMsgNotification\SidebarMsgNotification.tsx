import React from 'react'
import { where } from '@/config/firebase'
import { IChatAttAppointment } from '@umahealth/entities'
import { useAppSelector } from '@/store/hooks'
import { useOnSnapshot } from '@/hooks/useOnSnapshot'
import SidebarNotificationDot from '@/components/AsyncAttention/ui/SidebarNotificationDot'
import useQuickPrescriptionsAtt from '@/services/reactQuery/quickPrescriptions/useQuickPrescriptions'
import usePrescriptionSpecialtiesPermission from '@/components/QuickPrescriptions/utils/usePrescriptionSpecialtiesPermission'
import { BadgeNotification } from './BadgeNotification'

/**
 * Muestra una notificación de mensajes nuevos en la barra lateral.
 *
 *   - BadgeNotification con la cantidad de mensajes nuevos si hay mensajes sin leer.
 *   - SidebarNotificationDot si no hay mensajes nuevos.
 **/

export const SidebarMsgNotification = () => {
    const { currentUser } = useAppSelector(state => state.user)
    const chatAppointments = useOnSnapshot<IChatAttAppointment>(
        `assignations/chatAtt/${process.env.NEXT_PUBLIC_COUNTRY}`,
        [where('state', '==', 'ATT'), where('uid', '==', currentUser.uid)],
        !!currentUser.uid,
        'SidebarMsgNotification'
    )
    const unreadMessages = chatAppointments.reduce(
        (prev, acc) => prev + Number(acc.unreadMessagesDoctor ?? 0),
        0
    )

    const newMessageText = unreadMessages === 1 ? 'Mensaje nuevo' : `${unreadMessages} mensajes nuevos`

    if ( unreadMessages || unreadMessages !== 0) {
        return (
            <BadgeNotification>
                {newMessageText}
            </BadgeNotification>
        )
    }

    // No necesitas else porque si entra al if ya retorna y se va
    return <SidebarNotificationDot/>
}

/**
 * Muestra una notificación de recetas pendientes en la barra lateral.
 *
 *   - BadgeNotification con la cantidad de recetas pendientes si hay recetas y la página tiene permisos.
 *   - null si no se cumplen las condiciones.
 **/

export const SidebarPrescriptionMsgNotification = () => {
    const prescriptionsPendings = useQuickPrescriptionsAtt()
    const isSpecialtiePermissionPage = usePrescriptionSpecialtiesPermission()
    const hasPrescriptions = prescriptionsPendings?.length || 0

    if (!isSpecialtiePermissionPage || !hasPrescriptions) {
        return null
    }

    const newPendingPrescription = hasPrescriptions === 1
    ? 'Receta nueva'
    : `${hasPrescriptions} recetas nuevas`

    return (
        <BadgeNotification>
            {newPendingPrescription}
        </BadgeNotification>
    )
}
