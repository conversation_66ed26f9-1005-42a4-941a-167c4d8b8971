import React, { useEffect, useState } from 'react'
import style from './SideBarNotificationDot.module.scss'
import { where } from '@/config/firebase'
import { useOnSnapshot } from '@/hooks/useOnSnapshot'
import { useAppSelector } from '@/store/hooks'

const SidebarNotificationDot = () => {
	const { profile } = useAppSelector(state => state.user)
	const isPediatric = profile?.matricula_especialidad === 'pediatria'
	const amountAsyncAppointments = useOnSnapshot('assignations/chatAtt/AR', [where('state', 'in', ['ASSIGN','PENDING']), where('pediatric', '==', isPediatric)], true, 'SidebarNotificationDot')
	const lastVisitAsynAppointments = Number(localStorage.getItem('amountAsyncAppointments'))
	const [showDot, setShowDot] = useState<boolean>(false)

	useEffect(() => {
		if(amountAsyncAppointments?.length > lastVisitAsynAppointments){
			setShowDot(true)
		}else{
			setShowDot(false)
		}
	}, [amountAsyncAppointments, lastVisitAsynAppointments])

	return <>
		{showDot ? <div className={style.notificationDot}/> : <></>}
	</>
}

export default SidebarNotificationDot