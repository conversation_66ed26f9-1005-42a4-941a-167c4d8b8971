import { finalDestinations } from '@umahealth/entities'


export const generateRestString = (rest: string | undefined, restEnd: Date | undefined, restStart: Date | undefined, finalDestination: finalDestinations) => {
	const notEffectiveDestinations = ['Paciente ausente', 'Anula el paciente', 'Anula por falla de conexión']
	if(!rest || notEffectiveDestinations.includes(finalDestination??'')) return ''
	if(rest === 'justificado') return `${rest} ${restEnd ? `//${restStart}//${restEnd} ` : ''}`
	return rest
}


