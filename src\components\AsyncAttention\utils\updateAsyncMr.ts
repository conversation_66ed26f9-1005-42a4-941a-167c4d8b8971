import { generateRestString } from './generateRestString'
import { IMedicalRecord, finalDestinations } from '@umahealth/entities'
import { Timestamp } from '@/config/firebase'

interface IupdateAsyncMrProps {
	asyncMr: {
		mrInView: IMedicalRecord<Timestamp> | undefined;
		setMrInView: React.Dispatch<React.SetStateAction<IMedicalRecord<Timestamp> | undefined>>;
	} | undefined, 
	epicrisis: string
	finalDestination: finalDestinations, 
	repose: string | undefined, 
	reposeStart: Date | undefined, 
	reposeEnd: Date | undefined,
	specialist_referral: string | undefined, 
}

export const updateAsyncMr = ({ asyncMr, finalDestination, repose, reposeStart, reposeEnd, specialist_referral, epicrisis } : IupdateAsyncMrProps) => {
	if(!asyncMr?.mrInView || !asyncMr?.setMrInView) return
	const reposeString = generateRestString(repose, reposeEnd, reposeStart, finalDestination)
	asyncMr.setMrInView({
		...asyncMr.mrInView,
		mr: {
			...asyncMr.mrInView.mr,
			destino_final: finalDestination,
			epicrisis,
			reposo: reposeString,
			specialist_referral: specialist_referral??''
		}
	})
}
