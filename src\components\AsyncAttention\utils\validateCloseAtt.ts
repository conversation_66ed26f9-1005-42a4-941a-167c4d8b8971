import { null_finalDestinations } from '@/config/finalDestinations'
import { IMedicalRecord } from '@umahealth/entities'
import { Timestamp } from '@/config/firebase'
import { final_destination_derivation } from '@/config/finalDestinations'
import swal from 'sweetalert'
import { ambulanceAddressValidations } from '@/components/Consultorio/AttFile/store/fichaActions'
import { errorHandler } from '@/config/stackdriver'

export const validateCloseAtt = async (mrInView: IMedicalRecord<Timestamp> | undefined) => {
	try{

    if (!mrInView) {
      await swal(
        "No encontramos registro medico mrInView",
        "por favor, vuelva a intentarlo",
        "warning"
      );
    }

		if(!mrInView?.mr){
			await swal('No encontramos registro medico', 'por favor, vuelva a intentarlo', 'warning')
			return false
		}
		if (!null_finalDestinations?.includes(mrInView?.mr?.destino_final??'') && !mrInView?.mr?.diagnostico) {
			await swal('Error al cerrar la consulta', 'Si el paciente no está ausente, debe cerrar la atención con algún diagnóstico.', 'warning')
			return false
		} else if (
      !null_finalDestinations?.includes(mrInView?.mr?.destino_final ?? "") &&
      !mrInView?.mr?.epicrisis
    ) {
      await swal(
        "Error al cerrar la consulta",
        "Si el paciente no está ausente, debes redactar la evolución del mismo.",
        "warning",
      );
      return false;
    } else if (
      null_finalDestinations?.includes(mrInView?.mr?.destino_final ?? "") &&
      mrInView?.mr?.diagnostico
    ) {
      await swal(
        "Error al cerrar la consulta",
        "Si el paciente está ausente, no debe cerrar la atención con algún diagnóstico.",
        "warning",
      );
      return false;
    } else if (
      mrInView?.mr?.destino_final === "Indico seguimiento con especialista" &&
      mrInView?.mr?.specialist_referral === ""
    ) {
      await swal(
        "Error al cerrar la consulta",
        "El campo Derivación a especialista no puede estar vacío.",
        "warning",
      );
      return false;
    } else if (!mrInView?.mr?.destino_final) {
      await swal(
        "Error al cerrar la consulta",
        "El campo de destino final no puede estar vacío",
        "warning",
      );
      return false;
    } else if (
      final_destination_derivation.includes(mrInView?.mr?.destino_final)
    ) {
      const validationsAddres = await ambulanceAddressValidations;
      if (!validationsAddres) return false;
    }
		return true
	}catch(error: any){
		const timestamp = new Date().toLocaleString();
		await errorHandler?.report(`VALIDATION CHATATT: ${error}`)
		await swal('Error al cerrar la consulta', 'Por favor, intente nuevamente. Si el error persiste comuníquese con <NAME_EMAIL>', `Detalle: ${timestamp}`)
		return false
	}
}