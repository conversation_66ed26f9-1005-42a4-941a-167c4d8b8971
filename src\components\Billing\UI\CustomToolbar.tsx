import React from 'react'
import { useAppSelector } from '@/store/hooks'
import mom from 'moment'
import { Title, Paragraph, Button } from 'occipital-new'
import styles from '../styles/Billing.module.scss'

export const CustomToolbar = ({ month, year }: { month: string, year: string}) => {

	const isAR = process.env.NEXT_PUBLIC_COUNTRY === 'AR'

	const { totalIncomes, totalIncomesOnline, totalIncomesGuardia, totalIncomesChatAtt } = useAppSelector(state => state.liquidacion)

	const preSettlement = `Preliquidación de ${mom(`${month}/${year}`, 'MM/YYYY').locale('es').format('MMMM [de] YYYY')} ${totalIncomes && totalIncomes > 0 ? ` -Total de ingresos: $${totalIncomes}` : '- Sin ingresos'}`
	const storageLink = 'https://storage.googleapis.com/uma-development-ar_public-files/pricing_system/simulador_docs.xlsx'

	return (
		<div className={styles.billing__title}>
			<div>
				<Title weight='bold' size='l' color='primary' hierarchy="h2">{preSettlement}</Title>
				{totalIncomesGuardia !== 0 && <Paragraph color='primary' size='m' weight='regular'>Total guardia: ${totalIncomesGuardia}</Paragraph>}
				{totalIncomesOnline !== 0 && <Paragraph color='primary' size='m' weight='regular'>Total especialista: ${totalIncomesOnline}</Paragraph>}
				{totalIncomesChatAtt !== 0 && <Paragraph color='primary' size='m' weight='regular'>Total chat: ${totalIncomesChatAtt}</Paragraph>}
				<small><strong>¿Notás discrepancias?</strong><br/>Esto puede deberse a consultas durante Franja Turbo. </small>
				<small>Si el problema persiste luego de 3 días hábiles, contacta al equipo de soporte.</small>
			</div>
			<div className={styles.billing__actions}>
				<div className={styles.billing__cbu}>
				</div>
				<div className={styles.billing__bill}>
					<Button type='button' size='small' occ_type='outlined'>
					{isAR &&
						<a href={storageLink} target="_blank" rel="noopener noreferrer">Descargar Simulador</a>
					}
					</Button>
					<Button type='button' size='small' occ_type='outlined'>
						<a href="https://emergencias.nosconecta.com/v/login?ref=%2F">
							Subir factura
						</a>
					</Button>
				</div>
			</div>
		</div>)
}
