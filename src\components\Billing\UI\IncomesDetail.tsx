import React, { useState, useEffect } from 'react';
import { useAppSelector } from '@/store/hooks';
import { IActivity } from '@/store/reducers/liquidacionReducers';
import { TurboInformation } from '../components/TurboInformation';
import { TableTitle } from '../components/TableTitle';
import { TableHead } from '../components/TableHead';
import { TableBody } from '../components/TableBody';
import { Loader } from '@umahealth/occipital';
import { Text } from 'occipital-new'; 
import moment from 'moment';
import Image from 'next/image';
import CalendarImage from '@/assets/Schedule-rafiki.png';
import { deepEqual } from '@/lib/utils';



const IncomesDetail = () => {
  const { dayActivity, monthActivityWithTurbo } = useAppSelector(state => state.liquidacion);
  const [activities, setActivities] = useState<IActivity[]>([]);
  const [dayTurbo, setDayTurbo] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false); 

  const turboStartDate = moment('2024-05-01', 'YYYY-MM-DD');
  const momentDate = moment(dayActivity[0]?.day);
  const formattedDate = dayActivity && dayActivity?.length > 0 ? momentDate?.format('DD/MM/YYYY') : '';

  useEffect(() => {
    setLoading(true); 
    if (dayActivity?.length > 0) {
      const orderedActivities = Array.isArray(dayActivity) ? dayActivity.sort((a, b) => (a.time > b.time ? 1 : -1)) : [dayActivity];
      setActivities(orderedActivities);
      setLoading(false); 
    }
  }, [dayActivity]);

  useEffect(() => {
    if (dayActivity?.length > 0) {
      const day = dayActivity[0].day;
      const isAfterStartDate = moment(day).isSameOrAfter(turboStartDate);
      if (isAfterStartDate) {
        const dayActivityWithTurbo = monthActivityWithTurbo.filter(activity => activity.day === day);
        const dayTurbos = dayActivityWithTurbo.filter(activity => activity.isTurboPrice);
        const hasTurbo = dayTurbos?.length > 0;
        if (dayTurbo !== hasTurbo) {
          setDayTurbo(hasTurbo);
        }

        if (hasTurbo) {
          const activitiesWithTurbo = dayActivity.map(activity => ({
            ...activity,
            isTurbo: dayTurbos.some(turbo => turbo.hour === activity.time.slice(0, 2))
          }));

          if (!deepEqual(activities, activitiesWithTurbo)) {
            setActivities(activitiesWithTurbo);
          }
        }
      }
    }
  }, [dayActivity, monthActivityWithTurbo, turboStartDate, dayTurbo, activities]);

  const printTable = async () => {
    const table = document.getElementById('incomesDetailTable') as HTMLTableElement;
    if (table) {
      let textContent = '';
      for (let i = 0; i < table.rows?.length; i++) {
        const row = table.rows[i];
        const rowContent = [];
        for (let j = 0; j < row.cells?.length; j++) {
          rowContent.push(row.cells[j].innerText);
        }
        textContent += rowContent.join('\t') + '\n';
      }
      const blob = new Blob([textContent], { type: 'text/plain' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = 'liquidaciones.txt';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <>
      <TableTitle printTable={printTable} formattedDate={formattedDate} loading={loading} />
      {loading ? ( 
        <div className='flex justify-center items-center h-full'>
          <Loader height={40} width={40} />
        </div>
      ) : activities?.length >= 1 ? (
        <>
          {dayTurbo && <TurboInformation date={momentDate?.toDate()} />}
          <table id="incomesDetailTable" className='container'>
            <TableHead />
            <TableBody activities={activities} />
          </table>
        </>
      ) : (
        <div className='flex flex-col justify-center items-center h-full p-6'>
          <Text weight='regular' size='m' color='grey-1' tag='p'>No se ha encontrado actividad para el día seleccionado.</Text>
          <Text weight='regular' size='m' color='grey-1' tag='p'>Si considerás que esto puede ser un error, comunícate con Soporte.</Text>
          <Image src={CalendarImage} alt='Calendario' height={200} width={200} />
        </div>
      )}
    </>
  );
};

export default IncomesDetail;
