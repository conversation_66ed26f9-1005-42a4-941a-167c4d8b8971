import React from 'react';
import { But<PERSON> } from 'occipital-new';
import styles from '../styles/Billing.module.scss';

export function ActionButtons() {
  const isAR = process.env.NEXT_PUBLIC_COUNTRY === 'AR';
  const storageLink = 'https://storage.googleapis.com/uma-development-ar_public-files/pricing_system/simulador_docs.xlsx';

  return (
    <div className={styles.billing__actions}>
      <div className={styles.billing__cbu}></div>
      <div className={styles.billing__bill}>
        <Button type='button' size='small' occ_type='filled'>
          {isAR && (
            <a href={storageLink} target="_blank" rel="noopener noreferrer">
              Descargar Simulador
            </a>
          )}
        </Button>
          <Button type='button' size='small' occ_type='filled'>
            <a href="https://emergencias.nosconecta.com/v/login?ref=%2F">
              Subir factura
            </a>
          </Button>
      </div>
    </div>
  );
}