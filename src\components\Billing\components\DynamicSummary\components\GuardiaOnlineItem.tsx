import { Text } from '@umahealth/occipital'
import { SummaryItem } from '../types'

interface GuardiaOnlineItemProps {
  item: SummaryItem
}

export const GuardiaOnlineItem = ({
  item,
}: GuardiaOnlineItemProps) => (
  <>
    <div className="flex justify-between items-center">
      <div className="flex items-center">
        <span className={`w-2 h-2 mr-2 ${item.color}`} />
        <Text tag="span" className="ml-2 text-[#455A64]">
          {item.name} {typeof item.count === 'number' && `(${item.count})`}
        </Text>
      </div>
      <Text tag="span" className="text-[#455A64]">
        ${(item.amount / 1.5).toLocaleString()}
      </Text>
    </div>
    <div className="flex justify-between items-center mt-2 ml-2">
      <Text tag="span" className="ml-4 text-success italic">
        Bonus 50 primeras consultas
      </Text>
      <Text tag="span" className="text-success">
        +${((item.amount / 1.5) * 0.5).toLocaleString()}
      </Text>
    </div>
    <div className="flex justify-between items-center mt-2 ml-2">
      <Text tag="span" className="ml-4 text-[#455A64] font-semibold">
        Subtotal:
      </Text>
      <Text tag="span" className="text-[#455A64] font-semibold">
        ${item.amount.toLocaleString()}
      </Text>
    </div>
  </>
)
