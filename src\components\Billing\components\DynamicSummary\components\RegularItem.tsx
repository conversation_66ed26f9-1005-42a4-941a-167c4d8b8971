import { Text } from '@umahealth/occipital';
import { SummaryItem } from '../types';

interface RegularItemProps {
  item: SummaryItem
}

export const RegularItem = ({ item }: RegularItemProps) => (
  <div className="flex justify-between items-center">
    <div className="flex items-center">
      <span className={`w-2 h-2 mr-2 ${item.color}`} />
      <Text tag="span" className="ml-2 text-[#455A64]">
        {item.name} {typeof item.count === 'number' && `(${item.count})`}
      </Text>
    </div>
    <Text tag="span" className="text-[#455A64]">
      ${item.amount.toLocaleString()}
    </Text>
  </div>
);