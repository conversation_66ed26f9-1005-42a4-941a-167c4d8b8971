import { Title } from '@umahealth/occipital'

interface SummaryHeaderProps {
  title: string
  date: string
}

export const SummaryHeader = ({ title, date }: SummaryHeaderProps) => (
  <>
    <div className="flex justify-between items-center mb-4">
      <Title
        hierarchy="h2"
        className="flex normal-case text-s text-primary font-semibold"
      >
        {title}
      </Title>
    </div>
    <div className="w-full max-w-md mx-auto text-center text-gray-600 text-base pb-2 font-semibold border-b border-solid border-gray-300">
      {date}
    </div>
  </>
)
