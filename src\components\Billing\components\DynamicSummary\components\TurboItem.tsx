import React from 'react'
import { SummaryItem } from '../types'
import { Icon } from '@umahealth/occipital/client'

interface TurboItemProps {
  item: SummaryItem
}

const TurboItem = ({ item }: TurboItemProps) => {
  return (
    <div
      className="flex justify-between items-center"
    >
      <div className="flex items-center">
        <span className={`w-2 h-2 mr-2 ${item.color}`} />
        <span className="ml-2 text-[#455A64]">
          {item.name} {typeof item.count === 'number' && `(${item.count})`}
        </span>
      </div>
      <span
        className="flex gap-2 items-center text-emerald-500 font-medium"
      >
        <Icon name="turbo" className="w-4 h-4" />
        + ${item.amount.toLocaleString()}
      </span>
    </div>
  ) 
}

export default TurboItem
