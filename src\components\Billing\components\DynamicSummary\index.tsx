import { useAppSelector } from '@/store/hooks';
import { DynamicSummaryProps, SummaryItem } from './types';
import { SummaryHeader } from './components/SummaryHeader';
import { GuardiaOnlineItem } from './components/GuardiaOnlineItem';
import { RegularItem } from './components/RegularItem';
import TurboItem from './components/TurboItem';

const DynamicSummary = ({ 
  currentDate, 
  selectedDate, 
  isDailyView, 
  isEligibleForBonus
}: DynamicSummaryProps) => {
  const { 
    totalIncomesOnline, 
    totalIncomesGuardia, 
    totalIncomesChatAtt, 
    totalConsultations, 
    totalDayIncomesOnline, 
    totalDayIncomesGuardia, 
    totalDayIncomesChatAtt, 
    totalDayConsultations,
    monthActivityWithTurbo,
    dayActivityWithTurbo
  } = useAppSelector(state => state.liquidacion);

  // Función para crear el array de datos del resumen
  const createSummaryData = (isDaily: boolean): SummaryItem[] => {
    // Calcular datos de Turbo
    const turboActivities = isDaily ? dayActivityWithTurbo : monthActivityWithTurbo;
    const turboData = turboActivities.filter(activity => activity.isTurboPrice);
    const turboCount = turboData.length;
    const turboAmount = turboData.reduce((sum, activity) => sum + (activity.value || 0), 0);

    const summaryItems: SummaryItem[] = [
      {
        name: 'Chat en Línea',
        count: isDaily ? totalDayConsultations.chatAtt : totalConsultations.chatAtt,
        amount: isDaily ? totalDayIncomesChatAtt : totalIncomesChatAtt,
        color: 'bg-yellow-500'
      },
      {
        name: 'Especialista',
        count: isDaily ? totalDayConsultations.specialist : totalConsultations.specialist,
        amount: isDaily ? totalDayIncomesOnline : totalIncomesOnline,
        color: 'bg-indigo-600'
      },
      {
        name: 'Guardia Online',
        count: isDaily ? totalDayConsultations.guardia : totalConsultations.guardia,
        amount: isDaily ? totalDayIncomesGuardia : totalIncomesGuardia,
        color: 'bg-emerald-500'
      }
    ];

    // Agregar Franja Turbo solo si hay datos
    if (turboCount > 0 && turboAmount > 0) {
      summaryItems.push({
        name: 'Franja Turbo',
        count: turboCount,
        amount: turboAmount,
        color: 'bg-purple-500'
      });
    }

    return summaryItems;
  };

  // Formateo de fecha
  const dateToFormat = isDailyView ? (selectedDate || currentDate) : currentDate;
  const dateFormatOptions: Intl.DateTimeFormatOptions = isDailyView
    ? { day: 'numeric', month: 'long', year: 'numeric' }
    : { month: 'long', year: 'numeric' };
  const formattedDate = new Intl.DateTimeFormat('es-ES', dateFormatOptions).format(dateToFormat);
  const capitalizedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1).toLowerCase();
  const resumeTitle = isDailyView ? 'Resumen Diario Liquidaciones' : 'Resumen Mensual Liquidaciones';

  const summaryData = createSummaryData(isDailyView);
  const filteredSummaryData = summaryData.filter(item => item.amount > 0);
  const totalIncome = filteredSummaryData.reduce((sum, item) => sum + item.amount, 0);

  return (
    <div className="flex flex-col w-[417px] bg-[#FEFEFF] p-6 m-4 rounded-2xl shadow-[4px_4px_8px_0px_rgba(0,0,0,0.1)]">
      <SummaryHeader title={resumeTitle} date={capitalizedDate} />
      
      <div className="space-y-3 mb-6">
        {filteredSummaryData.length > 0 && (
          <p className="font-medium px-4 text-[#455A64] py-3">Subtotal Ingresos</p>
        )}
        
        {filteredSummaryData.length > 0 ? (
          filteredSummaryData.map((item, index) => (
            <div key={index} className="flex flex-col p-3 rounded-md border border-slate-200 border-solid">
              {item.name === 'Guardia Online' && isEligibleForBonus ? (
                <GuardiaOnlineItem item={item} />
              ) : (
                item.name === 'Franja Turbo' ? (
                  <TurboItem item={item} />
                ) : (
                  <RegularItem item={item} />
                )
              )}
            </div>
          ))
        ) : (
          <div className="text-center text-[#455A64]/80 items-center justify-center mt-8">
            {isDailyView ? "Este día" : "Este mes"} no cuenta con actividad registrada
          </div>
        )}
      </div>
      
      <div>
        <div className="border-b border-gray-300 w-full h-px bg-gray-300 mb-2"/>
        <div className='items-center flex justify-between py-4'>
          <span className="text-[#455A64] font-semibold text-[24px] leading-[26.4px] mb-[5px]">
            Total Ingresos
          </span>
          <span className="text-[#455A64] font-semibold text-[24px] leading-[26.4px] mb-[5px]">
            ${totalIncome.toLocaleString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default DynamicSummary; 