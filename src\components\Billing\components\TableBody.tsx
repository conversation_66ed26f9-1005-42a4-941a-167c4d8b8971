import Image from 'next/image';
import { Tooltip } from '../components/Tooltip';
import Turbo from '@/assets/turbo.svg';
import { useState } from 'react';

type ActivityType = "MI_ESPECIALISTA" | "GUARDIA_RANDOM" | "CHAT" | "QUICKPRESCRIPTION";

interface Activity {
  event: string;
  dni: string;
  name: string;
  time: string;
  isTurbo: boolean;
  type: ActivityType | string;
}

interface TableBodyProps {
  activities: Activity[]
}

const typeMapping: Record<ActivityType, string> = {
  MI_ESPECIALISTA: "Especialista",
  GUARDIA_RANDOM: "Guardia",
  CHAT: "Chat",
  QUICKPRESCRIPTION: "Receta Rápida"
};

const getReadableType = (type: ActivityType | string): string => {
  return (type in typeMapping) ? typeMapping[type as ActivityType] : type;
};

export const TableBody = ({ activities }: TableBodyProps) => {
  const [tooltipIndex, setTooltipIndex] = useState<number | null>(null);

  return (
    <tbody>
      {activities.map((activity, index) => (
        activity.event !== 'in' && activity.event !== 'out' && (
          <tr key={index}>
            <td className='px-3 py-3 border-b bg-white text-sm'>
              <div className='flex items-center'>
                {activity.isTurbo && (
                  <>
                    <div onMouseEnter={() => setTooltipIndex(index)} onMouseLeave={() => setTooltipIndex(null)}>
                      <Image src={Turbo} alt='Consulta Turbo' width={18} height={18} />
                    </div>
                    <Tooltip index={index} content='Esta consulta pertenece a Franja Turbo.' tooltipIndex={tooltipIndex} />
                  </>
                )}
                <div className='ml-2'>
                  <p className='text-gray-900'>{activity.name}</p>
                  <p>DNI: {activity.dni}</p>
                </div>
              </div>
            </td>
            <td className='px-5 py-5 border-b bg-white text-sm'>
              <p className='text-gray-900 whitespace-no-wrap'>
                {activity.event}
              </p>
            </td>
            <td className='px-5 py-5 border-b bg-white text-sm'>
              <p className='text-gray-900 whitespace-no-wrap'>
                {activity.time.split(':')[0]}:{activity.time.split(':')[1]} hs
              </p>
            </td>
            <td className='px-5 py-5 border-b bg-white text-sm'>
              <p className='text-gray-900 whitespace-no-wrap'>
                {getReadableType(activity.type)}
              </p>
            </td>
          </tr>
        )
      ))}
    </tbody>
  );
};