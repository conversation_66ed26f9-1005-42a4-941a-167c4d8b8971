import { Icon } from "@umahealth/occipital/client";

export const TableTitle = ({
  formattedDate,
  printTable,
  loading
}: {
  formattedDate: string;
  printTable: () => void;
  loading: boolean;
}) => {
  const titleText = loading ? 'Cargando' : `Resumen diario ${formattedDate}`
  return (
      <div className="flex justify-center mb-4">
        <h1 className="font-bold text-l">{titleText}</h1>
        {!loading && <Icon className="mt-2 ml-6 cursor-pointer" name="download" onClick={printTable} />}
      </div>
  );
};
