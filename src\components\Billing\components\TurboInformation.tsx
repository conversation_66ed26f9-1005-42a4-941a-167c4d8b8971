import { useProviderTurboIncomesByDate } from "@/services/reactQuery/useProviderTurboIncomesByDate"
import { Icon } from "occipital-new"
import { Loader } from "@umahealth/occipital"
import { useRouter } from "next/navigation"
import { errorHandler } from "@/config/stackdriver"
import swal from "sweetalert"

export const TurboInformation = ({ date }: { date: Date }) => {
  
  const router = useRouter()
  const incomes = useProviderTurboIncomesByDate(date)

  if (incomes.isLoading) return <Loader />

  if (incomes.isError) {
    swal(
      "Error",
      "Ocurrió un error cargando la información de Franjas Turbo. Por favor comuníquese con Soporte.",
      "warning",
    )
    errorHandler?.report(`Error obteniendo franjas turbo: ${incomes.error}`)
    router.push("/liquidacion")
  }

  const filteredTodayIncomes = incomes.data?.filter((income) => {
    return (
      !!income.timestamps.dt_period_finish
    )
  })

  if (!filteredTodayIncomes?.length) {
    return null
  }

  const turboTimes = filteredTodayIncomes.map((income) => {
    const start = income.timestamps.dt_period_start
      .toDate()
      .toLocaleTimeString()
    const end = income.timestamps.dt_period_finish
      .toDate()
      .toLocaleTimeString()
    return `desde las ${start} hasta las ${end} hs`
  })

  const message = `Este día tuvo Franja Turbo ${turboTimes.join(", ")}. Puede demorar hasta 3 días en impactar la tarifa diferenciada.`

  return (
    <div className="flex items-center space-x-3 bg-indigo-100 p-2 rounded-md my-2">
      <Icon name="info" size="s" color="primary" />
      <p>{message}</p>
    </div>
  )
}
