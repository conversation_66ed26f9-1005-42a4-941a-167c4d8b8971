import { useAppSelector } from "@/store/hooks";
import {
  SpecialDay,
} from "@/storybook/components/Calendar/CalendarUtils/CustomDayComponent";
import { DayPrice } from "@/storybook/components/Calendar/CalendarUtils/DayPrice";
import { TurboIcon } from "@/storybook/components/Calendar/CalendarUtils/TurboIcon";

export interface IBilling {
  allDay: boolean;
  end: Date;
  start: Date;
  title: string;
  dt: string;
}

export const useFormatBillingForCalendar = (billing: IBilling[]): SpecialDay[] => {
  const { monthActivityWithTurbo } = useAppSelector((state) => state.liquidacion);

  const monthlyBillingWithTurbo = monthActivityWithTurbo
    .filter((activity) => activity.isTurboPrice)
    .map((activityWithTurbo) => ({
      day: parseInt(activityWithTurbo.day.slice(8), 10),
      fullDate: activityWithTurbo.day, 
  }));

  const pricesByDay = billing.reduce<Record<string, number>>((acc, item) => {
    const day = item.dt.split('-')[2];
    const price = Number(item.title.replace('$', ''));
    acc[day] = (acc[day] || 0) + price;
    return acc;
  }, {});

  const formattedPricesBilling: SpecialDay[] = Object.entries(pricesByDay).map(
    ([day, price]) => ({
      daysOfTheMonth: [parseInt(day, 10)],
      position: 'bottom',
      render: <DayPrice price={price} />,
    })
  );

  const formattedTurboDaysBilling: SpecialDay[] = monthlyBillingWithTurbo.map(({ day, fullDate }) => ({
    daysOfTheMonth: [day],
    position: 'right',
    render: <TurboIcon date={fullDate} />,
  }));

  return [...formattedPricesBilling, ...formattedTurboDaysBilling];
};