import { useState } from "react";
import { startOfMonth, endOfMonth, isBefore, isAfter } from 'date-fns';
import { useFormatBillingForCalendar } from "./useBillingFormatter";
import { SpecialDay } from "@/storybook/components/Calendar/CalendarUtils/CustomDayComponent";
import { useMonthProviderIncomes } from "./useMonthProviderIncomes";
import { useDayProviderIncomes } from "./useDayProviderIncomes";
import { useAppDispatch } from "@/store/hooks";
interface UseCalendarReturn {
  selectedDate: Date | undefined;
  setSelectedDate: React.Dispatch<React.SetStateAction<Date>>;
  currentDate: Date;
  setCurrentDate: React.Dispatch<React.SetStateAction<Date>>;
  hoveredDate: Date | null;
  handleDayMouseEnter: (day: Date) => void;
  handleDayMouseLeave: () => void;
  handleDayClick: (day: Date) => void;
  monthlyBilling: SpecialDay[];
  isOutsideDay: (day: Date) => boolean;
  isDailyView: boolean;
  setIsDailyView: React.Dispatch<React.SetStateAction<boolean>>;
  handleCloseModal: () => void;
  openModal: boolean;
}

export interface IBilling {
	allDay: boolean;
	end: Date;
	start: Date;
	title: string;
	dt: string;
}

export function useCalendar(): UseCalendarReturn {

  const initialDate = new Date();
  const dispatch = useAppDispatch();
  const [selectedDate, setSelectedDate] = useState<Date>(initialDate);
  const [currentDate, setCurrentDate] = useState<Date>(initialDate);
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [isDailyView, setIsDailyView] = useState<boolean>(false)
  const [billing, setBilling] = useState<IBilling[]>([
    {
      allDay: true,
      end: new Date(),
      start: new Date(),
      title: 'Hoy',
      dt: ''
    }
  ]);
  const [,setDayBilling] = useState<IBilling[]>([
    {
      allDay: true,
      end: new Date(),
      start: new Date(),
      title: 'Hoy',
      dt: ''
    }
  ]);
  
  const monthlyBilling = useFormatBillingForCalendar(billing);
  useFormatBillingForCalendar(billing);
  
  useMonthProviderIncomes({
    year: currentDate.getFullYear().toString(),
    month: (currentDate.getMonth() + 1).toString(),
    setActualMonth: setCurrentDate,
    setBilling
  });
    
  useDayProviderIncomes({
      selectedDate,
      setSelectedDate,
      setDayBilling
  });
  
  const handleDayClick = (day: Date) => {
    if (isOutsideDay(day)) return;
    setSelectedDate(day);
    setIsDailyView(true);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    dispatch({ type: 'RESET_MODALS', payload: false});
  };
  
  const handleDayMouseEnter = (day: Date) => {
    setHoveredDate(day);
  };
  
  const handleDayMouseLeave = () => {
    setHoveredDate(null);
  };
  
  const isOutsideDay = (day: Date): boolean => {
    const firstDayOfMonth = startOfMonth(currentDate);
    const lastDayOfMonth = endOfMonth(currentDate);
    return isBefore(day, firstDayOfMonth) || isAfter(day, lastDayOfMonth);
  };
  
  return {
    selectedDate,
    setSelectedDate,
    currentDate,
    setCurrentDate,
    hoveredDate,
    handleDayMouseEnter,
    handleDayMouseLeave,
    handleDayClick,
    monthlyBilling,
    isOutsideDay,
    isDailyView,
    setIsDailyView,
    handleCloseModal,
    openModal,
  };
}