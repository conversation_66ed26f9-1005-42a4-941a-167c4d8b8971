import { AvailablePermissionsContext } from "@/components/User/AvailablePermissionsProvider"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import moment from "moment"
import { useTranslations } from "next-intl"
import { useContext, useEffect } from "react"
import { useSpecialistService } from "./useSpecialistService"
import { useGuardiaService } from "./useGuardiaService"
import { getTotalIncomes, getTotalIncomesNoCalendar } from "../utils/incomesGenericsUtils"

export interface IBilling {
	allDay: boolean;
	end: Date;
	start: Date;
	title: string;
	dt: string;
}
interface IProps {
  setSelectedDate: React.Dispatch<React.SetStateAction<Date>>,
  selectedDate: Date,
  setDayBilling: React.Dispatch<React.SetStateAction<IBilling[]>>
}

export const useDayProviderIncomes = ({ 
  setSelectedDate, 
  selectedDate, 
  setDayBilling 
}: IProps) => {
  const t = useTranslations('time')
  const doctor = useAppSelector(state => state.user.profile)
  const dispatch = useAppDispatch()
  const availablePermissions = useContext(AvailablePermissionsContext)
  
  const enableSpecialistAndChat = moment(selectedDate).isAfter(
    moment(new Date('2023/08/31'))
  )

  // 1. Usar los hooks especializados
  const specialist = useSpecialistService(
    doctor,
    selectedDate,
    !!availablePermissions?.online,
    enableSpecialistAndChat
  )

  const guardia = useGuardiaService(
    doctor,
    selectedDate,
    !!availablePermissions?.guardia
  )

  // 2. useEffect más limpio y mantenible
  useEffect(() => {
    setSelectedDate(selectedDate)
    
    // Combinar los resultados de cada servicio
    const totalRequestsFormatted = [
      ...specialist.formattedRequests,
      ...guardia.formattedRequests,
    ]

    // Dispatch de la actividad del día
    dispatch({ 
      type: 'SET_DAY_ACTIVITY', 
      payload: totalRequestsFormatted 
    })

    // Calcular ingresos totales
    const totalDayIncomes = getTotalIncomes(
      totalRequestsFormatted, 
      setDayBilling, 
      t, 
      guardia.incomes
    )
    
    // Objeto con totales de consultas
    const totalDayConsultations = {
      specialist: specialist.totalConsultations,
      guardia: guardia.totalConsultations,
    }

    // Dispatch final con todos los datos
    dispatch({
      type: 'SET_ALL_DAY_INCOMES',
      payload: {
        totalDayIncomes,
        totalDayIncomesGuardia: getTotalIncomesNoCalendar(
          guardia.formattedRequests, 
          guardia.incomes, 
          true, 
          dispatch
        ),
        totalDayIncomesOnline: getTotalIncomesNoCalendar(
          specialist.formattedRequests, 
          guardia.incomes
        ),
        totalDayConsultations
      }
    })
  }, [
    selectedDate, 
    specialist.isSuccess, 
    guardia.isSuccess
  ])

  return null
}