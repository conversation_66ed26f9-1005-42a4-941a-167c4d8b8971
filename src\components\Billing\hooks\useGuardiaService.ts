import { useProviderIncomes } from "@/services/reactQuery/useProviderIncomes"
import { getDateParts } from "../utils/getDateParts"
import { useProviderDayRequests } from "@/services/reactQuery/useProviderDayRequests"
import { formatRequests } from "../utils/incomesUtils"
import { null_finalDestinations } from "@/utils/arrayUtils"

export const useGuardiaService = (
    doctor: any,
    date: Date,
    hasPermission: boolean
  ) => {
    const { year, month, day } = getDateParts(date)
    
    const incomes = useProviderIncomes(
      doctor?.matricula_especialidad,
      'guardia',
      undefined,
      { enabled: hasPermission }
    )
  
    const dayRequests = useProviderDayRequests(
      {
        type: 'GUARDIA_RANDOM',
        year,
        month,
        day,
        cuit: doctor?.cuit ?? 'NO',
        requestType: 'online'
      },
      undefined,
      { enabled: hasPermission && !!doctor?.cuit }
    )
  
    const formatData = () => {
      if (!incomes.isSuccess || !dayRequests.isSuccess) return []
      return formatRequests(dayRequests.data)
    }
  
    const totalConsultations = dayRequests.data?.filter(
      consulta => !null_finalDestinations.includes(consulta.destino_final ?? '')
    ).length ?? 0
  
    return {
      formattedRequests: formatData(),
      totalConsultations,
      incomes: incomes.data, 
      isSuccess: incomes.isSuccess && dayRequests.isSuccess
    }
  }