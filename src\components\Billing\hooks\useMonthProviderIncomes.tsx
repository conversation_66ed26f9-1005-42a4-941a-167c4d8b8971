import React, { useContext, useEffect} from 'react'
import { useTranslations } from 'next-intl'
import swal from 'sweetalert'
import { useRouter } from 'next/navigation'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import { useProviderIncomes } from '@/services/reactQuery/useProviderIncomes'
import { useProviderMonthRequests } from '@/services/reactQuery/useProviderMonthRequests'
import { AvailablePermissionsContext } from '@/components/User/AvailablePermissionsProvider'
import { formatRequests, getIncomesWithOnePrice } from '../utils/incomesUtils'
import { errorHandler } from '@/config/stackdriver'
import { getTotalIncomes, getTotalIncomesNoCalendar } from '../utils/incomesGenericsUtils'
import { FullLoader } from '@umahealth/occipital'
import { null_finalDestinations } from '@/config/finalDestinations'
import moment from 'moment'
import usePrescriptionSpecialtiesPermission from '@/components/QuickPrescriptions/utils/usePrescriptionSpecialtiesPermission'

export interface IBilling {
	allDay: boolean;
	end: Date;
	start: Date;
	title: string;
	dt: string;
}
interface IProps{
	setActualMonth: React.Dispatch<React.SetStateAction<Date>>,
	year: string,
	month: string,
	setBilling: React.Dispatch<React.SetStateAction<IBilling[]>>
}

export const useMonthProviderIncomes = ({setActualMonth, year, month, setBilling }: IProps) => {
	const t = useTranslations('time')
	const enableSpecialistAndChat = (!!year && !!month) ? (moment(new Date(`${year}/${month}/01`))?.isAfter(moment(new Date('2023/08/31')))) : false
	const router = useRouter()
	const doctor = useAppSelector(state => state.user.profile)
	const dispatch = useAppDispatch()
	const availablePermissions = useContext(AvailablePermissionsContext)
	const quickPrescriptionPermission = usePrescriptionSpecialtiesPermission()

	// Query de precios de cada tipo de producto (especialista, chat, guardia).
	const specialistIncomes = useProviderIncomes(doctor?.matricula_especialidad, 'online', undefined, { enabled: (!!doctor?.matricula_especialidad && !!availablePermissions?.online && !!enableSpecialistAndChat)})
	const chatAttIncomes = useProviderIncomes(doctor?.matricula_especialidad, 'chat', undefined, { enabled: (!!doctor?.matricula_especialidad && !!availablePermissions?.chatAtt && !!enableSpecialistAndChat)})
	const guardiaIncomes = useProviderIncomes(doctor?.matricula_especialidad, 'guardia', undefined, { enabled: !!availablePermissions?.guardia})

	// Query de consultas que tuvo el proveedor por cada tipo de producto en el mes seleccionado.
	const specialistMonthRequests = useProviderMonthRequests({type: 'MI_ESPECIALISTA', year: year??'NO', month: month??'NO', cuit: doctor?.cuit??'NO', requestType: 'online'}, undefined, { enabled: (!!availablePermissions?.online && !!year && !!month && !!doctor?.cuit && !!enableSpecialistAndChat)})
	const guardiaMonthRequests = useProviderMonthRequests({type: 'GUARDIA_RANDOM', year: year??'NO', month: month??'NO', cuit: doctor?.cuit??'NO', requestType:'online'}, undefined, { enabled: (!!availablePermissions?.guardia && !!year && !!month && !!doctor?.cuit)}) 
	const chatAttMonthRequests = useProviderMonthRequests({type: 'CHAT', year: year??'NO', month: month??'NO', cuit: doctor?.cuit??'NO', requestType:'chatAtt'}, undefined, { enabled: (!!availablePermissions?.chatAtt && !!year && !!month && !!doctor?.cuit && !!enableSpecialistAndChat)})
	const quickPrescriptionsMonthRequests = useProviderMonthRequests({type: 'QUICKPRESCRIPTION', year, month, cuit: doctor?.cuit??'NO', requestType:'quickPrescription'}, undefined, { enabled: (!!quickPrescriptionPermission?.data && !!year && !!month && !!doctor?.cuit && !!enableSpecialistAndChat)})

	//Cantidad de consultas por producto en un mes
	const totalSpecialist = specialistMonthRequests.data?.filter(
		consulta => !null_finalDestinations.includes(consulta.destino_final??'')
	)?.length ?? 0;
	const totalGuardia = guardiaMonthRequests.data?.filter(
		consulta => !null_finalDestinations.includes(consulta.destino_final??'')
	)?.length ?? 0;
	const totalChatAtt = chatAttMonthRequests.data?.filter(
		consulta => !null_finalDestinations.includes(consulta.destino_final??'')
	)?.length ?? 0;

	const totalConsultations = { specialist: totalSpecialist, guardia: totalGuardia, chatAtt: totalChatAtt}
		
	useEffect(() => {
		//Formateamos la data relativa a las consultas por el total y por tipo de producto
		setActualMonth(new Date(`${year}/${month}/05`))
		let totalRequestsFormated: ReturnType<typeof formatRequests> = []
		let guardiaRequestsFormated: ReturnType<typeof formatRequests> = []
		let onlineRequestsFormated: ReturnType<typeof formatRequests> = []
		let chatAttRequestsFormated: ReturnType<typeof formatRequests> = []
		if (doctor?.uid && availablePermissions?.guardia && guardiaMonthRequests.isSuccess && guardiaIncomes.isSuccess) {
			totalRequestsFormated = [...totalRequestsFormated, ...formatRequests(guardiaMonthRequests.data)]
			guardiaRequestsFormated = formatRequests(guardiaMonthRequests.data)
		}	
		if(doctor?.uid && availablePermissions?.online && specialistIncomes.isSuccess && specialistMonthRequests.isSuccess){
			const { requestsWithPrice: specialistRequestsWithPrice } = getIncomesWithOnePrice(specialistMonthRequests.data, specialistIncomes.data)
			totalRequestsFormated = [...totalRequestsFormated, ...formatRequests(specialistRequestsWithPrice)]
			onlineRequestsFormated = formatRequests(specialistRequestsWithPrice)
		}
		if(doctor?.uid && availablePermissions?.chatAtt && chatAttIncomes.isSuccess && chatAttMonthRequests.isSuccess){
			const { requestsWithPrice: chatRequestsWithPrice } = getIncomesWithOnePrice(chatAttMonthRequests.data, chatAttIncomes.data)
			totalRequestsFormated = [...totalRequestsFormated, ...formatRequests(chatRequestsWithPrice)]
			chatAttRequestsFormated = formatRequests(chatRequestsWithPrice)
		}
		if(doctor?.uid && quickPrescriptionPermission?.data && specialistIncomes.isSuccess && quickPrescriptionsMonthRequests.isSuccess){
			const { requestsWithPrice: quickPrescriptionRequestsWithPrice } = getIncomesWithOnePrice(quickPrescriptionsMonthRequests.data, specialistIncomes.data)
			totalRequestsFormated = [...totalRequestsFormated, ...formatRequests(quickPrescriptionRequestsWithPrice)]
			onlineRequestsFormated = [...onlineRequestsFormated, ...formatRequests(quickPrescriptionRequestsWithPrice)]
		}

		//Dispatcheamos la actividad del mes al estado monthActivity
		dispatch({ type: 'SET_MONTH_ACTIVITY', payload: totalRequestsFormated })

		//Total de ingresos del mes por todos los servicios prestados por el proveedor (preliquidación del mes).
		const totalIncomes = getTotalIncomes(totalRequestsFormated, setBilling, t, guardiaIncomes)

		//Total de ingresos del mes por cada tipo de servicio (preliquidación del mes).
		const totalIncomesGuardia = getTotalIncomesNoCalendar(guardiaRequestsFormated, guardiaIncomes, true, dispatch)
		const totalIncomesOnline = getTotalIncomesNoCalendar(onlineRequestsFormated, guardiaIncomes)
		const totalIncomesChatAtt = getTotalIncomesNoCalendar(chatAttRequestsFormated, guardiaIncomes)

		//Dispatcheamos los ingresos del mes separados por producto
		dispatch({ type: 'SET_TOTAL_INCOMES', payload: totalIncomes })
		dispatch({ type: 'SET_TOTAL_INCOMES_GUARDIA', payload: totalIncomesGuardia })
		dispatch({ type: 'SET_TOTAL_INCOMES_ONLINE', payload: totalIncomesOnline })
		dispatch({ type: 'SET_TOTAL_INCOMES_CHAT_ATT', payload: totalIncomesChatAtt })
		dispatch( {type: 'SET_TOTAL_CONSULTATIONS', payload: totalConsultations})

	}, [doctor, month, year ,guardiaMonthRequests.isSuccess, specialistIncomes.isSuccess, specialistMonthRequests.isSuccess, quickPrescriptionsMonthRequests.isSuccess, chatAttIncomes.isSuccess, chatAttMonthRequests.isSuccess, guardiaIncomes.isSuccess])
	
	//Loader
	if(
		specialistIncomes.isLoading || 
		specialistMonthRequests.isLoading || 
		guardiaMonthRequests.isLoading ||
		guardiaIncomes.isLoading ||
		chatAttIncomes.isLoading ||
		chatAttMonthRequests.isLoading  ||
		quickPrescriptionsMonthRequests.isLoading
	) return <FullLoader>Cargando liquidación...</FullLoader>

	//Manejo de errores 
	if(specialistIncomes.isError){
		errorHandler?.report(specialistIncomes.error)
		swal('Error trayendo parámetros de especialista', 'Por favor, intente nuevamente. Si el error persiste <NAME_EMAIL>', 'warning')
			.then(() => router.push('/'))
		return <></>
	} 

	if(specialistMonthRequests.isError){
		errorHandler?.report(specialistMonthRequests.error)
		swal('Error trayendo liquidación de especialista', 'Por favor, intente nuevamente. Si el error persiste <NAME_EMAIL>', 'warning')
			.then(() => router.push('/'))
		return <></>
	}  
	
	if(guardiaMonthRequests.isError){
		errorHandler?.report(guardiaMonthRequests.error)
		swal('Error trayendo liquidación de guardia', 'Por favor, intente nuevamente. Si el error persiste <NAME_EMAIL>', 'warning')
			.then(() => router.push('/'))
		return <></>
	} 

	if(guardiaIncomes.isError){
		errorHandler?.report(guardiaIncomes.error)
		swal('Error trayendo parámetros de guardia', 'Por favor, intente nuevamente. Si el error persiste <NAME_EMAIL>', 'warning')
			.then(() => router.push('/'))
		return <></>
	}

	if(chatAttMonthRequests.isError){
		errorHandler?.report(chatAttMonthRequests.error)
		swal('Error trayendo liquidación de atenciones por chat', 'Por favor, intente nuevamente. Si el error persiste <NAME_EMAIL>', 'warning')
			.then(() => router.push('/'))
		return <></>
	} 

	if(chatAttIncomes.isError){
		errorHandler?.report(chatAttIncomes.error)
		swal('Error trayendo parámetros de atenciones por chat', 'Por favor, intente nuevamente. Si el error persiste <NAME_EMAIL>', 'warning')
			.then(() => router.push('/'))
		return <></>
	}

	if(quickPrescriptionsMonthRequests.isError){
		errorHandler?.report(quickPrescriptionsMonthRequests.error)
		swal('Error trayendo liquidación de recetas de especialistas', 'Por favor, intente nuevamente. Si el error persiste <NAME_EMAIL>', 'warning')
			.then(() => router.push('/'))
		return <></>
	} 
}
