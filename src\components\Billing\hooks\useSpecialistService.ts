import { useProviderIncomes } from "@/services/reactQuery/useProviderIncomes"
import { getDateParts } from "../utils/getDateParts"
import { useProviderDayRequests } from "@/services/reactQuery/useProviderDayRequests"
import { formatRequests, getIncomesWithOnePrice } from "../utils/incomesUtils"
import { null_finalDestinations } from "@/utils/arrayUtils"

export const useSpecialistService = (
    doctor: any,
    date: Date,
    hasPermission: boolean,
    enableSpecialistAndChat: boolean
  ) => {

    const { year, month, day } = getDateParts(date)
    
    const incomes = useProviderIncomes(
      doctor?.matricula_especialidad,
      'online',
      undefined,
      { 
        enabled: hasPermission && enableSpecialistAndChat 
      }
    )
  
    const dayRequests = useProviderDayRequests(
      {
        type: 'MI_ESPECIALISTA',
        year,
        month,
        day,
        cuit: doctor?.cuit ?? 'NO',
        requestType: 'online'
      },
      undefined,
      { 
        enabled: hasPermission && !!doctor?.cuit && enableSpecialistAndChat 
      }
    )

    const formatData = () => {
      if (!incomes.isSuccess || !dayRequests.isSuccess) return []
      
      const { requestsWithPrice } = getIncomesWithOnePrice(
        dayRequests.data, 
        incomes.data
      )
      return formatRequests(requestsWithPrice)
    }
  
    const totalConsultations = dayRequests.data?.filter(
      consulta => !null_finalDestinations.includes(consulta.destino_final ?? '')
    ).length ?? 0
  
    return {
      formattedRequests: formatData(),
      totalConsultations,
      isSuccess: incomes.isSuccess && dayRequests.isSuccess
    }
  }