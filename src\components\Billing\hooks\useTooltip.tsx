import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { errorHand<PERSON> } from "@/config/stackdriver";
import { useProviderTurboIncomesByDate } from "@/services/reactQuery/useProviderTurboIncomesByDate";
import swal from "sweetalert";

export function useTooltip(date: Date) {
  const [tooltipMessage, setTooltipMessage] = useState<string | null>(null);
  const router = useRouter();
  const incomes = useProviderTurboIncomesByDate(date);

  useEffect(() => {
    if (incomes.isError) {
      swal(
        "Error",
        "Ocurrió un error cargando la información de Franjas Turbo. Por favor comuníquese con Soporte.",
        "warning",
      );
      errorHandler?.report(`Error obteniendo franjas turbo: ${incomes.error}`);
      router.push("/liquidacion");
      return;
    }

    if (incomes.data) {
      const filteredTodayIncomes = incomes.data.filter(
        (income) => income.timestamps.dt_period_finish != null
      );

      if (filteredTodayIncomes?.length) {
        const uniqueIncomes = Array.from(
          new Set(
            filteredTodayIncomes.map(
              (income) =>
                `${income.timestamps.dt_period_start}-${income.timestamps.dt_period_finish}`
            )
          )
        ).map((uniqueKey) =>
          filteredTodayIncomes.find(
            (income) =>
              `${income.timestamps.dt_period_start}-${income.timestamps.dt_period_finish}` ===
              uniqueKey
          )
        ).filter((income): income is typeof filteredTodayIncomes[0] => income !== undefined);

        uniqueIncomes.sort((a, b) => {
          const startA = a.timestamps.dt_period_start?.toDate();
          const startB = b.timestamps.dt_period_start?.toDate();

          if (startA && startB) {
            return startA.getTime() - startB.getTime();
          }

          return !startA ? 1 : -1;
        });

        const formatTime = (date: Date) => {
          return date.toLocaleTimeString('es-ES', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          }) + ' hs';
        };

        const turboTimes = uniqueIncomes.map((income) => {
          const start = income.timestamps.dt_period_start?.toDate();
          const end = income.timestamps.dt_period_finish?.toDate();

          if (start && end) {
            return `desde las ${formatTime(start)} hasta las ${formatTime(end)}`;
          }
          return null;
        }).filter((time): time is string => time !== null);

        setTooltipMessage(`El día seleccionado tuvo Franja Turbo ${turboTimes.join(" y ")}.`);
      } else {
        setTooltipMessage(null);
      }
    }
  }, [incomes.isError, incomes.data, router]);

  return tooltipMessage;
}
