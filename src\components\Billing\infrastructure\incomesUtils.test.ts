import { IRequest } from '@umahealth/entities'
import { Timestamp } from '@/config/firebase'
import { formatRequests } from './../utils/incomesUtils'

describe('Function: formatRequests', () => {
	test('should format requests correctly', () => {
		const requests = [{
			destino_final: 'En domicilio con instrucciones',
			dt_cierre: '2023-09-15 10:45:00',
			user: 'user 1',
			dni: '12345678',
			att_category: 'CHAT',
			timestamps: {
				dt_close: Timestamp.fromDate(new Date('2023-09-15T10:45:00')),
			},
		},{
			destino_final: 'Indico seguimiento con especialista',
			dt_cierre: '2023-09-15 10:45:00',
			user: 'user 2',
			dni: '123456789',
			income: 1000, 
			att_category: 'GUARDIA_RANDOM',
			timestamps: {
				dt_close: Timestamp.fromDate(new Date('2023-09-15T10:45:00')),
			},
		}] as (IRequest & { income?: number })[]
		const formattedRequests = formatRequests(requests)
		const expectedResults: ReturnType<typeof formatRequests> = [
			{
				event: 'En domicilio con instrucciones',
				day: '2023-09-15',
				name: 'user 1',
				dni: '12345678',
				time: '10:45:00',
				income: null,
				type: 'CHAT',
				close: Timestamp.fromDate(new Date('2023-09-15T10:45:00'))
			},
			{
				event: 'Indico seguimiento con especialista',
				day: '2023-09-15',
				name: 'user 2',
				dni: '123456789',
				time: '10:45:00',
				income: 1000,
				type: 'GUARDIA_RANDOM',
				close: Timestamp.fromDate(new Date('2023-09-15T10:45:00'))
			}
		]
		expect(formattedRequests).equal(expectedResults)
	})
})