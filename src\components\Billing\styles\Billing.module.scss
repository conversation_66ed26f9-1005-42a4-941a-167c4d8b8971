@use '@/styles/global/Vars.scss';

.billing__title {
	display: flex;
	justify-content: space-between;
	font-weight: 700;
}

.billing__month > * {
	margin: 5px;
}

.billing__month {
	display: flex;
	flex-wrap: wrap;
	margin: 15px;
	.billing__month--filter{
		padding: 5px;
		display: flex;
		align-items: center;
		justify-content: space-around;
		label{
			margin-right: 4px;
			margin-bottom: 0;
		}
		select{
			padding: 8px ;
			text-align: center;
			border: 1px solid Vars.$data-grey;
		}
	}
	.btn.btn-info {
		margin: 5px 15px 0 15px;
		cursor: pointer;
		background: Vars.$primary-color;
	}
}

.billing__container {
	padding: 0 20px;
}

.billing__actions {
	display: flex;
	.billing__check {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	.billing__cbu {
		display: flex;
		margin: 10px 10px;
		vertical-align: bottom;
		align-items: flex-end;
	}
	.billing__bill{
		display: flex;
		margin: 5px;
		vertical-align: bottom;
		align-items: flex-end;
		button{
			margin-right: 4px;
		}
	}
}

.incomesDetail__container {
	width: 100%;
	padding: 0 20px;
	max-height: 50vh;
	overflow-y: auto;
	min-height: 50vh;
	.incomesDetail__event {
		width: 100%;
		font-size: 1.2rem;
		thead {
			font-weight: 700;
		}
	}
}