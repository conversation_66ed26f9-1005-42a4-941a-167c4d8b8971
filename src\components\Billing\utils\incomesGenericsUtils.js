import moment from 'moment-timezone'
import { getGuardiaTotalHour, getIncome, buildGrilla, totalIncomeReduce } from './incomesUtils'

//TODO pasar estas funciones a incomesUtils.ts
export const incomesByHour = (hours, guardiaIncomes) => {
    const groupedByHours = Object.entries(hours).map((e) => ({ [e[0]]: e[1] }))
    const ibd = groupedByHours.map((el) => {
        const day = Object.keys(el)[0].slice(0, 10)
        const guardiaAtts = el[Object.keys(el)]?.filter(att => att?.type === 'GUARDIA_RANDOM')
        const guardiaClosestIncome = guardiaAtts?.length ? getIncome(guardiaAtts[0], guardiaIncomes.data) : null
        const guardiaTotalHour = guardiaClosestIncome ? getGuardiaTotalHour(guardiaClosestIncome, guardiaAtts) : 0
        const otherIncomes = el[Object.keys(el)]?.reduce((acc, curr) => acc + curr.income || 0 , 0)
        return {
            day: day,
            hour: Object.keys(el)[0].slice(11, 13),
            value: guardiaTotalHour + otherIncomes,
            isTurboPrice: guardiaClosestIncome?.isTurboPrice ?? false
        }
    })
    return ibd
}

export const arrayGroupByKeys = (arr, property) => {
    return arr.reduce(function (memo, x) {
        if (!memo[x[property]]) { memo[x[property]] = [] }
        memo[x[property]].push(x)
        return memo
    }, {})
}

export const printCalendar = (data, setBilling, t) => {
    let dates = Object.keys(data)
    let preliquidacion = dates.map(date => {
        return {
            allDay: true,
            end: moment(date).tz(t('zone')).format('YYYY-MM-DD HH:mm:ss'),
            start: moment(date).tz(t('zone')).format('YYYY-MM-DD HH:mm:ss'),
            title: getTitle(data, date),
            dt: date
        }
    })
    setBilling(preliquidacion)
}

export const getTitle = (data, date) => {
    return `$${data[date].reduce(
        (acum, num) => {
            return parseInt(acum) + parseInt(num.value)
        }, 0)}`
}

export const openDay = (e, monthActivity, dispatch) => {
    dispatch({ type: 'SET_MODAL', payload: true })
    let dayActivity = monthActivity?.filter(el => el.day === e)
    dispatch({ type: 'SET_DAY_ACTIVITY', payload: dayActivity })
}

export const getTotalIncomes = (requests, setBilling, t, guardiaIncomes) =>{
    const grilla = buildGrilla(requests)
    const groupedByHours = arrayGroupByKeys(grilla, 'time')
    const ibh = incomesByHour(groupedByHours, guardiaIncomes)
    const groupedByDays = arrayGroupByKeys(ibh, 'day')
    printCalendar(groupedByDays, setBilling, t)
    const totalIncomes = totalIncomeReduce(ibh)
    return totalIncomes
}

export const getTotalIncomesNoCalendar = (requests, guardiaIncomes, isGuardia, dispatch) =>{
    const grilla = buildGrilla(requests)
    const groupedByHours = arrayGroupByKeys(grilla, 'time')
    const ibh = incomesByHour(groupedByHours, guardiaIncomes)
    if(isGuardia){
        dispatch({ type: 'SET_MONTH_ACTIVITY_WITH_TURBO', payload: ibh })
    }
    const totalIncomes = totalIncomeReduce(ibh)
    return totalIncomes
}