import { IRequest } from '@umahealth/entities'
import { notEffectiveFinalDestinations } from '@umahealth/entities'
import { Timestamp } from '@/config/firebase'
import { IProviderIncomes } from '@/services/reactQuery/useProviderIncomes'

interface IGridElement { 
	day: string, 
	time: string, 
	income: number | null, 
	type: string, 
	close?: Timestamp
}

interface IncomeProp {
	day:string,
	hour: string, 
	value: string | number
}

const orderIncomes = (possibleIncomes: IProviderIncomes[]) => possibleIncomes?.sort((a, b) => {
	const dateA = a.timestamps.dt_period_finish?.toDate()
	const dateB = b.timestamps.dt_period_finish?.toDate()
	if(dateA && dateB) return dateA.getTime() - dateB.getTime()
	return 0
})

const getClosestIncome = (possibleIncomes: IProviderIncomes[]) => {
	const orderedIncomes = orderIncomes(possibleIncomes)
	return orderedIncomes[0]
}

const getPossibleIncomes = (request: IRequest | IGridElement, incomes: IProviderIncomes[]) => {
	if (!incomes?.length) return []
	const timestampRequestClose = (request && 'close' in request && !!request.close 
	? (request as IGridElement).close 
	: (request as IRequest).timestamps.dt_close) as Timestamp
	if (!timestampRequestClose) return []
	return incomes.filter(income => {
	if (!income?.timestamps) return false
	if (income.timestamps.dt_period_finish === null) return true
	return income.timestamps.dt_period_finish?.toDate() >= timestampRequestClose.toDate()
	})
  }

export const getIncome = (request: IRequest | IGridElement, incomes: IProviderIncomes[]) => {
	const possibleIncomes = getPossibleIncomes(request, incomes)
	if(possibleIncomes?.length === 1) return possibleIncomes[0]
	const closestIncome = getClosestIncome(possibleIncomes.filter(income => !!income?.timestamps?.dt_period_finish))
	return closestIncome
}

const getRequestPrice = (request: IRequest, incomes: IProviderIncomes[]) =>{
	const income = getIncome(request, incomes)
	return income.prices[0].price
}

export const getIncomesWithOnePrice = (monthRequests: IRequest[], incomes: IProviderIncomes[]) =>{
	const requestsWithPrice = monthRequests.map(request => {
		return {
			...request,
			income: getRequestPrice(request, incomes)
		}
	}) 
	const totalIncomes = getTotalMonthIncomes(requestsWithPrice)
	return { requestsWithPrice, totalIncomes }
}

export const formatRequests = (requests: (IRequest & { income?: number })[]) => requests?.map(request => {
	return {
		event: request.destino_final,
		day: request.dt_cierre.slice(0, 10),
		name: request.user,
		dni: request.dni,
		time: request.dt_cierre.slice(11, 19),
		income: request.income || null,
		type: request.att_category,
		close: request.timestamps.dt_close
	}
})

const getTotalMonthIncomes = (specialistMonthRequests: (IRequest & { income: number })[]) => specialistMonthRequests.reduce((acc, curr) => acc + curr.income, 0)

export const buildGrilla = (monthAtts: {
	event: string;
	day: string;
	name: string;
	dni: string;
	time: string;
	income: number | null;
	type: string;
	close?: Timestamp
}[]) => {
	const grilla: IGridElement[] = [] 
	monthAtts.forEach(att => {
		if (!notEffectiveFinalDestinations.some(nonEffectiveCase => att.event === nonEffectiveCase)) {
			grilla.push({ day: `${att.day}`, time: `${att.day}_${att.time?.slice(0, 2)}`, income: att?.income || null, type: att.type, close: att.close })
		}
	})
	return grilla
}

export const getGuardiaTotalHour = (income: IProviderIncomes, guardiaAtts: IGridElement[]) =>{
	const quantity = guardiaAtts.length
	if(!income.rangeTurbo?.days || !income.rangeTurbo?.hours) return guardiaPrices(income.prices, quantity, false)
	if(!isTurboPrice(income, guardiaAtts)) return guardiaPrices(income.prices, quantity, false)
	return guardiaPrices(income.prices, quantity, true)
}

const isTurboPrice = (income: IProviderIncomes, guardiaAtts: IGridElement[]) =>{
	//TODO mejorar Types
	const dayAttentions = guardiaAtts[0]?.close?.toDate()?.getDay() as number
	const hourAttentions = guardiaAtts[0]?.close?.toDate()?.getHours() as number
	if(dayAttentions >= (income.rangeTurbo?.days[0] as number) 
	&& dayAttentions <= (income.rangeTurbo?.days[1] as number)
	&& hourAttentions >= (income.rangeTurbo?.hours[0] as number) 
	&& hourAttentions < (income.rangeTurbo?.hours[1] as number)) return true
	return false
}

const orderPrices = (prices: IProviderIncomes['prices'], order: 'ASC' | 'DSC'): IProviderIncomes['prices'] => prices.sort((a, b) => {
	return order === 'DSC' ? 
		(a.rangeFinish && b.rangeFinish) ? 
			b.rangeFinish - a.rangeFinish : 
			0 : 
		(a.rangeFinish && b.rangeFinish) ? 
			a.rangeFinish - b.rangeFinish : 
			0
})

const guardiaPrices = (prices: IProviderIncomes['prices'], guardiaAttsQuantity: number, turboPrice: boolean) =>{ 
	// Filtrar precios que tienen rangeFinish
	const pricesWithRangeFinish = prices.filter(price => !!price.rangeFinish)
	
	// Ordenar precios en orden descendente
	const maxPrice = orderPrices(pricesWithRangeFinish, 'DSC')[0]
	
	// Si la cantidad de consultas no supera el rangeFinish del maxPrice
	if (guardiaAttsQuantity <= (maxPrice.rangeFinish as number)) {
		const price = orderPrices(pricesWithRangeFinish.filter(price => (price.rangeFinish as number) >= guardiaAttsQuantity), 'ASC')[0]
		/** no se usa turbo price JUNIO/JULIO 2025, metimos el valor directamente en price */
		return turboPrice ? price.price : price.price
	}
	
	// Calcular precio adicional por consultas extra
	const restAttsPrice = prices.find(price => price.rangeFinish === null)
	const accumulatorRestAtts = (guardiaAttsQuantity - (maxPrice.rangeFinish as number)) * (turboPrice ? restAttsPrice?.turboPrice as number : restAttsPrice?.price as number)
	
	// Retornar el precio total considerando turbo
	return turboPrice ? (maxPrice.price as number) + accumulatorRestAtts : maxPrice.price + accumulatorRestAtts
};

/**
 * Calcula el total de ingresos de una lista de ingresos.
 *
 * Esta función se utiliza para calcular el total de ingresos de una lista de ingresos. La lista debe contener ingresos con las propiedades `day` y `hour` de tipo `string` y la propiedad `value` de tipo `string` o `number`. Si la propiedad `value` es un string, se convierte a número antes de realizar la suma.
 *
 * **Ejemplos:**
 *
 * ```typescript
 * const incomes = [
 *   { day: "2023-07-20", hour: "10:00", value: 100 },
 *   { day: "2023-07-21", hour: "11:00", value: 200 },
 * ];
 *
 * const totalIncomes = getTotalIncomes(incomes);
 *
 * console.log(totalIncomes); // 300
 * ```
 *
 * ```typescript
 * const incomes = [
 *   { day: "2023-07-22", hour: "12:00", value: "1000" },
 * ];
 *
 * const totalIncomes = getTotalIncomes(incomes);
 *
 * console.log(totalIncomes); // 1000
 * ```
 *
 * @param incomes Lista de ingresos.
 * @returns Total de ingresos.
 **/

export const totalIncomeReduce = (incomes: IncomeProp[]) => {
	return incomes.reduce((acum: number, num: IncomeProp) => {
		const intValue = Number(num.value)
		if (Number.isNaN(intValue)) {
		return 0
		}
		return acum + intValue
	}, 0)
}

/** Se utilizan estos valores para equiparar el criterio de comparación con la cloud function que liquida guardia **/
export const noneffectiveCloudFunction = [
	"USER CANCEL",
	"Paciente ausente",
	"Anula el paciente",
	"Anula por falla de conexión",
	"No encontrado",
	"CANCEL_BY_CF_TIME_ELAPSED",
	"AUTOMATIC_CANCEL",
	"PROVIDER CANCEL",
	"USER_CANCEL",
	"CANCEL",
]
  
export const effectiveCloudFunction = [
	"En domicilio con instrucciones",
	"En domicilio con seguimiento",
	"Envía Médico a Domicilio",
	"Evaluación en amarillo VMD",
	"Evaluación en amarillo",
	"Evaluación en rojo",
	"Evaluación en verde VMD",
	"Indico concurrir a guardia externa",
	"Indico seguimiento con especialista",
	"Indico seguimiento por consultorio externo",
	"Solicito Médico para visita a domicilio",
	"Traslado protocolo pandemia",
	"Requiere atención de guardia presencial",
]
  
/** Se utilizan estos valores para equiparar el criterio de comparación con el looker de especialista **/
export const effectiveLookerEspecialista = [
	"En domicilio con instrucciones",
	"Evaluación en verde VMD",
	"Evaluación en amarillo",
	"Evaluación en rojo",
	"Indico concurrir a guardia externa",
	"Indico seguimiento con especialista",
	"Indico seguimiento por consultorio externo",
	"Traslado protocolo pandemia",
	"Requiere atención de guardia presencial",
	"Requiere atención urgente",
]