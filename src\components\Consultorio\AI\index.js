import React from 'react'
import Tags from '../Tags'
import Diagnostic from '../Diagnostic'
import Stt from '../STT'
import { useSearchParams } from 'next/navigation'

const AiTools = () => {
  const searchParams = useSearchParams()
  const attType = searchParams.get('attType')

	return (
		<>
			{['onsite', 'consultorio'].includes(attType) ? <Stt /> :
				<>
					<Tags />
					<Diagnostic />
				</>
			}
		</>
	)
}

export default AiTools