import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { useSearchParams } from 'next/navigation'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import { getFinalDestinations } from '@/utils/arrayUtils'
// Utilizamos nuestra propia función para manejar el destino final y guardar en cookies
import useSpecialties from '@/services/reactQuery/useSpecialties'
import {
  IncompletedField,
  RequiredField,
} from '@/modules/consultorio/presentation/components/AttFile/AttForm'
import {
  Select,
  SelectItem,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  RadioRoot,
  RadioItem
} from '@umahealth/occipital/client'
import { differenceInYears, parseISO } from 'date-fns'
import { useQueryClient } from '@tanstack/react-query'
import { useGetPatientConnected } from '@/services/reactQuery/Appointment/useGetLogs'
import { ICorporateServices } from '@umahealth/entities'
import { attTypeTelehealthRoom } from '@/app/(providers)/(loggedIn)/doctor/page'
import { setFormFieldCookie } from '@/cookies/AssignationFormData'
import { useAssignationFormData } from '@/cookies/AssignationFormDataContext'
import { usePostHog } from 'posthog-js/react'

// Dynamically import less critical components
const DomicilioPatient = dynamic(() => import('@/components/Consultorio/AttFile/components/DomicilioPatient'), { ssr: false })

const DestinoFinal = ({
  autoflip,
  isValidToCLoseFinalDestination,
  isValidToCloseReferral,
  activeServices,
  defaultValue,
  corporate
}: {
  autoflip?: boolean
  isValidToCLoseFinalDestination?: boolean
  isValidToCloseReferral?: boolean
  activeServices?: ICorporateServices
  defaultValue?: string
  corporate?: string
}) => {
  const posthog = usePostHog()
  const dispatch = useAppDispatch()
  const searchParams = useSearchParams()
  const attType = searchParams.get('attType') as attTypeTelehealthRoom
  const currentAtt = useAppSelector((state) => state.queries.currentAtt)
  const { patient } = useAppSelector((state) => state.queries)
  const [firstTime, setFirstTime] = useState(true)
  const specialistiesArray = useSpecialties().data?.specialties_list
  const queryClient = useQueryClient()

  const hasPatientConnected = useGetPatientConnected({
    assignation_id: currentAtt?.assignation_id,
    patientPrincipalUid: currentAtt?.patient?.uid,
    enabled: attType === 'chatAtt' || attType === 'consultorio' ? false : true && !!currentAtt?.patient?.uid && !!currentAtt?.assignation_id
  })

  // Usando el contexto de datos de formulario
  const { formData, setFormField, assignationId } = useAssignationFormData()
  const [loadedDestinoFinal, setLoadedDestinoFinal] = useState(false)
  

  useEffect(() => {
    const loadDestinoFinal = () => {
      // Si hay datos en el contexto, usarlos
      if (formData.destino_final) {
        dispatch({ type: 'DESTINATION_WRITE', payload: formData.destino_final })
      }
      setLoadedDestinoFinal(true)
    }
    
    if (!loadedDestinoFinal) {
      loadDestinoFinal()
    }
  }, [formData.destino_final, dispatch, loadedDestinoFinal])

  const destinos = getFinalDestinations({
    attType: attType,
    coverageServices: activeServices,
    hasPatientConnected: hasPatientConnected.data,
    corporate: corporate
  })

  useEffect(() => {
    if (
      ((formData.evolucion && formData.evolucion.length < 40) || autoflip) &&
      firstTime
    ) {
      updateTextareas() // changeTreatment, changeAlerts
    }
  }, [autoflip, formData.evolucion])

  function updateTextareas(
    changeTreatment = true,
    changeAlerts = true,
    epicrisis = true
  ) {
    if (autoflip === true) {
      if (currentAtt?.mr?.diagnostico) {
        dispatch({
          type: 'DIAGNOSTIC_WRITE',
          payload: currentAtt?.mr?.diagnostico,
        })
      }

      const destinoValue = currentAtt?.mr?.destino_final
        ? {
          value: currentAtt?.mr?.destino_final,
          label: currentAtt?.mr?.destino_final,
        }
        : null
      if (destinoValue?.value) {
        handleDestinoChange(destinoValue.value)
      }
    } else {
      if (changeTreatment) {
        dispatch({ type: 'TREATMENT_WRITE', payload: formData.plan_terapeutico || '' })
      }
      if (changeAlerts) {
        dispatch({ type: 'ALERTS_WRITE', payload: formData.evolucion || '' })
      }
      if (epicrisis) {
        const epicrisis = buildEpicrisis()
        dispatch({ type: 'EPICRISIS_WRITE', payload: epicrisis })
      }
    }
    setFirstTime(false)
  }

  function buildEpicrisis() {
    let motives = '',
      answers = ''

    let age
    const sex = patient.sex

    try {
      age = differenceInYears(new Date(), parseISO(patient.dob))
    } catch (err) {
      /** lo pongo así para que no rompa, pero el error salte y lo podamos corregir */
      age = '¿?'
    }

    if (currentAtt?.mr) {
      if (currentAtt?.mr?.motivos_de_consulta?.length > 4) {
        motives = `consulta por ${currentAtt?.mr?.motivos_de_consulta?.replace(
          /\./g,
          ','
        )}.`
      }
      if (currentAtt?.mr_preds?.epicrisis?.length > 4) {
        answers = `Al interrogatorio dirigido refiere ${currentAtt?.mr_preds?.epicrisis?.replace(
          /\./g,
          ','
        )}`
      }
    }
    return `Paciente ${sex} de ${age} años de edad ${motives}. ${answers} `
  }

  const saveDestinoFinalCookie = async (destinoValue: string) => {
    if (assignationId && destinoValue) {
      try {
        // Actualizar el contexto
        setFormField('destino_final', destinoValue)
        
        // Guardar en cookies para persistencia
        if (assignationId) {
          await setFormFieldCookie(assignationId, 'destino_final', destinoValue)
        }
      } catch (error) {
        console.warn('Error al guardar destino final en cookies:', error)
      }
    }
  }

  const handleDestinoChange = (value: string) => {
    posthog.capture('medical_record_completed')
    dispatch({ type: 'DESTINATION_WRITE', payload: value })
    saveDestinoFinalCookie(value)
  }

  return (
    <>
      <IncompletedField
        flag={isValidToCLoseFinalDestination ?? false}
        showTooltip={false}
      />
      <>
        <form className="my-4">
          <div>
            <RadioRoot
               className="flex flex-col w-full gap-3"
               defaultValue={defaultValue || ''}
               value={formData.destino_final || ''}
               onValueChange={(value) => {
                 handleDestinoChange(value)
                 // Refetch patient logs when changing value (preserves the behavior from onOpenChange in Select)
                 queryClient.refetchQueries({
                   queryKey: [
                     'hasPatientLogs',
                     currentAtt.assignation_id,
                     patient.uid,
                   ],
                 })
               }}
            >
              {destinos
                .filter(destino => destino.enabled)
                .map((destino) => (
                  <div key={destino.value} className="flex flex-col">
                    <RadioItem
                      id={destino.value}
                      className="bg-transparent border-none flex-row-reverse justify-end gap-2"
                      itemClassName="size-5"
                      indicatorClassName="size-4"
                      value={destino.value}
                    >
                      {destino.label}
                    </RadioItem>
                    
                    {/* Renderizar DomicilioPatient inmediatamente después del radio button cuando sea relevante */}
                    {formData.destino_final === destino.value && [
                      'Evaluación en rojo',
                      'Evaluación en amarillo',
                      'Evaluación en verde VMD'
                    ].includes(destino.value) && (
                      <div className="mt-2 ml-6">
                        <DomicilioPatient />
                      </div>
                    )}
                  </div>
                ))}
              
              {destinos
                .filter(destino => !destino.enabled)
                .map((destino) => (
                  <TooltipProvider delayDuration={3} key={destino.label}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                      <RadioItem
                        key={destino.value}
                        id={destino.value}
                        className="bg-transparent border-none flex-row-reverse justify-end gap-2"
                        itemClassName="size-5"
                        indicatorClassName="size-4"
                        value={destino.value}
                        disabled
                        aria-disabled
                      >
                        {destino.label}
                      </RadioItem>
                      </TooltipTrigger>
                      <TooltipContent
                        className="max-w-96 flex items-center justify-center p-2"
                        align="start"
                        side="top"
                        alignOffset={12}
                      >
                        <span>
                          {destino.tooltip}
                        </span>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ))}
            </RadioRoot>
          </div>
          <RequiredField flag={isValidToCLoseFinalDestination ?? false} />
        </form>
      </>
      {formData.destino_final === 'Indico seguimiento con especialista' && (
        <>
          <label className='mt-4'>
            Derivación a especialista{' '}
            <IncompletedField
              flag={isValidToCloseReferral ?? false}
              showTooltip={false}
            />
          </label>
          <Select
            onValueChange={(value) => {
              dispatch({ type: 'REFERRAL_SELECT', payload: value })
              // También actualizar en el contexto
              setFormField('evolucion',  `${formData.evolucion} Derivación a especialista: ${value}`)
            }}
            placeholder="Indicar especialidad:"
          >
            {specialistiesArray?.map((specialist) => {
              return (
                <SelectItem key={specialist.label} value={specialist.value}>
                  {specialist.label}
                </SelectItem>
              )
            })}
          </Select>

          <RequiredField flag={isValidToCloseReferral ?? false} />
        </>
      )}
    </>
  )
}

export default DestinoFinal
