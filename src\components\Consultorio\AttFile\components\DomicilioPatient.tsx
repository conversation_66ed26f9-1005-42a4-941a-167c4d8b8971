import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { useForm, Controller } from 'react-hook-form';
import { useAssignationFormData } from '@/cookies/AssignationFormDataContext';
import { mapConfig } from '@/utils/mapsApiHandler';
import { setFormFieldCookie } from '@/cookies/AssignationFormData';

// Extendemos Window para incluir google
declare global {
  interface Window {
    google?: {
      maps?: any;
    };
  }
}

// Tipos para Google Maps API
interface GooglePlace {
  formatted_address: string;
  geometry: {
    location: {
      lat: () => number;
      lng: () => number;
    }
  };
  address_components?: Array<{
    long_name: string;
    short_name: string;
    types: string[];
  }>;
}

interface FormValues {
  addressType: 'depto' | 'house' | 'other';
  user_number: string;
  user_floor: string;
  user_obs: string;
  user_ws: string;
  user_address: string;
}

const DomicilioPatient: React.FC = () => {
  const dispatch = useAppDispatch()
  const autocompleteRef = useRef<any>(null)
  const addressInputRef = useRef<HTMLInputElement>(null)

  // Usar AssignationFormDataContext en lugar de Redux
  const { formData, setFormField, assignationId } = useAssignationFormData()
  const patientAddress = formData.patient_address || {
    destination: {},
    coords: { lat: 0, lng: 0 },
    validAddress: false
  }

  // Consultar el estado de Redux solo para la información del paciente
  const { patient } = useAppSelector(state => state.queries)
  
  // Usar formData del contexto en lugar de ficha de Redux
  const isAmbulanceDestination = [
    "Evaluación en rojo",
    "Evaluación en verde VMD",
    "Evaluación en amarillo"
  ].includes(formData.destino_final || "")

  // Estado para manejar errores de API
  const [apiLoadError, setApiLoadError] = useState(false)
  const [mapApiLoaded, setMapApiLoaded] = useState(false)
  const [mapApi, setMapApi] = useState<any>(null)

  // Usamos el patrón existente de Google Maps en la aplicación
  useEffect(() => {
    // Si no tenemos el script de Google Maps, lo cargamos usando la configuración existente
    if (!window.google?.maps && !document.getElementById('google-maps-api')) {
      // Usamos la configuración centralizada que ya tiene la API key
      const { bootstrapURLKeys } = mapConfig()

      // Crear y añadir el script
      const script = document.createElement('script')
      script.id = 'google-maps-api'
      script.src = `https://maps.googleapis.com/maps/api/js?key=${bootstrapURLKeys.key}&libraries=${bootstrapURLKeys.libraries.join(',')}&language=${bootstrapURLKeys.language}`
      script.async = true
      script.defer = true
      script.onload = () => {
        setMapApiLoaded(true)
        setMapApi(window.google?.maps)
      }
      script.onerror = () => {
        console.error('No se pudo cargar Google Maps API')
        setApiLoadError(true)
      }

      document.head.appendChild(script)
    } else if (window.google?.maps) {
      // Si ya está cargado, simplemente usamos la API
      setMapApiLoaded(true)
      setMapApi(window.google.maps)
    }

    return () => {
      // Limpiar
      const loadedScript = document.getElementById('google-maps-api')
      if (loadedScript && loadedScript.parentNode) {
        loadedScript.parentNode.removeChild(loadedScript)
      }
    }
  }, [])

  // Inicializar autocomplete cuando la API de Google Maps está cargada
  useEffect(() => {
    if (mapApiLoaded && mapApi && addressInputRef.current) {
      try {
        // Opciones para priorizar CABA, Argentina
        const options = {
          componentRestrictions: { country: 'ar' },
          types: ['address']
        }

        // Crear el objeto de autocomplete
        autocompleteRef.current = new mapApi.places.Autocomplete(
          addressInputRef.current,
          options
        )

        // Configurar límites para CABA si es destino de ambulancia - priorizar sin restringir
        if (isAmbulanceDestination) {
          const bounds = new mapApi.LatLngBounds(
            new mapApi.LatLng(-34.7, -58.5), // suroeste
            new mapApi.LatLng(-34.5, -58.3)  // noreste
          )
          autocompleteRef.current.setBounds(bounds)
          // Mantener strictBounds en false para priorizar pero no restringir a CABA
          autocompleteRef.current.setOptions({ strictBounds: false })
        }

        // Listener para cambio de lugar
        const placeChangedListener = autocompleteRef.current.addListener('place_changed', function () {
          const place = autocompleteRef.current.getPlace() as GooglePlace
          if (!place.geometry) return

          const address = place.formatted_address
          const lat = place.geometry.location.lat()
          const lng = place.geometry.location.lng()

          // Extraer componentes de dirección
          const getAddressComponent = (type: string) => {
            const component = place.address_components?.find(component =>
              component.types.includes(type)
            )
            return component?.short_name || ''
          }

          // Extraer información detallada
          const cp = getAddressComponent('postal_code')
          const locality = getAddressComponent('locality') || getAddressComponent('sublocality')
          const region = getAddressComponent('administrative_area_level_1')
          const country = getAddressComponent('country')

          // Actualizar formulario
          setValue('user_address', address)

          // Actualizar context
          const updatedPatientAddress = {
            ...patientAddress,
            destination: {
              ...patientAddress.destination,
              user_address: address,
              user_lat: lat,
              user_lon: lng,
              user_cp: cp,
              user_locality: locality,
              user_region: region,
              user_country: country
            },
            coords: { lat, lng },
            validAddress: true
          }

          setFormField('patient_address', updatedPatientAddress)
          dispatch({ type: 'SET_PATIENT_ADDRESS', payload: updatedPatientAddress })
          
          // Persistir los datos en cookies para evitar pérdida al recargar
          if (assignationId) {
            setFormFieldCookie(assignationId, 'patient_address', updatedPatientAddress);
          }
        })

        // Guardar referencia para limpieza
        return () => {
          if (placeChangedListener) {
            mapApi.event.removeListener(placeChangedListener)
          }
        }
      } catch (error) {
        console.error('Error al inicializar autocomplete:', error)
        setApiLoadError(true)
      }
    }
  }, [mapApiLoaded, mapApi, addressInputRef.current, isAmbulanceDestination])

  // React Hook Form
  const { control, setValue } = useForm<FormValues>({
    defaultValues: {
      addressType: 'depto',
      user_number: patientAddress.destination?.user_number || '',
      user_floor: patientAddress.destination?.user_floor || '',
      user_obs: patientAddress.destination?.user_obs || '',
      // Usar el teléfono del paciente como valor predeterminado si está disponible
      user_ws: patientAddress.destination?.user_ws || patient?.ws || '',
      user_address: patientAddress.destination?.user_address || ''
    }
  })


  // Inicializar el patientAddress en el contexto si no existe
  useEffect(() => {
    if (!formData.patient_address) {
      setFormField('patient_address', {
        destination: {},
        coords: { lat: 0, lng: 0 },
        validAddress: false
      })
    }
  }, [formData.patient_address, setFormField])

  // Actualizar los valores del formulario solo cuando se inicializa el componente
  useEffect(() => {
    if (patientAddress.destination) {
      setValue('user_number', patientAddress.destination.user_number || '')
      setValue('user_floor', patientAddress.destination.user_floor || '')
      setValue('user_obs', patientAddress.destination.user_obs || '')
      setValue('user_ws', patientAddress.destination.user_ws || '')
      setValue('user_address', patientAddress.destination.user_address || '')
    }
  }, []) // Solo se ejecuta una vez al montar el componente

  // Usar un debounce para evitar actualizaciones demasiado frecuentes
  const [debouncedUpdate, setDebouncedUpdate] = useState<NodeJS.Timeout | null>(null);

  const handleDetails = useCallback((field: string, details: string) => {
    // Cancelar cualquier actualización pendiente
    if (debouncedUpdate) {
      clearTimeout(debouncedUpdate);
    }

    // Configurar un nuevo temporizador para actualizar después de un retraso
    const timer = setTimeout(() => {
      // Actualizar en el contexto
      const updatedPatientAddress = {
        ...patientAddress,
        destination: { ...patientAddress.destination, [field]: details }
      }
      setFormField('patient_address', updatedPatientAddress)

      // Mantener compatibilidad con Redux mientras se migra
      dispatch({ type: 'SET_PATIENT_ADDRESS', payload: updatedPatientAddress })
      
      // Persistir los datos en cookies para evitar pérdida al recargar
      if (assignationId) {
        setFormFieldCookie(assignationId, 'patient_address', updatedPatientAddress);
      }
    }, 300); // 300ms de retraso

    setDebouncedUpdate(timer);
  }, [patientAddress, dispatch, setFormField, debouncedUpdate])

  // Función para actualizar la dirección en el contexto
  const updateAddressInContext = useCallback((address: string) => {
    // Cancelar cualquier actualización pendiente
    if (debouncedUpdate) {
      clearTimeout(debouncedUpdate);
    }

    // Configurar un nuevo temporizador para actualizar después de un retraso
    const timer = setTimeout(() => {
      const updatedPatientAddress = {
        ...patientAddress,
        destination: {
          ...patientAddress.destination,
          user_address: address
        }
      }
      setFormField('patient_address', updatedPatientAddress)

      // Mantener compatibilidad con Redux mientras se migra
      dispatch({ type: 'SET_PATIENT_ADDRESS', payload: updatedPatientAddress })
    }, 300);

    setDebouncedUpdate(timer);
  }, [patientAddress, dispatch, setFormField, debouncedUpdate])


  return (
    <div >
      <div >
        <div className="domicilio_map_inputs mt-4 mx-4 mb-4">
          <div className='grid grid-cols-3 gap-4 mb-4 '>
            <Controller
              name="user_address"
              control={control}
              render={({ field }) => (
                <div className="relative">
                  <input
                    {...field}
                    ref={addressInputRef}
                    type="text"
                    placeholder="Buscar dirección en CABA, Argentina"
                    className={`w-full p-2 border ${apiLoadError ? 'border-red-300' : 'border-gray-300'} rounded focus:ring-2 focus:ring-blue-300 focus:border-blue-500`}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      field.onChange(e);
                      // No actualizamos inmediatamente para permitir que el autocompletado funcione
                    }}
                    onBlur={(e) => {
                      // Si el usuario sale del campo sin seleccionar una dirección del autocompletado
                      updateAddressInContext(e.target.value);
                    }}
                    disabled={apiLoadError}
                  />
                </div>
              )}
            />

            <Controller
              name="user_floor"
              control={control}
              render={({ field }) => (
                <div>
                  <input
                    {...field}
                    id={field.name}
                    placeholder="Piso"
                    className="w-full p-2 border border-gray-300 rounded"
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      field.onChange(e);
                      // Esperar a que termine de escribir antes de actualizar el contexto
                      handleDetails('user_floor', e.target.value);
                    }}
                  />
                </div>
              )}
            />

            <Controller
              name="user_number"
              control={control}
              render={({ field }) => (
                <div>
                  <input
                    {...field}
                    id={field.name}
                    placeholder="Departamento"
                    className="w-full p-2 border border-gray-300 rounded"
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      field.onChange(e);
                      // Esperar a que termine de escribir antes de actualizar el contexto
                      handleDetails('user_number', e.target.value);
                    }}
                  />
                </div>
              )}
            />
          </div>

          <Controller
            name="user_obs"
            control={control}
            render={({ field }) => (
              <div className="mb-4">
                <input
                  {...field}
                  id={field.name}
                  placeholder="Observaciones sobre domicilio"
                  className="w-full p-2 border border-gray-300 rounded"
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    field.onChange(e);
                    // Esperar a que termine de escribir antes de actualizar el contexto
                    handleDetails('user_obs', e.target.value);
                  }}
                />
              </div>
            )}
          />

          <Controller
            name="user_ws"
            control={control}
            render={({ field }) => (
              <div>
           
                <input
                  {...field}
                  id={field.name}
                  placeholder="telefono..."
                  className={`w-full p-2 border ${isAmbulanceDestination ? 'border-red-300 focus:ring-red-200' : 'border-gray-300'} rounded`}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    field.onChange(e);
                    // Esperar a que termine de escribir antes de actualizar el contexto
                    handleDetails('user_ws', e.target.value);
                  }}
                />
              </div>
            )}
          />
        </div>
      </div>
    </div>
  )
}

export default DomicilioPatient
