import React, { useEffect, useState } from 'react'
import { doc, getDoc } from '@/config/firebase'
import firestore from '@/config/firebase'
import { useAppSelector } from '@/store/hooks'
import { errorHandler }  from '@/config/stackdriver'

function HisopadoPromise() {
	const { patient } = useAppSelector((state) => state.queries)
	const [promise, setPromise] = useState('')
	
	useEffect(() => {
		const fetchData = async () => {
			try {
				const docRef = doc(firestore, 'parametros/userapp/delivery/hisopados')
				const docSnap = await getDoc(docRef)
				if (docSnap.exists()) {
					setPromise(docSnap.data().delay)
				} else {
					console.log('No such document!')
				}
			} catch (err) {
				errorHandler.report(err)
			}
		}
	
		fetchData()
	}, [patient, firestore])
	

	return <p>
        El hisopado se programará para {promise}.
        Avisar al paciente y en caso de necesitar hisopado con anterioridad solicitar acercarse a un punto de testeo o unidad febril.
	</p>
}

export default HisopadoPromise
