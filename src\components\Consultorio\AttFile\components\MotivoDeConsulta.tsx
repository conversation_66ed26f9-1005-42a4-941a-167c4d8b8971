import React, { useState, useCallback } from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import { Button, Icon, Paragraph, Row } from '@umahealth/occipital-ui'
import { motive_edit } from '@/config/endpoints'
import axios from 'axios'
import { formatterReasonsWithPoint, formatterReasonsWithoutPoint } from '../utils/formatterReasons'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";

const MotivoDeConsulta = () => {
	const dispatch = useAppDispatch()
	const currentAtt = useAppSelector((state: any) => state.queries.currentAtt)
	const [edit, setEdit] = useState<boolean>(false)
	const [motive, setMotive] = useState<string>(currentAtt?.mr?.motivos_de_consulta)
	const handleSubmit = useCallback(async () => {
		const temp_motive = currentAtt?.mr?.motivos_de_consulta
		if (motive && temp_motive !== motive) {
			const token = await getFirebaseIdToken();
			dispatch({ type: 'SET_MOTIVO_CONSULTA', payload: formatterReasonsWithPoint(motive) })
			const headers = {
				'content-type': 'application/json',
				'Authorization': `Bearer ${token}`
			}
			axios.patch(motive_edit, {
				motive: formatterReasonsWithPoint(motive),
				uid: currentAtt?.patient?.uid,
				assignationId: currentAtt.assignation_id
			}, { headers })
				.catch(() => dispatch({ type: 'SET_MOTIVO_CONSULTA', payload: temp_motive }))
		}
		setEdit(false)
		// eslint-disable-next-line
	}, [motive])

	return (
		<>
			<div className="sectionTitle">
				Motivo de consulta
			</div>
			<div style={{ padding: '15px 0px' }}>
				<Row>
					{!edit ?
						<>
							<Paragraph>{currentAtt?.mr?.motivos_de_consulta ? <span className='reasons-container'>{formatterReasonsWithoutPoint(currentAtt?.mr?.motivos_de_consulta)}</span> : 'No informado'}</Paragraph>
						</>
						:
						<>
							<textarea
								onChange={e => setMotive(e.target.value)}
								defaultValue={formatterReasonsWithoutPoint(currentAtt?.mr?.motivos_de_consulta)}
							/>
						</>
					}
					{!edit ?
						<div
							onClick={() => setEdit(true)}
							className='w-10 h-10 flex items-center justify-center cursor-pointer rounded-full'
						>
							<Icon
								color="primary"
								name="pencil"
								size="xs"
								aria-hidden='true'
							/>
						</div>
						:
						<Button
							type="text"
							size="small"
							action={() => handleSubmit()}
						>
							Enviar
						</Button>
					}

				</Row>

			</div>
		</>
	)
}

export default MotivoDeConsulta
