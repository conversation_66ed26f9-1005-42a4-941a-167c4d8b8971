import React from 'react'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import Select from 'react-select'
import { Textarea } from 'occipital-new'

const ProblemReport = ({ setIsTagsEmpty }) => {
	const dispatch = useAppDispatch()
	const ficha = useAppSelector(state => state.ficha)
	const customStyles = {
		control: (provided) => ({
      ...provided,
      minHeight: '56px',
      display: 'flex',
      alignItems: 'center',
    }),
		indicatorSeparator: () => ({
      display: 'none',
    }),
    valueContainer: (provided) => ({
      ...provided,
      minHeight: '56px',
      padding: '0 12px',
    }),
    menu: (provided) => ({
      ...provided,
      marginTop: '0',
    }),
    option: (provided) => ({
      ...provided,
      padding: '12px',
    }),
	}


	function renderOptions() {
		return ([
			{value: 'no_audio', label: 'Llamada sin audio'},
			{value: 'no_video', label: 'Llamada sin video'},
			{value: 'chat_issues', label: 'Chat no funciona'},
			{value: 'prescription_problem', label: 'No se pudo realizar receta'},
			{value: 'order_problem', label: 'No se pudo realizar orden'},
			{value: 'performance_issues', label: 'La aplicación anduvo lenta'},
			{value: 'video_issues', label: 'Llamada sin audio y video'},
			{value: 'other', label: 'Otro problema'},
		])
	}
	
	const handleSelectProblemReport = (e) => {
		if (!e || e.length === 0) {
			dispatch({ 
				type: 'REPORT_PROBLEM', 
				payload: {
					tags: [], 
					otherIssue: ficha.technicalIssue.otherIssue,
				}
			});
			setIsTagsEmpty(true)
			return;
		}

		let technicalIssues = [];
		e.forEach(element => {
			technicalIssues.push(element.value);
		});
		
		dispatch({ 
			type: 'REPORT_PROBLEM', 
			payload: {
				tags: technicalIssues,
				otherIssue: ficha.technicalIssue.otherIssue,
			}
		});
		setIsTagsEmpty(false)
	};

	const handleOtherIssue = (value) => {
		if(value) {
			dispatch({ type: 'REPORT_PROBLEM', payload: {
				tags: ficha.technicalIssue.tags,
				otherIssue: value,
			} })
		}
	}

	return (
		<>
			{/* <label>Reporte de problemas técnicos</label> */}
			<Select
				autoFocus
				isClearable
				isSearchable={true}
				maxMenuHeight={250}
				menuPlacement="bottom"
				isMulti={true}
				onChange={handleSelectProblemReport}
				options={renderOptions()}
				placeholder='Seleccionar incidente'
				styles={customStyles}
			/>
			{ficha.technicalIssue.tags.includes('other') &&
			<>
			<label>Describa el problema técnico</label>
			<Textarea
				id='technicalProblem'
				placeholder={'Problema técnico'}
				style={{width: '100%'}}
				action={(e) => handleOtherIssue(e.target.value)}
				value={ficha.technicalIssue.otherIssue}/>
			</>
			}
			
		</>
	)
}

export default ProblemReport