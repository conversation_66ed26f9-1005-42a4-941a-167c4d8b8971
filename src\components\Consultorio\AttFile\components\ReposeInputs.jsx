import React from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import moment from 'moment'

const Repose = () => {
	const dispatch = useAppDispatch()
	const ficha = useAppSelector((state) => state.ficha)
	
	return (
		<div>
			{ficha.rest_start && ficha.rest_end && <span className={ficha.rest_start !== '' && ficha.rest_end !== '' ? 'attFile__advice confirm' : 'attFile__advice warning'}>
			Tiempo de reposo {moment.duration(
					moment(ficha.rest_start)
						.diff(moment(ficha.rest_end).add(1, 'day').format('YYYY-MM-DD')))
					.humanize()}
			</span>}
			<div >
				<div>
					<small>Desde: </small>
					<input
						type='date'
						name='destinations'
						id='destinations'
						onChange={e => dispatch({ type: 'REST_WRITE_START', payload: e.target.value })}
						value={ficha.rest_start}
						max={ficha.rest_end && moment(ficha.rest_end).format('YYYY-MM-DD') }
						required
					/>
				</div>
				<div>
					<small>Hasta: </small>
					<input
						type='date'
						name='destinations'
						id='destinations'
						onChange={e => dispatch({ type: 'REST_WRITE_END', payload: e.target.value })}
						value={ficha.rest_end}
						min={ficha.rest_start && moment(ficha.rest_start).format('YYYY-MM-DD')}
						required
					/>
				</div>
			</div>
		</div>)
}

export default Repose