import React from 'react'
import ReposeInputs from './ReposeInputs'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import { useSearchParams } from 'next/navigation'
import { useAssignationFormData } from '@/cookies/AssignationFormDataContext'
import { setFormFieldCookie, FormFieldType } from '@/cookies/AssignationFormData'
import { RadioRoot, RadioItem } from "@umahealth/occipital/client"

interface ReposoOption {
  value: string;
  label: string;
}

export default function Reposo({ value } : { value : string}) {
  const { profile } = useAppSelector((state) => state.user)
  const dispatch = useAppDispatch()
  const searchParams = useSearchParams();
  const specialty = searchParams.get('specialty')
  
  // Usar el contexto de datos de formulario
  const { setFormField, assignationId } = useAssignationFormData()

  function renderOptions(): ReposoOption[] {
    const isOccupationalDoctor = ['medicinalaboral', 'psiquiatria'].includes(profile.matricula_especialidad) && specialty !== 'bag'
    if (isOccupationalDoctor && process.env.NEXT_PUBLIC_COUNTRY === 'AR') {
      return ([
        { value: '', label: ' - ' },
        { value: 'justificado', label: 'Reposo justificado' },
        { value: 'no justificado', label: 'Sin reposo' },
        { value: 'alta', label: 'Alta' }
      ])
    } else if (isOccupationalDoctor && process.env.NEXT_PUBLIC_COUNTRY === 'MX') {
      return ([
        { value: '', label: 'La consulta no justifica reposo' },
        { value: 'justificado', label: 'Con reposo' },
        { value: 'alta', label: 'Alta' }
      ])
    } else {
      return ([
        { value: 'alta', label: 'Alta' },
        { value: '24', label: 'Reposo 24hs' },
        { value: '48', label: 'Reposo 48hs' },
        { value: '72', label: 'Reposo 72hs' },
        { value: 'No', label: 'La consulta no justifica reposo' },
      ])
    }
  }
  
  // Guardar en cookies y actualizar el contexto
  const saveReposoCookie = async (value: string): Promise<void> => {
    if (assignationId && value) {
      try {
        // Actualizar el contexto
        setFormField('reposo', value)
        
        // También guardar en cookies para persistencia
        if (assignationId) {
          await setFormFieldCookie(assignationId, 'reposo' as FormFieldType, value)
        }
      } catch (error) {
        console.warn('Error al guardar reposo en cookies:', error)
      }
    }
  }
  
  const handleSelectReposo = (value: string): void => {
    dispatch({ type: 'REST_WRITE', payload: value })
    saveReposoCookie(value)
  }

  if(process.env.NEXT_PUBLIC_COUNTRY === 'MX' &&  profile.matricula_especialidad !== 'medicinalaboral') {
    return <></>
  }

  return (
    <form className="my-4">
      <RadioRoot 
        className="flex flex-col w-full gap-3" 
        defaultValue={value || ''}
        value={value}
        onValueChange={handleSelectReposo}
      >
        {renderOptions().map((option) => (
          <RadioItem 
            key={option.value}
            id={option.value} 
            className="bg-transparent border-none flex-row-reverse justify-end gap-2" 
            itemClassName="size-5" 
            indicatorClassName="size-4" 
            value={option.value}
          >
            {option.label}
          </RadioItem>
        ))}
      </RadioRoot>
      
      <div className='attFile__selects mt-3'>
        {value === 'justificado' && 
          <ReposeInputs />
        }
      </div>
    </form>
  )
}