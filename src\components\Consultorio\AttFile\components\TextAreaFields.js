import React from 'react'
import { useAppDispatch } from '@/store/hooks'
import { useTranslations } from 'next-intl'
import { TextArea } from '@/components/GeneralComponents/Input/Inputs'

const TextAreaFields = () => {
	const dispatch = useAppDispatch()
	const t = useTranslations('attention')
	const [treatment, setTreatment] = React.useState('')
	const [epicrisis, setEpicrisis] = React.useState('')

	const handleIndicaciones = () => {
		dispatch({ type: 'TREATMENT_WRITE', payload: treatment })
	}

	const handleEpicrisis = () => {
		dispatch({ type: 'EPICRISIS_WRITE', payload: epicrisis })
	}

	return (
		<>
			<label>{t('textarea-epicrisis_label')}</label>
			<TextArea
				placeholder={t('textarea-epicrisis_placeholder')}
				setTextareaValue={setEpicrisis}
				value={epicrisis}
				onBlur={handleEpicrisis}
				expandibleButton
			/>
			<label>{t('textarea-treatment_label')}</label>
			<TextArea
				placeholder={t('textarea-treatment_placeholder')}
				value={treatment}
				setTextareaValue={setTreatment}
				onBlur={handleIndicaciones}
				expandibleButton
			/>
		</>
	)
}

export default TextAreaFields