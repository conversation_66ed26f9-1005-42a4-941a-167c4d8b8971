import { store } from '@/store/configStore'
import swal from 'sweetalert'
import { AssignationFormDataType, PatientAddressType } from '@/cookies/AssignationFormDataContext'

// Interfaces para tipado
interface Patient {
  core_id?: string
  corporate_norm?: string
  padronCorporate?: string
  fullname?: string
  uid?: string
  dni?: string
  ws?: string
  dob?: string
  sex?: string
  address?: string
  piso?: string
  email?: string
}

interface DeliveryService {
  service: string
  status: string
}

interface ReduxState {
  queries: {
    patient: Patient
    deliveryService: DeliveryService
    attention?: {
      assignation_id?: string
    }
  }
  user: {
    currentUser: {
      getIdToken: () => Promise<string>
    }
  }
  selectedAtt?: {
    id?: string
  }
}

interface ValidationItem {
  condition: boolean
  message: string
  description: string
}



export function destinoHandler(value: string): void {
  const { dispatch } = store
  dispatch({ type: 'DESTINATION_WRITE', payload: value })
}

export const ambulanceAddressValidations = async (): Promise<boolean> => {
  const { dispatch, getState } = store
  const state = getState() as ReduxState
  const assignationId = state.selectedAtt?.id || state.queries?.attention?.assignation_id

  // Obtener los datos del contexto AssignationFormData
  const { getFormData } = await import('@/cookies/AssignationFormData')
  const formData: AssignationFormDataType = await getFormData(assignationId)

  console.log('formData', formData)
  const patientAddress: PatientAddressType = formData.patient_address || {}

  const validations: ValidationItem[] = [
    {
      condition: !patientAddress?.destination?.user_address || patientAddress.destination?.user_address === '',
      message: 'No se puede cerrar la consulta sin dirección',
      description: 'Debe ingresar la dirección del paciente para continuar',
    },
    {
      condition: !patientAddress?.destination?.user_ws || patientAddress.destination?.user_ws === '',
      message: 'No se puede cerrar la consulta sin teléfono',
      description: 'Debe ingresar el teléfono del paciente para continuar',
    }
  ]

  for (const validation of validations) {
    if (validation.condition) {
      await showValidationError(validation.message, validation.description, dispatch)
      return false
    }
  }

  return true
}

const showValidationError = async (title: string, text: string, dispatch: any): Promise<void> => {
  await swal(title, text, 'info')
  dispatch({ type: 'SET_LOADING', payload: false })
}

export const addressValidations = async (): Promise<boolean | void> => {
  const { dispatch, getState } = store
  const state = getState() as ReduxState
  const assignationId = state.selectedAtt?.id || state.queries?.attention?.assignation_id

  // Obtener los datos del contexto AssignationFormData
  const { getFormData } = await import('@/cookies/AssignationFormData')
  const formData = await getFormData(assignationId)
  const patientAddress = formData.patient_address || {}

  if (!patientAddress?.destination?.user_address) {
    swal(
      'No se puede cerrar la consulta sin dirección',
      'Debe ingresar la dirección del paciente para continuar',
      'info'
    )
    dispatch({ type: 'SET_LOADING', payload: false })
    return
  }
  if (!patientAddress.validAddress) {
    swal(
      'Fuera de zona de cobertura',
      'Nuestros servicios no alcanzan la direccion del paciente. Cerrar la consulta con destino final "En domicilio con instrucciones"',
      'info'
    )
    dispatch({ type: 'SET_LOADING', payload: false })
    return
  }

}
