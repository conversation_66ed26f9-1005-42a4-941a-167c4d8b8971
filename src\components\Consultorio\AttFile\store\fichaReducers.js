import * as fichaTypes from './fichaTypes'
import moment from 'moment'

export const initialFichaReducerState = {
	antecedentes: '',
	alerts: '',
	reason: '',
	epicrisis: '',
	diagnostic: '',
	treatment: '',
	notes: '',
	plan: '',
	rest: '',
	rest_start: moment().format('YYYY-MM-DD'),
	rest_end: '',
	destinofinal: '',
	referral: '',
	patientAddress: {
		destination: {
			user_address: '',
			user_floor: '',
			user_number: '',
			user_lat: 0,
			user_lon: 0,
			user_cp: '',
			user_region: '',
			user_locality: '',
			user_country: '',
			user_obs: ''
		},
		coords: {
			lat: '',
			lng: ''
		},
		validAddress: true,
		loadingAddress: false
	},
	doctorWebcamStreamOn: false,
	technicalIssue: {
		tags: [],
		otherIssue: ''
	}
}

export default function fichaReducers(state = initialFichaReducerState, action) {
	switch (action.type) {
	case fichaTypes.EPICRISIS_WRITE:
		return { ...state, epicrisis: action.payload }
	case fichaTypes.NOTE_WRITE:
		return {
			...state,
			notes: action.payload
		}
	case fichaTypes.REST_WRITE:
		return {
			...state,
			rest: action.payload
		}
	case fichaTypes.REST_WRITE_START:
		return {
			...state,
			rest_start: action.payload
		}
	case fichaTypes.REST_WRITE_END:
		return {
			...state,
			rest_end: action.payload
		}
	case fichaTypes.ANTECEDENTES_WRITE:
		return { ...state, antecedentes: action.payload }
	case fichaTypes.TREATMENT_WRITE:
		return { ...state, treatment: action.payload }
	case fichaTypes.DESTINATION_SELECT:
		return { ...state, destinofinal: action.payload }
	case fichaTypes.REFERRAL_SELECT:
	return { ...state, referral: action.payload }
	case fichaTypes.DIAGNOSTIC_WRITE:
		return { ...state, diagnostic: action.payload }
	case fichaTypes.REASON_WRITE:
		return { ...state, reason: action.payload }
	case fichaTypes.RESET_FICHA:
		return { ...initialFichaReducerState }
	case fichaTypes.DESTINATION_WRITE:
		return { ...state, destinofinal: action.payload }
	case fichaTypes.DESTINATION_WRITE_AND_CLEAN:
		return {
			...state,
			notes: state.notes, 
			destinofinal: action.payload,
			diagnostic: '', treatment: '', epicrisis: '', alerts: '',
			rest: '',
			rest_start: '',
			rest_end: '',
		}
	case fichaTypes.REPORT_PROBLEM:
		return { ...state, technicalIssue: action.payload }
	case fichaTypes.SET_DOCTOR_WEBCAM_STREAM_ON:
		return { ...state, doctorWebcamStreamOn: action.payload }
	case fichaTypes.SET_FICHA_CORPORATE:
		return { ...state, corporate: action.payload }
	case fichaTypes.SET_FICHA_PLAN:
		return { ...state, plan: action.payload }
	case fichaTypes.SET_FICHA_N_AFILIATE:
		return { ...state, n_afiliado: action.payload }
	case fichaTypes.SET_PATIENT_ADDRESS:
		return { ...state, patientAddress: action.payload}
	default:
		return state
	}
}