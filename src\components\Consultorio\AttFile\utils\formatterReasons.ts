export const formatterReasonsWithoutPoint = (reasons: string): string => {
  if (reasons) {
    const uniqueReasons = Array.from(
      new Set(reasons.split(".").map((reason) => reason.trim())),
    ).join(" - ");
    return uniqueReasons;
  } else return "no reason";
};

export const formatterReasonsWithPoint = (reasons: string): string => {
  if (reasons) {
    const uniqueReasons = Array.from(
      new Set(reasons.split(/-| - |- | -/g).map((reason) => reason.trim())),
    ).join(".");
    return uniqueReasons;
  } else return "no reason";
};
