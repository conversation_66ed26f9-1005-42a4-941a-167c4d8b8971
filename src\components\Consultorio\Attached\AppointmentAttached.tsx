/* eslint-disable no-mixed-spaces-and-tabs */
import React from 'react'
import FilesAttached from './Components/FilesAttached'
import { Button } from 'occipital-new'
import { IFile } from './interfaces'
import './styles/stylesAttached.scss'

interface IAppointmentAttachedProps {
	appointmentAttached: IFile[]
	goToIndex: () => void
}

const AppointmentAttached = ({ appointmentAttached, goToIndex }: IAppointmentAttachedProps) => {

	return (
		<>
			<div className="attached-title sectionTitle">
				<label>Archivos adjuntos de la consulta</label>
				<hr />
			</div>
			<div className="attached relative h-[600px]">
				<>
					<div className="files-list">
						{appointmentAttached?.length > 0 ?
							appointmentAttached.map((file: IFile) => {
								return <FilesAttached file={file} key={file.name} />
							})
							:
							<p>No hay archivos adjuntos</p>
						}
					</div>
				</>
				<Button
					occ_type='outlined'
					size='small'
					type='button'
					action={goToIndex}>
					Volver
				</Button>
			</div>
		</>
	)
}

export default AppointmentAttached