import React, {useState} from 'react'
import { Icon, Spacer } from '@umahealth/occipital-ui'
import { downloadImage } from '../Utils/downloadImage'
import Image from 'next/image'
import { IFile } from '../interfaces'
import '../styles/stylesAttached.scss'

interface IFileViewProps {
	att: {
		file: IFile
	}
}

function FileView({att}: IFileViewProps) {
	const [visible, setVisible] = useState(true)

	const fileUrl = att?.file?.url
	const fileType = att?.file?.metadata?.contentType
	const fileName = att?.file?.name || att?.file?.metadata?.name
	
	return (
		<>
			{visible && <div
				className='absolute top-4 left-4 z-30 bg-slate-50 rounded-full p-2'>
				<Icon
					name='arrowBack' 
					color='primary' 
					isClickable={true}
					action={() => setVisible(!visible)} />
			</div>}
			{visible && fileType === 'application/pdf' && (
				<div className='viewerChild-container' >
						<div className='iframe-container'>
							<iframe 
								src={fileUrl} 
								title={fileUrl} 
								frameBorder={'0'}  
							/>
						</div>
						<Spacer value= '16px' direction='vertical'/>
						<div className='absolute top-4 left-16 z-30 bg-slate-50 rounded-full p-2'>
							<Icon
								name='download' 
								color='primary' 
								isClickable={true}
								action={() => downloadImage(fileUrl as string, `${fileName}`)}
							/>    
						</div>
				</div>  
			)}

			{visible && fileType !== 'application/pdf' && (
				<div className='viewerChild-container' >
						<Image 
							src={fileUrl as string} 
							alt='Archivo adjunto'
							fill
							style={{ objectFit: 'cover' }}
						/>
						<Spacer value= '8px' direction='vertical'/>
						<div className='absolute top-4 left-16 z-30 bg-slate-50 rounded-full p-2'>
							<Icon
								name='download' 
								color='primary' 
								isClickable={true}
								action={() => downloadImage(fileUrl as string, `${fileName}`)}
							/>    
						</div>
				</div>  
			)}           
		</>
	)
}

export default FileView