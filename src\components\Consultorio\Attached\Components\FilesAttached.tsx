import React, { <PERSON>actNode, useState } from 'react'
import { <PERSON><PERSON>, I<PERSON>, Spacer, Paragraph, Loader } from '@umahealth/occipital-ui'
import moment from 'moment'
import FileView from './FileView'
import { IFile } from '../Utils/interfaces'
import '../styles/stylesAttached.scss'

function FilesAttached(file: { file: IFile }): ReactNode {
	const [visible, setVisible] = useState(false)
	const [loading, setLoading] = useState(false)

	const fileVisible = () => {
		setLoading(true)
		if (file) {
			setVisible(!visible)
			setLoading(false)
		}
	}

	const fileDate = moment(file?.file?.metadata?.timeCreated).format('DD/MM/YYYY HH:mm')

	function getFileName() {
		const maxLength = 20;
		let fileName = '';
	
		if (file?.file?.name) {
			fileName = file.file.name;
		} else if (file?.file?.metadata?.name) {
			fileName = file.file.metadata.name;
		} else {
			return 'Archivo';
		}
	
		if (fileName.length > maxLength) {
			const extension = fileName.split('.').pop();
			const nameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.'));
			return `${nameWithoutExtension.substring(0, 10)}...${nameWithoutExtension.slice(-10)}.${extension}`;
		}
	
		return fileName;
	}

	return (
		<>
			<div className='file-row'>
				<div className='file-name'>
					<Icon
						color="primary"
						name="file"
						size="sm"
						aria-hidden='true'
					/>
					<Spacer value='8px' />
					<Paragraph>{`${getFileName()} - ${fileDate}`} Hs.</Paragraph>
				</div>
				<Button
					key={file?.file?.date}
					action={fileVisible}
					color="primary"
					size="small"
					textSize="xs"
					type="text"
				>Ver</Button>
				{loading ? (
					<div className='loader-container' >
						<Loader />
					</div>) : null}
				{visible && !loading ? <FileView att={file} /> : null}
			</div>
			<hr className='file-line' />
		</>
	)
}

export default FilesAttached