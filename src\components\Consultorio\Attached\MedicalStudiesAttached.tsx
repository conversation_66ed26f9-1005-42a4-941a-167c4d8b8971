/* eslint-disable no-mixed-spaces-and-tabs */
import React from 'react'
import { DocumentData } from '@/config/firebase'
import { Button } from 'occipital-new'
import FilesAttached from './Components/FilesAttached'
import './styles/stylesAttached.scss'

interface IMedicalStudiesAttachedProps {
	patientMedicalStudies: DocumentData[]
	goToIndex: () => void
}

const MedicalStudiesAttached = ({ patientMedicalStudies, goToIndex }: IMedicalStudiesAttachedProps) => {

	return (
		<>
			<div className="attached-title sectionTitle">
				<label>Repositorio de estudios</label>
				<hr />
			</div>
			<div className="attached relative h-[600px]">
				<>
					<div className="files-list">
						{patientMedicalStudies?.length > 0 ?
							patientMedicalStudies?.map(ms => {
								const fileData = {
									url: ms.data.file,
									name: ms.data.studieType,
									date: ms.timestamps.dt_create
								}
								return <FilesAttached file={fileData} key={ms.data.date} />
							}
							)
							:
							<p>No hay archivos cargados</p>
						}
					</div>
				</>
				<Button
					occ_type='outlined'
					size='small'
					type='button'
					action={goToIndex}>
					Volver
				</Button>
			</div>
		</>
	)
}

export default MedicalStudiesAttached