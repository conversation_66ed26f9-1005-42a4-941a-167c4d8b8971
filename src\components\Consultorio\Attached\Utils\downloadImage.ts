export function downloadImage(url: string, name: string): void {
    fetch(url)
        .then(resp => resp.blob())
        .then(blob => {
            if (typeof window !== 'undefined') {  // Verifica que estés en un entorno del navegador
                const blobUrl = window.URL.createObjectURL(blob); // Usa `blobUrl` en lugar de volver a usar `url`
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = blobUrl;
                a.download = name;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(blobUrl);
            }
        })
        .catch(() => alert('Ocurrió un error y el archivo no pudo ser descargado'));
}
