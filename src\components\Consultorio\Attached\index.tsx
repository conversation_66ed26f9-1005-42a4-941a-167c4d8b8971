/* eslint-disable no-mixed-spaces-and-tabs */
import React, { useState } from 'react'
import { AiOutlineRight, AiOutlineMedicineBox, AiOutlinePaperClip, AiOutlineReload } from 'react-icons/ai'
import { Title } from 'occipital-new'
import { Loader } from '@umahealth/occipital-ui'
import usePatientMedicalStudies from '@/services/reactQuery/usePatientMedicalStudies'
import style from './styles/attachedSelect.module.scss'
import AppointmentAttached from './AppointmentAttached'
import MedicalStudiesAttached from './MedicalStudiesAttached'
import useAttachedFromDocumentReferences from '@/services/reactQuery/useAttachedFromDocumentReferences'
import {useCurrentAppointment, attType} from '@/services/reactQuery/useCurrentAppointment'
import { useSearchParams } from 'next/navigation'
import { queryClient } from '@/providers/QueryClient'


const Attached = () => {
  const searchParams = useSearchParams();
  const assignationId = searchParams.get('assignationId') as string
  const attTypeParams = searchParams.get('attType') as attType
  const specialty = searchParams.get('specialty') as string
  const patientUid = searchParams.get('patientUid') as string
  const isBag = specialty === 'bag'
  const attType = isBag ? 'bag' : attTypeParams
  const patientMedicalStudies = usePatientMedicalStudies(patientUid)
  const currentAppointment = useCurrentAppointment(attType, assignationId)
  const attachedFromDocumentReferences = useAttachedFromDocumentReferences(currentAppointment.data?.documentReferences??[])
  const [section, setSection] = useState('index')
  const refreshAttached = () => {
    queryClient.invalidateQueries({ queryKey: ['useCurrentAppointment'] })
    attachedFromDocumentReferences.refetch()
  }

  const goToIndex = () => {
    setSection('index')
  }

  if (attachedFromDocumentReferences.isFetching || patientMedicalStudies.isFetching) return <div className={style.loaderContainer}>
    <Loader />
  </div>

  if (section === 'appointment' && true) return <AppointmentAttached appointmentAttached={attachedFromDocumentReferences?.data ?? []} goToIndex={goToIndex} />
  if (section === 'studies' && true) return <MedicalStudiesAttached patientMedicalStudies={patientMedicalStudies?.data ?? []} goToIndex={goToIndex} />

  return (
    <div>
      <label>Archivos adjuntos</label>
      <hr />
      {attachedFromDocumentReferences.data && attachedFromDocumentReferences.data?.length > 0 ?
        <div className={style.containerSection} onClick={() => setSection('appointment')} >
          <div className={style.sections} >
            <div className={style.infoSection}>
              <AiOutlinePaperClip size={'16px'} color='#333333' />
              <Title size='s' hierarchy='h2' weight='semibold' color='grey-1'>Adjuntos de la consulta</Title>
            </div>
            <AiOutlineRight size={'16px'} color='#333333' />
          </div>
        </div> : <p>No se encontraron archivos</p>}
        <button
          className={"bg-white hover:bg-gray-200 text-primary font-bold py-2 px-4 mb-3 rounded-full w-full flex justify-center items-center border border-gray-300"}
          onClick={refreshAttached}
        >
          <span className='mr-1'><AiOutlineReload size={'16px'} color='#3562FF'/></span> Actualizar Adjuntos
        </button>
      {(patientMedicalStudies.data && patientMedicalStudies.data?.length > 0) &&
        <div className={style.containerSection} onClick={() => setSection('studies')} >
          <div className={style.sections} >
            <div className={style.infoSection}>
              <AiOutlineMedicineBox size={'16px'} color='#333333' />
              <Title size='s' hierarchy='h2' weight='semibold' color='grey-1'>Repositorio de estudios</Title>
            </div>
            <AiOutlineRight size={'16px'} color='#333333' />
          </div>
        </div>}
    </div>
  )
}

export default Attached
