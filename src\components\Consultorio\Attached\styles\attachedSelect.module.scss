@use '../../../../styles/global/Vars.scss';

.sections{
    width: auto;
    background-color: Vars.$color-grey-6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    padding: 16px;
    margin: 8px 0;
    &:hover{
        cursor: pointer;
    }
}
.infoSection{
    display: flex;
    align-items: center;
    gap: 16px;
}

.containerSection{
    width: auto;
    align-items: center;
    gap: 16px;
}

.loaderContainer{
    width: 100%;
    display: flex;
    justify-content: center;
}