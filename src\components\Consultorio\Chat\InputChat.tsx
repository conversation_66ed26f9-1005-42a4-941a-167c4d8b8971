import React, { useContext } from 'react'
import { AttContext } from '@/components/AsyncAttention'
import { SubmitHandler, useForm } from 'react-hook-form'
import { TMessageData, useSendChatMessage } from './useSendChatMessage'
import { useAppSelector } from '@/store/hooks'
import { useSearchParams } from 'next/navigation'
import { errorHandler } from '@/config/stackdriver'

const InputChat = () => {
	const searchParams = useSearchParams()
	const dependant = searchParams.get('dependant')
	const assignationId = searchParams.get('assignationId')
	const currentAssignation = useAppSelector((state : any) => state.queries.currentAppointment)
	const patient = useAppSelector((state) => state.queries.patient)
	const asyncAttContext = useContext(AttContext)
	const {currentUser} = useAppSelector((state) => state.user)
	const asyncAtt = asyncAttContext?.attInView

	/** <PERSON>uan<PERSON> el doctor en el Chat de una consulta asnyc decide cerrar la consulta, pierde la capacidad de seguir mandando mensajes! */
	const disabled = asyncAttContext?.attInView && asyncAttContext.attInView.state === 'PENDING_DONE'

	const { register, handleSubmit, reset, setError, formState: { errors } } = useForm<{message : string}>()

	const sendMessageMutation = useSendChatMessage()

	const onSubmit : SubmitHandler<{
    message: string;
}> = (messageForm) => {

	
	let sendChatMessageData : TMessageData 

	if(asyncAtt){
		sendChatMessageData = {
			assignation_id: asyncAtt?.assignation_id,
			dependant_uid: asyncAtt?.patient?.uid_dependant === 'false' || asyncAtt.patient?.uid_dependant === undefined  ? false : asyncAtt.patient?.uid_dependant,
			doctorUid: currentUser.uid,
			uid: asyncAtt.patient?.uid,
			module: 'async',
			text: messageForm.message
		}
	} else {
		sendChatMessageData = {
			assignation_id: assignationId || (currentAssignation && currentAssignation?.assignation_id),
			dependant_uid: dependant === 'false' || dependant === null ? false : dependant,
			doctorUid: currentUser.uid,
			uid: (patient.uid || currentAssignation.patient.uid),
			module: 'chat',
			text: messageForm.message
		}
	}

	if (!sendChatMessageData.assignation_id){
		setError('message', {type: 'custom', message: `No se logro encontrar el id de la consulta, vuelva a intentar mandar el mensaje`})
		return
	}

	if (!sendChatMessageData.doctorUid){
		setError('message', {type: 'custom', message: `No se logro encontrar el uid del doctor, vuelva a intentar mandar el mensaje`})
		return
	}

	if (!sendChatMessageData.uid){
		setError('message', {type: 'custom', message: `No se logro encontrar el uid del paciente, vuelva a intentar mandar el mensaje`})
		return
	}

	if (messageForm.message === ''){
		setError('message', {type: 'custom', message: `No puede enviar un mensaje vacío`})
		return
	}

	sendMessageMutation.mutate(sendChatMessageData, {
		onSuccess: () => {
			reset()
		},
		onError: (error) => {
			setError('message', {type: 'custom', message: error.message})
			errorHandler?.report(`Error enviando mensaje del módulo ${asyncAtt ? 'chat async' : 'chat normal'} de ${currentUser.uid} : ${error.message}`)
		}
	})
}

	return (
		<div className="block  w-full bg-grey-1 max-h-40">
			{errors?.message && <label id='errorLabel' className="text-white m-4">{errors?.message?.message}</label>}
			<form className="flex w-full h-[62px] justify-between items-center" aria-describedby='errorLabel' onSubmit={handleSubmit(onSubmit)}>
				<input 
					onMouseDown={(e) => e.stopPropagation()} //detiene la propagacion del evento disparado por la libreria react-draggable y así evita que pierda el foco el input
					className={errors?.message ? "w-full h-[35px] mr-[12px] ml-[16px] pl-[12px] rounded-[6px] bg-grey-1 border-0 text-error focus:border-0 focus:border-grey-1" : "w-full h-[35px] mr-[12px] ml-[16px] pl-[12px] rounded-[6px] bg-grey-1 border-0 text-white focus:border-0 focus:border-grey-1"}
					placeholder="Escribe un mensaje al paciente aquí" 
					{...register('message')}
				/>
				<button type='submit' aria-label='Enviar' disabled={disabled} className={`flex bg-transparent border border-transparent mr-[12px] ${disabled && "cursor-not-allowed"}`}>
					<svg aria-hidden viewBox="0 0 24 24" width="24" height="24">
						<path fill="white" d="M1.101 21.757 23.8 12.028 1.101 2.3l.011 7.912 13.623 1.816-13.623 1.817-.011 7.912z"></path>
					</svg>
				</button>
			</form>
		</div>
	)
}

export default InputChat
