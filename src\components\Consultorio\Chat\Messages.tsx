import React, { Fragment, useContext, useEffect } from 'react'
import { useAppDispatch } from '@/store/hooks'
import { useSearchParams } from 'next/navigation'
import { Loader } from '@umahealth/occipital-ui'
import { useAppSelector } from '@/store/hooks/useAppSelector'
import { AttContext } from '@/components/AsyncAttention'
import { setMessageAsViewed } from './utils/setMessageAsViewed'
import { useOnSnapshot } from '@/hooks/useOnSnapshot'
import { where } from '@/config/firebase'
import { IChatMessages } from '@umahealth/entities'
import { SeparatorChat } from './SeparatorChat'

let ReactMarkdown: any
let rehypeRaw: any
(async () => {
  const moduleMarkdown = await import('react-markdown')
  const moduleRehypeRaw = await import('rehype-raw')
  ReactMarkdown = moduleMarkdown.default
  rehypeRaw = moduleRehypeRaw.default
})();

const Messages = () => {
	const asyncAtt = useContext(AttContext)
	const dispatch = useAppDispatch()
	const searchParams = useSearchParams()
    const assignationId = searchParams.get('assignationId')
	const currentAssignation = useAppSelector(state => state.queries.currentAtt)
	const paramsAssignationId = asyncAtt?.attInView ? asyncAtt.attInView.assignation_id : (assignationId && assignationId !== undefined ? assignationId.toString() : '')
	const userId = asyncAtt?.attInView ?  asyncAtt.attInView.patient?.uid : currentAssignation?.patient?.uid
	const { currentUser } = useAppSelector((state) => state.user)
	const messages = useOnSnapshot<IChatMessages>(`user/${userId}/chat_messages`, [where('assignation_id', '==', paramsAssignationId), where('rol', 'in', ['doctor', 'patient', 'UMA'])], (!!userId && !!paramsAssignationId), 'Messages')
	
	const sortMessages = React.useMemo(() => {
		const messagesSorted = messages?.sort((a, b) => (a?.timestamps?.dt_create && b?.timestamps?.dt_create) ? (a?.timestamps?.dt_create < b?.timestamps?.dt_create ? -1 : 1) : 1)
		dispatch({ type: 'SET_DATA_CHAT', payload: messagesSorted })
		return messagesSorted
	}, [dispatch, messages])

	useEffect(() => {
		setMessageAsViewed(asyncAtt?.attInView, currentUser)
	}, [messages])

	return (
		<div className="flex flex-col ">
			{!messages ?
				<Loader /> :
				(sortMessages?.length !== 0 ? sortMessages?.map((content, index) => (
					<Fragment key={`${index}-${new Date().getFullYear()}`}>
						{content.rol === 'doctor' &&
							<div className='bg-primary m-[2px_12px_0_0] max-w-[80%] rounded-[7.5px] ml-[10px] self-end p-[6px_7px_8px_9px]'>
								<div className="overflow-wrap break-word text-white">
									<ReactMarkdown
										linkTarget='_blank'
										rehypePlugins={[rehypeRaw]}
										remarkRehypeOptions={{ allowDangerousHtml: true }}
									>
										{content?.msg?.toString()}
									</ReactMarkdown>
								</div>
							</div>}
						{content.rol === 'UMA' && content.msg === 'separator' && <SeparatorChat />}	
						{content.rol === 'patient' && content?.type === 'file' ? <div className="bg-grey-1 max-w-[50%] rounded-[7.5px] ml-[10px] self-start p-[6px_7px_8px_9px] m-[2px_0_0_12px]">
							<div className="overflow-wrap break-word text-white flex flex-col items-center justify-center text-center">
								<iframe className='max-w-full' scrolling='no' src={`${content.msg.toString()}`}></iframe>
								<a 
									target='_blank' 
									className='text-white underline' 
									rel='noreferrer' href={`${content.msg.toString()}`}>
										Abrir Archivo
								</a>
							</div>
						</div> : content.rol === 'patient' &&
							<div className="bg-secondary max-w-[50%] rounded-[7.5px] ml-[10px] self-start p-[6px_7px_8px_9px] m-[2px_0_0_12px]">
								<div className="overflow-wrap break-word text-white">
									{content?.msg?.toString()}
								</div>
							</div>
						}
					</Fragment>
				)) : null)
			}
		</div>
	)
}

export default Messages