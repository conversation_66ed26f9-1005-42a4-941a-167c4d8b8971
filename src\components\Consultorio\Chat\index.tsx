import React, { useContext } from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import { Column, Paragraph, Spacer } from 'occipital-new'
import { ButtonIcon } from '@umahealth/occipital-ui'
import { AttContext } from '@/components/AsyncAttention'
import Image from 'next/image';
import moment from 'moment'
import ChatBox from './ChatBox'

const ChatContainer = () => {
	const asyncAtt = useContext(AttContext)
	const dispatch = useAppDispatch()
	const { patient } = useAppSelector((state: any) => state.queries)

	return (
		<div className={!asyncAtt?.attInView ? 'flex flex-col justify-start items-start flex-nowrap flex-grow bg-grey-1 m-0 p-0 shadow-[0_1px_4px_rgba(0,0,0,0.25),0_3px_6px_rgba(0,0,0,0.25)] absolute bg-[rgba(0,0,0,0.9)] rounded-[4px] resize overflow-hidden w-[512px]' : 'flex flex-col flex-grow resize-none w-full !important h-full !important bg-grey-1'}>
			<div className="flex items-center justify-between h-[50px] w-full relative bg-grey-1 font-bold m-0 p-[8px] rounded-[5px] text-left cursor-pointer">
				<div className="flex align-center">
					{asyncAtt?.attInView ?
						<Column>
							<Paragraph color='background-light' size='s' weight='regular'>
								{asyncAtt.attInView.patient?.fullname} ({moment().diff(asyncAtt.attInView.patient?.dob, 'years')} años)
							</Paragraph>
							<Spacer value='4px' direction='vertical' />
							<Paragraph color='background-light' size='xxs' weight='regular'>
								DNI: {asyncAtt.attInView.patient?.dni} - Sexo: {asyncAtt.attInView.patient?.sex}
							</Paragraph>
						</Column>
						:
						<>
							{patient.profile_pic !== undefined &&
								<Image src={patient.profile_pic} width={24} height={24} alt='Foto de perfil del paciente' />
							}
							<h2 className="text-white">Chat</h2>
						</>}
				</div>
				{!asyncAtt?.attInView && <div className="p-4" onClick={() => dispatch({ type: 'LAYOUT_ATT_CHAT', payload: false })}>
					<ButtonIcon name="close" size='xs' color='primary' />
				</div>
				}
			</div>
			<ChatBox />
		</div>
	)
}

export default ChatContainer