import { auth } from "@/config/firebase";
import { chat, chatAtt } from "@/config/endpoints";
import axios, { AxiosError } from "axios";
import { useMutation } from "react-query";

export interface IChatMessageData {
  assignation_id: string;
  country: string;
  doctorUid: string;
  dependant_uid: string | false;
  text: string;
  uid: string;
  rol?: "doctor";
}

export type TMessageData = Omit<IChatMessageData, "rol" | "country"> & {
  module: "async" | "chat";
};

/**
 * Este hook permite enviar un mensaje en los módulos de ChatAsync, como en las videoconsultas típicas (guardia y especialista)
 * aún si no difiere la data a pasarle, sí difiere el endpoint al cual hay que pegarle
 */
export function useSendChatMessage() {
  return useMutation<void, AxiosError, TMessageData>(
    ["sendMessage"],
    async ({ module, ...messageData }: TMessageData) => {
      const token = await auth?.currentUser?.getIdToken();
      const timestamp = new Date().toLocaleString();

      const headers = {
        Authorization: `Bearer ${token}`,
        uid: messageData.uid,
        "x-api-key": process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY,
      };

      if (module === "async") {
        const sendMessageData: IChatMessageData = {
          ...messageData,
          country: process.env.NEXT_PUBLIC_COUNTRY,
        };

        const result = await axios.post(
          chatAtt,
          { ...sendMessageData },
          { headers },
        );

        if (result.status === 200 || result.status === 201) {
          return;
        } else {
          throw new Error(
            `Hubo un problema enviando el mensaje,  date: ${timestamp}`,
          );
        }
      }

      const sendMessageData: IChatMessageData = {
        ...messageData,
        rol: "doctor",
        country: process.env.NEXT_PUBLIC_COUNTRY,
      };

      const result = await axios.post(chat, sendMessageData, { headers });

      if (result.status === 200 || result.status === 201) {
        return;
      } else {
        throw new Error(
          `Hubo un problema enviando el mensaje date: ${timestamp}`,
        );
      }
    },
  );
}
