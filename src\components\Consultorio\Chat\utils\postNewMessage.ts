import axios from 'axios'
import { AttContext } from '@/components/AsyncAttention'
import { errorHandler } from '@/config/stackdriver'
import { useContext, useState } from 'react'
import { useAppSelector } from '@/store/hooks'
import swal from 'sweetalert'
import { chat, chatAtt } from '@/config/endpoints'
import { dependantUid } from '@umahealth/entities'
import { useSearchParams } from 'next/navigation'
import { auth } from '@/config/firebase'

interface DataToSaveI {
	assignation_id: string;
	country: string | undefined;
	doctorUid: string;
	dependant_uid: string[] | dependantUid | null | undefined;
	text: string;
	uid: string;
	rol?: string; // Propiedad opcional
	type: string;
}

const usePostNewMessage = () => {
	const asyncAttContext = useContext(AttContext)
	const [inputValue, setInputValue] = useState('')
	const [hasError, setHasError] = useState<boolean>(false)
	const {dni} = useAppSelector((state : any) => state.queries.patient)
	const searchParams = useSearchParams()
	const dependant = searchParams.get('dependant')
	const assignationId = searchParams.get('assignationId')
	const currentAssignation = useAppSelector((state : any) => state.queries.currentAppointment)
	const patient = useAppSelector((state : any) => state.queries.patient)

	async function postDataConversation() {
		const asyncAtt = asyncAttContext?.attInView
		const messageValue = inputValue
		setInputValue('')
		if (messageValue !== '' && asyncAtt ? asyncAtt.patient?.dni : dni) {
			const assignation_id = asyncAtt ? asyncAtt.assignation_id : assignationId || (currentAssignation && currentAssignation?.assignation_id) || currentAssignation?.appointments?.[0]['14']
			const dependant_uid = asyncAtt ? (asyncAtt.patient?.uid_dependant === 'false' ? false : asyncAtt.patient?.uid_dependant) : dependant === 'false' ? false : dependant
			const uid = asyncAtt ? asyncAtt.patient?.uid : (patient.uid || currentAssignation.patient.uid)

			if (!auth?.currentUser){
				return 
			}
			const dataToSave:DataToSaveI = {
				assignation_id,
				'country': process.env.NEXT_PUBLIC_COUNTRY,
				'doctorUid': auth?.currentUser?.uid,
				dependant_uid,
				'text': messageValue,
				uid,
				'rol': 'doctor',
				'type': 'message'
			}
			if(asyncAtt) delete dataToSave.rol
			const token = await auth?.currentUser?.getIdToken()
			const headers = { 
				'Authorization': `Bearer ${token}`,
				'uid': auth?.currentUser?.uid, 
			}
			axios.post(asyncAtt ? chatAtt : chat, dataToSave, {headers} )
				.then(() => {
					setHasError(false)
					setInputValue('')
				})
				.catch(err => {
					setHasError(true)
					setInputValue(messageValue)
					console.error('Transcription error', err)
					errorHandler?.report(err)
				})
		} else {
			swal('Aviso', 'Debe escribir un mensaje', 'warning')
		}
	}
	return {
		inputValue, setInputValue, postDataConversation, hasError
	}
}

export default usePostNewMessage