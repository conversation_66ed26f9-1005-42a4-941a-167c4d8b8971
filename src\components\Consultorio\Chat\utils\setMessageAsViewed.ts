import { IChatAttAppointment } from '@umahealth/entities'
import axios from 'axios'
import { errorHandler } from '@/config/stackdriver'
import { chatAtt_viewMessage } from '@/config/endpoints'
import { auth, Timestamp } from '@/config/firebase'

export async function setMessageAsViewed(attInView: IChatAttAppointment<Timestamp> | undefined, currentUser: any) {
	if (attInView) {
		const dataToSave = {
			assignation_id: attInView?.assignation_id,
			'country': process.env.NEXT_PUBLIC_COUNTRY,
			uid: attInView?.patient.uid
		}
		const token = await auth?.currentUser?.getIdToken()
		const headers = { 
			'Authorization': `Bearer ${token}`,
			'uid': currentUser.uid, 
		}
		axios.post(chatAtt_viewMessage, dataToSave, {headers} )
			.then().catch(err => {
				console.error('Set message as viewed error', err)
				errorHandler?.report(err)
			})
	}
}