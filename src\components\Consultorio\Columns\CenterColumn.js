import React from 'react';
import CloseAtt from './CloseAtt';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { useTranslations } from 'next-intl';
import { Spacer } from '@umahealth/occipital-ui';
import { FiActivity } from 'react-icons/fi';
import { FaNotesMedical, FaFileMedicalAlt } from 'react-icons/fa';
import { AiOutlineRobot } from 'react-icons/ai';
import VideoCall from '../VideoCall';
import AttachedFiles from '../VideoCall/AttachedFiles';
import { trackEventCenterButtonsClicks } from '@/events/videocallEvents';

const buttonStyles = 'text-primary border p-[8px] my-[5px] cursor-pointer font-bold flex items-center text-[0.9rem] transition-all duration-200 rounded-[24px] bg-white shadow-[7px_7px_14px_#cccccc,-7px_-7px_14px_#ffffff]';
const activeButtonStyles = 'text-primary border border-primary';

const CenterColumn = () => {
  const t = useTranslations('attention');
  const dispatch = useAppDispatch();
  const { leftColumn } = useAppSelector((state) => state.front.attLayout);
  const att_category = useAppSelector((state) => state.queries.currentAtt?.att_category);

  const buttons = [
    { type: 'prescriptions', icon: FaNotesMedical, text: 'prescriptions-btn' },
    { type: 'antecedentes', icon: FaFileMedicalAlt, text: 'antecedentes-btn' },
    { type: 'monitoring', icon: FiActivity, text: 'monitoring-btn' },
    { type: 'ai', icon: AiOutlineRobot, text: 'ai-btn' },
  ];

  const switchBetweenTabs = (type, payload) => {
    if (att_category ===  "CONSULTORIO") {
      trackEventCenterButtonsClicks(payload)
    }
    dispatch({ type, payload })
  }
  return (
    <div className='flex flex-col max-height-[93vh]'>
      <VideoCall />
      <Spacer direction="vertical" value="4px" />
      <CloseAtt />
      <Spacer direction="vertical" value="4px" />
      <div className="flex justify-between flex-col">
        {buttons.map((btn) => (
          <button
            key={btn.type}
            className={`${buttonStyles} ${leftColumn === btn.type ? activeButtonStyles : ''}`}
            onClick={() => switchBetweenTabs('LAYOUT_ATT_LEFT', btn.type)}
          >
            <btn.icon className="text-[1.2rem] mx-[10px]" aria-hidden='true' />
            {t(btn.text)}
          </button>
        ))}
        <AttachedFiles />
      </div>
    </div>
  );
};

export default CenterColumn;
