/* eslint-disable @typescript-eslint/no-non-null-assertion */
import React, { useState, useContext } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAttentionParameters } from '@/services/reactQuery/useAttentionParameters'
import {
  useCloseAttention,
  Patient,
  Profile,
  Prescriptions,
  PatientAddress
} from './hooks/useCloseAttLogic'
import { useValidateAppointmentClosure } from './hooks/useCloseAttValidations'
import { AppointmentRecommendationModal } from '@/storybook/components/AppointmentRecommendationModal/components/AppointmentRecommendationModal'
import { useGetShifts } from '@/components/Schedule/Utils/shifts'
import { useAppSelector } from '@/store/hooks'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { save_new_patient, close_chat } from '@/config/endpoints'
import axios, { AxiosError } from 'axios'
import { useResetFicha } from '@/components/Appointments/hooks'
import { QueryClient } from 'react-query'
import { errorHandler } from '@/config/stackdriver'
import CloseAppointmentModal, {
  ValidationCloseAppointmentModal,
} from '@/storybook/components/modals/CloseAppointmentModal/CloseAppointmentModal'
import { null_finalDestinations } from '@/config/finalDestinations'
import { SessionContext } from '../Telehealth/TelehealthRoom/SessionManager'
import usePostLogs from '@/services/reactQuery/Logs/usePostLogs'
import { DialogTriggerEndButton } from '@/modules/consultorio/presentation/components/EndButton/EndButton'
import { EndButtonModals } from '@/modules/consultorio/presentation/components/EndButton/EndButtonModals'
import { useAttFormValidation } from '@/modules/consultorio/presentation/components/AttFile/AttFormValidationContext'
import { getAccordionItemByValidation } from '@/utils/getAccordionItemByValidation'
import { CurrentTabContext } from '../Telehealth/hooks/useCurrentTab'
import { logErrorToServer } from '@/lib/utils'
import { appointmentStates } from '@umahealth/entities'
import { DialogContent, DialogDescription, DialogOverlay, DialogPortal, DialogRoot, DialogTitle } from '@umahealth/occipital/client';
import { Paragraph, Title } from '@umahealth/occipital';
import { SchedulingModal } from './SchedulingModalComponent/SchedulingModal'
import { useAssignationFormData } from '@/cookies/AssignationFormDataContext'


interface SaveNewPatientParams {
  patient?: {
    address?: any;
    email?: string;
    fullname?: string;
    uid?: string;
    ws?: string;
  };
  currentAssignation?: {
    patient?: {
      dni?: string;
      email?: string;
      fullname?: string;
      uid?: string;
      ws?: string;
    };
    address?: {
      destination?: {
        user_address?: string;
        user_floor?: string;
        user_number?: string;
        user_obs?: string;
        user_ws?: string;
      };
    }
  };
  destino_final?: string;
}

const saveNewPatient = async ({ patient, currentAssignation, destino_final }: SaveNewPatientParams) => {
  const token = await getFirebaseIdToken()

  // Generar la información de dirección para ambulancia
  const generateAddressNotes = () => {
    // Solo incluir notas con formato de dirección si es un cierre con ambulancia
    const isAmbulanceDestination = ["Evaluación en rojo", "Evaluación en verde VMD", "Evaluación en amarillo"].includes(destino_final || "");
    
    if (!isAmbulanceDestination || !currentAssignation?.address?.destination) return [];
    
    const address = currentAssignation.address.destination;
    if (!address?.user_address) return [];
    
    const addressNote = (
      `Dirección: ${address.user_address || ""} - ` +
      `Piso: ${address.user_floor || ""} - ` +
      `Departamento: ${address.user_number || ""} - ` +
      `Observaciones: ${address.user_obs || ""} - ` +
      `Teléfono: ${address.user_ws || ""}`
    );
    return [addressNote];
  };

  await axios.post(save_new_patient, {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: {
      address: patient?.address || currentAssignation?.address || '',
      dni: currentAssignation?.patient?.dni || '',
      email: patient?.email || currentAssignation?.patient?.email || '',
      fullname: patient?.fullname || currentAssignation?.patient?.fullname,
      notes: generateAddressNotes(),
      uid: currentAssignation?.patient?.uid || patient?.uid || '',
      user_email: patient?.email || currentAssignation?.patient?.email || '',
      ws: currentAssignation?.patient?.ws || '',
    },
  })
}


export default function CloseAtt() {
  const searchParams = useSearchParams()
  const queryClient = new QueryClient()
  const router = useRouter()
  const [endcallError, setEndcallError] = useState('')
  const { session } = useContext(SessionContext)
  const [isAppointmentFinishedModal, setIsAppointmentFinishedModal] = useState({open: false, message: ''})

  const dependant = searchParams.get('dependant') || ''
  const attType = searchParams.get('attType')
  const specialty = searchParams.get("specialty");
  const { resetFicha } = useResetFicha()
  const { patient, currentAtt, currentAppointment } = useAppSelector(
    (state) => state.queries
  )
  const { formData, assignationId } = useAssignationFormData()

  const profile = useAppSelector((state) => state.user.profile)
  const prescriptions = useAppSelector((state) => state?.prescriptions)
  const getDoctorShifts = useGetShifts()
  const { currentUser } = useAppSelector((state) => state.user)
  const { mutate } = usePostLogs(
    currentUser.uid ?? 'NO',
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    assignationId!,
    dependant
  )
  const { setValidations } = useAttFormValidation()
  const { setCurrentTabView } = useContext(CurrentTabContext)

  const [showEndCallModal, setShowEndCallModal] = useState(false)
  const [showEndAttentionModal, setShowEndAttentionModal] = useState(false)
  const [showSchedulingModal, setShowSchedulingModal] = useState(false)

  // La variable formattedPatientAddress se eliminó ya que su lógica se trasladó directamente a la llamada de useValidateAppointmentClosure
  
  // Convertimos patientAddress al formato exacto que espera useValidateAppointmentClosure
  // segun la interfaz Props en useCloseAttValidations.ts
  const validateAppointmentCierre = useValidateAppointmentClosure({
    profile: profile,
    mr: currentAtt?.mr,
    attType: attType ?? undefined,
    null_finalDestinations: null_finalDestinations,
    patientAddress: formData.patient_address ? {
      destination: {
        user_ws: formData.patient_address.destination?.user_ws || '',
        // Agregamos el indexer para satisfacer la interfaz
        ...((formData.patient_address.destination || {}) as { [key: string]: any })
      }
    } : undefined,
  })


  const isValidToClose = validateAppointmentCierre.listOfValidations.some(
    (validation) => validation.condition === true
  )
  // Puede cerrar la atención aunque las validaciones de is_null_final_destinations y prescription sean true dado que no son obligatorias, si el destino final es de la lista de is_null_final_destinations se avisa que los datos no se guardarán, si es de prescripciones se le pregunta si desea cerrar sin receta
  const canCloseAttention = !validateAppointmentCierre.listOfValidations.some(
    (validation) => validation.reference !== 'is_null_final_destinations' && 
                   validation.reference !== 'prescription' && 
                   validation.reference !== 'phone_number' && 
                   validation.condition === true
  )
  
  const [
    specialistAppointmentRecommendationModal,
    setSpecialistAppointmentRecommendationModal,
  ] = useState(false)

  const closeAppointmentRecommendationModal = () => {
    setSpecialistAppointmentRecommendationModal(false)
    router.push('/appointments')
  }

  const closeAttention = useCloseAttention({
    assignationId: assignationId!,
    attType: attType,
    dependant: dependant,
    prescriptions: prescriptions as unknown as Prescriptions,
    patient: patient as unknown as Patient,
    profile: profile as unknown as Profile,
    currentAssignation: currentAppointment,
    patientAddress: formData.patient_address as unknown as PatientAddress
  })

  const postDataAppointmentRecommendation = {
    providerUid: currentAtt?.provider?.uid,
    patientUid: currentAtt?.patient?.dependant_uid
      ? currentAtt?.patient?.dependant_uid
      : currentAtt?.patient?.uid,
    specialty: currentAtt?.especialidad,
  }

  const attention = useAttentionParameters()

  const handleEndVideoCall = async () => {
    try {
      if (!assignationId) return

      const closeChatData = {
        assignation_id: assignationId,
        uid: patient.core_id,
        provider_uid:
          profile.uid || profile.core_id || currentAppointment.patient.uid,
        type: 'online',
      }
      console.log('getToken')
      const token = await getFirebaseIdToken()
      const headers = {
        'Content-Type': 'Application/Json',
        Authorization: `Bearer ${token}`,
      }
      console.log('afterToken')
      session?.disconnect()
      await axios.post(close_chat, closeChatData, { headers })
      mutate({ events: 'providerFinishedCall' })
      setShowEndCallModal(false)
    } catch (err:any) {
      console.error('err',err)
      setEndcallError(err?.response?.data?.message || 'Ocurrió un error y no pudimos finalizar la consulta')
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      logErrorToServer(err, assignationId!, profile.uid, patient.uid, 'handleEndVideoCall')
      if (err) {
        errorHandler?.report('ERROR DISCONNECTING', err)
      }
    }
  }

  const handleEndAttention = () => {
    console.debug('closeAttention')
    // Se comprueba que el estado de la consulta no haya sido ya cerrado por el usuario o la barredora de consultas
    if (
      currentAppointment.state === 'USER_CANCEL' ||
      currentAppointment.state === 'DONE' ||
      currentAppointment.state === 'AUTOMATIC_CANCEL' ||
      currentAppointment.state === 'AUTOMATIC_CLOSE' ||
      currentAppointment.state === 'CANCEL_BY_ELAPSED_TIME' as appointmentStates
    ) {
      
      const message = currentAppointment.state === 'DONE' ? 'Esta consulta ya fue cerrada' :	'El paciente canceló la consulta'

      setIsAppointmentFinishedModal({open: true, message})

      setTimeout(() => {
        router.replace('/appointments')
      }, 3000);
      return 
    }

    if (localStorage.getItem(`transcription_${assignationId}`) !== null) {
      localStorage.removeItem(`transcription_${assignationId}`)
    }
    closeAttention.mutate(undefined, {
      onSuccess: async () => {
        console.debug('exito')
        queryClient.removeQueries('useAreMessages')

        if (localStorage.getItem('medikit_att_hash')) {
          localStorage.removeItem('medikit_att_hash')
        }

        if (!currentAtt?.att_category?.includes('GUARDIA')) {
          try {
            await saveNewPatient({
              currentAssignation: currentAppointment,
              patient: patient,
              destino_final: formData.destino_final
            })
          } catch (error) {
            const timestamp = new Date().toLocaleString()
            errorHandler?.report(
              `[SAVE NEW PATIENT] Error: ${error} - Timestamp: ${timestamp}`
            )
          }
        }
        setShowEndAttentionModal(false)
      if (currentAtt?.att_category?.includes('MI_ESPECIALISTA')) {
        const finalDestination = formData.destino_final;
        if(finalDestination === 'Indico seguimiento por consultorio externo') {
          setShowSchedulingModal(true)
        } else {
          setSpecialistAppointmentRecommendationModal(true)
        }
      } else {
        // Mostrar mensaje y redireccionar a appointments cuando no es consulta de especialista
        setIsAppointmentFinishedModal({
          open: true, 
          message: 'Consulta finalizada correctamente'
        });
        
        // Agregar pequeña pausa antes de la redirección para que el usuario pueda ver el mensaje
        setTimeout(() => {
          router.replace('/appointments');
        }, 2000);
      }  
      },
      onError: (error) => {
        const axiosError: AxiosError = error as AxiosError
        console.log(axiosError.response?.data.msg)
        console.log('onError handleEndAttention')
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        logErrorToServer(error, assignationId!, profile.uid, patient.uid, 'handleEndAttention')
        setEndcallError(axiosError.response?.data.msg || 'Ocurrió un error y no pudimos finalizar la consulta')
      },
    })
  }

  const handleRedirect = () => {
    if (!currentAtt?.att_category?.includes('MI_ESPECIALISTA')) {
      resetFicha()
      router.push('/appointments')
    }
  }

  const onClickCloseVideoCall = () => {
    setShowEndCallModal(true)
  }

  const onClickCloseAttention = async () => {

    if (!assignationId) return

  try {

    if (canCloseAttention) {
      setShowEndAttentionModal(true)
      setValidations({ active: false, tab: null, openAccordion: null })
    } else {
      setCurrentTabView("consulta")
      setValidations({
        active: true,
        tab: 'ficha',
        openAccordion: getAccordionItemByValidation(validateAppointmentCierre.listOfValidations),
      })
    }
  } catch (err) {
    console.error('Error fetching appointment logs', err)
    setShowEndAttentionModal(true)
  }
  }

  return (
    <>
          <DialogRoot>
            <DialogTriggerEndButton
              timeout={attention}
              dtStartChatAtt={false}
              timerEnabled={!attention.isLoading}
              isRedesignActive={true}
              onEndVideoCall={async () => await onClickCloseVideoCall()}
              onEndAttention={onClickCloseAttention}
            >
              Finalizar consulta
            </DialogTriggerEndButton>
            {isValidToClose && specialty !== 'aptofisico' ?  (
              <ValidationCloseAppointmentModal
                validations={validateAppointmentCierre.listOfValidations}
                onClickCloseAttention={handleEndAttention}
              />
            ) : closeAttention.error ? null : (
              <CloseAppointmentModal
                onClickCloseAttention={handleEndAttention}
              />
            )}
            {closeAttention.error && (
              <CloseAppointmentModal error={closeAttention.error} />
            )}
            <EndButtonModals
              showEndCallModal={showEndCallModal}
              showEndAttentionModal={showEndAttentionModal}
              onEndVideoCall={handleEndVideoCall}
              onEndAttention={handleEndAttention}
              endcallError={endcallError}
              onRedirect={handleRedirect}
              onCloseEndCallModal={() => setShowEndCallModal(false)}
              onCloseEndAttentionModal={() => setShowEndAttentionModal(false)}
              validateAppointmentCierre={validateAppointmentCierre.listOfValidations}
              isRedesignActive={true}
              attCategory={currentAtt?.att_category}
            />
          </DialogRoot>
      {specialistAppointmentRecommendationModal && (
        <AppointmentRecommendationModal
          isOpen={specialistAppointmentRecommendationModal}
          onClose={closeAppointmentRecommendationModal}
          hasAgenda={getDoctorShifts.data ? true : false}
          postData={postDataAppointmentRecommendation}
        />
      )}
      {showSchedulingModal && (
        <SchedulingModal
          showSchedulingModal={showSchedulingModal}
          setShowSchedulingModal={setShowSchedulingModal}
        />
      )}
      {/* Modal de consulta ya finalizada */}
			<DialogRoot open={isAppointmentFinishedModal.open} modal={true}>
				<DialogPortal>
					<DialogOverlay className='bg-black/30' />
					<DialogContent className="flex items-center flex-col p-8">
					<DialogTitle asChild>
					<Title
						hierarchy="h1"
						color="text-primary-800"
						size="text-l"
						weight="font-semibold"
						className="mb-4"
					>
						{isAppointmentFinishedModal.message}
					</Title>
					</DialogTitle>
					<DialogDescription asChild>
					<Paragraph className="text-center pb-6">
						Serás redirigido a la pantalla de consultas
					</Paragraph>
					</DialogDescription>
					</DialogContent>
				</DialogPortal>
			</DialogRoot>
    </>
  )
}
