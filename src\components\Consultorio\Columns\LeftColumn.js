import React, { useCallback } from 'react'
import { useAppSelector } from '@/store/hooks'
import { Spacer } from '@umahealth/occipital-ui'
import AiTools from '../AI'
import Attached from '../Attached/index'
import Notes from '../Notes'
import Prescriptions from '../Prescriptions'
import MedikitForm from '@/components/Consultorio/Prescriptions/Recipe/RecipeMX/ui/MedikitTokenForm.js'
import RecipeView from '@/components/Consultorio/Prescriptions/Recipe'
import Monitoring from '@/components/Consultorio/Monitoring/presentation/views/Monitoring'
import StudiesOrder from '@/components/Consultorio/Prescriptions/Studie'
import HistoryContainer from '@/components/MyPatients/presentation/ui/VerDetalles/History'
import Antecedentes from '@/components/MyPatients/presentation/ui/VerDetalles/Antecedentes'
import PrescriptionHistory from '@/components/MyPatients/presentation/ui/VerDetalles/Antecedentes/PrescriptionHistory'
import { FarmacoAttModules } from '@/components/Consultorio/FarmacoAtt/FarmacoAttModules'
import { isFarmatodo } from '@/config/endpoints'
import { RedirectDetailButton } from '../FarmacoAtt/RedirectDetailButton'
/**
 * Comentarios de eventos de PostHog para medir los clicks en las videollamadas.
 * 
 * Este archivo contiene los eventos que se registran en PostHog para el flujo de videollamada .
 * Sirve como referencia para cuando se quiera volver a medir.
 */

//import { trackEventClickToSwitchLeftColumn } from '@/events/videocallEvents'

const LeftColumn = () => {
	const { attLayout } = useAppSelector(state => state.front)

	const renderModule = useCallback(() => {
		switch (attLayout.leftColumn) {
			case 'ai':
				//trackEventClickToSwitchLeftColumn('ai')
				return <AiTools />
			case 'antecedentes':
				//trackEventClickToSwitchLeftColumn('antecedentes')
				return <Antecedentes eyeslash={'currentUser'} edit={true} hideLabel={true} showLabel={true} />
			case 'attached':
				//trackEventClickToSwitchLeftColumn('attached')
				return <Attached />
			case 'monitoring':
				//trackEventClickToSwitchLeftColumn('monitoring')
				return <Monitoring />	
			case 'medikit':
				//trackEventClickToSwitchLeftColumn('medikit')
				return <MedikitForm />
			case 'recipe':
				//trackEventClickToSwitchLeftColumn('recipe')
				return <RecipeView />
			case 'orders':
				//trackEventClickToSwitchLeftColumn('orders')
				return <StudiesOrder />
			case 'prescriptions':
				//trackEventClickToSwitchLeftColumn('prescriptions')
				return <Prescriptions />
			default:
				//trackEventClickToSwitchLeftColumn('default')
				return <Antecedentes eyeslash={'currentUser'} edit={true} hideLabel={true} showLabel={true} />
		}
	}, [attLayout.leftColumn])

	return (
		<div className="flex flex-col w-100">
			{attLayout.leftColumn === 'ai' ?
				<AiTools /> :
				<>
					{!isFarmatodo && <>
					<HistoryContainer />
					<Spacer direction="vertical" value="4px" />
					<PrescriptionHistory />
					<Spacer direction="vertical" value="4px" />
					</>}
					{!isFarmatodo ? renderModule() : <>
						<RedirectDetailButton />
						<FarmacoAttModules />
					</>}
					<Spacer direction="vertical" value="4px" />
					{!isFarmatodo && <Notes />}
				</>
			}
		</div>
	)
}

export default LeftColumn
