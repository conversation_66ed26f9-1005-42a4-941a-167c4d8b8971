import Select from 'react-select'
import { useAppDispatch } from '@/store/hooks'
import { AccordionContent, AccordionItem, AccordionTrigger, Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@umahealth/occipital/client';
import { InfoIcon } from 'lucide-react';
import { useEffect, useState } from 'react';

function RestDefaultComponent({ open }: {  open?: any }) {

    const dispatch = useAppDispatch()
    const [tooltipVisible, setTooltipVisible] = useState(false);

    useEffect(() => {
        //Para las obras sociales que no tienen habilitado el certificado de reposo, el campo de reposo es null y no puede ser completado 
        dispatch({ type: 'REST_WRITE', payload: null })
        return () => {
            //Para las obras sociales que tienen habilitado el certificado de reposo, el campo de reposo es vacío y debe ser completado por eso se lo setea a vacío al desmontar el componente
            dispatch({ type: 'REST_WRITE', payload: '' })
        }
    }, [])


    const defaultOption = { value: 'No', label: 'Sin reposo' }

    return <div className={`my-4 border extraThinBorder border-solid rounded-lg p-2 ${open.includes('item-4') ? 'border-secondary-500' : 'border-gray-200'}`}>
                <TooltipProvider>
                    <AccordionItem value="item-4">
                        <Tooltip delayDuration={0} open={tooltipVisible} onOpenChange={setTooltipVisible}>
                            <AccordionTrigger showArrowDown={false} className="py-2 w-96">
                                <div className='flex gap-2'>
                                    <span className='text-secondary-500 font-bold text-base ml-4 leading-5 tracking-wide'>Reposo</span>
                                    <TooltipTrigger >
                                        <InfoIcon className='text-grey-600' size={16} />
                                    </TooltipTrigger>
                                </div>
                                <TooltipContent className='mb-2' side='right' sideOffset={4}>
                                    <p className='font-normal'>La cobertura médica seleccionada por el paciente no admite reposos que requieran certificados.</p>
                                </TooltipContent>
                            </AccordionTrigger>
                            <AccordionContent>
                                <div className='py-1' onClick={() => setTooltipVisible(true)}>
                                    <Select
                                        className='my-2'
                                        maxMenuHeight={250}
                                        isDisabled={true}
                                        defaultValue={defaultOption}
                                    />
                                </div>
                            </AccordionContent>
                        </Tooltip>
                    </AccordionItem>
                </TooltipProvider>
            </div >
}

export default RestDefaultComponent;