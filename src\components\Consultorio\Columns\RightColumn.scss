@use '@/styles/global/Vars.scss';

.grid-column-right {
	display: flex;
	flex-direction: column;
	max-height: 93vh;
}

.tags-container {
	position: relative;
	height: 32vh;
	overflow: auto;
	.tags-title {
		background: Vars.$uma-primary;
		color: Vars.$white-color;
		font-weight: bold;
		font-size: 1rem;
		margin: 0;
		padding: 8px;
		border-radius: 5px;
		text-align: left;
		display: flex;
		justify-content: space-between;
		font-size: 16px;
		.tag-add {
			cursor: pointer;
			color: white;
			display: flex;
			align-items: center;
			&.tag-attached {
				cursor: auto;
			}
			.tag-addicon {
				margin-right: 10px;
				font-size: 1rem;
			}
			.tag-att {
				margin-right: 7px;
				font-size: 1.35rem;
				position: relative;
				top: -1px;
			}
		}
	}
	.tags-box {
		display: flex;
		flex-wrap: wrap;
		.tag-leyend {
			width: 100%;
			display: flex;
			justify-content: space-between;
			margin: 3px 0;
			padding: 2px 10px;
			font-size: 13px;
			font-weight: bold;
			box-shadow: Vars.$main-shadow;
			border-radius: 6px;

			svg {
				font-size: 15px;
			}
		}

		.tag-loadingMsg{
			padding: 8px;
			height: 100%;
			width: 100%;
			text-align: center;
			font-weight: bold;
			color: Vars.$uma-primary;
		}

		.assessment-container {
			width: 100%;
			background: Vars.$uma-primary;
			color: #51626d;
			margin: 5px 0;
			border-radius: 8px;
			padding: 5px 10px;

			.assessment-title {
				font-size: 17px;
				margin: 0;
				font-weight: bold;
			}
			.text {
				text-transform: capitalize;
				font-size: 15px;
				color: #000;
			}
		}
		.tag {
			background: Vars.$uma-primary;
			color: Vars.$white-color;
			font-size: 0.9rem;
			border-radius: 6px;
			margin: 3px;
			padding: 7px 7px;
			display: flex;
			align-items: center;
			&.motive {
				background-color: Vars.$whater-blue;
			}
			.tag-text {
				margin-right: 6px;
				line-height: 1;
				text-transform: capitalize;
			}
			.tag-delicon {
				font-size: 1rem;
				cursor: pointer;
				&:hover {
					color: rgb(228, 69, 6);
				}
			}
			.tag-confirmicon {
				font-size: 1rem;
				margin-right: 3px;
				cursor: pointer;
				&:hover {
					color: greenyellow;
				}
			}
		}
	}
}

.attFile__container {
	position: relative;
	font-weight: bold;
	height: 93vh;
	max-height: 93vh;
	width: 98%;
	label {
		display: block;
		margin: 2px;
		font-size: 0.9rem;
		color: #6f6f6f;
	}
	textarea {
		text-align: left;
		overflow-y: scroll;
		border: 1px solid Vars.$data-grey !important;
		height: 60px;
		&::-webkit-scrollbar-thumb {
			background-color: Vars.$primary-background;
			border-radius: 5px;
		}
		&::-webkit-scrollbar {
			width: 6px;
			background-color: transparent;
		}
		&::placeholder {
			font-size: 1rem;
			color: Vars.$data-grey;
		}
		&:focus {
			box-shadow: none;
			border: 2px solid Vars.$uma-primary-light !important;
		}
	}
	.attFile__selects {
		display: flex;
		justify-content: space-between;
		align-items: center;
		text-align: left;
		select {
			border: 1px solid Vars.$data-grey !important;
			background: none !important ;
			height: 35px;
			padding: 0;
			width: 100%;
			cursor: pointer;
			&:focus {
				border: 2px solid Vars.$uma-primary-light !important;
			}
		}
	}
	.attFile__advice {
		&.warning {
			color: Vars.$red-high;
		}
		&.confirm {
			color: Vars.$green-uma;
		}
	}

}

.domicilio__patient {
	position: relative;
	background: Vars.$white-color;
	overflow-y: auto;
	flex-direction: column;
	margin-top: 16px;
	.domicilio__addressType {
		display: flex;
		justify-content: space-around;
	}
	.domicilio__label{
		background: Vars.$primary-color;
		width: 100%;
		color: Vars.$white-color;
		display: block;
		padding: 8px;
		margin-bottom: 8px;
		font-weight: bold;
		border-radius: 4px;
	}
}

.domicilio_map_inputs {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	& > input[type="text"] {
		background: #fff !important;
		padding: 5px;
		width: 100%;
		margin-bottom: 10px;

		&:first-of-type {
			width: 100%;
			display: block;
		}
	}
}

.reasons-container{
	height: 64px;
	overflow-y: scroll;
	width: 100%;
}
