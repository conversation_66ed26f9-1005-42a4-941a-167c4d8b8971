'use client'

import React from 'react'
import moment from 'moment-timezone'
import PatientInfo from '../PatientInfo/PatientInfo'
import MotivoDeConsulta from '../AttFile/components/MotivoDeConsulta'
import './RightColumn.scss'
import { IAppointment, ICorporateServices, IPatient } from '@umahealth/entities'
import { RawTimestamp } from '@/components/Appointments/utils/filterMandatoryFields'
import AttForm from '@/modules/consultorio/presentation/components/AttFile/AttForm'

moment.locale('es')


const RightColumn = ({
  patient,
}: {
  assignation: IAppointment<RawTimestamp>
  patient: IPatient<RawTimestamp> & { n_afiliado?: string, plan?: string }
  activeServices: ICorporateServices
}) => {



  return (
    <div className="attFile__container">
      <PatientInfo
        patientInfo={{
          address: patient.address,
          chosenName: patient.chosenName,
          dni: patient.dni,
          email: patient.email,
          corporate: patient.corporate,
          dob: patient.dob,
          fullname: patient.fullname,
          pronouns: patient.pronouns,
          sex: patient.sex,
          n_afiliado: patient.n_afiliado,
          plan: patient.plan,
        }}
      />
      <MotivoDeConsulta />
      <div className="sectionTitle">Ficha de atención</div>
      {/* Utilizamos el componente AttForm directamente */}
      <AttForm />
    </div>
  )
}

export default RightColumn
