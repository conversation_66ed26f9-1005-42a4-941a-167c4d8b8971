import { Button, Paragraph, Title } from "@umahealth/occipital";
import { DialogContent, DialogDescription, DialogOverlay, DialogPortal, DialogRoot, DialogTitle } from "@umahealth/occipital/client";
import { useRouter } from "next/navigation";
export function SchedulingModal({ showSchedulingModal, setShowSchedulingModal }: { showSchedulingModal: boolean, setShowSchedulingModal: (showSchedulingModal: boolean) => void, }) {
  const router = useRouter()
  return (
    <DialogRoot open={showSchedulingModal}>
      <DialogPortal>
        <DialogOverlay className="bg-black/40 fixed inset-0" />
        <DialogContent className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
          <DialogTitle asChild>
            <Title
              hierarchy="h1"
              color="text-primary-800"
              size="text-l"
              weight="font-semibold"
              className="text-center mb-4"
            >
              ¿Deseas agendar un turno para el paciente?
            </Title>
          </DialogTitle>
          <DialogDescription asChild>
            <Paragraph className="text-center text-gray-600 mb-8">
              Puedes agendar un turno ahora o continuar sin agendar
            </Paragraph>
          </DialogDescription>
          <div className="flex flex-col gap-4 w-full">
            <Button
              type="button"
              variant="filled"
              color="secondary"
              className="w-full py-3"
              onClick={() => {
                console.log("agendando turno")
                setShowSchedulingModal(false)
                window.open('http://**************/schedule/e1b0c9dc-43ae-4e22-853c-2c018610ad5a/appointments/list?step=3&specialtyCode=394579002', '_blank')
                router.push("/appointments")
              }}
            >
              Agendar turno
            </Button>
            <Button
              type="button"
              variant="text"
              color="secondary"
              className="w-full py-3"
              onClick={() => {
                setShowSchedulingModal(false)
                router.push("/appointments")
              }}
            >
              Continuar sin agendar
            </Button>
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
}