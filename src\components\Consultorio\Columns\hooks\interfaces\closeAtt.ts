export interface ICloseAttData {
  doctorWebcamStreamOn?: boolean;
  event?: {
    appointment_path?: string;
    assignation_id?: string;
    type?: "onsite" | "online";
  };
  mr?: {
    alerts?: string;
    diagnostic?: string;
    epicrisis?: string;
    final_destination?: string;
    medical_history?: string;
    specialist_referral?: string;
    notes?: string;
    rest?: string;
    treatment?: string;
  };
  orders?: string[]; // Assuming orders is an array of strings (study IDs?)
  patient?: {
    affiliate_number?: string;
    corporate?: string;
    dependantUid?: string;
    isdependant: boolean;
    plan?: string;
    uid: string;
    user_email?: string;
  };
  prescriptions?: string[]; // Assuming prescriptions is an array of strings (medicine names?)
  provider: {
    uid: string;
  };
  technicalIssue?: string;
  transcription?: string;
}