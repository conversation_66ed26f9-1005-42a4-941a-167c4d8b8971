import { close_att } from '@/config/endpoints'
import axios from 'axios'
import { useMutation } from 'react-query'
import { ICloseAttData } from './interfaces/closeAtt'


export async function closeAtt (closeAttData: ICloseAttData) {
    const headers = {
      "Content-Type": "application/json",
    };

    return await axios.post(close_att, closeAttData, {
      headers,
      timeout: 60000,
    });
  }

const useCloseAtt = () => {

return useMutation(["close Att"], closeAtt);
}

export default useCloseAtt