import { useAppDispatch } from "@/store/hooks";
import { errorHand<PERSON> } from "@/config/stackdriver";
import swal from "sweetalert";
import { closeAtt } from "./useCloseAtt";
import { handleSecurityHashMedikit } from "@/components/Consultorio/Prescriptions/Recipe/RecipeMX/medikitHelpers";
import * as ordersActions from "@/components/Consultorio/Prescriptions/store/prescriptionsActions";
import {
  addressValidations,
  ambulanceAddressValidations,
} from "@/components/Consultorio/AttFile/store/fichaActions";
import { useMutation } from "@tanstack/react-query";
import { logErrorToServer } from "@/lib/utils";
import { useAssignationFormData } from "@/cookies/AssignationFormDataContext";
import { ICloseAttData } from "./interfaces/closeAtt";
import { IAppointment } from "@umahealth/entities";
import { updateInvitationByAssignation } from "@/services/requests/updateInvitationByAssignation";

const DESTINATIONS_WITHOUT_CONNECTION = [
  "Paciente ausente",
  "Anula el paciente",
  "Anula por falla de conexión",
];

interface RestParams {
  rest: string | undefined;
  rest_end: string | undefined;
  rest_start: string | undefined;
  destinofinal: string | undefined;
}

const generateRestString = ({ rest, rest_end, rest_start, destinofinal }: RestParams): string => {
  if (
    rest === "justificado" &&
    destinofinal &&
    !DESTINATIONS_WITHOUT_CONNECTION.includes(destinofinal)
  ) {
    return `${rest} ${rest_end ? `//${rest_start}//${rest_end} ` : ""}`;
  }
  // Si rest es null en este punto significa que proviene de una OS que no tiene habilitado el reposo por lo que se graba en bbdd con vacío ("")
  return rest || "";
};

export interface PrescriptionRecipe {
  medicines: Array<string>;
}

export interface Prescriptions {
  token_consulta_medikit?: string;
  signature_medikit?: string;
  recipes?: PrescriptionRecipe;
  orderStudies?: Array<string>;
}

interface MedikitValidationsParams {
  prescriptions: Prescriptions;
  dispatch: ReturnType<typeof useAppDispatch>;
}

const handleMedikitValidations = async ({ prescriptions, dispatch }: MedikitValidationsParams): Promise<void> => {
  const { token_consulta_medikit, recipes, signature_medikit } = prescriptions;

  if (
    token_consulta_medikit && recipes?.medicines?.length &&
    recipes?.medicines?.length > 0 &&
    !signature_medikit
  ) {
    await swal({
      title: "Error.",
      text: "Ha comenzado una receta pero no la ha firmado. Fírmela para poder finalizar la atención",
      icon: "warning",
    });

    dispatch({
      type: "SET_MODAL_IN_ATTENTION",
      payload: { state: true, content: "recipe" },
    });
    throw new Error("Recipe not signed");
  }

  dispatch({ type: "LAYOUT_ATT_CHAT", payload: false });
  dispatch({ type: "SET_LOADING", payload: true });

  try {
    const secHash = await handleSecurityHashMedikit();
    if (secHash) ordersActions?.setSendedRecipe();
  } catch (error) {
    errorHandler.report(error);
    ordersActions.resetRecipe();
    dispatch({ type: "SET_LOADING", payload: false });
    await swal({
      title: "Error",
      text: "Ocurrió un error inesperado al intentar generar la receta",
      icon: "warning",
    });
    throw error;
  }
};

export interface Patient {
  uid: string;
  email?: string;
  [key: string]: any; // Para compatibilidad con los campos dinámicos del paciente
}


export interface Profile {
  uid: string;
  [key: string]: any; // Para compatibilidad con los campos adicionales del perfil
}


export interface PatientAddress {
  destination?: {
    user_address?: string;
    user_floor?: string;
    user_number?: string;
    user_obs?: string;
    user_ws?: string;
    user_lat?: number | string;
    user_lon?: number | string;
    user_cp?: string;
    user_locality?: string;
    user_region?: string;
    user_country?: string;
    [key: string]: any;
  };
  coords?: {
    lat: number;
    lng: number;
  };
  validAddress?: boolean;
  [key: string]: any;
}

interface UseCloseAttentionParams {
  assignationId: string;
  attType: string|null;
  dependant: string;
  prescriptions: Prescriptions;
  patient: Patient;
  profile: Profile;
  currentAssignation: IAppointment;
  patientAddress?: PatientAddress;
  doctorWebcamStreamOn?: boolean;
  technicalIssue?: string;
}

export const useCloseAttention = ({
  assignationId,
  attType,
  dependant,
  prescriptions,
  patient,
  profile,
  currentAssignation,
  patientAddress,
  doctorWebcamStreamOn,
  technicalIssue
}: UseCloseAttentionParams) => {
  const dispatch = useAppDispatch();
  const { formData } = useAssignationFormData();

  const generatePatientAddress = () => {
    // Solo generar dirección formateada para destinos de ambulancia
    const needsAmbulance = [
      "Evaluación en rojo", 
      "Evaluación en verde VMD", 
      "Evaluación en amarillo"
    ].includes(formData.destino_final || "");
    
    if (!needsAmbulance) return '';
    
    // Asegurar que patientAddress exista y tenga datos
    const addressData = patientAddress?.destination || {};
    
    // Verificar si hay dirección y teléfono
    if (!addressData.user_address) return 'Dirección no especificada';
    
    // Construir string formateado
    const addressParts = [];
    
    // Agregar dirección principal
    addressParts.push(`Dirección: ${addressData.user_address || 'No especificada'}`);
    
    // Agregar piso y departamento si es necesario
    if (addressData.user_floor || addressData.user_number) {
      const floorDept = [
        addressData.user_floor ? `Piso: ${addressData.user_floor}` : '',
        addressData.user_number ? `Depto: ${addressData.user_number}` : ''
      ].filter(Boolean).join(', ');
      
      if (floorDept) addressParts.push(floorDept);
    }
    
    // Agregar observaciones si existen
    if (addressData.user_obs) {
      addressParts.push(`Observaciones: ${addressData.user_obs}`);
    }
    
    // Agregar teléfono si existe
    if (addressData.user_ws) {
      addressParts.push(`Teléfono: ${addressData.user_ws}`);
    }
    
    return addressParts.join(' | ');
  };

  return useMutation({
    mutationFn: async () => {
      if (
        process.env.NEXT_PUBLIC_COUNTRY === "MX" &&
        prescriptions.token_consulta_medikit &&
        prescriptions.signature_medikit &&
        prescriptions.recipes?.medicines?.length &&
        prescriptions.recipes?.medicines?.length > 0
      ) {
        await handleMedikitValidations({
          prescriptions: prescriptions,
          dispatch: dispatch
        });
      }

      let monitoreoSuccess : boolean | undefined | void = true;

      if (formData.destino_final === "En domicilio con monitoreo") {
        monitoreoSuccess = await addressValidations();
      } else if (
        [
          "Evaluación en rojo",
          "Evaluación en verde VMD",
          "Evaluación en amarillo",
        ].includes(formData.destino_final || "")
      ) {
        monitoreoSuccess = await ambulanceAddressValidations();
      }

      if (!monitoreoSuccess) return;

      const data: ICloseAttData = {
        doctorWebcamStreamOn: doctorWebcamStreamOn,
        event: {
          appointment_path: currentAssignation?.path,
          assignation_id: currentAssignation?.assignation_id || assignationId,
          type: attType?.includes("consultorio") ? "onsite" : "online",
        },
        mr: {
          alerts: formData.evolucion || "",
          diagnostic: (formData.diagnostico || "").trim(),
          epicrisis: formData.plan_terapeutico || "",
          final_destination: formData.destino_final || "",
          medical_history: formData.evolucion || "",
          specialist_referral: "",
          notes: generatePatientAddress(),
          rest: generateRestString({
            destinofinal: formData.destino_final,
            rest: formData.reposo,
            rest_end: undefined,
            rest_start: undefined
          }),
          treatment: formData.plan_terapeutico || "",
        },
        orders: prescriptions.orderStudies || [],
        patient: {
          affiliate_number:
            currentAssignation?.patient?.n_afiliado || "",
          corporate:
            currentAssignation?.patient?.corporate || "",
          dependantUid: dependant !== "false" ? dependant : "",
          isdependant: dependant !== "false",
          plan: currentAssignation?.patient?.plan || "",
          uid: currentAssignation?.patient?.uid || patient?.uid,
          user_email:
            patient?.email || currentAssignation?.patient?.email || "",
        },
        prescriptions: prescriptions.recipes?.medicines,
        provider: {
          uid: profile?.uid,
        },
        technicalIssue: technicalIssue,
        transcription: "",
      };
      
      //para evitar problemas de cierre, envuelvo esto en un try asi el cierre puede proseguir en caso de fallo
      if(currentAssignation.invitationId && currentAssignation.emergenciaIncidenteId){
        try {
            await updateInvitationByAssignation(assignationId, {
              destino_final: formData.destino_final || "",
              status: "DONE",
              incidenteId: currentAssignation.emergenciaIncidenteId || ""
            });
          } catch (error) {
            errorHandler.report(`Error al actualizar la invitación del assignation ${assignationId} con invitationId ${currentAssignation.invitationId} y incidenteId ${currentAssignation.emergenciaIncidenteId}`,error)
            console.log(error)
          }
      }
    
      await closeAtt(data);
    },
    onError: (error) => {
      logErrorToServer(error, assignationId, profile.uid, patient.uid, 'useCloseAttention');
      throw error;
    },
  });
};
