import { firestore } from "@/config/firebase";
import { useAttFormValidation } from "@/modules/consultorio/presentation/components/AttFile/AttFormValidationContext";
import useGetExperimentalFeaturesFlagActivation from "@/services/reactQuery/useGetExperimentalFeatureFlagActivation";
import { useAssignationFormData } from "@/cookies/AssignationFormDataContext";
import { medicalRecordMr } from "@umahealth/entities";

interface ValidationRule {
  condition: boolean;
  message: string;
  type: 'GENERAL' | 'PRESCRIPTION' | 'MEDICAL_LABOR'
  reference?: 'diagnostic' | 'epicrisis' | 'treatment' | 'rest' | 'referral' | 'final_destination' | 'is_null_final_destinations' | 'prescription' | 'phone_number';
}

interface IValidateAppointmentClosure {
  listOfValidations: ValidationRule[];
  validated: {
    diagnostic: boolean;
    epicrisis: boolean;
    referral: boolean;
    final_destination: boolean;
    rest: boolean;
    phone_number: boolean;
  };
}

interface Props {
  profile: {
    especialidad: string;
  };
  mr: medicalRecordMr;
  attType?: string;
  null_finalDestinations: string[];
  patientAddress?: {
    destination: {
      user_ws?: string;
      [key: string]: any;
    };
  };
}

export const getisValidToCloseByReference = (
  reference: 'diagnostic' | 'epicrisis' | 'treatment' | 'rest' | 'referral' | 'final_destination' | 'is_null_final_destinations' | 'prescription' | 'phone_number',
  validations: ValidationRule[]
) => {
  return validations.find((validation) => validation.reference === reference && validation.condition) || null;
};

/**
 * Hook que retorna las reglas de validación para cerrar la atención de un paciente.
 *
 * @param props - Objeto con los datos necesarios para las validaciones
 * @returns Array de objetos con las condiciones y mensajes de validación
 */
export const useValidateAppointmentClosure = ({
  profile,
  mr,
  attType = '',
  null_finalDestinations,
  patientAddress,
}: Props): IValidateAppointmentClosure => {
  const { formData } = useAssignationFormData();
  // Reglas generales de validación
  const { validations } = useAttFormValidation()
  const { isSuccess , data } = useGetExperimentalFeaturesFlagActivation(firestore, 'onlinedoctor')
  const generalValidations: ValidationRule[] = [
    {
      condition:
        !null_finalDestinations?.includes(formData.destino_final || '') &&
        !formData.evolucion,
      message:
        "Si el paciente no está ausente, debes redactar la evolución del mismo.",
      type: 'GENERAL',
      reference: 'epicrisis'
    },
    {
      condition:
        !null_finalDestinations?.includes(formData.destino_final || '') &&
        !formData.diagnostico,
      message:
        "Si el paciente no está ausente, debe cerrar la atención con algún diagnóstico.",
      type: 'GENERAL',
      reference: 'diagnostic'
    },
    {
      condition: null_finalDestinations?.includes(formData.destino_final || '') && !!formData.diagnostico,
      message:
        "Si el paciente está ausente, no debe cerrar la atención con algún diagnóstico.",
      type: 'GENERAL',
      reference: 'is_null_final_destinations'
    },
    {
      condition:
        formData.destino_final === "Indico seguimiento con especialista" &&
        !formData.evolucion, // Assuming referral info is stored in evolucion field
      message: "El campo Derivación a especialista no puede estar vacío.",
      type: 'GENERAL',
      reference: 'referral'
    },
    {
      condition: !formData.destino_final,
      message: "El campo de destino final no puede estar vacío.",
      type: 'GENERAL',
      reference: 'final_destination'
    },
    //Para las obras sociales que no tienen habilitado el certificado de reposo, el campo de reposo es null y no puede ser completado por lo tanto esta validación no aplica
    {
      condition: isSuccess && data?.new_rest ? !null_finalDestinations?.includes(formData.destino_final || '') && formData.reposo === '' : false,
      message: 'Si el paciente no está ausente, debe cerrar la atención con algún reposo.',
      type: 'GENERAL',
      reference: 'rest'
    },
    {
      condition:
        null_finalDestinations.includes(formData.destino_final || '') && !!formData.evolucion,
      message:
        "Si el paciente está ausente, no debe cerrar la atención con alguna evolución.",
      type: 'GENERAL',
      reference: 'is_null_final_destinations'
    },
    {
      condition:
        null_finalDestinations.includes(formData.destino_final || '') && !!formData.plan_terapeutico,
      message:
        "Si el paciente está ausente, no debe cerrar la atención con algún tratamiento.",
      type: 'GENERAL',
      reference: 'is_null_final_destinations'
    },
    {
      condition:
        null_finalDestinations.includes(formData.destino_final || '') && !!formData.reposo,
      message:
        "Si el paciente está ausente, no debe cerrar la atención con algún reposo.",
      type: 'GENERAL',
      reference: 'is_null_final_destinations'
    },
    {
      condition:
        formData.destino_final === "Evaluación en rojo" &&
        !patientAddress?.destination.user_ws,
      message: "El número de teléfono del paciente es obligatorio.",
      type: 'GENERAL',
      reference: 'phone_number'
    },
  ];

  // Reglas específicas para medicina laboral en Argentina
  const medicinalLaboralValidations: ValidationRule[] = [];

  if (process.env.NEXT_PUBLIC_COUNTRY === "AR") {
    if (
      profile.especialidad === "medicinalaboral" &&
      !formData.reposo &&
      !null_finalDestinations?.includes(formData.destino_final || '')
    ) {
      medicinalLaboralValidations.push({
        condition: true,
        message: "Debe indicar si el reposo está justificado o no.",
		type: 'MEDICAL_LABOR'
      });
    }

    if (
      profile.especialidad === "medicinalaboral" &&
      formData.reposo === "justificado" &&
      true // We need to add rest_start and rest_end to the form context if needed
    ) {
      medicinalLaboralValidations.push({
        condition: true,
        message: "Las fechas de inicio y fin del reposo deben ser válidas.",
		type: 'MEDICAL_LABOR'
      });
    }
  }

  // Validación de prescripciones
  const prescriptionsList = mr?.prescriptions ?? false;
  const prescriptionValidations: ValidationRule[] = [];

  const isConsultorio = ["onsite", "consultorio"].includes(attType);
  const isEmptyList = !prescriptionsList || prescriptionsList?.length === 0;
  const destinationIsNull = null_finalDestinations?.includes(
    formData.destino_final || ''
  );

  if (isEmptyList && !destinationIsNull && !isConsultorio) {
    prescriptionValidations.push({
      condition: isSuccess && data?.prescription_alert,
      message: "No se han agregado prescripciones a la consulta.",
      type: 'PRESCRIPTION',
      reference: 'prescription'
    });
  }

  return { 
    listOfValidations: [...generalValidations, ...medicinalLaboralValidations, ...prescriptionValidations], 
    validated: {
      diagnostic: validations.active ? getisValidToCloseByReference('diagnostic', [...generalValidations, ...medicinalLaboralValidations, ...prescriptionValidations])?.condition ?? false : false,
      epicrisis: validations.active ? getisValidToCloseByReference('epicrisis', [...generalValidations, ...medicinalLaboralValidations, ...prescriptionValidations])?.condition ?? false : false,
      referral: validations.active ? getisValidToCloseByReference('referral', [...generalValidations, ...medicinalLaboralValidations, ...prescriptionValidations])?.condition ?? false : false,
      final_destination: validations.active ? getisValidToCloseByReference('final_destination', [...generalValidations, ...medicinalLaboralValidations, ...prescriptionValidations])?.condition ?? false : false,
      rest: validations.active ? getisValidToCloseByReference('rest', [...generalValidations, ...medicinalLaboralValidations, ...prescriptionValidations])?.condition ?? false : false,
      phone_number: validations.active ? getisValidToCloseByReference('phone_number', [...generalValidations, ...medicinalLaboralValidations, ...prescriptionValidations])?.condition ?? false : false,
  }  };
};
