import { Timestamp } from '@/config/firebase'
import { useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'

export const useEndCallTimer = (milliseconds = 0, dt_start: Timestamp | undefined) => {
	const t = useTranslations('attention')
	const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(true)
	const [buttonText, setButtonText] = useState<string>('')

	const startedTime = useMemo(
		() => {
			//pasamos el timestamp a milisegundos
			if(dt_start) {
				return dt_start?.seconds * 1000 + Math.floor(dt_start?.nanoseconds / 1000000)
			} else {
				return new Date().getTime()
			}
		}, [dt_start]
	)
	useEffect(() => {
		let interval: NodeJS.Timer
		const now = Date.now()
		//en caso de que la fecha de la consulta más los milisegundos de timeout sea menor a la actual habilitamos el botón
		if(startedTime && (new Date(startedTime + milliseconds) < new Date(now))){
			setButtonText(t('closeattention-btn'))
			return setIsButtonDisabled(false)
		}
		//caso contrario, deshabilitamos el botón durante el tiempo restante de la suma de la fecha de la consulta y los milisegundos de timeout
		if (startedTime) {
			setIsButtonDisabled(true)
			const endTime = startedTime + milliseconds
			interval = setInterval(() => {
				const currentTime = Date.now()
				const timeLeft = Math.max(0, endTime - currentTime)
				
				if(timeLeft > 60000) setButtonText(`Podrás finalizar la consulta en ${Math.ceil(timeLeft / 60000)} minutos`)
				if(timeLeft < 60000) setButtonText(`Podrás finalizar la consulta en ${Math.ceil(timeLeft / 1000)} segundos`)
				if(timeLeft < 1) {
					setButtonText(t('closeattention-btn'))
					setIsButtonDisabled(false)
					clearInterval(interval)
				}
				if((timeLeft) > milliseconds) {
					setButtonText(t('closeattention-btn'))
					setIsButtonDisabled(false)
					clearInterval(interval)
				}
			}, 1000)
		}

		return () => clearInterval(interval)
	}, [startedTime, milliseconds])

	return {buttonText, isButtonDisabled}
}
