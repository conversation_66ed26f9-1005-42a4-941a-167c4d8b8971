import React, { useEffect, useState } from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
/**
 * Comentarios de eventos de PostHog para videollamada.
 * 
 * Este archivo contiene los eventos que se registran en PostHog para el flujo de videollamada.
 * Sirve como referencia para cuando se necesite debuggear.
 */
//import {trackEventDiagnosticAI, trackEventDiagnosticAIError} from '@/events/videocallEvents'
import './diagnosticStyles.scss'

const Diagnostic = ({diagnosticAsProp}) => {
	const dispatch = useAppDispatch()
	const [diagnostic, setDiagnostic] = useState([])
	const currentAtt = useAppSelector((state) => state.queries.currentAtt)
	let diagnostico = ''

	if (currentAtt && currentAtt.mr_preds && currentAtt.mr_preds.diagnostico) {
		diagnostico = currentAtt.mr_preds.diagnostico
	} else {
		diagnostico = diagnosticAsProp
	}

	useEffect(() => {
		if(currentAtt &&currentAtt?.mr_preds){
			try {
				let diagSplit = diagnostico ? diagnostico.split('#', 6) : ''
				setDiagnostic(diagSplit)
				dispatch({ type: 'DIAGNOSTIC_WRITE', payload: diagSplit?.[0] })
				//trackEventDiagnosticAI()
			} catch (err) {
				//trackEventDiagnosticAIError()
				console.error('Error spliting diagnosis. Maybe this patient doesn\'t have medical records.')
			}
		}else if(diagnosticAsProp){
			setDiagnostic(diagnosticAsProp)
		}
		// eslint-disable-next-line
	}, [diagnostico]);

	return (
		<div className='diagnostic-container'>
			<div className='diagnostic-title'>Diagnóstico asistido</div>
			{diagnostic[1] ?
				<div className='diagnostic-text-container'>
					<small>{diagnostic[0]}</small>
					<div className='diagnostic-progressbar'>
						<div
							className='progress-bar progress-bar-striped'
							style={{ 
								width: `${diagnostic[1] ? `${diagnostic[1]}%` : `${100}%`}`, 
								height: '37px',
							}}>
							{diagnostic[1] ? `${diagnostic[1]}%` : `${0}%`}
						</div>
					</div>
					<small>{diagnostic[2]}</small>
					<div className='diagnostic-progressbar'>
						<div
							className='progress-bar progress-bar-striped'
							style={{ 
								width: `${diagnostic[3] ? `${diagnostic[3]}%` :`${100}%`}`, 
								height: '37px' ,
							}}>
							{diagnostic[3] ? `${diagnostic[3]}%` :`${0}%`}
						</div>
					</div>
					<small>{diagnostic[4]}</small>
					<div className='diagnostic-progressbar'>
						<div
							className='progress-bar progress-bar-striped'
							style={{ 
								width: `${diagnostic[5] ? `${diagnostic[5]}%` : `${100}%`}`, 
								height: '37px' ,
							}}>
							{diagnostic[5] ? `${diagnostic[5]}%` : `${0}%`}
						</div>
					</div>
				</div> : <p>Aún no se pudo identificar un diagnóstico mediante inteligencia artificial</p>}
		</div>
	)
}

export default Diagnostic
