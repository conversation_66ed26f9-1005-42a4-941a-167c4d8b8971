'use client'
import React from 'react'
import { useClosePharmacovigilancy } from '@/services/reactQuery/useClosePharmacovigilancy'
import { onErrorClosePharmacovigilancyAtt } from '@/utils/handleClosePharmacovigilancyAtt'
import { useRouter, useSearchParams } from 'next/navigation'
import { requestTypes } from '@umahealth/entities'
import { Button } from '@umahealth/occipital'
import { farmatodoCountry } from '@/utils/farmatodoCountry'
import { useAppSelector } from '@/store/hooks'

const CloseFarmacoAtt = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { currentAppointment } = useAppSelector((state) => state.queries)
  const provider = useAppSelector((state) => state.user.profile)

  const attType = searchParams.get('attType') as requestTypes
  const encounterId = searchParams.get('encounterId') as string
  const assignationId = searchParams.get('assignationId') as string
  const patientUid = searchParams.get('patientUid') as string

  const closePharmacovigilancy = useClosePharmacovigilancy(
    attType,
    encounterId,
    router,
    {
      onError: (error) => {
        onErrorClosePharmacovigilancyAtt(error)
      }
    }
  )

  const handleClose = () => {
    closePharmacovigilancy.mutate({
      assignation: currentAppointment,
      assignationId,
      country: farmatodoCountry(provider?.country),
      providerUid: provider?.uid,
      patientUid,
    })
  }

  return (
    <Button
      size="full"
      action={() => handleClose()}
      type="button"
      variant="filled"
      className='max-w-[300px]'
      loading={closePharmacovigilancy.isLoading}
    >
      Finalizar consulta
    </Button>
  )
}

export default CloseFarmacoAtt