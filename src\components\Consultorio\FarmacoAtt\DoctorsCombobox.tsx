import React, { useEffect } from 'react'
import { Combobox } from '@/components/Shadcn/Combobox'
import { FieldValues, UseFormSetValue, UseFormWatch } from 'react-hook-form'
import { useDebounce } from '@/hooks/useDebounce'
import useSearchFarmacoDoctors from '@/services/reactQuery/useSearchFarmacoDoctors'

interface IProps {
  watch: UseFormWatch<FieldValues>
  setValue: UseFormSetValue<FieldValues>
  isPharmacist?: boolean
  defaultValue?: string
}

export const DoctorsCombobox = ({
  watch,
  setValue,
  isPharmacist,
  defaultValue,
}: IProps) => {
  const searchDoctors = useSearchFarmacoDoctors()
  const debouncedValue = useDebounce<string>(watch('doctor.name') as string)

  useEffect(() => {
    if (debouncedValue && debouncedValue !== '') {
      console.log(debouncedValue)
      searchDoctors.mutate({ doctorName: debouncedValue, isPharmacist })
    }
  }, [debouncedValue])

  return (
    <Combobox
      options={
        searchDoctors.data?.length
          ? searchDoctors.data.map((doctor) => {
              return isPharmacist
                ? {
                    value:
                      doctor?.identifier?.find((id) => id?.use === 'old')
                        ?.value || '',
                    label:
                      doctor?.identifier?.find((id) => id?.use === 'old')
                        ?.value || '',
                  }
                : {
                    value: `${doctor?.name?.[0]?.text} - ${JSON.stringify(
                      doctor
                    )}`,
                    label: `${doctor?.name?.[0]?.text} - ${
                      doctor?.extension?.[0]?.valueString || ''
                    }`,
                  }
            })
          : []
      }
      onChange={(doctor) => {
        const doctorSplited = doctor ? doctor.split(' - ') : ''
        setValue('doctor.name', doctorSplited[0])
        {
          !isPharmacist &&
            setValue(
              'doctor.doctor',
              doctorSplited[1] && JSON.parse(doctorSplited[1])
            )
        }
      }}
      isLoading={searchDoctors.isLoading}
      shouldFilter={false}
      label={isPharmacist ? 'Farmaceutico' : 'Médico tratante'}
      placeholder="Nombre"
      emptyPlaceHolder={`No encontramos ningun ${
        isPharmacist ? 'farmaceutico' : 'médico'
      } con ese nombre`}
      defaultValue={defaultValue}
      className="h-12 w-[350px]"
    />
  )
}
