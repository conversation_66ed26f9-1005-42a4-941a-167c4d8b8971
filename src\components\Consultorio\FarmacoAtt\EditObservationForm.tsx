import { groupItemsByLinkId } from '@/components/MyPatients/presentation/utils/groupItemsByLinkId'
import { IQuestionnaire } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IQuestionnaire'
import style from "./styles/moduleViewer.module.scss";
import React from 'react'
import { InputContainer } from '@/components/GeneralComponents/InputFarmatodo/InputContainer';
import { FieldValues, useForm } from 'react-hook-form';
import { InputsEncounter } from './InputsEncounter';
import { ModuleValue } from './FarmacoAttModules';
import { transformToFhirResource } from './utils/transformToFhirResource';
import { Button } from '@umahealth/occipital'
import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation';
import useUpdateFhirResource from '@/services/reactQuery/useUpdateFhirResource';
import swal from 'sweetalert';

interface IProps {
    observation: IObservation,
    questionnaire: IQuestionnaire,
    defaultValue?: Record<string, any>,
    moduleInView: ModuleValue,
    patientId: string
}

export const EditObservationForm = ({ observation, questionnaire, defaultValue, moduleInView, patientId }: IProps) => {
    const { register, watch, setValue, handleSubmit, control, formState: { errors } } = useForm({
        defaultValues: defaultValue
    })
    const updateObservation = useUpdateFhirResource<IObservation>()
    
    const questionnaireGroupedByLinkId = groupItemsByLinkId(questionnaire.item)

    const submitForm = async (formData: FieldValues) => {
        const formDataFormated = moduleInView === 'medicineHour' && formData.medicine ? { hour: formData.hour, drug: formData.medicine?.drug, ...formData.medicine?.medicine } : formData
        const dataTransformed = transformToFhirResource.toObservation(
            formDataFormated,
            observation.encounter?.reference ?? '',
            observation.code.text ?? '',
            patientId,
            observation.valueDateTime
        )
        updateObservation.mutate({ newData: dataTransformed, resourceId: observation.id ?? '', resourceType: 'Observation' })  
    }

    if(updateObservation.isError){
        swal('No hemos podido editar el documento', 'Por favor, intente nuevamente', 'warning')
            .then(() => updateObservation.reset())
    }
    
    if(updateObservation.isSuccess){
        swal('Documento editado correctamente', '', 'success')
            .then(() => updateObservation.reset())
    }

    return (
        <form className={style.form} onSubmit={handleSubmit(async (data) => {
            await submitForm(data)
        })}>
            {questionnaireGroupedByLinkId?.map((arrayGrouped, i) => {
                return (
                    <>
                        <div className={style.formSectionContainer}>
                            {arrayGrouped.map((input) => (
                                <InputContainer key={input.linkId} full={moduleInView === 'medicineHour'}>
                                    <InputsEncounter
                                        watch={watch}
                                        register={register}
                                        item={input}
                                        moduleInView={moduleInView}
                                        setValue={setValue}
                                        control={control}
                                        errors={errors}
                                    />
                                </InputContainer>
                            ))}
                        </div>
                        {questionnaireGroupedByLinkId?.length - 1 !== i && (
                            <div className={style.separatorInputs}></div>
                        )}
                    </>
                )
            })}
            <Button
                size="full"
                type="submit"
                variant="filled"
                loading={updateObservation.isLoading}
            >
                Editar
            </Button>
        </form>
    )
}
