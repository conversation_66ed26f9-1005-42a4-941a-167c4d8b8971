import React from "react";
import { Icon } from "occipital-new";
import module from "./styles/encounterFiles.module.scss";
import { useAppSelector } from "@/store/hooks";
import { farmatodoCountry } from "@/utils/farmatodoCountry";

const pharmacovigilancyReportVE =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Reporte%20de%20Farmacovigilancia.pdf";
const pharmacovigilancyReportCO =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Reporte%20de%20Farmacovigilancia%20CO.pdf";
const consentLetterVE =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Carta%20consentimiento.pdf";
const consentLetterCO =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Consentimiento%20Informado%20CO.pdf";
const medicalContacLetter =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Carta%20contacto%20medico.pdf";
const diabetesRecommendationsVE =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Recomendaciones%20Nutricionales%20paciente%20diabetico.pdf";
const diabetesRecommendationsCO =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Recomendaciones%20nutricionales%20paciente%20con%20diabetes%20CO.pdf";
const htaRecommendationsVE =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Recomendaciones%20Nutricionales%20paciente%20hipertenso.pdf";
const htaRecommendationsCO =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Recomendaciones%20nutricionales%20paciente%20con%20hipertension%20CO.pdf";
const serviceProtocol =
  "https://storage.googleapis.com/uma-v2.appspot.com/FARMATODO/encounterFiles/Protocolo%20servicio.pdf";

export const EncounterFiles = () => {
  const { profile } = useAppSelector((state) => state.user);
  const country = farmatodoCountry(profile?.country);
  const pharmacovigilancyReport =
    country === "CO" ? pharmacovigilancyReportCO : pharmacovigilancyReportVE;
  const consentLetter = country === "CO" ? consentLetterCO : consentLetterVE;
  const diabetesRecommendations =
    country === "CO" ? diabetesRecommendationsCO : diabetesRecommendationsVE;
  const htaRecommendations =
    country === "CO" ? htaRecommendationsCO : htaRecommendationsVE;

  return (
    <div className='flex justify-evenly flex-wrap gap-2 mt-2'>
      <a
        href={pharmacovigilancyReport}
        className={module.download}
        rel="noreferrer"
        target="_blank"
        download
      >
        Reporte de farmacovigilancia
        <Icon name="file" color="primary" isClickable={true} size="l" />
      </a>
      <a
        href={consentLetter}
        className={module.download}
        rel="noreferrer"
        target="_blank"
        download
      >
        Carta de consentimiento
        <Icon name="file" color="primary" isClickable={true} size="l" />
      </a>
      <a
        href={medicalContacLetter}
        rel="noreferrer"
        target="_blank"
        className={module.download}
        download
      >
        Carta de contacto médico
        <Icon name="file" color="primary" isClickable={true} size="l" />
      </a>
      <a
        href={diabetesRecommendations}
        className={module.download}
        rel="noreferrer"
        target="_blank"
        download
      >
        Recomendaciones nutricionales para diabetes
        <Icon name="file" color="primary" isClickable={true} size="l" />
      </a>
      <a
        href={htaRecommendations}
        className={module.download}
        rel="noreferrer"
        target="_blank"
        download
      >
        Recomendaciones nutricionales para hipertensión
        <Icon name="file" color="primary" isClickable={true} size="l" />
      </a>
      <a
        href={serviceProtocol}
        className={module.download}
        rel="noreferrer"
        target="_blank"
        download
      >
        Protocolo de servicios
        <Icon name="file" color="primary" isClickable={true} size="l" />
      </a>
    </div>
  );
};
