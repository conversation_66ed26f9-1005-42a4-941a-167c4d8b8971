'use client'
import React, { useState } from "react";
import { ModuleViewer } from "./ModuleViewer";
import { MdExpandMore, MdExpandLess } from "react-icons/md";
import style from "./styles/farmacoAttModules.module.scss";
/**
 * Comentarios de eventos de PostHog para videollamada.
 * 
 * Este archivo contiene los eventos que se registran en PostHog para el flujo de videollamada.
 * Sirve como referencia para cuando se necesite debuggear.
 */
//import { trackEventFarmacoAttModules } from "@/events/videocallEvents";

export type ModuleValue =
  | "PHARMACIST"
  | "labs"
  | "parameters"
  | "treatment"
  | "medicineHour"
  | "PRH"
  | "PRM"
  | "patientEvolution"
  | "patientRecord"
  | "PRHForm"
  | "PRMForm"
  | "encounterFiles";

export type ModuleValueFhir = Exclude<ModuleValue, "PRHForm" | "PRMForm" | "encounterFiles">;


const modules: { value: ModuleValue; label: string }[] = [
  {
    value: "PHARMACIST",
    label: "Farmacéutico tratante",
  },
  {
    value: "patientRecord",
    label: "Nuevo registro",
  },
  {
    value: "patientEvolution",
    label: "Evolución del paciente",
  },
  {
    value: "labs",
    label: "Análisis de laboratorio",
  },
  {
    value: "parameters",
    label: "Parámetros de paciente",
  },
  {
    value: "treatment",
    label: "Tratamiento",
  },
  {
    value: "medicineHour",
    label: "Horario de medicamentos",
  },
  {
    value: "PRH",
    label: "Problemas relacionados con hábitos",
  },
  {
    value: "PRM",
    label: "Problemas relacionados con medicamentos",
  },
  {
    value: "encounterFiles",
    label: "Archivos",
  },
];

export const FarmacoAttModules = () => {
  const [moduleView, setModuleView] = useState<ModuleValue | null>();
  const [display, setDisplay] = useState<ModuleValue | false>(false);
  //trackEventFarmacoAttModules(display)

  const handleClick = (moduleClicked: ModuleValue) => {
    if (moduleView === moduleClicked) {
      setModuleView(null);
      setDisplay(false);
      return;
    }
    setModuleView(moduleClicked);
    setDisplay(moduleClicked);
  };

  return (
    <div className='flex flex-col w-full gap-3'>
      {modules.map((eachModule) => (
        <div
          key={eachModule.value}
          className='flex flex-col w-full'
        >
          <div
            className={style.sectionTitle}
            onClick={() => handleClick(eachModule.value)}
          >
            <label className="pointer">{eachModule.label}</label>
            <div className="text-white text-[1.2rem] p-[0_8px]">
              {display !== eachModule.value ? <MdExpandMore /> : <MdExpandLess />}
            </div>
          </div>
          {moduleView === eachModule.value && (
            <ModuleViewer moduleInView={moduleView} />
          )}
        </div>
      ))}
    </div>
  );
};
