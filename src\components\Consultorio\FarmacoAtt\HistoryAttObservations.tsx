import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation'
import { errorHandler } from '@/config/stackdriver'
import { HistoryAttObservationsList } from './HistoryAttObservationsList'
import { useGetResourceByFilters } from '@/services/reactQuery/useGetResourceByFilters'
import swal from 'sweetalert'

interface IProps {
    moduleInView: 'treatment' | 'labs' | 'medicineHour' | 'parameters',
    patientId: string
}

export const filterByModule: Record<'treatment' | 'labs' | 'medicineHour' | 'parameters', string> = {
    "labs": "code:text=LAB_OBSERVATION",
    "treatment": "code:text=TREATMENT_OBSERVATION",
    "medicineHour": "code:text=MEDICINE_HOUR_OBSERVATION",
    "parameters": "code:text=PARAMETERS_OBSERVATION"
}

export const HistoryAttObservations = ({ moduleInView, patientId }: IProps) => {
    const observations = useGetResourceByFilters<IObservation>('Observation', `subject=Patient/${patientId}&${filterByModule[moduleInView]}`)
            
    if(observations.isLoading) return <>Cargando documentos previos...</>

    if(observations.isSuccess && (!observations.data || !observations.data?.length)) return <>El paciente no tiene documentos previos</>

    if(observations.isError){
        errorHandler?.report(`Error obteniendo historial de observations ${moduleInView} - ${patientId} - ${observations.error}`)
        swal('No hemos podido obtener los documentos previos', 'Por favor, intente nuevamente', 'warning')
    }

    return <HistoryAttObservationsList observations={observations.data as IObservation[]} />
}
