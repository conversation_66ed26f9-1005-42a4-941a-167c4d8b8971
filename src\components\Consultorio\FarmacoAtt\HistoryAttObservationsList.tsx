import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation'
import moment from 'moment'
import React, { useState } from 'react'
import { Title } from 'occipital-new'
import { ModalHistoryObservation } from './ModalHistoryObservation'
import style from './styles/historyAttObservationsList.module.scss'

interface IProps {
    observations: IObservation[]
}

moment.locale('es')

export const HistoryAttObservationsList = ({ observations }: IProps) => {
    const [modalInfo, setModalInfo] = useState<{
        show: boolean,
        observation: IObservation | null
    }>({ show: false, observation: null })

    return (
        <div>
            <div className={style.containerCards}>
                <Title hierarchy='h2' color='text-primary' size='m' weight='bold'>Observaciones previas</Title>
                {observations?.map(observation => <div key={observation.id} className={style.container} onClick={() => setModalInfo({
                    show: true,
                    observation
                })}>
                    Observación del {moment(observation?.valueDateTime)?.format('ddd D [de] MMMM [de] YYYY HH:mm')}
                </div>)}
            </div>
            {(modalInfo.show && modalInfo.observation) && <ModalHistoryObservation observation={modalInfo.observation} setModalInfo={setModalInfo}/>}
        </div>
    )
}
