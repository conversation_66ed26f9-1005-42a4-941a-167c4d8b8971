import React from 'react'
import { useSearchParams } from 'next/navigation'
import { useGetPatientEverythingFhir } from '@/services/reactQuery/useGetPatientEverythingFhir'
import { Button } from '@umahealth/occipital'
import { usePrmPrhReport } from '@/services/reactQuery/usePrmPrhReport'
import { cleanObjectKeys } from './utils/cleanObjectKeys'
import { filterObservationsOfEncounter } from './utils/filterObservationsOfEncounter'
import { UseFormSetValue, FieldValues } from 'react-hook-form'
import { errorHandler } from '@/config/stackdriver'
import swal from 'sweetalert'

interface IProps {
  type: 'PRM' | 'PRH'
  setValue: UseFormSetValue<FieldValues>
}

export const IaReport = ({ type, setValue }: IProps) => {
  const searchParams = useSearchParams()
  const patientId = searchParams.get('healthcareId') ?? ''
  const encounterId = searchParams.get('encounterId') as string
  const patientEverything = useGetPatientEverythingFhir(patientId)
  const reportIa = usePrmPrhReport({
    onError: async (err) => {
      errorHandler?.report(`[ Farmatodo | IAReport ] => Error autocompletando con IA - ${JSON.stringify(err)}`)
      await swal(`Error autocompletando con IA`, `${err.message}`, 'warning')
    },
    onSuccess: (data) => {
      const objectClean = cleanObjectKeys(data?.output)
      Object.keys(objectClean).forEach((key) => setValue(key, objectClean[key]))
      reportIa.reset()
    }
  })

  return (
    <Button
      disabled={patientEverything.isLoading}
      loading={reportIa.isLoading}
      action={() => {
        reportIa.mutate(
          filterObservationsOfEncounter(
            patientEverything.data,
            encounterId,
            type
          )
        )
      }}
      type="button"
      variant="filled"
      size="extended"
    >
      Autocompletar con IA
    </Button>
  )
}
