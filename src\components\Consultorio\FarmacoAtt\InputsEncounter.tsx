import { QuestionnaireItem } from '@smile-cdr/fhirts/dist/FHIR-R4/classes/questionnaireItem'
import {
  UseFormRegister,
  FieldValues,
  UseFormWatch,
  UseFormSetValue,
  Control,
  FieldErrors,
} from 'react-hook-form'
import { DoctorsCombobox } from './DoctorsCombobox'
import { MedicineCombobox } from './MedicineCombobox'
import { ModuleValue } from './FarmacoAttModules'
import TextareaFarmatodo from '@/components/GeneralComponents/InputFarmatodo/TextareaFarmatodo'
import InputFarmatodo from '@/components/GeneralComponents/InputFarmatodo/InputFarmatodo'
import DateFarmatodo from '@/components/GeneralComponents/InputFarmatodo/DateFarmatodo'
import SelectFarmatodoRHF from '@/components/GeneralComponents/InputFarmatodo/SelectFarmatodoRHF'

interface IProps {
  control: Control<FieldValues>
  errors: FieldErrors<FieldValues>
  item: QuestionnaireItem
  moduleInView: ModuleValue
  register: UseFormRegister<FieldValues>
  setValue: UseFormSetValue<FieldValues>
  watch: UseFormWatch<FieldValues>
}

export const InputsEncounter = ({
  item,
  register,
  moduleInView,
  watch,
  setValue,
  control,
  errors,
}: IProps) => {
  const type = item.extension?.find((extension) => extension.url === 'type')
  const placeholder = item.extension?.find(
    (extension) => extension.url === 'placeholder'
  )
  const registerName =
    moduleInView === 'treatment' ||
    moduleInView === 'PHARMACIST' ||
    moduleInView === 'medicineHour'
      ? item.extension?.find((extension) => extension.url === 'register')
      : { valueString: '' }

  const inputName = moduleInView === 'labs'
    ? item.text?.split('(')?.[0]?.trim() || ''
    : moduleInView === 'medicineHour'
      ? registerName?.valueString || ''
      : item.text || ''

  if (item.type === 'choice') {
    return (
      <SelectFarmatodoRHF
        control={control}
        label={item.text}
        answerOption={item.answerOption}
        name={inputName}
        error={errors?.[inputName]?.message as string}
      />
    )
  }

  if (
    moduleInView === 'PHARMACIST' &&
    registerName?.valueString === 'name' &&
    placeholder?.valueString === 'Nombre'
  ) {
    return (
      <>
        <label>{item.text}</label>
        <DoctorsCombobox watch={watch} setValue={setValue} isPharmacist />
      </>
    )
  }
  if (
    moduleInView === 'treatment' &&
    registerName?.valueString === 'indicatedBy'
  )
    return (
      <>
        <label>{item.text}</label>
        <DoctorsCombobox
          watch={watch}
          setValue={setValue}
          defaultValue={watch(registerName.valueString)}
        />
      </>
    )
  if (
    ((moduleInView === 'treatment' || moduleInView === 'medicineHour') &&
      registerName?.valueString === 'medicine') ||
    registerName?.valueString === 'drug'
  ) {
    return (
      <>
        <label>{item.text}</label>
        <MedicineCombobox
          watch={watch}
          setValue={setValue}
          defaultValue={watch(registerName.valueString)}
        />
      </>
    )
  }
  if (item.type === 'date')
    return (
      <DateFarmatodo
        {...register(inputName)}
        placeholder={placeholder?.valueString}
        label={item.text}
        error={errors?.[inputName]?.message as string}
      />
    )
  if (item.type === 'decimal')
    return (
      <InputFarmatodo
        label={item.text}
        type="number"
        placeholder={'0'}
        step="0.01"
        {...register(inputName)}
        error={errors?.[inputName]?.message as string}
      />
    )
  if (type?.valueString === 'text-area') {
    return (
      <TextareaFarmatodo
        label={item.text}
        {...register(inputName)}
        placeholder={placeholder?.valueString}
        value={watch(inputName)}
        error={errors?.[inputName]?.message as string}
      />
    )
  }
  if (type?.valueString === 'text')
    return (
      <InputFarmatodo
        label={item.text}
        placeholder={placeholder?.valueString}
        {...register(inputName)}
        error={errors?.[inputName]?.message as string}
      />
    )
  if (type?.valueString === 'email')
    return (
      <InputFarmatodo
        type="email"
        placeholder={placeholder?.valueString}
        {...register(inputName)}
        error={errors?.[inputName]?.message as string}
      />
    )
  return <></>
}
