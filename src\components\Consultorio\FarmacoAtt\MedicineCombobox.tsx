import React, { useEffect } from 'react'
import { UseFormWatch, UseFormSetValue, FieldValues } from 'react-hook-form'
import { useDebounce } from '@/hooks/useDebounce'
import useSearchMedicine from '../Prescriptions/Recipe/SearchMedicine/useSearchMedicine'
import { Combobox } from '@/components/Shadcn/Combobox'
import { MedicineFarmatodo } from '../Prescriptions/Recipe/SearchMedicine/useSearchMedicineFarmatodo'
import { useAppSelector } from '@/store/hooks/useAppSelector'

interface IProps{
  watch: UseFormWatch<FieldValues>,
  setValue: UseFormSetValue<FieldValues>,
  defaultValue?: string
}

export const MedicineCombobox = ({ watch, setValue, defaultValue }: IProps) => {
  const { profile } = useAppSelector((state) => state.user)
  const searchMedicine = useSearchMedicine(profile?.country?.[0] || process.env.NEXT_PUBLIC_COUNTRY)
  const debouncedValue = useDebounce<string>(watch('medicine.drug') as string)

  useEffect(() => {
    if (debouncedValue && debouncedValue !== '') {
      searchMedicine.mutate(debouncedValue as any)
    }
  }, [debouncedValue])
    
  return (
    <Combobox
      options={
        searchMedicine.data?.output?.length
          ? searchMedicine.data.output.map((medicine) => {
              const drug = medicine as MedicineFarmatodo
              return {
                value: `${drug.mediaDescription} - ${JSON.stringify(drug)}`,
                label: `${drug.mediaDescription}`,
              }
            })
          : []
      }
      onChange={(medicine) => {
        const medicineSplited = medicine.split(' - ')
        setValue('medicine.drug', medicineSplited[0])
        setValue(
          'medicine.medicine',
          medicineSplited[1] && JSON.parse(medicineSplited[1])
        )
      }}
      isLoading={searchMedicine.isLoading}
      shouldFilter={false}
      label="Fármaco con marca"
      placeholder="Medicamento"
      emptyPlaceHolder="No encontramos ningun medicamento"
      defaultValue={defaultValue}
      className='h-12 w-[300px]'
    />
  )
}
