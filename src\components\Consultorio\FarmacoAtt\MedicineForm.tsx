import React, { useEffect } from 'react'
import { MedicineFormData } from './MedicineRecord'
import { MedicineFarmatodo } from '../Prescriptions/Recipe/SearchMedicine/useSearchMedicineFarmatodo'
import { Combobox } from '@/components/Shadcn/Combobox'
import { useDebounce } from '@/hooks/useDebounce'
import { UseMutationResult } from 'react-query'
import useSearchMedicine from '../Prescriptions/Recipe/SearchMedicine/useSearchMedicine'
import { UseFormHandleSubmit, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form'
import { Button, Text } from '@umahealth/occipital'
import { Input } from '@umahealth/occipital/client'
interface IProps{
  setValue: UseFormSetValue<MedicineFormData>,
  watch: UseFormWatch<MedicineFormData>,
  register: UseFormRegister<MedicineFormData>,
  handleSubmit: UseFormHandleSubmit<MedicineFormData>,
  encounterId: string,
  saveMedicine: UseMutationResult<void, unknown, MedicineFormData, unknown>
}

export const MedicineForm = ({ handleSubmit, watch, register, setValue, saveMedicine }: IProps) => {
  const searchMedicine = useSearchMedicine('VE')
  const debouncedValue = useDebounce<string>(watch('medicine.drug') as string)

  useEffect(() => {
    if (debouncedValue && debouncedValue !== '') {
      searchMedicine.mutate(debouncedValue as any)
    }
  }, [debouncedValue])

  return (
    <form
      onSubmit={handleSubmit((formData) => saveMedicine.mutate(formData))}
      className="flex flex-col w-full p-6"
    >
      <Text tag="h3" weight="font-bold" size="text-m" color="text-primary">
        Horario de medicamentos
      </Text>
      <div className="flex gap-6 my-3">
        <Input
          label="Horario"
          classNameContainer="mt-0"
          type="text"
          placeholder="HH:mm"
          {...register('medicine.hour')}
        />
        <Combobox
          className="w-1/2 h-12"
          options={
            searchMedicine.data?.output?.length
              ? searchMedicine.data.output?.map((medicine) => {
                  const drug = medicine as MedicineFarmatodo
                  return {
                    value: `${drug.mediaDescription} - ${JSON.stringify(drug)}`,
                    label: `${drug.mediaDescription}`,
                  }
                })
              : []
          }
          onChange={(medicine) => {
            const medicineSplited = medicine.split(' - ')
            setValue('medicine.drug', medicineSplited[0])
            setValue(
              'medicine.medicine',
              medicineSplited[1] && JSON.parse(medicineSplited[1])
            )
          }}
          isLoading={searchMedicine.isLoading}
          shouldFilter={false}
          label="Fármaco con marca"
          placeholder="Medicamento"
          emptyPlaceHolder="No encontramos ningun medicamento"
        />
      </div>
      <Button
        loading={saveMedicine.isLoading}
        size="full"
        type="submit"
        variant="filled"
        className="max-w-[300px] self-center"
      >
        Guardar
      </Button>
    </form>
  )
}
