import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import swal from 'sweetalert'
import { IfhirR4 } from '@smile-cdr/fhirts'
import { HistoryAttObservations } from './HistoryAttObservations'
import { ModalMedicineGuide } from './ModalMedicineGuide'
import { useMedicineGuide } from '@/services/reactQuery/useMedicineGuide'
import { useSaveMedicineRecord } from '@/services/reactQuery/useSaveMedicineRecord'
import { useGetResourceByFilters } from '@/services/reactQuery/useGetResourceByFilters'
import { IEncounter } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IEncounter'
import { MedicineForm } from './MedicineForm'
import { MedicinesList } from './MedicinesList'

interface Medicine {
    hour: string
    drug: string
    medicine?: {
        brand: string
        detailDescription: string
        id: number
        largeDescription: string
        mediaDescription: string
        mediaImageUrl: string
        objectID: number
    }
}

export interface MedicineFormData {
    medicine: Medicine
}

interface MedicineFormProps {
    patient: IfhirR4.IPatient | null,
    encounterId: string
}

export const MedicineRecord = ({ patient, encounterId }: MedicineFormProps) => {
    const { setValue, watch, handleSubmit, register } = useForm<MedicineFormData>()
    const patientName = patient?.name?.[0].text
    const [ modalMedicineGuide, setModalMedicineGuide ] = useState<string | null>(null)
    const medicineGuideContent = useMedicineGuide()
    const encounter = useGetResourceByFilters<IEncounter>('Encounter', `_id=${encounterId}`)
    const saveMedicineRecord = useSaveMedicineRecord(patient?.id || '', encounter.data?.[0])

    if(saveMedicineRecord.isSuccess){
        swal("Observacion creada con exito", "", "success")
            .then(() => saveMedicineRecord.reset())
    }

    return (
        <div className='py-4 px-3 flex flex-col w-full'>
            {(modalMedicineGuide && medicineGuideContent.isSuccess) && <ModalMedicineGuide setMedicineGuide={setModalMedicineGuide} content={medicineGuideContent.data} />}
            <HistoryAttObservations moduleInView={'medicineHour'} patientId={patient?.id || ''} />
            <MedicineForm setValue={setValue} watch={watch} encounterId={encounterId} handleSubmit={handleSubmit} register={register} saveMedicine={saveMedicineRecord}/>
            <MedicinesList patientId={patient?.id || ''} patientName={patientName || ''} medicineGuideContent={medicineGuideContent} setModalMedicineGuide={setModalMedicineGuide} />
        </div>
    )
}
