import { errorHandler } from '@/config/stackdriver'
import { useGetResourceByFilters } from '@/services/reactQuery/useGetResourceByFilters'
import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation'
import { Loader, Text, Button } from '@umahealth/occipital'
import { Spacer } from 'occipital-new'
import { downloadPdf } from './utils/downloadPdf'
import { UseMutationResult } from 'react-query'
import { AxiosError } from 'axios'
import { IMedicineGuideResponse } from '@/services/reactQuery/useMedicineGuide'
import React from 'react'
import { getMedicineFields } from './utils/getMedicineFields'
import styles from './styles/medicineTable.module.scss'

interface IProps {
    patientId: string,
    patientName: string,
    medicineGuideContent: UseMutationResult<IMedicineGuideResponse, AxiosError, string | null, unknown>,
    setModalMedicineGuide: React.Dispatch<React.SetStateAction<string | null>>
}

export const MedicinesList = ({ patientId, patientName, medicineGuideContent, setModalMedicineGuide }: IProps) => {
    const medicinesList = useGetResourceByFilters<IObservation>('Observation', `code:text=MEDICINE_HOUR_OBSERVATION&subject=${patientId}`)

    if (medicinesList.isLoading) {
        return <Loader>Cargando medicinas...</Loader>
    }

    if (medicinesList.isError) {
        errorHandler?.report(medicinesList.error as Error)
        return <Text tag='p'>Ha ocurrido un error cargando las medicinas, por favor intente nuevamente</Text>
    }

    if (!medicinesList.data || !medicinesList.data?.length) {
        return <Text tag='p'>El paciente no tiene cargada ninguna medicina</Text>
    }

    return (<>
        <table className={styles.medicineTable} id="medicineTable">
            <h3>{patientName}</h3>
            <thead>
                <tr>
                    <th>Horario</th>
                    <th>Fármaco</th>
                    <th>Pauta medicamentosa IA</th>
                </tr>
            </thead>
            <tbody>
                {medicinesList.data?.map((medicine, index) => {
                    const { drug, hour, id } = getMedicineFields(medicine)
                    return <tr key={index}>
                        <td>{hour}</td>
                        <td>{drug}</td>
                        <td>
                            <Button loading={medicineGuideContent.isLoading} type='button' action={() => {
                                setModalMedicineGuide(id ?? null)
                                medicineGuideContent.mutate(id ?? null)
                            }}>
                                Ver
                            </Button>
                        </td>
                    </tr>
                })}
            </tbody>
        </table>
        <Spacer direction="vertical" value="32px" />
        <Button
            size="small"
            type="submit"
            variant="outlined"
            action={() => downloadPdf('medicineTable', 'medicinas')}
        >
            Imprimir Tabla
        </Button>
    </>
    )
}
