import React from 'react'
import { Modal } from 'occipital-new'
import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation'
import style from './styles/modalHistoryObservation.module.scss'
import { EditObservationForm } from './EditObservationForm'
import { useGetResourceByFilters } from '@/services/reactQuery/useGetResourceByFilters'
import { Loader } from '@umahealth/occipital'
import { errorHandler } from '@/config/stackdriver'
import swal from 'sweetalert'
import { IQuestionnaire } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IQuestionnaire'
import { useSearchParams } from 'next/navigation'
import { getDefaultValues } from './utils/getDefaultValues'

interface IProps{
    observation: IObservation,
    setModalInfo: React.Dispatch<React.SetStateAction<{
        show: boolean;
        observation: IObservation | null;
    }>>
}

type ModuleValue = 'PHARMACIST' | 'labs' | 'PRH' | 'PRM' | 'treatment' | 'parameters' | 'medicineHour' | 'patientEvolution' | 'patientRecord' | 'encounterFiles' | 'PRHForm' | 'PRMForm';

const invertedNameObservations: { [key: string]: ModuleValue } = {
    "PHARMACIST": "PHARMACIST",
    "LAB_OBSERVATION": "labs",
    "PRH_OBSERVATION": "PRH",
    "PRM_OBSERVATION": "PRM",
    "TREATMENT_OBSERVATION": "treatment",
    "PARAMETERS_OBSERVATION": "parameters",
    "MEDICINE_HOUR_OBSERVATION": "medicineHour",
    "PATIENT_EVOLUTION": "patientEvolution",
    "PATIENT_RECORD": "patientRecord",
} 

export const ModalHistoryObservation = ({ observation, setModalInfo }: IProps) => {
    const searchParams = useSearchParams()
    const patientId = searchParams.get('healthcareId') ?? ''
    const moduleValue = invertedNameObservations[observation.code.text as string]
    const questionnaire = useGetResourceByFilters<IQuestionnaire>('Questionnaire', `identifier=${moduleValue}`)

    if(questionnaire.isLoading) return <Modal width='max-content' onClose={() => setModalInfo({
        observation: null,
        show: false
    })}>
        <div className={style.container}>
            <Loader size='size-4' color='stroke-grey-1'/>
        </div>
    </Modal>

    if(questionnaire.isError || !questionnaire.data?.length) {
        questionnaire.isError && errorHandler?.report(`Error obteniendo questionnaire ${JSON.stringify(questionnaire.error)}`)
        swal('No hemos podido obtener el cuestionario', 'Por favor, intente nuevamente', 'warning')
        return <></>
    }

  return (
    <Modal width='max-content' onClose={() => setModalInfo({
        observation: null,
        show: false
    })}>
        <div className={style.container}>
            <EditObservationForm defaultValue={getDefaultValues(observation)} patientId={patientId} observation={observation} questionnaire={questionnaire.data[0]} moduleInView={moduleValue}/>
        </div>
        
    </Modal>
  )
}
