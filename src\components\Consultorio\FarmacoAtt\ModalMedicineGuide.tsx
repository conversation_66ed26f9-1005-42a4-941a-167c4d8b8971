import React from 'react'
import { Title, Text, Button } from '@umahealth/occipital'
import { Modal } from 'occipital-new'
import { IMedicineGuideResponse } from '@/services/reactQuery/useMedicineGuide'
import { downloadPdf } from './utils/downloadPdf'

interface IProps {
    setMedicineGuide: React.Dispatch<React.SetStateAction<string | null>>
    content: IMedicineGuideResponse
}

export const ModalMedicineGuide = ({ setMedicineGuide, content }: IProps) => {

  return (
    <Modal width='50%' height='384px' onClose={() => setMedicineGuide(null)}>
        <div id='medicineGuide' className='w-full h-72 overflow-y-auto'>
            <Title hierarchy='h3' weight='font-semibold' size='text-l'>
                Pauta para - {content?.output?.name}
            </Title>
            <Text tag='p'>
                {content?.output?.summary}
            </Text>
        </div>
        <Button action={() => downloadPdf('medicineGuide', `Pauta - ${content?.output?.name}`, true)} className='mt-2' type='button'>Descargar</Button>
    </Modal>
  )
}
