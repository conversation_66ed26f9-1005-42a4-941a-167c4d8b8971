import React, { useEffect, useState } from 'react'
import { ModuleValue } from './FarmacoAttModules'
import { Button, Title } from '@umahealth/occipital'
import { InputContainer } from '@/components/GeneralComponents/InputFarmatodo/InputContainer'
import { useSearchParams } from 'next/navigation'
import { FieldValues, useForm } from 'react-hook-form'
import { IQuestionnaire } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IQuestionnaire'
import { transformToFhirResource } from './utils/transformToFhirResource'
import { createFhirResource } from '@/components/MyPatients/infraestructure/services/writeFhirResource'
import { getResourceByFilters } from '@/components/MyPatients/infraestructure/services/getResourceByFilters'
import style from './styles/moduleViewer.module.scss'
import swal from 'sweetalert'
import { groupItemsByLinkId } from '@/components/MyPatients/presentation/utils/groupItemsByLinkId'
import { PatientEvolution } from '@/components/MyPatients/presentation/ui/VerDetalles/Evloution/PatientEvolution'
import { SelectRecord } from '@/components/MyPatients/presentation/ui/VerDetalles/records/SelectRecord'
import { IfhirR4 } from '@smile-cdr/fhirts'
import { IPatient } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPatient'
import { patchFhirResource } from '@/components/MyPatients/infraestructure/services/patchFhirResource'
import { MedicineRecord } from './MedicineRecord'
import { EncounterFiles } from './EncounterFiles'
import { getFhirPatient } from '@/components/MyPatients/infraestructure/services/getFhirPatient'
import { errorHandler } from '@/config/stackdriver'
import PRM from './PRM'
import PRH from './PRH'
import { queryClient } from '@/providers/QueryClient'
import { classifyImc } from './utils/classifyImc'
import { HistoryAttObservations } from './HistoryAttObservations'
import { trackCustomEvent } from '@/components/MyPatients/Utils/trackPatientData'
import { useBraze } from '@/providers/BrazeProvider'
import { IaReport } from './IaReport'
import { InputsEncounter } from './InputsEncounter'
import { SwitchParametersView } from './SwitchParametersView'
import { IEncounter } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IEncounter'

const transformObjectValuesToEmptyString = (object: object) => {
  return Object.fromEntries(Object.keys(object).map((key) => [key, '']))
}
interface IProps {
  moduleInView: ModuleValue | undefined
}

export interface RowData {
  id: number
  'Objetivo de la intervención': string
  'Descripción del posible PRH': string
  'Intervención farmacéutica': string
  interventionClasification: string
  medicine: string
}

export const ModuleViewer = ({ moduleInView }: IProps) => {
  const braze = useBraze()
  const [loading, setLoading] = useState(false)
  const [loadingSubmit, setLoadingSubmit] = useState(false)
  const searchParams = useSearchParams()
  const encounterId = searchParams.get('encounterId') as string
  const healthcareId = searchParams.get('healthcareId') ?? ''
  const [questionnaireModule, setQuestionnaireModule] = useState<{
    data: IQuestionnaire | null | undefined
    error: true | null
  }>()
  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    control,
    formState: { errors },
  } = useForm()
  const [patient, setPatient] = useState<IPatient | null>(null)
  const [data, setData] = useState<RowData[]>([])
  const [editingItem, setEditingItem] = useState<RowData | null>(null)
  const formData: any = watch()

  useEffect(() => {
    const getQuestionnaire = async () => {
      try {
        setLoading(true)
        const patient = await getFhirPatient(healthcareId)
        setPatient(patient)

        const response = await getResourceByFilters<IQuestionnaire>(
          'Questionnaire',
          `identifier=${moduleInView}`
        )
        setQuestionnaireModule({
          data: response?.data?.[0]?.resource,
          error: null,
        })
      } catch (error) {
        setQuestionnaireModule({ data: null, error: true })
      } finally {
        setLoading(false)
      }
    }
    moduleInView && getQuestionnaire()
  }, [moduleInView])

  if (moduleInView === 'patientEvolution') {
    return <PatientEvolution healthcareId={healthcareId} patient={patient} />
  }
  if (moduleInView === 'patientRecord') {
    return <SelectRecord healthcareId={healthcareId} />
  }
  if (moduleInView === 'medicineHour') {
    return <MedicineRecord patient={patient} encounterId={encounterId} />
  }
  if (moduleInView === 'encounterFiles') {
    return <EncounterFiles />
  }

  if (loading) {
    return <span className="ml-2">Cargando...</span>
  }

  if (!questionnaireModule?.data?.item?.length) {
    return <>No hemos podido obtener el cuestionario</>
  }

  const submitForm = async (formData: FieldValues) => {
    setLoadingSubmit(true)
    try {
      const dataTransformed = transformToFhirResource.toObservation(
        formData,
        `Encounter/${encounterId}`,
        nameObservations[moduleInView as Exclude<ModuleValue, 'PRH' | 'PRM'>],
        healthcareId
      )
      const { data } = await getResourceByFilters<IEncounter>(
        'Encounter',
        `_id=${encounterId}`
      )
      let responseFhirId
      if (data?.[0]?.resource) {
        const prevReasonData: NonNullable<
          IfhirR4.IEncounter['reasonReference']
        > = data[0].resource.reasonReference || []
        responseFhirId = await createFhirResource(
          'Observation',
          JSON.stringify(dataTransformed)
        )
        const reasonDataWithNewObservation = [
          ...prevReasonData,
          {
            type: 'Observation',
            reference: `Observation/${responseFhirId}`,
            display: nameObservations[moduleInView as ModuleValue],
          },
        ]
        await patchFhirResource(
          encounterId,
          'Encounter',
          '/reasonReference',
          JSON.stringify(reasonDataWithNewObservation)
        )
      }

      if (responseFhirId) {
        await swal('Observacion creada con exito', '', 'success')
        reset(transformObjectValuesToEmptyString(formData))
        if (
          moduleInView &&
          (moduleInView === 'treatment' || moduleInView === 'labs')
        ) {
          queryClient.removeQueries('useGetResourceByFilters')
        }
        if (moduleInView === 'PRH' || moduleInView === 'PRM') {
          queryClient.removeQueries('prh_observation')
          queryClient.removeQueries('prm_observation')
        }
        trackCustomEvent(braze, healthcareId, moduleInView ?? '', formData)
        return
      }
      await swal(
        'No hemos podido crear la observacion',
        'Si el error persiste...',
        'warning'
      )
    } catch (error) {
      errorHandler?.report(error as string)
      await swal(
        'No hemos podido crear la observacion',
        'Por favor, intente nuevamente',
        'warning'
      )
    } finally {
      setLoadingSubmit(false)
    }
  }

  const nameObservations: { [key in ModuleValue]: string } = {
    PHARMACIST: 'PHARMACIST',
    labs: 'LAB_OBSERVATION',
    PRH: 'PRH_OBSERVATION',
    PRM: 'PRM_OBSERVATION',
    treatment: 'TREATMENT_OBSERVATION',
    parameters: 'PARAMETERS_OBSERVATION',
    medicineHour: 'MEDICINE_HOUR_OBSERVATION',
    patientEvolution: 'PATIENT_EVOLUTION',
    patientRecord: 'PATIENT_RECORD',
    encounterFiles: '',
    PRHForm: '',
    PRMForm: '',
  }

  const questionnaireGroupedByLinkId = groupItemsByLinkId(
    questionnaireModule.data.item
  )

  const handleFormSubmit = () => {
    if (editingItem) {
      setData(
        data.map((item) =>
          item.id === editingItem.id
            ? { ...formData, id: editingItem.id }
            : item
        )
      )
      setEditingItem(null)
    } else {
      setData([...data, { ...formData, id: Date.now() }])
      reset()
    }
  }

  const renderIMC =
    moduleInView === 'parameters' && watch('Altura (cm)') && watch('Peso (kg)')
  const IMC =
    Number(watch('Peso (kg)')) / (Number(watch('Altura (cm)')) / 100) ** 2

  return (
    <>
      {!moduleInView ? (
        <></>
      ) : (
        <>
          {moduleInView === 'parameters' && (
            <SwitchParametersView
              moduleInView={moduleInView}
              patientId={healthcareId}
            />
          )}
          {['treatment', 'labs'].includes(moduleInView) && (
            <HistoryAttObservations
              moduleInView={moduleInView as 'treatment' | 'labs'}
              patientId={healthcareId}
            />
          )}
          {(moduleInView === 'PRM' || moduleInView === 'PRH') && (
            <div className="mt-3 w-full flex justify-end content-center">
              <IaReport type={moduleInView} setValue={setValue} />
            </div>
          )}
          <form
            className="flex flex-col w-full"
            onSubmit={handleSubmit(async (data) => {
              await submitForm(data)
              handleFormSubmit()
            })}
          >
            <Title
              hierarchy="h3"
              weight="font-bold"
              size="text-m"
              color="text-primary"
              className="w-fit"
            >
              {questionnaireModule.data.title}
            </Title>
            {questionnaireGroupedByLinkId?.map((arrayGrouped, i) => {
              return (
                <>
                  <div className={style.formSectionContainer}>
                    {arrayGrouped?.map((input) => (
                      <InputContainer key={input.linkId}>
                        <InputsEncounter
                          item={input}
                          moduleInView={moduleInView}
                          register={register}
                          watch={watch}
                          setValue={setValue}
                          control={control}
                          errors={errors}
                        />
                      </InputContainer>
                    ))}
                  </div>
                  {questionnaireGroupedByLinkId?.length - 1 !== i && (
                    <div className={style.separatorInputs}></div>
                  )}
                </>
              )
            })}
            {renderIMC && (
              <span className={style.imcText}>
                Índice de masa corporal (IMC): {Math.round(IMC * 100) / 100}
                {' - '}
                Clasificación: {classifyImc(IMC)}
              </span>
            )}
            <Button
              size="full"
              type="submit"
              variant="filled"
              loading={loadingSubmit}
              className="max-w-[300px] my-3 self-center"
            >
              Guardar
            </Button>
          </form>
        </>
      )}
      {moduleInView === 'PRH' && (
        <PRH rowData={data} healthcareId={healthcareId} patient={patient} />
      )}
      {moduleInView === 'PRM' && (
        <PRM rowData={data} healthcareId={healthcareId} patient={patient} />
      )}
    </>
  )
}
