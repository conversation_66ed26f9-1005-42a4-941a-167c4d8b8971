import React, { useState } from "react";
import { Icon, Modal, Paragraph } from "occipital-new";
import { useSavePrmPdf } from "@/services/reactQuery/useSavePrmPdf";
import styles from "./styles/phrReport.module.scss";
import { showPdfError } from "../utils/showPdfError";
import { useGetResourceByFilters } from "@/services/reactQuery/useGetResourceByFilters";
import { useSearchParams } from "next/navigation";
import { IObservation } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation";

const PRHReport = ({
  patientName,
  observations,
  intervention,
  pharmacyEmail,
  resource
}: {
  patientName: string;
  observations: string;
  intervention: string;
  pharmacyEmail: string;
  resource: IObservation
}) => {
  const searchParams = useSearchParams()
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const encounterId = searchParams.get('encounterId') || ''
  const patientId = searchParams.get('healthcareId') || ''
  const pharmacistObservation = useGetResourceByFilters<IObservation>('Observation', `code:text=PHARMACIST&subject=${patientId}&encounter=${encounterId}`)
  const savePdf = useSavePrmPdf({
    type: 'prh',
    patientName,
    observations,
    intervention,
    resource
  })

  if (savePdf.isError) {
    showPdfError()
      .then(() => {
        savePdf.reset()
      })
  }

  const openModal = () => {
    setModalIsOpen(true);
  };

  const closeModal = () => {
    setModalIsOpen(false);
  };

  const disabledButton = pharmacistObservation.isLoading

  return (
    <div>
      <button className={`${styles.button} !w-11 !h-11`} onClick={openModal}>
        <Icon name="book" size="l" color="background-light" />
      </button>
      {modalIsOpen && (
        <Modal width="max-width" onClose={closeModal}>
          <div id="PRHReport" className={styles.PRHReport}>
            <h1>PRHReport Especial</h1>
            <Paragraph size="s" color="grey-1" weight="regular">
              Farmacia:
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="regular">
              Dirección:
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="regular">
              Atención Sr(a): {patientName}
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="regular">
              Estimado Sr(a):
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="semibold">
              Durante su participación en nuestro servicio de Atención
              Farmacéutica y gracias a la información suministrada por usted,
              pudimos detectar la siguiente situación relacionada con sus
              hábitos, la cual consideramos puede afectar sus resultados en el
              control de su condición de salud:
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="regular">
              {observations}
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="semibold">
              Ante esto, nuestra recomendación para usted es:
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="regular">
              {intervention}
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="semibold">
              Esperando que esta información sea de utilidad y contribuya con su
              bienestar, quedo a su orden.
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="semibold">
              Firma y número de colegiado del farmacéutico
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="regular">
              Nombre del Farmacéutico:
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="semibold">
              Teléfono de contacto con la tienda:
            </Paragraph>
            <Paragraph size="s" color="grey-1" weight="regular">
              Email Tienda: {pharmacyEmail}
            </Paragraph>
          </div>
          <button disabled={disabledButton} className={styles.PRHReportButton} onClick={() => savePdf.mutate({
            pharmacistName: pharmacistObservation.data?.[0]?.component?.find(comp => comp.code.text === 'doctor')?.valueString ?? '',
            pharmacistCredential: String(pharmacistObservation.data?.[0]?.component?.find(comp => comp.code.text === 'Cédula')?.valueQuantity?.value) ?? '',
          })}>
            {pharmacistObservation.isLoading ? 'Cargando...' : 'Imprimir Reporte'}
          </button>
          <button className={styles.PRHReportButton} onClick={closeModal}>
            Cerrar
          </button>
        </Modal>
      )}
    </div>
  );
};

export default PRHReport;
