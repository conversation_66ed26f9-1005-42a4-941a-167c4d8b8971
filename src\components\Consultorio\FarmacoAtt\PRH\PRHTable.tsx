import React, { useState } from "react";
import styles from "./styles/prhTable.module.scss";
import PRHReport from "./PRHReport";
import { getComponentsEditedFromTable } from "../utils/getComponentsEditedFromTable";
import { IfhirR4 } from "@smile-cdr/fhirts";
import { Loader, Paragraph, Text } from "@umahealth/occipital";
import { Icon } from "@umahealth/occipital/client";
import {
  patientEvolution,
  typeOfPrh,
  typeOfintervention,
} from "./answerOptions";
import usePrhUpdate, { UpdatePrhData } from "../utils/usePrhUpdate";
import TextareaFarmatodo from "@/components/GeneralComponents/InputFarmatodo/TextareaFarmatodo";
import SelectFarmatodo from "@/components/GeneralComponents/InputFarmatodo/SelectFarmatodo";
import { ObservationComponent } from "@smile-cdr/fhirts/dist/FHIR-R4/classes/observationComponent";
import { ICustomBundleEntry } from "@/components/MyPatients/infraestructure/services/getResourceByFilters";
import { IObservation } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation";

interface TableProps {
  data: ICustomBundleEntry<IObservation>[];
  isLoading: boolean;
  isFetching: boolean;
  patient: IfhirR4.IPatient | null;
}

const PrhTable: React.FC<TableProps> = ({
  data,
  isLoading,
  isFetching,
  patient,
}) => {
  const [visibleRows, setVisibleRows] = useState(5);
  const [editingRow, setEditingRow] = useState<null | string>(null);
  const [editedData, setEditedData] = useState(data);
  const updatePrh = usePrhUpdate();

  const showMoreRows = () => {
    setVisibleRows((prevVisibleRows) => prevVisibleRows + 5);
  };

  const handleEdit = (id: string) => {
    setEditingRow(id);
    const currentData = data.find((d) => d.resource?.id === id);
    if (currentData) {
      setEditedData([currentData]);
    }
  };

  const handleSave = async () => {
    const updatedData = data.map((d) => {
      if (d.resource?.id === editingRow) {
        return editedData[0];
      }
      return d;
    });
    setEditingRow(null);
    setEditedData(updatedData);

    const observationToUpdate = {
      idResource: editedData?.[0]?.resource?.id ?? "",
      dataFhir: editedData[0].resource,
    };
    await updatePrh.mutateAsync(observationToUpdate as UpdatePrhData);
  };

  const getComponentValue = (
    components: IObservation["component"],
    codeText: string
  ) => {
    const component = components?.find((comp) => comp.code.text === codeText);
    return component ? component.valueString : "";
  };

  const handleChange = (id: string, codeText: string, value: string) => {
    setEditedData(
      editedData.map((d: any) => {
        if (d.resource.id === id) {
          return {
            ...d,
            resource: {
              ...d.resource,
              component: getComponentsEditedFromTable(d.resource.component, codeText, value),
            },
          };
        }
        return d;
      })
    );
  };

  const renderInputField = (
    resource: IObservation,
    comp?: ObservationComponent | string,
  ) => {
    const fieldToUpdate = typeof comp === 'string' ? comp : comp?.code.text || ''
    const value = getComponentValue(
      editedData[0].resource?.component,
      fieldToUpdate
    );

    switch (fieldToUpdate) {
      case "Clasificación del PRH":
        return (
          <SelectFarmatodo
            value={value}
            className="text-xxs !w-full"
            onChange={(e) =>
              handleChange(resource?.id as string, fieldToUpdate, e.target.value)
            }
            answerOption={typeOfPrh}
          />
        );
      case "Clasificación de intervención":
        return (
          <SelectFarmatodo
            value={value}
            className="text-xxs !w-full"
            onChange={(e) =>
              handleChange(resource?.id as string, fieldToUpdate, e.target.value)
            }
            answerOption={typeOfintervention}
          />
        );
      case "Evolución del paciente":
        return (
          <SelectFarmatodo
            value={value}
            className="text-xxs !w-full"
            onChange={(e) =>
              handleChange(resource?.id as string, fieldToUpdate, e.target.value)
            }
            answerOption={patientEvolution}
          />
        );
      default:
        return (
          <TextareaFarmatodo
            className="!text-xxs w-full !resize-y"
            name={`${fieldToUpdate}/${resource.id}`}
            value={value}
            onChange={e => handleChange(resource.id as string, fieldToUpdate, e.target.value)}
          />
        );
    }
  };

  const getRow = (
    resource: IObservation,
    editingRow: string | null,
    fieldToSearch: string,
  ) => {
    const component = resource?.component?.find(comp => comp?.code.text === fieldToSearch)

    return <td className={`${styles.td} max-w-[150px] !overflow-hidden`}>
      {editingRow === resource?.id
        ? renderInputField(resource, component || fieldToSearch)
        : <Text tag="span" size="text-xxs" weight="font-regular">{getComponentValue(resource.component, fieldToSearch)}</Text>
      }
    </td>
  }


  return (
    <div className="overflow-auto w-full">
      {!isLoading || data?.length ? (
        <table className='table-fixed w-full'>
          <thead>
            <tr>
              <th className="whitespace-normal"><Text tag="span" size="text-xs" weight="font-semibold">Descripción del posible PRH</Text></th>
              <th className="whitespace-normal"><Text tag="span" size="text-xs" weight="font-semibold">Intervención farmacéutica</Text></th>
              <th className="whitespace-normal"><Text tag="span" size="text-xs" weight="font-semibold">Objetivo de la Intervención</Text></th>
              <th className="whitespace-normal"><Text tag="span" size="text-xs" weight="font-semibold">Clasificación del PRH</Text></th>
              <th className="whitespace-normal"><Text tag="span" size="text-xs" weight="font-semibold">Clasificación de Intervención</Text></th>
              <th className="whitespace-normal"><Text tag="span" size="text-xs" weight="font-semibold">Evolución del paciente</Text></th>
              <th className="whitespace-normal"><Text tag="span" size="text-xs" weight="font-semibold">Acciones</Text></th>
            </tr>
          </thead>
          <tbody>
            {data.slice(0, visibleRows).map(({ resource }) => (
              <tr key={resource?.id}>
                {getRow(resource, editingRow, 'Descripción del posible PRH')}
                {getRow(resource, editingRow, 'Intervención farmacéutica')}
                {getRow(resource, editingRow, 'Objetivo de la intervención')}
                {getRow(resource, editingRow, 'Clasificación del PRH')}
                {getRow(resource, editingRow, 'Clasificación de intervención')}
                {getRow(resource, editingRow, 'Evolución del paciente')}
                <td className={styles.td}>
                  {editingRow === resource?.id ? (
                    <>
                      <button
                        type="button"
                        className={styles.button}
                        onClick={handleSave}
                      >
                        Guardar
                      </button>
                      <button
                        className={styles.button}
                        onClick={() => setEditingRow(null)}
                      >
                        Cancelar
                      </button>
                    </>
                  ) : (
                    <div className="flex flex-col gap-1">
                      <button
                        type="button"
                        className={`${styles.button} !w-11 !h-11`}
                        onClick={() => handleEdit(resource?.id as string)}
                      >
                        <Icon name="pencil" size="size-5" color="text-grey-6" />
                      </button>
                      <PRHReport
                        patientName={patient?.name?.[0].text ?? ""}
                        observations={
                          getComponentValue(
                            resource?.component,
                            "Descripción del posible PRH"
                          ) ?? ""
                        }
                        intervention={
                          getComponentValue(
                            resource?.component,
                            "Intervención farmacéutica"
                          ) ?? ""
                        }
                        resource={resource}
                        pharmacyEmail="<EMAIL>"
                      />
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <Paragraph
          className="flex gap-2 items-center"
          weight="font-regular"
          size="text-m"
          color="text-primary"
        >
          Cargando historico... <Loader size='size-4' color="stroke-primary" />
        </Paragraph>
      )}
      <div className="flex flex-col gap-2 items-center justify-center mb-8 w-full">
        {isFetching && !isLoading && (
          <Paragraph
            className="flex gap-2 items-center"
            weight="font-regular"
            size="text-m"
            color="text-primary"
          >
            Cargando... <Loader size='size-4' color="stroke-primary" />
          </Paragraph>
        )}
        {visibleRows < data?.length && (
          <button
            type="button"
            onClick={showMoreRows}
            className={styles.showMoreButton}
          >
            Mostrar más
          </button>
        )}
      </div>
    </div>
  );
};

export default PrhTable;
