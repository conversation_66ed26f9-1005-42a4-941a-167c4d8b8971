export const typeOfPrh = [
  {
    valueCoding: {
      code: "No presenta PRH NP: No presente PRH",
      display: "No presenta PRH NP: No presente PRH"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Adecuación AE: Ejercicio no adecuado",
      display: "Problemas de Adecuación AE: Ejercicio no adecuado"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Adecuación AD: Dieta no adecuada",
      display: "Problemas de Adecuación AD: Dieta no adecuada"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Cumplimiento CT: Suspender hábito de fumar indicado, pero no cumple",
      display: "Problemas de Cumplimiento CT: Suspender hábito de fumar indicado, pero no cumple"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Cumplimiento Cau: Autocontrol indicado, pero no cumple",
      display: "Problemas de Cumplimiento Cau: Autocontrol indicado, pero no cumple"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Cumplimiento CI: Suspender consumo de café indicado, pero no cumple",
      display: "Problemas de Cumplimiento CI: Suspender consumo de café indicado, pero no cumple"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Cumplimiento CA: Suspender consumo de alcohol indicado, pero no cumple",
      display: "Problemas de Cumplimiento CA: Suspender consumo de alcohol indicado, pero no cumple"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Cumplimiento CE: Ejercicio indicado, pero no cumple",
      display: "Problemas de Cumplimiento CE: Ejercicio indicado, pero no cumple"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Cumplimiento CD: Dieta indicada, pero no cumple",
      display: "Problemas de Cumplimiento CD: Dieta indicada, pero no cumple"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Indicación -IT: Necesita indicación de suspender hábito de fumar",
      display: "Problemas de Indicación -IT: Necesita indicación de suspender hábito de fumar"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Indicación -Iau: Necesita indicación de autocontrol",
      display: "Problemas de Indicación -Iau: Necesita indicación de autocontrol"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Indicación -II: Necesita indicación de suspender consumo de café",
      display: "Problemas de Indicación -II: Necesita indicación de suspender consumo de café"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Indicación -IA: Necesita indicación de suspender consumo de alcohol",
      display: "Problemas de Indicación -IA: Necesita indicación de suspender consumo de alcohol"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Indicación -IE: Necesita indicación de ejercicio",
      display: "Problemas de Indicación -IE: Necesita indicación de ejercicio"
    }
  },
  {
    valueCoding: {
      code: "Problemas de Indicación -ID: Necesita indicación de dieta",
      display: "Problemas de Indicación -ID: Necesita indicación de dieta"
    }
  }
];

export const typeOfintervention = [
  {
    valueCoding: {
      code: "H1: Se asesora y entrega consejo por escrito",
      display: "H1: Se asesora y entrega consejo por escrito"
    }
  },
  {
    valueCoding: {
      code: "H2: Se sugiere consulta con el médico o especialista",
      display: "H2: Se sugiere consulta con el médico o especialista"
    }
  },
]

export const patientEvolution = [
  {
    valueCoding: {
      code: "Está Igual",
      display: "Está Igual"
    }
  },
  {
    valueCoding: {
      code: "Objetivo cumplido",
      display: "Objetivo cumplido"
    }
  },
  {
    valueCoding: {
      code: "Mejoró",
      display: "Mejoró"
    }
  },
  {
    valueCoding: {
      code: "Empeoró",
      display: "Empeoró"
    }
  }
];
