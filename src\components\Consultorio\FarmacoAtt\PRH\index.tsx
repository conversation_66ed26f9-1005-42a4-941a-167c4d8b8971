import React from "react";
import PrhTable from "./PRHTable";
import { RowData } from "../ModuleViewer";
import { usePrhData } from "../utils/useGetPrhData";
import { IfhirR4 } from "@smile-cdr/fhirts";

const PRH = (props: {
  rowData: RowData[];
  healthcareId: string;
  patient: IfhirR4.IPatient | null;
}) => {
  const { data, isFetching, isLoading } = usePrhData(props.healthcareId);

  return (
    <>
      {data && (
        <PrhTable
          data={data}
          isLoading={isLoading}
          isFetching={isFetching}
          patient={props.patient}
        />
      )}
    </>
  );
};

export default PRH;
