/* styles/Tabla.module.css */
.table {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
  }

.th,
.td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.th {
    background-color: #f2f2f2;
}

.tr:nth-child(even) {
    background-color: #f9f9f9;
}

.tr:hover {
    background-color: #ddd;
}

.button {
    padding: 5px 10px;
    margin-right: 5px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.button:hover {
    background-color: #0056b3;
}
