export const typeOfPrm = [
  {
    code: "Efectividad - Fármaco Inadecuado - Trastorno resistente a la medicación",
    display:
      "Efectividad - Fármaco Inadecuado - Trastorno resistente a la medicación",
  },
  {
    code: "Efectividad - Posología Baja - Administración incorrecta",
    display: "Efectividad - Posología Baja - Administración incorrecta",
  },
  {
    code: "Efectividad - Posología Baja - Conservación incorrecta",
    display: "Efectividad - Posología Baja - Conservación incorrecta",
  },
  {
    code: "Efectividad - Posología Baja - Interacción farmacológica",
    display: "Efectividad - Posología Baja - Interacción farmacológica",
  },
  {
    code: "Efectividad - Posología Baja - Por calidad del medicamento",
    display: "Efectividad - Posología Baja - Por calidad del medicamento",
  },
  {
    code: "Efectividad - Posología Baja - Sospecha de dosis inadecuada",
    display: "Efectividad - Posología Baja - Sospecha de dosis inadecuada",
  },
  {
    code: "Efectividad - Posología Baja - Sospecha de duración insuficiente del tratamiento",
    display:
      "Efectividad - Posología Baja - Sospecha de duración insuficiente del tratamiento",
  },
  {
    code: "Efectividad - Posología Baja - Sospecha de intervalo de dosis inadecuado",
    display:
      "Efectividad - Posología Baja - Sospecha de intervalo de dosis inadecuado",
  },
  {
    code: "Indicación - Necesita Tratamiento - Presenta un problema de salud que requiere tratamiento",
    display:
      "Indicación - Necesita Tratamiento - Presenta un problema de salud que requiere tratamiento",
  },
  {
    code: "Indicación - Necesita Tratamiento - Requiere tratamiento asociado o preventivo",
    display:
      "Indicación - Necesita Tratamiento - Requiere tratamiento asociado o preventivo",
  },
  {
    code: "Indicación - No Necesita Tratamiento - Adicción/consumo de droga",
    display: "Indicación - No Necesita Tratamiento - Adicción/consumo de droga",
  },
  {
    code: "Indicación - No Necesita Tratamiento - Automedicación irresponsable",
    display:
      "Indicación - No Necesita Tratamiento - Automedicación irresponsable",
  },
  {
    code: "Indicación - No Necesita Tratamiento - Tratamiento innecesario",
    display: "Indicación - No Necesita Tratamiento - Tratamiento innecesario",
  },
  {
    code: "Seguridad - Posología Alta - Interacción farmacológica",
    display: "Seguridad - Posología Alta - Interacción farmacológica",
  },
  {
    code: "Seguridad - Posología Alta - Por calidad del medicamento",
    display: "Seguridad - Posología Alta - Por calidad del medicamento",
  },
  {
    code: "Seguridad - Posología Alta - Sospecha de dosis inadecuada",
    display: "Seguridad - Posología Alta - Sospecha de dosis inadecuada",
  },
  {
    code: "Seguridad - Posología Alta - Sospecha de duración exagerada del tratamiento",
    display:
      "Seguridad - Posología Alta - Sospecha de duración exagerada del tratamiento",
  },
  {
    code: "Seguridad - Posología Alta - Sospecha de intervalo de dosis inadecuada",
    display:
      "Seguridad - Posología Alta - Sospecha de intervalo de dosis inadecuada",
  },
  {
    code: "Seguridad - Reacción Adversa al Medicamento - Administración incorrecta",
    display:
      "Seguridad - Reacción Adversa al Medicamento - Administración incorrecta",
  },
  {
    code: "Seguridad - Reacción Adversa al Medicamento - Fármaco peligroso para el paciente",
    display:
      "Seguridad - Reacción Adversa al Medicamento - Fármaco peligroso para el paciente",
  },
  {
    code: "Seguridad - Reacción Adversa al Medicamento - Interacción farmacológica",
    display:
      "Seguridad - Reacción Adversa al Medicamento - Interacción farmacológica",
  },
  {
    code: "Seguridad - Reacción Adversa al Medicamento - Posible efecto indeseable o adverso",
    display:
      "Seguridad - Reacción Adversa al Medicamento - Posible efecto indeseable o adverso",
  },
  {
    code: "Seguridad - Reacción Adversa al Medicamento - Reacción Alérgica",
    display: "Seguridad - Reacción Adversa al Medicamento - Reacción Alérgica",
  },
  {
    code: "Cumplimiento - Producto no disponible",
    display: "Cumplimiento - Producto no disponible",
  },
  {
    code: "Cumplimiento - Recursos insuficientes para adquirir el medicamento",
    display:
      "Cumplimiento - Recursos insuficientes para adquirir el medicamento",
  },
  {
    code: "Cumplimiento - Dificultad para realizar el tratamiento",
    display: "Cumplimiento - Dificultad para realizar el tratamiento",
  },
  {
    code: "Cumplimiento - Falta de comprensión del tratamiento",
    display: "Cumplimiento - Falta de comprensión del tratamiento",
  },
  {
    code: "Cumplimiento - Negativa al tratamiento",
    display: "Cumplimiento - Negativa al tratamiento",
  },
  { code: "Cumplimiento - Olvidos", display: "Cumplimiento - Olvidos" },
  {
    code: "Efectividad - Fármaco Inadecuado - Sospecha de fármaco inadecuado",
    display:
      "Efectividad - Fármaco Inadecuado - Sospecha de fármaco inadecuado",
  },
];

export const typeOfintervention = [
  {
    code: "Educacion y/o informacion por escrito",
    display: "Educacion y/o informacion por escrito",
  },
  {
    code: "Cambio de posologia o forma farmaceutica de medicamento de venta sin prescripcion (SPF)",
    display:
      "Cambio de posologia o forma farmaceutica de medicamento de venta sin prescripcion (SPF)",
  },
  {
    code: "No se dispensa el medicamento y se deriva al medico",
    display: "No se dispensa el medicamento y se deriva al medico",
  },
  {
    code: "Se suspende la medicacion con o sin derivacion al medico",
    display: "Se suspende la medicacion con o sin derivacion al medico",
  },
  {
    code: "Se inicia tratamiento con medicacion de venta SPF",
    display: "Se inicia tratamiento con medicacion de venta SPF",
  },
  {
    code: "Sustitucion por un generico con o sin consultar al medico",
    display: "Sustitucion por un generico con o sin consultar al medico",
  },
  {
    code: "Se consulta al Centro de Farmacovigilancia (CEFARVI)",
    display: "Se consulta al Centro de Farmacovigilancia (CEFARVI)",
  },
  {
    code: "Recomendacion de medidas no farmacologicas con o sin derivacion al medico",
    display:
      "Recomendacion de medidas no farmacologicas con o sin derivacion al medico",
  },
  {
    code: "Sugerencia de cambio de posologia o forma farmaceutica de un medicamento de venta con prescripcion (CPF)",
    display:
      "Sugerencia de cambio de posologia o forma farmaceutica de un medicamento de venta con prescripcion (CPF)",
  },
  {
    code: "Advertencia de RAM, interaccion farmacologica o problema de calidad del medicamento con o sin derivacion al medico",
    display:
      "Advertencia de RAM, interaccion farmacologica o problema de calidad del medicamento con o sin derivacion al medico",
  },
  {
    code: "La solucion no depende del farmaceutico. Se deriva al medico o especialista",
    display:
      "La solucion no depende del farmaceutico. Se deriva al medico o especialista",
  },
];

export const patientEvolution = [
  {
    code: "Está Igual",
    display: "Está Igual",
  },
  {
    code: "Objetivo cumplido",
    display: "Objetivo cumplido",
  },
  {
    code: "Mejoró",
    display: "Mejoró",
  },
  {
    code: "Empeoró",
    display: "Empeoró",
  },
];
