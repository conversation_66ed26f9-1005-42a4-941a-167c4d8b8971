import React from "react";
import { RowData } from "../ModuleViewer";
import { IfhirR4 } from "@smile-cdr/fhirts";
import { usePrmData } from "../utils/useGetPrmData";
import PrmTable from "./PRMTable";

const PRM = (props: {
  rowData: RowData[];
  healthcareId: string;
  patient: IfhirR4.IPatient | null;
}) => {
  const { data, isFetching, isLoading } = usePrmData(props.healthcareId);

  return (
    <>
      {data && (
        <PrmTable
          data={data}
          isLoading={isLoading}
          isFetching={isFetching}
          patient={props.patient}
        />
      )}
    </>
  );
};

export default PRM;
