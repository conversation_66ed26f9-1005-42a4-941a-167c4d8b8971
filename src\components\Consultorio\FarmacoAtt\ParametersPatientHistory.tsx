import React, { useState } from 'react'
import { errorHandler } from '@/config/stackdriver'
import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation'
import { PatientParametersChart } from '../Monitoring/presentation/components/charts/PatientParametersChart'
import { PatientParametersLogs } from '../Monitoring/presentation/components/logs/PatientParametersLogs'
import { useGetResourceByFilters } from '@/services/reactQuery/useGetResourceByFilters'

export const ParametersPatientHistory = ({ patientId }: { patientId: string }) => {
    const observations = useGetResourceByFilters<IObservation>('Observation', `subject=Patient/${patientId}&code:text=PARAMETERS_OBSERVATION`)
    const [view, setView] = useState<'logs' | 'chart'>('chart')

    if (observations.isLoading) return <>Cargando documentos previos...</>

    if (observations.isError || !observations.data?.length) {
        observations.isError && errorHandler?.report(`Error obteniendo historial de PARAMETERS_OBSERVATION- ${patientId} - ${JSON.stringify(observations.error)}`)
        return <>El paciente no tiene documentos previos</>
    }

    return <>
        <div className="m-4 flex justify-start flex-col w-1/2">
            <select onChange={e => setView(e.target.value as 'chart' | 'logs')}>
                <option value="chart">
                    Parametros (Evolución)
                </option>
                <option value="logs">
                    Parametros (Registros)
                </option>
            </select>
            {view === 'chart' && <PatientParametersChart observations={observations.data} />}
            {view === 'logs' && <PatientParametersLogs observations={observations.data} />}
        </div>
    </>

}
