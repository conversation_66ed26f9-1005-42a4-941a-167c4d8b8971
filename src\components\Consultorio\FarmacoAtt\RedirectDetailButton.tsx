'use client'
import React from 'react'
import { Button } from '@umahealth/occipital'
import Link from 'next/link'
import { useSearchParams } from "next/navigation";
/**
 * Comentarios de eventos de PostHog para videollamada.
 * 
 * Este archivo contiene los eventos que se registran en PostHog para el flujo de videollamada.
 * Sirve como referencia para cuando se necesite medir este componente.
 */
//import { trackEventRedirectDetailButton } from '@/events/videocallEvents';

export const RedirectDetailButton = () => {
  const searchParams = useSearchParams()
  const patientFhirId = searchParams.get('healthcareId') ?? ''
  const encounterId = searchParams.get('encounterId') ?? ''
  const assignationId = searchParams.get('assignationId') ?? ''
  const patientUid = searchParams.get('patientUid') ?? ''
  const attType = searchParams.get('attType') ?? ''

  return (
    <div className='w-full flex justify-end mb-2'>
      <Link 
        href={`/pacientes/detalle/${patientFhirId}/${encounterId}/${assignationId}/${patientUid}/${attType}`} 
      >
        <Button variant='filled' size='extended' type='button'>
          Ver detalle
        </Button>
      </Link>
    </div>
  )
}
