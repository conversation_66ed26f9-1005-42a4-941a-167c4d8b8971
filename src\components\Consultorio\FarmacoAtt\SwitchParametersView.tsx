import { useState } from 'react'
import { HistoryAttObservations } from './HistoryAttObservations'
import { ParametersPatientHistory } from './ParametersPatientHistory'

interface IProps {
    moduleInView: 'treatment' | 'labs' | 'medicineHour' | 'parameters',
    patientId: string
}

export const SwitchParametersView = ({ patientId, moduleInView }: IProps) => {
    const [view, setView] = useState<'view' | 'edit'>('view')

    return (
        <div className="m-4 flex justify-start flex-col w-1/2">
            <select onChange={e => setView(e.target.value as 'view' | 'edit')}>
                <option value="view">
                    Ver documentos previos
                </option>
                <option value="edit">
                    Editar documentos previos
                </option>
            </select>
            {view === 'view' && <ParametersPatientHistory patientId={patientId} />}
            {view === 'edit' && <HistoryAttObservations moduleInView={moduleInView} patientId={patientId} />}
        </div>
    )
}
