@use '@/styles/global/Vars.scss';

.containerCards{
    background-color: Vars.$uma-background;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 18px 24px;
    border-radius: 36px;
    margin: 12px auto;
    h2{
        margin-bottom: 8px;
    }
}

.container{
    width: 100%;
    padding: 4px;
    margin: 2px 0;
    &:hover{
        cursor: pointer;
        color: Vars.$color-primary;
        text-decoration: underline;
    }
}