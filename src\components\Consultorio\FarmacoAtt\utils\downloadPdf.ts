import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

export const downloadPdf = async (id: string, nameFile: string, hasOverflow?: boolean) => {
    const div = document.getElementById(id)
    if (div) {
        const originalStyle = {
            height: div.style.height,
            overflowY: div.style.overflowY,
            paddingBottom: div.style.paddingBottom
        }
        if (hasOverflow) {
            div.style.height = '100%'
            div.style.paddingBottom = '100px'
            div.style.overflowY = 'visible'
        }
        const canvas = await html2canvas(div)
        const imgData = canvas.toDataURL('image/png')
        const pdf = new jsPDF({
            orientation: 'landscape',
        })
        const imgProps = pdf.getImageProperties(imgData)
        const pdfWidth = pdf.internal.pageSize.getWidth()
        const pdfHeight = hasOverflow ? pdf.internal.pageSize.getHeight() : (imgProps.height * pdfWidth) / imgProps.width

        const imgWidth = pdfWidth * 0.8
        const imgHeight = (imgProps.height * imgWidth) / imgProps.width

        const marginLeft = (pdfWidth - imgWidth) / 2
        const marginTop = (pdfHeight - imgHeight) / 2

        hasOverflow ?
            pdf.addImage(imgData, 'PNG', marginLeft, marginTop, imgWidth, imgHeight) :
            pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight)

        pdf.save(`${nameFile}.pdf`)

        div.style.height = originalStyle.height
        div.style.overflowY = originalStyle.overflowY
        div.style.paddingBottom = originalStyle.paddingBottom
    }
}

