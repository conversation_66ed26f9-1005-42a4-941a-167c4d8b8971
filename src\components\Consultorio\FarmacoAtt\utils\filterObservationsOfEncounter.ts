import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation';

const desctructuringObservation = (observation: IObservation): Record<string, string> | undefined => {
    if(!observation || !observation.component?.length) return undefined
    return observation.component.reduce((acc, curr) => {
        const value = curr.valueString
        acc[(curr.code.text as string)] = value || (curr.valueQuantity?.value ? String(curr.valueQuantity?.value) : '') || ''
        return acc
    }, {} as Record<string, string>)
}

const cleanEmptyValues = (obj: Record<string, string>) => Object.fromEntries(Object.entries(obj).filter(([, value]) => value !== ''))

const unionDataObservations = (data: Record<string, string>[]): Record<string, string>  =>{
    return data.reduce((acc, curr) => {
        const cleanObservationData = cleanEmptyValues(curr)
        const object = { ...acc, ...cleanObservationData }
        return object
      },{})
}

export function filterObservationsOfEncounter(resources: any[], encounterId: string, type: 'PRM' | 'PRH'){
    const observations = resources.filter((document: any ) => (document?.resourceType === 'Observation' && document?.code?.text !== 'PRH_OBSERVATION' && document?.code?.text !== 'PRM_OBSERVATION')) as IObservation[]
    const observationsEncounter = observations.filter(observation => observation.encounter?.reference === `Encounter/${encounterId}`)
    const dataDesctructured = observationsEncounter.map(obsEncounter => desctructuringObservation(obsEncounter)).filter(obs => !!obs) as  Record<string, string>[]
    
    return {
        patient_data: {
            ...unionDataObservations(dataDesctructured)
        },
        type: type.toLowerCase() as 'prm' | 'prh'
    }
}
