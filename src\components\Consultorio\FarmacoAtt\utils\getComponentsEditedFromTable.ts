import { IObservation } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation"

export const getComponentsEditedFromTable = (components: IObservation['component'], codeText: string, value: string) => {
    if (!components) return
    const field = components?.find(comp => comp?.code?.text === codeText)
    if (!field) {
        return [...components, {
            "code": {
                "text": codeText
            },
            "valueString": value
        }]
    }
    return components.map(comp => {
        if (comp.code.text === codeText) {
            return { ...comp, valueString: value };
        }
        return comp;
    })
}