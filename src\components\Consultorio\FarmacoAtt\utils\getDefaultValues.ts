import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation';

function transformArrayToObject(component: NonNullable<IObservation['component']>): { [key: string]: string | number } {
    return component.reduce((acc, item) => {
        const key = item.code.text ?? '';
        const value = item.valueQuantity ? item.valueQuantity.value || '' : item.valueString || '';
        acc[key] = value;
        return acc;
    }, {} as { [key: string]: string | number });
}

export const getDefaultValues = (observation: IObservation) => {
    if(!observation?.component) return undefined
    const defaultValues = transformArrayToObject(observation.component)
    if(defaultValues.doctor){
        defaultValues.indicatedBy = defaultValues.doctor
    }
    return defaultValues
}
