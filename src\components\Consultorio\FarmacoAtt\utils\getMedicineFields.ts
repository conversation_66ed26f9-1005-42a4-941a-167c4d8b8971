import { IObservation } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation";

export const getMedicineFields = (medicineObservation: IObservation) => {
  const fields = medicineObservation.component || [];

  const id =
    fields.find((component) => component.code?.text === "id")?.valueQuantity?.value || "";
  const drug =
    fields.find((component) => component.code?.text === "drug")?.valueString ||
    "";
  const hour =
    fields.find((component) => component.code?.text === "hour")?.valueString ||
    "";

  return { drug, hour, id: String(id) };
};
