import { ObservationComponent } from "@smile-cdr/fhirts/dist/FHIR-R4/classes/observationComponent"
import { IObservation } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation"
import moment from "moment"

const getComponentOptions = (data: any): ObservationComponent[] => {
    const inputsName = Object.keys(data)
    return inputsName.filter(inputsName => inputsName !== 'date').map(inputName => {
        const object: ObservationComponent = {
            "code": {
                "text": inputName
            }
        }
        if(inputName === 'medicine') {
            const value = data[inputName]?.drug ?? data[inputName] ?? ''
            object.valueString = String(value)
            return object
        } 
        if(inputName === 'doctor' || inputName === 'Cédula') {
            const value = data[inputName]?.name ?? data[inputName] ?? ''
            object.valueString = String(value)
            return object
        }
        if (Number(data[inputName])) {
            object.valueQuantity = {
                "value": Number(data[inputName]) || 0,
                "unit": 'mg/dL'
            }
        } else {
            object.valueString = String(data[inputName] || '')
        }

        return object
    })
}

export const transformToFhirResource = {
    toObservation: (data: any, encounter: string, name: string, patientId: string, editTime?: string): IObservation => ({
        "resourceType": 'Observation',
        "status": 'final',
        "code": {
            "text": name
        },
        "subject": {
            "reference": `Patient/${patientId}`
        },
        "encounter": {
            "reference": encounter,
            "type": 'Encounter'
        },
        "valueDateTime": editTime ? editTime : moment().toISOString(),
        "component": getComponentOptions(data)
    })
}