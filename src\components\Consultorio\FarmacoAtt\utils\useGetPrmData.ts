import { useQuery } from 'react-query';
import { getResourceByFilters } from '@/components/MyPatients/infraestructure/services/getResourceByFilters';
import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation';

export const usePrmData = (
    healthcareId: string | undefined
) => {
    const subject = `Patient/${healthcareId ?? ''}`;

    const { data, isError, isFetching, isLoading, refetch } = useQuery(['prm_observation', subject], async () => {
        const result = await getResourceByFilters<IObservation>(
            'Observation',
            `code:text=PRM_OBSERVATION&subject=${subject}`
        );
        return result.data;
    });

    return {
        data: data ?? [],
        error: isError ? true : null,
        isFetching,
		isLoading,
        refetch
    };
};

