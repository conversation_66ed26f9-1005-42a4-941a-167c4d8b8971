import { useMutation, useQueryClient } from 'react-query';
import { updateFhirResource } from '@/components/MyPatients/infraestructure/services/updateFhirResource';
import { IfhirR4 } from '@smile-cdr/fhirts';

export type UpdatePrhData = {
    idResource: string;
    dataFhir: IfhirR4.IObservation;
};

const usePrhUpdate = () => {
	const queryClient = useQueryClient();

    const updatePrh = async (data: UpdatePrhData) => {
        const { idResource, dataFhir } = data;
        const result = await updateFhirResource(
            idResource,                                                        
            'Observation',
            JSON.stringify(dataFhir)
        );
        return result.data;
    };


	return useMutation(updatePrh, {
        onSuccess: () => {
            queryClient.invalidateQueries(['prh_observation']);
        },
    });
};

export default usePrhUpdate;
