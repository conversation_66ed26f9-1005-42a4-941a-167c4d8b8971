import React, { useState, Fragment } from 'react'
import { useAppSelector } from '@/store/hooks'
import { FaRegAddressCard, FaPrescriptionBottleAlt, FaThermometerHalf, FaPlusCircle, FaAngleDown, FaAngleUp } from 'react-icons/fa'
import 'moment/locale/es'
import { formatDistanceToNow } from 'date-fns'
import { es } from 'date-fns/locale';

const MedicalRecord = ({ title }) => {
	const records = useAppSelector(state => state.queries.medicalRecord)
	const [arrow, setArrow] = useState(false)

	return (
		<div className='medical-record-container'>
			<div className="medical-record-title">{title ? title : 'Historia médica'}</div>
			<div className="history">
				<div onClick={() => setArrow(!arrow)}>
					<div className="subtitle" data-toggle="collapse" aria-expanded="true" aria-controls="#subtitle-collapse" href="#subtitle-collapse" role="button">
						<span>
							<FaRegAddressCard aria-hidden='true' />
							Últimas atenciones
						</span>
						{arrow ? <FaAngleUp className="history-more-btn" /> : <FaAngleDown className="history-more-btn" />}
					</div>
				</div>
				<div className="medical-record-box">
					<div className="history-records collapse multi-collapse" id="subtitle-collapse">
						{records && records.map((att, index, records) => index !== records?.length - 1 && <Fragment key={index}>
							<div className="history-record" data-toggle="collapse" href={`#att${index}`} role="button" aria-expanded="true" aria-controls={`att${index}`}>
								<span className="att"><b>{index === 0 && 'Consulta actual'}{att?.mr?.diagnostico && att?.mr?.diagnostico !== '' ? att?.mr?.diagnostico : att?.mr?.destino_final}</b></span>
								<span className="date flex-shrink col-md-4 text-right">
									<div>
										{formatDistanceToNow(new Date(att?.created_dt), {
											addSuffix: true,
											locale: es,
										})
										}
									</div>
								</span>
								<FaPlusCircle aria-hidden='true' className="history-more-btn" />
							</div>
							<div className="history-detail collapse multi-collapse" id={`att${index}`}>
								<b>Destino Final: </b>{att?.mr?.destino_final || '-'}<br />
								<b>Diagnóstico: </b>{att?.mr?.diagnostico || '-'}<br />
								<b>Epicrisis: </b>{att?.mr?.epicrisis || '-'}<br />
								<b>Observaciones: </b>{att?.mr?.observaciones || '-'}<br />
								<b>Tratamiento indicado: </b>{att?.mr?.tratamiento || '-'}<br />
							</div>
						</Fragment>)}
					</div>
				</div>
				{!arrow &&
					<>
						<div className="subtitle">
							<span>
								<FaPrescriptionBottleAlt />
								Prescripciones
							</span>
							<small>Ninguna</small>
						</div>
						<div className="subtitle">
							<span>
								<FaThermometerHalf />
								Alergias
							</span>
							<small>Ninguna</small>
						</div>
					</>
				}
			</div>
		</div>
	)
}

export default MedicalRecord