
@use '@/styles/global/Vars.scss';

.medical-record-container {
	height: 270px;
	@media (min-height: 700px) {
		height: 310px;
	}
	.medical-record-title {
		background: #c6dbe8;
		color: #51626d;
		font-weight: bold;
		border-radius: 5px;
		padding: 10px;
	}
	.history {
		padding: 0 10px;
		.subtitle {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 2px 0;
			span {
				display: flex;
				justify-content: space-between;
			}

			svg {
				margin-right: 10px;
				font-size: 1.3rem;
			}
		}
	}
	.medical-record-box {
		max-height: 163px;
		overflow-y: auto;
		@media (min-height: 700px) {
			max-height: 210px;
		}
		.history-records {
			cursor: pointer;
			.att {
				width: 400px;
			}
			.history-record {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 7px;
				border-radius: 6px;
				background: Vars.$extra-gray;
				color: Vars.$white-color;
				font-size: 14px;
				margin: 4px 6px;
			}
			.history-detail {
				background: #eaf2fa;
				color: Vars.$text-light;
				padding: 10px;
				border-radius: 6px;
				margin: 4px 8px;
			}
		}
	}
}