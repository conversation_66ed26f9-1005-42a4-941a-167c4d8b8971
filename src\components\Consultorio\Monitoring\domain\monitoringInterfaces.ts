import { Timestamp } from '@/config/firebase'

export interface BloodPressureValues {
	systolic: number;
	diastolic: number;
	measure_spot: string;
	cardioFrequency: number;
} 

export interface IMonitoringLog {
	activity: {
		discussion: boolean;
		excersice: boolean;
		food: boolean;
	};
	monitoring: string;
	other_activity: string;
	patient: object;
	result: string;
	timestamp: {
		dt_create: Timestamp;
	};
	values: BloodPressureValues,
	id?: string
}