import React, { useEffect } from 'react'
import moment from 'moment'
import { IMonitoringLog } from '@/components/Consultorio/Monitoring/domain/monitoringInterfaces'

let Chart: any
;(async () => {
    const moduleChart = await import('chart.js/auto')
    Chart = moduleChart.Chart
})()

const BloodPressureChart = ({ logs }: { logs: IMonitoringLog[] }) => {
    useEffect(() => {
        if (Chart) {
            const canvas: HTMLCanvasElement = document.getElementById(
                'bloodPressureChart'
            ) as HTMLCanvasElement
            const ctx: CanvasRenderingContext2D | null = canvas.getContext('2d')
            const labels = logs
                ?.sort(
                    (a, b) =>
                        a?.timestamp?.dt_create.toMillis() -
                        b?.timestamp?.dt_create.toMillis()
                )
                .map(log =>
                    moment(log?.timestamp?.dt_create.toDate()).format(
                        'HH:mm DD/MM/YYYY'
                    )
                )
            const pasDataset = logs?.map(
                (log: IMonitoringLog) => log?.values?.systolic
            )
            const padDataset = logs?.map(
                (log: IMonitoringLog) => log?.values?.diastolic
            )

            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    borderWidth: 2,
                    tension: 0.5,
                    borderColor: 'rgba(86, 63, 240, 1)',
                    labels: [...labels],
                    datasets: [
                        {
                            label: 'Sistólica (mmHg)',
                            data: [...pasDataset],
                            borderColor: 'rgba(255, 193, 0, 1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: 'Diastólica (mmHg)',
                            data: [...padDataset],
                            borderColor: 'rgba(86, 63, 240, 1)',
                            fill: false,
                            tension: 0.4
                        },
                    ],
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Fecha',
                            },
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Presión (mmHg)',
                            },
                        },
                    },
                },
            })

            return () => chart?.destroy()
        }
    }, [logs, Chart])

    return (
        <div className='max-w-screen-md my-2 mx-auto rounded-lg border extraThinBorder border-solid border-secondary-500 p-8 bg-white'>
            <canvas id="bloodPressureChart" width="400" height="200"></canvas>
        </div>
    )
}

export default BloodPressureChart
