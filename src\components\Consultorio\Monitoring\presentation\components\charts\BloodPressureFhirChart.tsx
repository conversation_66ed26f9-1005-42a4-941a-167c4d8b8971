import React, { useEffect } from 'react'
import moment from 'moment'
import { IfhirR4 } from '@smile-cdr/fhirts'

let Chart: any
    ; (async () => {
        const moduleChart = await import('chart.js/auto')
        Chart = moduleChart.Chart
    })()

interface ILogs {
    resource: IfhirR4.IObservation
    meta: { lastUpdated: string }
}

const BloodPressureFhirChart = ({ logs }: { logs: ILogs[] }) => {
    useEffect(() => {
        if (Chart) {
            const canvas: HTMLCanvasElement = document.getElementById(
                'BloodPressureChart',
            ) as HTMLCanvasElement
            const ctx: CanvasRenderingContext2D | null = canvas.getContext('2d')
            const labels = logs?.sort((a, b) => {
                const dateA = a?.resource?.valueDateTime
                const dateB = b?.resource?.valueDateTime
                if (!dateA) return -1
                if (!dateB) return 1
                return new Date(dateA).getTime() - new Date(dateB).getTime()
            }).map(log => {
                const date = log?.resource?.valueDateTime
                if (!date) return 'Fecha inválida'
                return moment(date)?.format('HH:mm DD/MM/YYYY')
            })
            const systolicDataset = logs?.map(logs => logs.resource.component?.find(component => component?.code?.text === 'Systolic Blood Pressure')?.valueQuantity?.value)
            const diastolicDataset = logs?.map(logs => logs.resource.component?.find(component => component?.code?.text === 'Diastolic Blood Pressure')?.valueQuantity?.value)
            const cardioFrequency = logs?.map(logs => logs.resource.component?.find(component => component?.code?.text === 'Cardio frequency')?.valueQuantity?.value)

            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [...labels],
                    datasets: [
                        {
                            label: 'Sistólica (mmHg)',
                            data: [...systolicDataset],
                            borderColor: 'rgba(239, 83, 80, 1)',
                            fill: false,
                        },
                        {
                            label: 'Diastólica (mmHg)',
                            data: [...diastolicDataset],
                            borderColor: 'rgba(54, 168, 83, 1)',
                            fill: false,
                        },
                        {
                            label: 'F. cardíaca',
                            data: [...cardioFrequency],
                            borderColor: '#182ec0',
                            fill: false
                        }
                    ],
                },
                options: {
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Fecha',
                            },
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Presión (mmHg)',
                            },
                        },
                    },
                },
            })

            return () => chart?.destroy()
        }
    }, [logs, Chart])

    return (
        <div>
            <canvas id="BloodPressureChart" width="400" height="100"></canvas>
        </div>
    )
}

export default BloodPressureFhirChart
