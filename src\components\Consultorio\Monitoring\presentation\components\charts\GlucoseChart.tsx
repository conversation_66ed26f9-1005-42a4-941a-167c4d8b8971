import React, { useEffect } from 'react'
import moment from 'moment'
import { IfhirR4 } from '@smile-cdr/fhirts'
import { glucoseDataSet } from '../../utils/useGlucoseDataSets'

let Chart: any
;(async () => {
    const moduleChart = await import('chart.js/auto')
    Chart = moduleChart.Chart
})()

interface ILogs {
    resource: IfhirR4.IObservation
    meta: { lastUpdated: string }
}

const GlucoseChart = ({ logs }: { logs: ILogs[] }) => {
    useEffect(() => {
        if (Chart) {
            const canvas: HTMLCanvasElement = document.getElementById(
                'GlucoseChart'
            ) as HTMLCanvasElement
            const ctx: CanvasRenderingContext2D | null = canvas.getContext('2d')

            const labels = logs?.sort((a, b) => {
                const dateA = a?.resource?.effectiveDateTime
                const dateB = b?.resource?.effectiveDateTime
                if (!dateA) return -1
                if (!dateB) return 1
                return new Date(dateA).getTime() - new Date(dateB).getTime()
            }).map(log => {
                const date = log?.resource?.effectiveDateTime
                if (!date) return 'Fecha inválida'
                return moment(date)?.format('HH:mm DD/MM/YYYY')
            })

            const { beforeEatingDataset, afterEatingDataset } =
                glucoseDataSet(logs)

            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [...labels],
                    datasets: [
                        {
                            label: 'Glicemia ayuno (mmHg)',
                            data: [...beforeEatingDataset],
                            borderColor: 'rgba(239, 83, 80, 1)',
                            fill: false,
                        },
                        {
                            label: 'Glicemia después de comer (mmHg)',
                            data: [...afterEatingDataset],
                            borderColor: 'rgba(54, 168, 83, 1)',
                            fill: false,
                        },
                    ],
                },
                options: {
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Fecha',
                            },
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Presión (mmHg)',
                            },
                        },
                    },
                },
            })

            return () => chart?.destroy()
        }
    }, [logs, Chart])

    return (
        <div>
            <canvas id="GlucoseChart" width="400" height="100"></canvas>
        </div>
    )
}

export default GlucoseChart
