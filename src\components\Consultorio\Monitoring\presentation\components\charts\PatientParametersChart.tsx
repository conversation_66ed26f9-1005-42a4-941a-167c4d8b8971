import React, { useEffect } from 'react'
import moment from 'moment'
import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation';

let Chart: any
    ; (async () => {
        const moduleChart = await import('chart.js/auto')
        Chart = moduleChart.Chart
    })()


export const PatientParametersChart = ({ observations }: { observations: IObservation[] }) => {
    useEffect(() => {
        if (Chart) {
            const canvas: HTMLCanvasElement = document.getElementById(
                'PatientParametersChart',
            ) as HTMLCanvasElement
            const ctx: CanvasRenderingContext2D | null = canvas.getContext('2d')
            const labels = observations
                ?.sort(
                    (a, b) =>
                        new Date(
                            a?.valueDateTime ?? '',
                        ).getTime() -
                        new Date(
                            b?.valueDateTime ?? '',
                        ).getTime(),
                )
                ?.map(log =>
                    moment(log?.valueDateTime).format(
                        'HH:mm DD/MM/YYYY',
                    ),
                )

            const heightDataset = observations?.map(observation => observation.component?.find(component => component?.code?.text === 'Altura (cm)')?.valueQuantity?.value)
            const weightDataset = observations?.map(observation => observation.component?.find(component => component?.code?.text === 'Peso (kg)')?.valueQuantity?.value)
            const diameterDataset = observations?.map(observation => observation.component?.find(component => component?.code?.text === 'Diámetro de cintura (cm)')?.valueQuantity?.value)

            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [...labels],
                    datasets: [
                        {
                            label: 'Altura (cm)',
                            data: [...heightDataset],
                            borderColor: 'rgba(239, 83, 80, 1)',
                            fill: false,
                        },
                        {
                            label: 'Peso (kg)',
                            data: [...weightDataset],
                            borderColor: 'rgba(54, 168, 83, 1)',
                            fill: false,
                        },
                        {
                            label: 'Diámetro de cintura (cm)',
                            data: [...diameterDataset],
                            borderColor: '#182ec0',
                            fill: false
                        }
                    ],
                },
                options: {
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Fecha',
                            },
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Valor',
                            },
                        },
                    },
                },
            })

            return () => chart?.destroy()
        }
    }, [observations, Chart])

    return (
        <div>
            <canvas id="PatientParametersChart" width="400" height="100"></canvas>
        </div>
    )
}

