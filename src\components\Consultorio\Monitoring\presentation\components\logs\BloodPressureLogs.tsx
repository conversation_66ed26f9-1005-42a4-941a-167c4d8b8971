import React, { useRef } from 'react'
import moment from 'moment'
import { Spacer } from '@umahealth/occipital-ui'
import { IMonitoringLog } from '@/components/Consultorio/Monitoring/domain/monitoringInterfaces'
import { Button } from '@umahealth/occipital'
import { useReactToPrint } from 'react-to-print'
import { extractDataForMeditionState, getMeditionState } from '../../utils/getMeditionState'
import { IPatient } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPatient'

interface ILogs extends IMonitoringLog {
    date?: string,
}

const BloodPressureLogs = ({ logs, patient }: { logs: ILogs[], patient: IPatient | null }) => {
    const totalLogs = logs?.length
    const { totalSystolic, totalDiastolic, totalFrequency } = logs.reduce(
        (acc, log: IMonitoringLog) => {
            acc.totalSystolic += log.values?.systolic || 0
            acc.totalDiastolic += log.values?.diastolic || 0
            acc.totalFrequency += log.values?.cardioFrequency || 0
            return acc
        },
        { totalSystolic: 0, totalDiastolic: 0, totalFrequency: 0 }
    )
    const avgSystolic: number = totalSystolic / totalLogs
    const avgDiastolic: number = totalDiastolic / totalLogs
    const avgCardioFrequency: number = totalFrequency / totalLogs


    const pdfRef = useRef<HTMLDivElement>(null)
    const handlePrint = useReactToPrint({
        content: () => pdfRef.current,
        documentTitle: `${moment().format(
            'DD/MM/YYYY, h:mm:ss a'
        )}_registro_de_HTA`,
    })

    return (
        <div className='my-4 border rounded-lg py-4 px-4 extraThinBorder border-solid border-secondary-500'>
            <div ref={pdfRef} className="m-4">
                <p>
                    <b>Promedio</b>
                </p>
                <p>
                    PAS: {Math.floor(avgSystolic) || '-'}. PAD:{' '}
                    {Math.floor(avgDiastolic) || '-'}
                    {' '}
                    FC: {' '}
                    {Math.floor(avgCardioFrequency) || '-'}
                </p>
                <Spacer direction="vertical" value="4px" />
                <p>
                    <b>Registros</b>
                </p>
                <table style={{ width: '100%' }}>
                    <thead>
                        <th className="text-start">Fecha</th>
                        <th className="text-start">PAS</th>
                        <th className="text-start">Estado PAS</th>
                        <th className="text-start">PAD</th>
                        <th className="text-start">Estado PAD</th>
                        <th className="text-start">FC</th>
                        <th className="text-start">Toma</th>
                        <th className="text-start">Tags</th>
                    </thead>
                    <tbody>
                        {logs?.map(log => {
                            return (
                                <tr key={log.id}>
                                    <td>
                                        {moment(log?.date)?.format(
                                            'HH:mm DD/MM/YYYY'
                                        ) || '-'}
                                    </td>
                                    <td>{log?.values?.systolic || '-'}</td>
                                    <td>{getMeditionState({ ...extractDataForMeditionState(patient), value: log?.values?.systolic, type: 'systolic' })}</td>
                                    <td>{log?.values?.diastolic || '-'}</td>
                                    <td>{getMeditionState({ ...extractDataForMeditionState(patient), value: log?.values?.diastolic, type: 'diastolic' })}</td>
                                    <td>{log?.values?.cardioFrequency || '-'}</td>
                                    <td>{log?.values?.measure_spot || '-'}</td>
                                    <td>
                                        {log?.activity?.food ? 'Comida' : ''}
                                        {log?.activity?.excersice
                                            ? 'Ejercicio'
                                            : ' '}
                                        {log?.activity?.discussion
                                            ? 'Discusión'
                                            : ' '}
                                    </td>
                                </tr>
                            )
                        })}
                    </tbody>
                </table>
            </div>
            <div className="flex justify-end">
                <Button
                    onClick={() => handlePrint()}
                    type="submit"
                    className="max-w-40 bg-secondary-500 hover:bg-blue-700 h-12 rounded-md text-white text-base leading-5 tracking-wide py-2 px-4 font-semibold disabled:bg-gray-50 disabled:text-gray-500 disabled:border disabled:border-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                    Descargar PDF
                </Button>
            </div>
        </div>
    )
}

export default BloodPressureLogs
