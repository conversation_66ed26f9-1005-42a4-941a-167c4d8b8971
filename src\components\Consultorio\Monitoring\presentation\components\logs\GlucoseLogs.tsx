import React, { useRef } from 'react'
import moment from 'moment'
import { Spacer } from '@umahealth/occipital-ui'
import { Button } from 'occipital-new'
import { useReactToPrint } from 'react-to-print'

interface ILogs {
    date?: string
    id: string
    values?: {
        glucose: number
    }
}

const GlucoseLogs = ({ logs }: { logs: ILogs[] }) => {
    const totalLogs = logs?.length
    const totalGlucose = logs.reduce((acc, log) => {
        acc += log.values?.glucose || 0
        return acc
    }, 0)

    const avgGlucose: number = totalGlucose / totalLogs

    const pdfRef = useRef<HTMLDivElement>(null)
    const handlePrint = useReactToPrint({
        content: () => pdfRef.current,
        documentTitle: `${moment().format(
            'DD/MM/YYYY, h:mm:ss a'
        )}_registro_de_glucosa`,
    })

    return (
        <div>
            <div ref={pdfRef} className="m-4">
                <p>
                    <b>Promedio</b>
                </p>
                <p>Glicemia: {Math.floor(avgGlucose) || '-'}</p>
                <Spacer direction="vertical" value="4px" />
                <p>
                    <b>Registros</b>
                </p>
                <table style={{ width: '100%' }}>
                    <thead>
                        <th className="text-start">Fecha</th>
                        <th className="text-start">Nivel de glicemia</th>
                        <th className="text-start">Tags</th>
                    </thead>
                    <tbody>
                        {logs?.map(log => {
                            return (
                                <tr key={log.id}>
                                    <td>
                                        {moment(log?.date).format(
                                            'HH:mm DD/MM/YYYY'
                                        ) || '-'}
                                    </td>
                                    <td>{log?.values?.glucose || '-'}</td>
                                    <td>{'-'}</td>
                                </tr>
                            )
                        })}
                    </tbody>
                </table>
            </div>
            <Button
                size="small"
                action={() => handlePrint()}
                occ_type="filled"
                type="submit"
            >
                Descargar PDF
            </Button>
        </div>
    )
}

export default GlucoseLogs
