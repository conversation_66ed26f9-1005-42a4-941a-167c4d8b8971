import React, { useRef } from 'react'
import moment from 'moment'
import { Button } from '@umahealth/occipital'
import { IObservation } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IObservation'
import { useReactToPrint } from 'react-to-print'
import { classifyImc } from '@/components/Consultorio/FarmacoAtt/utils/classifyImc'


interface IProps {
    observations: IObservation[]
}

export const PatientParametersLogs = ({ observations }: IProps) => {
    const pdfRef = useRef<HTMLDivElement>(null)
    const handlePrint = useReactToPrint({
        content: () => pdfRef.current,
        documentTitle: `${moment().format(
            'DD/MM/YYYY, h:mm:ss a'
        )}_registro_de_parametros`,
    })

    return (
        <div>
            <div ref={pdfRef} className="m-4">
                <p>
                    <b>Registros</b>
                </p>
                <table style={{ width: '100%' }}>
                    <thead>
                        <th className="text-start">Fecha</th>
                        <th className="text-start">Altura</th>
                        <th className="text-start">Peso</th>
                        <th className="text-start">Diámetro de cintura</th>
                        <th className="text-start">IMC</th>
                        <th className="text-start">Clasificación</th>
                    </thead>
                    <tbody>
                        {observations?.map(observation => {
                            const height = observation.component?.find(component => component?.code?.text === 'Altura (cm)')?.valueQuantity?.value
                            const weight = observation.component?.find(component => component?.code?.text === 'Peso (kg)')?.valueQuantity?.value
                            const IMC = (Number(weight) / ((Number(height) / 100) ** 2))
                            return (
                                <tr key={observation.id}>
                                    <td>
                                        {
                                            moment(observation.valueDateTime)?.format(
                                                'HH:mm DD/MM/YYYY'
                                            ) || '-'
                                        }
                                    </td>
                                    <td>
                                        {
                                            height
                                        }
                                    </td>
                                    <td>
                                        {
                                            weight
                                        }
                                    </td>
                                    <td>
                                        {
                                            observation.component?.find(component => component?.code?.text === 'Diámetro de cintura (cm)')?.valueQuantity?.value
                                        }
                                    </td>
                                    <td>
                                        {
                                            Math.round(IMC * 100) / 100
                                        }
                                    </td>
                                    <td>
                                        {
                                            classifyImc(IMC)
                                        }
                                    </td>
                                </tr>
                            )
                        })}
                    </tbody>
                </table>
            </div>
            <Button
                size="small"
                action={() => handlePrint()}
                variant="filled"
                type="submit"
            >
                Descargar PDF
            </Button>
        </div>
    )
}
