import { IPatient } from "@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPatient";
import moment from "moment";

const bloodPressureRanges = {
    male: [
        [[105, 135], [60, 86]],   // 16-18 
        [[105, 139], [62, 88]],   // 19-24 
        [[108, 139], [65, 89]],   // 25-29 
        [[110, 145], [68, 92]],   // 30-39 
        [[110, 150], [70, 96]],   // 40-49 
        [[115, 155], [70, 98]],   // 50-59 
        [[115, 160], [70, 100]],  // 60+ 
    ],
    female: [
        [[100, 130], [60, 85]],   // 16-18 
        [[100, 130], [60, 85]],   // 19-24 
        [[102, 135], [60, 86]],   // 25-29 
        [[105, 139], [65, 89]],   // 30-39 
        [[105, 150], [65, 96]],   // 40-49 
        [[110, 155], [70, 98]],   // 50-59 
        [[115, 160], [70, 100]],  // 60+ 
    ]
};

export const extractDataForMeditionState = (patient: IPatient | null) => {
    if(!patient) return { gender: 'male', age: 0 } as const
    const gender = (patient.gender !== 'male' && patient.gender !== 'female') ? 'male' : patient.gender
    const age = moment().diff(patient?.birthDate, 'years')
    return { gender, age } as const
}

export const getMeditionState = ({gender, age, value, type }: {gender: 'male' | 'female', age: number, value: number, type: 'systolic' | 'diastolic'}): string => {
    if (age < 16) {
        return 'Edad invalida +16';
    }

    const ranges = bloodPressureRanges[gender];
    let index = -1;
    
    if (age >= 16 && age <= 18) {
        index = 0;
    } else if (age >= 19 && age <= 24) {
        index = 1;
    } else if (age >= 25 && age <= 29) {
        index = 2;
    } else if (age >= 30 && age <= 39) {
        index = 3;
    } else if (age >= 40 && age <= 49) {
        index = 4;
    } else if (age >= 50 && age <= 59) {
        index = 5;
    } else {
        index = 6;
    }

    const [min, max] = ranges[index][type === 'systolic' ? 0 : 1];

    if (value < min) {
        return `Bajo -> ${min} - ${max}`;
    } else if (value > max) {
        return `Alto -> ${min} - ${max}`;
    } else {
        return 'Normal';
    }
}

