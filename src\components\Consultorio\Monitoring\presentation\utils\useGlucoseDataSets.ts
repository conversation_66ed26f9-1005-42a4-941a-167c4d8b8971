import { fhirR4 } from '@smile-cdr/fhirts'
import moment from 'moment'

interface IResource {
    resource: fhirR4.Observation
}

export const glucoseDataSet = (logs: any[]) => {
    const beforeEating: IResource[] = []
    const afterEating: IResource[] = []

    logs.forEach(log => {
        const mealTimeElement = log?.resource?.extension?.find(
            (element: { id: string }) => element.id === 'mealTime'
        )
        const mealTimeCode =
            mealTimeElement?.valueCodeableConcept?.coding?.[0]?.code

        if (mealTimeCode === 'B') {
            beforeEating.push(log)
        } else {
            afterEating.push(log)
        }
    })

    const beforeEatingDataset = beforeEating?.map(log => ({
        y: log.resource.valueQuantity?.value,
        x: moment(log?.resource?.meta?.lastUpdated).format('HH:mm DD/MM/YYYY'),
    }))

    const afterEatingDataset = afterEating?.map((log: any) => ({
        y: log.resource.valueQuantity?.value,
        x: moment(log?.resource?.meta?.lastUpdated).format('HH:mm DD/MM/YYYY'),
    }))

    return { beforeEatingDataset, afterEatingDataset }
}
