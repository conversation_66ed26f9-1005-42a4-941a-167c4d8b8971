import React from 'react'
import { where } from '@/config/firebase'
import { Spacer } from 'occipital-new'
import { useOnSnapshot } from '@/hooks/useOnSnapshot'
import BloodPressureChart from '../components/charts/BloodPressureChart'
import BloodPressureLogs from '../components/logs/BloodPressureLogs'
import { IMonitoringLog } from '@/components/Consultorio/Monitoring/domain/monitoringInterfaces'
import { useSearchParams } from 'next/navigation'

const Monitoring = () => {
	const searchParams = useSearchParams()
	const patientUid = searchParams.get('patientUid')
	const currentTrackings = useOnSnapshot<IMonitoringLog>(`user/${patientUid}/monitoring`, [where('monitoring', '==', 'hypertension')], !!patientUid, 'monitoring')
	const [monitorType, setMonitorType] = React.useState<string>('htaEvolution') 

	if(monitorType === 'empty' || currentTrackings?.length === 0 ) {
		return (
			<div>
				<div className="mt-6 w-full h-[612px] bg-gray-100 rounded-lg flex items-center justify-center">
					<p className="text-gray-500 text-lg">Paciente sin monitoreos previos</p>
				</div>
				<Spacer direction='vertical' value='32px'/>
			</div>
		)
	}

	return <div className='mt-4 min-h-[600px]'>
		<Spacer direction='vertical' value='4px'/>
		<div className="relative">
      <select 
        value={monitorType}
        onChange={(e) => setMonitorType(e.target.value)}
        className="w-full h-12 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-100 focus:border-purple-100 appearance-none cursor-pointer"
      >
        <option value='htaEvolution' className='my-5 rounded px-[25px] hover:bg-secondary-300 appearance-none'>Presión arterial (Evolución)</option>
        <option value='htaLogs' className='my-5 rounded px-[25px] hover:bg-secondary-300 appearance-none'>Presión arterial (Registros)</option>
      </select>
      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-secondary-600">
        <svg className="fill-current h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
        </svg>
      </div>
    </div>
		<Spacer direction='vertical' value='8px'/>
		{monitorType === 'htaEvolution' && currentTrackings?.length > 0 && <div className='max-w-screen-md mx-auto mt-4 rounded-lg px-2 py-0.5 max-h-[500px] bg-[#f1f4ff]'><BloodPressureChart logs={currentTrackings} /></div>}
		{monitorType === 'htaLogs' && currentTrackings?.length > 0 && <BloodPressureLogs patient={null} logs={currentTrackings} />}
	</div>
}


export default Monitoring