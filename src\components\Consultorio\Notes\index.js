import React, { useState } from 'react'
import { useTranslations } from 'next-intl'
import { TextArea } from '@/components/GeneralComponents/Input/Inputs'
import { Spacer } from '@umahealth/occipital-ui'
import { MdExpandMore, MdExpandLess } from 'react-icons/md'
import { useAssignationFormData } from '@/cookies/AssignationFormDataContext'
/**
 * Comentarios de eventos de PostHog para videollamada.
 * 
 * Este archivo contiene los eventos que se registran en PostHog para el flujo de videollamada.
 * Sirve como referencia para cuando se necesite medir este componente.
 */
//import { trackEventUseNotes } from '@/events/videocallEvents'

const Notes = () => {
	const [display, setDisplay] = useState(false)
	const { formData, setFormField } = useAssignationFormData()
	const t = useTranslations('attention')
	//trackEventUseNotes(display)
	const handleNotes = (value) => {
		setFormField('notes', value)
	}

	return (
		<>
			<div className="sectionTitle pointer" onClick={() => setDisplay(!display)}>
				<label className="pointer">{t('textarea-notes_label')}</label>
				<div className="text-white text-[1.2rem] p-[0_8px]">
					{display ? <MdExpandLess /> : <MdExpandMore />}
				</div>
			</div>
			{display &&
				<div className='notes-container'>
					<Spacer />
					<TextArea
						placeholder="No serán visualizadas por el paciente"
						value={formData.notes || ''}
						setTextareaValue={handleNotes}
						expandibleButton
					/>
				</div>
			}
		</>
	)
}


export default Notes
