import React from 'react'
import moment from 'moment'
import { useSearchParams } from 'next/navigation'
import './styles/PatientInfo.scss'

interface IPatientInfo {
	patientInfo: {
	chosenName?: string,
	fullname?: string,
	pronouns?: string,
	corporate?: string
	n_afiliado?: string
	email?: string
	dob?: string
	sex?: string
	address?: string
	dni?: string
	plan?: string
}
}

const PatientInfo = ( { patientInfo } : IPatientInfo)  => {
	const searchParams = useSearchParams()
	const attType = searchParams.get('attType')
	
	return (
		<div className='patient-info-container'>
			<div className='patient-info-title flex-col h-max'>
				<div className='flex w-full justify-between'>
					<b>Nombre: {patientInfo?.fullname}</b>
					<b>{patientInfo?.pronouns || ''}</b>
				</div>
				{patientInfo?.chosenName && <b>Nombre escogido: {patientInfo?.chosenName || 'No especifica'}</b>}
			</div>
			<div className='patient-info-content'>
				{process.env.NEXT_PUBLIC_COUNTRY === 'MX' ? <span><b>Email: </b>{patientInfo?.email}</span> : <span><b>DNI: </b>{patientInfo?.dni}</span>}
				<span><b>Edad:</b> {patientInfo?.dob && moment().diff(patientInfo?.dob, 'years')} </span>
				{patientInfo?.chosenName && patientInfo?.pronouns && <span><b>Género:</b> {patientInfo?.sex}</span>}
				{process.env.NEXT_PUBLIC_COUNTRY === 'AR' &&
					<span><b>Cobertura: </b> {typeof patientInfo?.corporate === 'string' ? patientInfo?.corporate : '-'} </span>}
				{patientInfo?.address && <span><b>Dirección:</b> {patientInfo?.address}</span>}
				{
					(attType === 'consultorio' || attType === 'online') && patientInfo?.n_afiliado && (
						<span>
							<b>Nro de Afiliado:</b> {patientInfo?.n_afiliado}
						</span>
					)
				}
				{
					attType === 'consultorio'  && patientInfo?.plan && (
						<span>
							<b>Plan:</b> {patientInfo?.plan}
						</span>
				)}
			</div>
		</div>
	)
}

export default PatientInfo
