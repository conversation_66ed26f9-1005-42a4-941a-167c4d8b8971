import { Title } from '@umahealth/occipital'
import { TabButtons } from '@/modules/consultorio/presentation/components/PatientInfo/TabButtons'
import AttForm from '@/modules/consultorio/presentation/components/AttFile/AttForm'
import { RecipeForm } from '@/modules/consultorio/presentation/components/Recipe/RecipeForm'
import { TabContentProps } from '@/modules/consultorio/domain/entities/ITabItem'
import OrderManager from '@/modules/consultorio/presentation/components/Orders/OrderManager'
import Recipes from '@/modules/consultorio/presentation/components/Recipe/Recipes'
import Scriba from '@/modules/consultorio/presentation/components/Scriba/Scriba'

const consultaContent: TabContentProps =
{
  title: "Consulta",
  tabs: process.env.NEXT_PUBLIC_NODE_ENV === 'development' ?
    [
      {
        value: 'ficha',
        label: 'Ficha de atención',
        content: <AttForm />,
      },
      {
        value: 'recetas',
        label: 'Recetas',
        content: <Recipes />
      },
      {
        value: 'ordenes',
        label: 'Órden<PERSON>',
        content: <OrderManager />,
      },
      {
        value: 'scriba',
        label: 'Scriba',
        content: <Scriba />,
      },
    ] :
    [
      {
        value: 'ficha',
        label: 'Ficha de atención',
        content: <AttForm />,
      },
      {
        value: 'recetas',
        label: 'Recetas',
        content: <RecipeForm />,
      },
      {
        value: 'ordenes',
        label: 'Órdenes',
        content: <OrderManager />,
      },
    ]
}

export const Consulta = () => {

  return (
    <div className="bg-white rounded-xl h-full flex flex-col ">
      <Title
        hierarchy="h3"
        weight="font-bold"
        className="py-4 mx-4 leading-5 tracking-wide text-secondary-600 text-lg"
      >
        {consultaContent?.title}
      </Title>
      <div className="flex mx-2 flex-1 min-h-0">
        <TabButtons tabs={consultaContent?.tabs} />
      </div>
    </div>
  )
}
