@use '../../../../styles/global/Vars.scss';

.patient-info-container {
	position: relative;
	border-radius: 6px;
	margin-bottom: 5px;

	.girl {
		background-color: Vars.$pink !important;
	}

	.patient-info-title {
		background-color: Vars.$uma-primary;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;
		border-radius: 5px;
		color: Vars.$white-color;
		padding: 6px 12px;
		margin-bottom: 2px;
		text-transform: capitalize;
	}

	.patient-info-content {
		display: grid;
		grid-template-columns: max-content max-content;
		background-color: Vars.$uma-primary;
		border-radius: 5px;
		gap: 8px 40px;
		color: Vars.$white-color;
		font-size: 0.9rem;
		line-height: 0.9rem;
		height: auto;
		padding: 8px 10px;
	}
	@media (max-width: 1400px) {
		.patient-info-content {
			grid-template-columns: 1fr;
		}
	}
	

	.patient-info-gender {
		position: absolute;
		border-radius: 50%;
		border: 4px solid Vars.$white-color;
		background: Vars.$uma-primary;
		right: 10px;
		top: 10px;
		width: 50px;
		height: 50px;
		color: Vars.$white-color;
		font-size: 2rem;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.pediatric {
		background-color: Vars.$pediatric-marker;
	}
}
