import { dependantUid, IChatAttAppointment } from '@umahealth/entities'
import { medicineFormater } from '../Recipe/Common/Utils/recipeHelpers'
import { Timestamp } from '@/config/firebase'
import { TCurrentView } from '@/components/AsyncAttention/ui/RightColumn/components/prescription/Prescription'

export interface medicine {
	accion_farmacologica: string,
	alfabetRegisterNum: number,
	dosis: string,
	drugIDMonoDro: number,
	drugName: string,
	duplicado: number,
	fecha: number,
	laboratorio: number,
	precio: number,
	presentationName: string,
	productName: string,
	snomed: number,
	venta_tipo: number	
  /**
   * Cobertura de medicamentos para IOMA
   * @type {boolean}
   * @description Este campo booleano indica si el medicamento tiene o no tiene cobertura. Actualmente esta feature existe sólo para IOMA.
   */
  corporate_coverage?: boolean;
}

export interface IBasePrescriptionData {
	entity?: string,
	diagnosis: string,
	medicines: medicinePrescription[], // Chequear esta nomas
	patient: {
		corporate: string,
		dni: string,
		fullname: string,
		n_afiliado: string,
		plan: string,
		uid?: string,
		chosenName?: string
	},
	assignation_id?: string,
	providerUid?: string,
	
}

export interface IPrescriptionDataWithValidator extends IBasePrescriptionData {
	validator: 'UNION PERSONAL' | 'FARMALINK' | 'OSDE' | 'PRESERFAR',
	integrations?: {
		provider: 'unionPersonal' | 'farmalink' | 'osde' | 'preserfar'
	}
}

export type IPrescriptionData = IBasePrescriptionData | IPrescriptionDataWithValidator
export interface medicinePrescription extends medicine {
	addNew: boolean
	details: string,
	quantity: string,
}

export interface patient {
	corporate: string
	dni: string,
	name: string,
	n_afiliado: string
	uid?: string
	chosenName?: string
	credentialVersion?: string
	dependantUid?: dependantUid,
	fullname?:string,
	plan?: string
	surname?: string,
}

export interface coverageForm {
	afiliateId: string,
	name: string,
	plan: string,
	credentialVersion?: string
}

export interface medicineByDrug {
	details: string
	dosis: string
	drug: string,
	medicine: medicine
	monodrugSearch: false
	presentationNames: string
	quantity: string
}

export interface medicineByBrand {
	details: string
	dosis: never
	drug: never,
	medicine: medicine,
	monodrugSearch: true
	presentationNames: never
	quantity: string

}

export interface IRecipeForm {
	coverage: coverageForm
	diagnosis: string
	medicine: medicineByDrug | medicineByBrand
	patient?: patient
}

export interface medicineDetails {
	accion_farmacologica: string,
	alfabetRegisterNum: number,
	dosis: string,
	drugIDMonoDro: number,
	drugName: string,
	duplicado: number,
	fecha: number,
	laboratorio: number,
	precio: number,
	presentationName: string,
	productName: string,
	snomed: number,
	venta_tipo: number	
  /**
   * Cobertura de medicamentos para IOMA
   * @type {boolean}
   * @description Este campo booleano indica si el medicamento tiene o no tiene cobertura. Actualmente esta feature existe sólo para IOMA.
   */
  corporate_coverage?: boolean;
}


export interface RecipeMedicineItem {
	details: string
	dosis: string
	drug: string,
	medicine?: medicineDetails,
	monodrugSearch: boolean
	presentationNames: string
	quantity: string
	uniqueKey?: string
}

export interface IRecipeFormNew {
	coverage: coverageForm
	diagnosis: string
	medicines: RecipeMedicineItem[]
	patient?: patient
}

export interface IPrescriptionPostFunctionParameters {
	formData: ReturnType<typeof medicineFormater>,
	assignationId: string,
	uid: string,
	goTo: React.Dispatch<React.SetStateAction<TCurrentView>>,
	asyncAttData?: IChatAttAppointment<Timestamp> | undefined,
	dependantUid?: dependantUid,
}

export interface IPrescriptionRequest {
	diagnosis: string
	assignation_id?: string
	coverage?: coverageForm
	details?: string
	entity?: string
	medicines?: medicine[]
	medicine?: medicine[]
	patient?: Omit<patient, 'name' | 'surname'>
	providerUid?: string
	provider?: string
	quantity?: number,
}


export interface IConfirmPrescriptionResponse {
	confirmed: boolean,
}

export type PrescriptionState = 'confirmed' | 'rejected' | 'waiting' | null