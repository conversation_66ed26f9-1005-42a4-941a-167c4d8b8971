import { RawTimestamp } from "@/components/Appointments/utils/filterMandatoryFields";

export interface IGetPrescriptionResponse {
  consumed:            boolean;
  country:             string;
  date:                string;
  diagnosis:           string;
  entity:              string;
  hc:                  string;
  items:               DetailedPrescriptionDrug[];
  medical_record_id:   string;
  patient:             Patient;
  prescription_number: string;
  provider:            Provider;
  response:            Response;
  status:              string;
  timestamps:          RawTimestamp;
  type:                string;
  uid:                 string;
  validator:           string;
  id:                  string;
}

export interface DetailedPrescriptionDrug {
  accion_farmacologica: string;
  duplicado:            number;
  fecha:                number;
  productName:          string;
  venta_tipo:           number;
  laboratorio:          string;
  alfabetRegisterNum:   number;
  drugIDMonoDro:        number;
  snomed:               number;
  dosis:                string;
  drugName:             string;
  presentationName:     string;
  precio:               number;
  corporate_coverage:   boolean;
  quantity:             string;
  details:              string;
}

export interface Patient {
  corporate:    string;
  dni:          string;
  fullname:     string;
  chosenName:   string;
  n_afiliado:   string;
  plan:         string;
  uid:          string;
  dependantUid: boolean;
}

export interface Provider {
  dni:          string;
  fullname:     string;
  matricula:    string;
  signature:    string;
  uid:          string;
  hash_medikit: string;
  securityHash: string;
}

export interface Response {
  approved:    boolean;
  code:        string;
  description: string;
  id:          string;
  msg:         string;
  reference:   number;
  status:      string;
}
