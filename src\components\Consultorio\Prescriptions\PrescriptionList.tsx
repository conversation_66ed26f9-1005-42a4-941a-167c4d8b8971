import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import axios from 'axios'
import { Row, Spacer, Loader, Paragraph, Icon, Modal, Title, Button } from '@umahealth/occipital-ui'
import { errorHandler } from '@/config/stackdriver'
import { studies } from '@/config/endpoints'
import { removePrescriptionByValidator } from '@/components/QuickPrescriptions/utils/removePrescription'
import OrdersListDisplay from './Studie/ListDisplay'
import PrescriptionsListDisplay from './Recipe/ListDisplay'
import RecipePDF from './Recipe/RecipeMX/ui/RecipePDF'
import { TCurrentView } from './index'
import { orderOrPrescription } from '@umahealth/entities'
import { removeOrder, removePrescription } from '@/store/actions/appointments/queries'
import { revalidateMedicalRecords } from '@/serverServices/getMedicalRecords'

interface ISetCurrentView {
	setCurrentView: React.Dispatch<React.SetStateAction<TCurrentView>>;
}
interface IModalContent {
	action?: () => void;
	buttonLabel?: string;
	text?: string;
	title?: string;
	show: boolean;
	singleResponse?: boolean
}
interface IButtonType {
	prescription: boolean;
	order: boolean;
}

const PrescriptionList = ({ setCurrentView }: ISetCurrentView) => {
	const currentAtt = useAppSelector(state => state.queries.currentAtt)
	const [loading, setLoading] = useState({
		prescriptions: false,
		orders: false
	})
	const dispatch = useAppDispatch()
	const [loadingModule, setLoadingModule] = useState<boolean>(false)
	const [viewPrescription, setViewPrescription] = useState<boolean>(false)
	const [item, setItem] = useState<orderOrPrescription>({} as orderOrPrescription)
	const [modalContent, setModalContent] = useState<IModalContent>({
		text: '',
		title: '',
		show: false,
		singleResponse: false
	})

	const [buttonType, setButtonType] = useState<IButtonType>({
		prescription: false,
		order: false
	})


	const handleDeletePrescription = async (prescriptionItem: orderOrPrescription) => {
		setItem(prescriptionItem)
		setButtonType({ ...buttonType, prescription: true })
		setModalContent({
			text: 'Una vez eliminada, no podrá recuperarla.',
			title: '¿Seguro que desea eliminar esta receta?',
			show: true,
			singleResponse: false
		})
		await revalidateMedicalRecords()
	}

	const handleDeleteOrder = async(orderItem: orderOrPrescription) => {
		setItem(orderItem)
		setButtonType({ ...buttonType, order: true })
		setModalContent({
			text: 'Una vez eliminada, no podrá recuperarla.',
			title: '¿Seguro que desea eliminar esta orden?',
			show: true,
			singleResponse: false
		})
		await revalidateMedicalRecords()
	}

	const removePrescriptionData = async (prescription: orderOrPrescription) => {
		setLoading({ ...loading, prescriptions: true })
		try {
			const data = {
				assignation_id : currentAtt?.assignation_id,
				id: prescription.id,
				prescriptions: currentAtt?.mr?.prescriptions || [],
				uid: currentAtt?.patient?.uid,
				country: process.env.NEXT_PUBLIC_COUNTRY as 'AR' | 'MX'
			}
			await removePrescriptionByValidator(prescription?.integrations || undefined, data)
			await revalidateMedicalRecords()
			dispatch(removePrescription(prescription.id))
			setLoading({ ...loading, prescriptions: false })
			setModalContent({
				show: false,
			})
			successDeletePrescription()
		} catch (error) {
			if (axios.isAxiosError(error)) {
				setLoading({ ...loading, orders: false })
				setModalContent({
					action: () => { setModalContent({ show: false }) },
					buttonLabel: 'Aceptar',
					text: `No se ha podido eliminar la receta. Por favor recargue la página e intente otra vez`,
					title: 'Atención',
					show: true,
					singleResponse: true
				})
			}
		}
	}

	const removeOrderData = async (order: orderOrPrescription) => {
		setLoading({ ...loading, orders: true })
		try {
			const data = {
				assignation_id: currentAtt?.assignation_id,
				id: order.id,
				ordenes: currentAtt?.mr?.ordenes || [],
				uid: currentAtt?.patient?.uid,
			}
			await axios.delete(studies, { data })
			await revalidateMedicalRecords()
			dispatch(removeOrder(order.id))
			setLoading({ ...loading, orders: false })
			successDeleteOrder()
		} catch (error) {
			if (axios.isAxiosError(error)) {
				setLoading({ ...loading, orders: false })
				setModalContent({
					action: () => { setModalContent({ show: false }) },
					buttonLabel: 'Aceptar',
					text: `No se ha podido eliminar la orden. Por favor recargue la página e intente otra vez.${error.response}`,
					title: 'Atención',
					show: true,
					singleResponse: true
				})
			}
		}
	}

	const successDeletePrescription = () => {
		setModalContent({
			action: () => { setModalContent({ show: false }) },
			buttonLabel: 'Aceptar',
			text: 'Receta eliminada',
			title: 'EXITOSO',
			show: true,
			singleResponse: true
		})
	}

	const successDeleteOrder = () => {
		setModalContent({
			action: () => { setModalContent({ show: false }) },
			buttonLabel: 'Aceptar',
			text: 'Orden eliminada',
			title: 'EXITOSO',
			show: true,
			singleResponse: true
		})
	}

	const createPrescription = async () => {
		if (process.env.NEXT_PUBLIC_COUNTRY === 'MX') {
			try {
				setLoadingModule(true)
				setCurrentView('newRecipe')
			} catch (err: any) {
				if (errorHandler != null && err) {
					errorHandler.report(err)
				}
			} finally {
				setLoadingModule(false)
			}
		} else {
			setCurrentView('newRecipe')
		}
	}

	return <>
	
		<div className="sectionTitle">
			<span>Prescripciones (órdenes y recetas)</span></div>
		<div className="prescriptions__container">
			{
				loadingModule ?

					<Row spacing='center'><Loader /></Row>

					:

					viewPrescription ?

						<>
							<div className="pdfRecipeModal">
								<div className='back' onClick={() => setViewPrescription(false)}>
									<Icon name='arrowBack' />
									<Paragraph text='Atrás' />
								</div>
								{process.env.NEXT_PUBLIC_COUNTRY === 'MX' && <RecipePDF />}
							</div>
						</>	

					:

						<>
							<PrescriptionsListDisplay
								handleViewPrescription={() => process.env.NEXT_PUBLIC_COUNTRY === 'MX' && setViewPrescription(true)}
								prescriptionArray={currentAtt?.mr?.prescriptions || []}
								loading={loading.prescriptions}
								createPrescription={createPrescription}
								handleDeletePrescription={handleDeletePrescription}
							/>
							<Spacer direction='horizontal' value="8px" />
							<OrdersListDisplay
								orderArray={currentAtt?.mr?.ordenes || []}
								loading={loading.orders}
								handleDeleteOrder={handleDeleteOrder}
								setCurrentView={setCurrentView}
							/>
						</>
			}

		</div>

		{modalContent.show === true &&
			<Modal onClose={() => { setModalContent({ show: false }) }}>
				<Title text={modalContent.title} color='default' size='sm' weight='normal' />
				<Spacer direction='vertical' value='32px' />
				<Paragraph text={modalContent.text} />
				<Spacer direction='vertical' value='32px' />
				{modalContent.singleResponse === true ?
					<Button label={modalContent.buttonLabel} action={() => { setModalContent({ show: false }) }} size='full' />
					:
					<Row >
						<Button label='Cancelar' action={() => { setModalContent({ show: false }) }} size='medium' type='outline' />
						<Spacer direction='horizontal' value='16px' />
						{buttonType?.prescription === true ?
							<Button label='Si' action={() => removePrescriptionData(item)} size='medium' />
							:
							<Button label='Si' action={() => removeOrderData(item)} size='medium' />
						}
					</Row>
				}
			</Modal>
		}

	</>
}

export default PrescriptionList
