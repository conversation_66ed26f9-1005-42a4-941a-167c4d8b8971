import { IconsNames } from "@umahealth/occipital"

interface IStates {
  confirmed: {
    icon: IconsNames,
    msg: string,
    button: string,
  },
  rejected: {
    icon: IconsNames,
    msg: string,
    button: string,
  },
  waiting: {
    msg: string,
    button: string,
  },
}

export const states: IStates = {
  confirmed: {
    icon: 'checkOutline',
    msg: 'Medicamentos confirmados',
    button: 'Confirmar receta',
  },
  rejected: {
    icon: 'error',
    msg: 'Los medicamentos no fueron confirmados. Modifica los datos para que coincida con lo recetado.',
    button: 'Modificar receta',
  },
  waiting: {
    msg: 'Esperando confirmación del paciente',
    button: 'Confirmar receta',
  }
}