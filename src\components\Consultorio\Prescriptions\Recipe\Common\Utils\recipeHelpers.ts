import { prescriptionItems } from './../../RecipeAR/index'
import { coverageForm, IRecipeForm } from './../../../Interfaces/Interfaces'
import { IHealthInsurance } from '@umahealth/entities'
import { Timestamp } from '@/config/firebase'
import swal from 'sweetalert'
import { store } from '@/store/configStore'
import { errorHandler } from '@/config/stackdriver'

const { dispatch, getState } = store

interface medicineMx{
	addNew: boolean,
	details: string,
	quantity: number,
	productId: string,
	productName: string,
	productEan: string
}

const addDrug = (currentMedicine : medicineMx) => {
	try {
		const state = getState()
		const { medicines } = state.prescriptions.temp
		const medicinesArr = [
			...medicines,
			currentMedicine
		]               
		if (!currentMedicine.productId) {
			throw new Error('El campo del medicamento no puede estar vacío.')
		} else if (!('quantity' in currentMedicine) || currentMedicine.quantity > 99 || currentMedicine.quantity === 0) {
			throw new Error('La cantidad del medicamento indicada es incorrecta o está vacía.')
		} else if (!('quantity' in currentMedicine) || currentMedicine.quantity > 99 || currentMedicine.quantity > 2 ) {
			throw new Error('La cantidad del medicamento indicada, no puede superar las dos unidades.')
		} else {
			dispatch({ type: 'HANDLE_RECIPE_ADD_DRUG', payload: medicinesArr })
		}
	} catch (error) {
		swal({ title: 'Atención', text: `${error}`, icon: 'warning', dangerMode: true })
	}
}

const validateOS = (value: string, os?: string) => {
	if(!os) return true
	const osValidations: {[key: string]: string | boolean} = {
		IOMA: /\d{12}/.test(value) || 'El "núm. de Afiliado" debe tener 12 posiciones - No incluir barra (/)',
		OSDIPP: /\d{11}/.test(value) || 'El "núm. de Afiliado" debe tener 11 posiciones',
		PAMI: /\d{14}/.test(value) || 'El "núm. de Afiliado" debe tener 14 posiciones - (Si la credencial cuenta con menos digitos, añadir los núm. 0 necesarios al inicio)',
		MEDIFE: /\d{12}/.test(value) || 'El "núm. de Afiliado" debe tener 12 dígitos numéricos',
		OSMECON: /\d{12}/.test(value) || 'El "núm. de Afiliado" debe tener 12 dígitos numéricos',
		'PROGRAMAS MEDICOS': /\d{6,26}/.test(value) || 'Sólo incluir dígitos numéricos',
		OSDE: /\d{11}/.test(value.replace(/-/g, "")) || 'El "núm. de Afiliado" debe tener 11 dígitos numéricos'
	}
	return osValidations[os]??true
}

const medicineFormater = (formData : IRecipeForm, medicines: prescriptionItems, assignationId: string | null, primaryCoverage: IHealthInsurance | undefined) => {
	const medicinesFormated = medicines?.map(medicine => {
		return {
			...medicine.medicine, 
			quantity: medicine.quantity,
			details: medicine.details
		}
	})
	const coverage: coverageForm = {
		...formData.coverage
	}
	if (!formData.coverage?.name) {
		errorHandler?.report(`[ Recipe | medicineFormater ] => Coverage name is not defined. Using primary ${primaryCoverage?.id} - assignationId: ${assignationId}`)
		coverage.name = primaryCoverage?.id || '',
		coverage.afiliateId = primaryCoverage?.affiliate_id || '',
		coverage.plan = primaryCoverage?.plan || '',
		coverage.credentialVersion = primaryCoverage?.credentialVersion
	}
	return {
		medicine: medicinesFormated, 
		diagnosis: formData?.diagnosis, 
		coverage,
	}
}

export const coverageNameFormater = (name: string) => {
	return /OS - /i.test(name) ? name?.split(' - ')[1]?.toUpperCase()?.trim() : name?.toUpperCase()?.trim()
}

const coverageFormater = (coverage: IHealthInsurance<Timestamp>) =>{
	return {
		name: coverage.id,
		plan: coverage.plan,
		afiliateId: coverage.affiliate_id
	}
}

export {
	addDrug,
	medicineFormater,
	validateOS,
	coverageFormater
}