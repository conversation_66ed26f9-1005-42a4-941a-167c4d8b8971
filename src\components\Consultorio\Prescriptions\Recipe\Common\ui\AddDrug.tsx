import React from 'react'
import DrugCombobox from './DrugCombobox'
import styles from './checkbox.module.scss'
import { Select } from '@/components/Shadcn/Select'
import { Input, Spacer, Text } from 'occipital-new'
import { Control, FieldErrors, UseFormRegister, UseFormSetValue, UseFormWatch, Controller, ControllerRenderProps } from 'react-hook-form'
import DrugUnitaryCombobox from './DrugUnitaryCombobox'
import { IRecipeForm } from '@/components/Consultorio/Prescriptions/Interfaces/Interfaces'
import Switch from 'react-switch'
export interface IAddDrug {
	register: UseFormRegister<IRecipeForm>
	watch: UseFormWatch<IRecipeForm>
	errors: FieldErrors<IRecipeForm>
	setValue: UseFormSetValue<IRecipeForm>
	control: Control<IRecipeForm, any>
}

export const AddDrug = ({ register, watch, errors, setValue, control }: IAddDrug) => {

	const toggle = ({ value, onChange }: ControllerRenderProps<IRecipeForm, 'medicine.monodrugSearch'>) => {
		return <Switch checked={!!value} onChange={onChange} />
	}

	const medicineDetails = <Input
		inputmode='text'
		type='text'
		label='Dosis, frecuencia y duración del tratamiento'
		register={register('medicine.details')}
		placeholder='Uno cada 8hs, por dos semanas'
		hasValue={!!watch('medicine.details')}
		size='full'
		error={errors?.medicine?.details}
		autocomplete='on'
		className='min-h-12 text-secondary-600'
	/>

	const medicineQuantity = <Select
		placeHolder='Cantidad'
		onChangeFn={quantity => setValue('medicine.quantity', quantity)}
		options={[{label: '1', value: '1'}, {label: '2', value: '2'}]}
		className='min-h-12 text-secondary-600'
	/>

	const restrictGenericPrescription = !(['OS - UNION PERSONAL', 'OS - ACCORD'].includes(watch('coverage.name')))

	return (
		<>
			{restrictGenericPrescription && <div className={styles['checkbox']}>
				<Text
					weight='regular'
					size='s'
					color='grey-1'
					tag='label'
					id='busquedaUnitaria'
					>
						<span className='text-secondary-600'>
							Buscar por genérico
						</span>
				</Text>
				<Controller
					name='medicine.monodrugSearch'
					control={control}
					render={({ field }) => toggle(field)}
				/>
				<Text
					weight='regular'
					size='s'
					color='grey-1'
					tag='label'
					id='busquedaUnitaria'>
						<span className='text-secondary-600'>
							Buscar por marca
						</span>
				</Text>
			</div>}
			{!(watch('medicine.monodrugSearch') === true) && restrictGenericPrescription ?
				<DrugCombobox register={register} watch={watch} errors={errors} setValue={setValue} control={control} /> :
				<DrugUnitaryCombobox register={register} watch={watch} errors={errors} setValue={setValue} control={control} /> }
			{medicineQuantity}
			<Spacer value='12px' />
			{medicineDetails}
		</>
	)
}
