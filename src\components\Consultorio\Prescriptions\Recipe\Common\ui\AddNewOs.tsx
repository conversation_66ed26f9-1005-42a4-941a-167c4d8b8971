import React, { useMemo } from 'react'
import { UseFormWatch, UseFormRegister, UseFormSetValue } from 'react-hook-form'
// import { Combobox, ComboboxList, ComboboxOption } from 'occipital-new'
import { Combobox } from '@/components/Shadcn/Combobox'
import { useDebounce } from '@/hooks/useDebounce'
import useGetOSSList from '@/services/reactQuery/requests/useGetOSSList'
import { IRecipeForm } from '@/components/Consultorio/Prescriptions/Interfaces/Interfaces'

interface IProps{
	register: UseFormRegister<IRecipeForm>,
	watch: UseFormWatch<IRecipeForm>,
	setValue: UseFormSetValue<IRecipeForm>
}

export const AddNewOs = ({ watch, setValue }: IProps) => {
	const OSSList = useGetOSSList()
	const debouncedValue = useDebounce<string>(watch('coverage.name'))

	useMemo(() =>{
		if (debouncedValue && debouncedValue != ''){ 
			OSSList.mutate(debouncedValue)
		}
	},[debouncedValue])

	return (
		<>
			{/* <Combobox loading={OSSList.isLoading} type='text' inputmode='text' label='Obra social' register={register('coverage.name')} hasValue={!!watch('coverage.name')}>
				{OSSList.data ?
					<ComboboxList persistSelection>
						{
							OSSList.data.map((element) => {
								return (
									<ComboboxOption
										key={element.value + element.label}
										value={element.value}
										onClick={() => setValue('coverage.name', element.value)}
									>
										{element.label}
									</ComboboxOption>
								)
							})
						}
					</ComboboxList>
					: watch('coverage.name')?.length && <ComboboxOption
						key={watch('coverage.name')}
						value={watch('coverage.name').toUpperCase()}
					>
						{watch('coverage.name').toUpperCase()}
					</ComboboxOption>
				}
			</Combobox> */}
			<Combobox 
				options={OSSList.data?.length ? OSSList.data : []}
				onChange={option => setValue('coverage.name', option)}
				label='Obra social'
				emptyPlaceHolder='No hemos encontrado ninguna obra social'
				placeholder='Ingrese una obra social'
				shouldFilter={false}
				isLoading={OSSList.isLoading}
				className='min-h-12'
			/>
		</>
	)
}
