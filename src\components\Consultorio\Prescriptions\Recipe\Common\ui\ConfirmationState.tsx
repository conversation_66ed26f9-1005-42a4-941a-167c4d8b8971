import { Button, Loader, Paragraph } from '@umahealth/occipital'
import { Icon } from '@umahealth/occipital/client'
import React from 'react'
import { PrescriptionState } from '../../../Interfaces/Interfaces'
import { states } from '../Utils/confirmationStates'

interface IConfirmationState {
  prescriptionState: PrescriptionState,
  buttonLoading: boolean,
  buttonOnClick: {
    confirmed: () => void,
    rejected: () => void,
  }
}

const ConfirmationState = ({ prescriptionState, buttonLoading, buttonOnClick }: IConfirmationState) => {
  const iconColor = prescriptionState === 'confirmed' ? 'text-success-400' : 'text-error-600'

  if (!prescriptionState) return <></> // Para ts

  return (
    <div className='h-full flex flex-col justify-between'>
      <div className='flex flex-col items-center min-h-96 justify-center gap-6'>
        {prescriptionState === 'waiting' ? (
          <Loader color='stroke-secondary-600' size='size-5'/>
        ) : (
          <Icon
            name={states[prescriptionState].icon}
            color={iconColor}
            size='size-12'
          />
        )}
        <Paragraph color='text-grey-800' className='text-center mx-5'>
          {states[prescriptionState].msg}
        </Paragraph>
      </div>
      <div className='flex justify-end'>
        <Button
          type='button'
          variant='filled'
          onClick={prescriptionState !== 'waiting' ? buttonOnClick[prescriptionState] : undefined}
          disabled={prescriptionState === 'waiting'}
          loading={buttonLoading}
          className='w-[175px] bg-secondary-500 hover:bg-blue-700 h-12 rounded-md text-white text-base leading-5 tracking-wide py-2 px-4 font-semibold  disabled:bg-gray-50 disabled:text-gray-500 disabled:border disabled:border-gray-300 disabled:cursor-not-allowed transition-colors'
        >
          {states[prescriptionState].button}
        </Button>
      </div>
    </div>
  )
}

export default ConfirmationState