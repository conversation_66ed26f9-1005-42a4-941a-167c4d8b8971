import React, { useEffect } from 'react'
import { Spacer } from 'occipital-new'
import useSearchMedicineAr	 from '../../RecipeAR/useSearchMedicineAr'
import { Combobox as ComboboxNew } from '@/components/Shadcn/Combobox'
import { matchSorter } from 'match-sorter'
import { IAddDrug } from './AddDrug'
import { useDebounce } from '@/hooks/useDebounce'

function DrugCombobox({ watch, setValue} : IAddDrug){
	const searchMedicine = useSearchMedicineAr()
	const debouncedValue = useDebounce<string>(watch('medicine.drug'))
	const providerIsIOMA = searchMedicine?.variables?.osName?.includes("IOMA")

	// const isReady = {
	// 	farmaco : !!searchMedicine.data?.names.find( (name) => name === watch('medicine.drug')),
	// 	dosis: !!searchMedicine.data?.output.find( (medicine) => medicine.dosis === watch('medicine.dosis')),
	// }

	useEffect(() => {
		if (debouncedValue && debouncedValue != ''){ /** Sino podría estarle mandando texto vacío, cosa no muy copada */
			searchMedicine.mutate({search: debouncedValue, osName: watch('coverage.name')})
		}
	},[debouncedValue])

	const dosisList = () => {
		const uniqueDosisNames = new Set<string>()
		const dosis = searchMedicine.data?.output.filter( medicine => medicine.drugName === watch('medicine.drug'))
		const dosisIsUnique = dosis?.filter( medicine => {
			const isDuplicate = uniqueDosisNames.has(`${medicine.dosis} - ${medicine.presentationName}`)
			uniqueDosisNames.add(`${medicine.dosis} - ${medicine.presentationName}`)
			if (isDuplicate) return false
			return true
		})
		if (!dosisIsUnique || dosisIsUnique?.length === 0) return []
		/** No hago match y sorter si no escribió nada, le muestro la lista completa */
		if (watch('medicine.dosis') === '' || !watch('medicine.dosis')) return dosisIsUnique
		return matchSorter(dosisIsUnique, watch('medicine.dosis'), {
			keys: ['dosis']
		})
	}

	return (
		<>
			<p className="text-secondary-600 pb-2 ml-2">
				Fármaco:
			</p>
			<ComboboxNew
				options={searchMedicine.data?.names?.length ? searchMedicine.data.names.map(name => ({value: name, label: name})) : []}
				onChange={drugName => setValue('medicine.drug', drugName)}
				label='Buscar fármaco por genérico'
				placeholder='Medicamento'
				emptyPlaceHolder= { debouncedValue?.length < 6 ? 'Escribe más para que podamos buscar el medicamento' : 'No hemos encontrado ningun medicamento'}
				isLoading={searchMedicine.isLoading}
				shouldFilter={false}
				className='min-h-12 text-secondary-600'
			/>
			<Spacer value='12px' direction='vertical'/>
			<ComboboxNew
			options={dosisList()?.length ? dosisList().map(medicine => ({
						value: `${medicine.dosis} - ${medicine.presentationName} - ${JSON.stringify(medicine)}`,
						label: `${medicine.dosis} - ${medicine.presentationName} ${providerIsIOMA ? (medicine.corporate_coverage ? ' - Con Cobertura' : ' - Sin Cobertura') : ''}`
					})) : []}
				onChange={medicine => {
					const medicineSplited = medicine.split(' - ')
					setValue('medicine.dosis', medicineSplited[0])
					setValue('medicine.medicine', JSON.parse(medicineSplited[2]))
					setValue('medicine.presentationNames', medicineSplited[1])
				}}
				label='Dosis y presentación'
				placeholder='Pastillas'
				emptyPlaceHolder='No hemos encontrado ninguna dosis'
				shouldFilter={true}
			/>
			<Spacer value='12px' direction='vertical'/>
		</>
	)
}

export default DrugCombobox
