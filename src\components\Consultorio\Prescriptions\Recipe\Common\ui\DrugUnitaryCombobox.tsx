import React, { useEffect } from 'react'
import { Spacer } from 'occipital-new'
import { Combobox } from '@/components/Shadcn/Combobox'
import useSearchMedicineAr from '../../RecipeAR/useSearchMedicineAr'
import { IAddDrug } from './AddDrug'
import { useDebounce } from '@/hooks/useDebounce'

function DrugUnitaryCombobox({ watch, setValue} : IAddDrug){
	const searchMedicine = useSearchMedicineAr()
	const debouncedValue = useDebounce<string>(watch('medicine.drug'))
	const providerIsIOMA = searchMedicine.variables?.osName?.includes("IOMA")

	useEffect(() => {
		if (debouncedValue && debouncedValue != ''){ /** Sino podría estarle mandando texto vacío, cosa no muy copada */
			searchMedicine.mutate({search: debouncedValue, osName: watch('coverage.name')})
		}
	},[debouncedValue])
	return (
		<>
			{/* <Combobox
				type="text"
				register={register('medicine.drug', {
					required: 'Por favor, introduce el nombre del fármaco',
				})}
				label="Fármaco con marca"
				placeholder="Medicamento"
				hasValue={!!watch('medicine.drug')}
				error={errors.medicine?.drug}
				autocomplete='off'
				inputmode='text'
				loading={searchMedicine.isLoading}
				openOnFocus={watch('medicine.drug')!=undefined || watch('medicine.drug')!='' }
				required
			>
				{searchMedicine.data
					?
					<ComboboxList persistSelection>
						{
							searchMedicine.data.output.map((medicine) => {
								return (
									<ComboboxOption
										onClick={() => {
											setValue('medicine.medicine',medicine)
											setValue('medicine.drug', medicine.productName)
										}}
										key={medicine.alfabetRegisterNum + medicine.drugIDMonoDro}
										value={medicine.productName}
									>
										{medicine.productName + ' - ' + medicine.dosis + ' - ' + medicine.presentationName }
									</ComboboxOption>
								)
							})
						}
					</ComboboxList> : !watch('medicine.drug') ? 'Ponga el nombre del medicamento que desea buscar' : 'No se encuentra el medicamento'
				}
			</Combobox> */}
			<p className="text-secondary-600 pb-2 ml-2">
				Fármaco:
			</p>
			<Combobox
				options={
					searchMedicine.data?.output?.length ? searchMedicine.data.output.map(medicine => ({
						value: `${medicine.productName} - ${JSON.stringify(medicine)}`,
						label: `${medicine.productName} - ${medicine.dosis} - ${medicine.presentationName} - ${medicine.drugName}` +
						`${providerIsIOMA ? (medicine.corporate_coverage ? ' - Con Cobertura' : ' - Sin Cobertura') : ''}`
						}))
					: []
				}

				onChange={(medicine) => {
					const [medicineDrug, ...rest] = medicine.split(' - ')
					const medicineData = rest.join(' - ')
					setValue('medicine.drug', medicineDrug)
					setValue('medicine.medicine', medicineData && JSON.parse(medicineData))
				}}
				isLoading={searchMedicine.isLoading}
				shouldFilter={false}
				label='Buscar fármaco por marca'
				placeholder='Medicamento'
				emptyPlaceHolder='No encontramos ningun medicamento'
				className='min-h-12'
			/>
			<Spacer value='12px' direction='vertical'/>
		</>
	)
}

export default DrugUnitaryCombobox
