import React, { useContext, useMemo, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { Input, Loader } from 'occipital-new'
import { Select, SelectItem } from '@umahealth/occipital/client'
import useCoverages from '@/services/reactQuery/useUserCoverage'
import { IRecipeForm } from '@/components/Consultorio/Prescriptions/Interfaces/Interfaces'
import { resetReactHookFormFields } from '@/components/Consultorio/Prescriptions/utils/resetReactHookFormFields'
import { validateOS } from '../Utils/recipeHelpers'
import { Control, FieldErrors, UseFormRegister, UseFormSetValue, UseFormWatch, UseFormReset, useWatch, Controller } from 'react-hook-form'
import { AddNewOs } from './AddNewOs'
import style from '../../styles/recipePatientOS.module.scss'
import { AttContext } from '@/components/AsyncAttention'

interface IProps {
	register: UseFormRegister<IRecipeForm>,
	watch: UseFormWatch<IRecipeForm>,
	control: Control<IRecipeForm>,
	setValue: UseFormSetValue<IRecipeForm>,
	errors: FieldErrors<IRecipeForm>,
	reset: UseFormReset<IRecipeForm>,
	/** Indicates if wants to edit the prescription because it was rejected by the patient */
	editForm?: boolean,
}

export default function PatientOS({ register, watch, control, setValue, errors, reset, editForm }: IProps) {
	const asyncAtt = useContext(AttContext)
	const [ seeForm, setSeeForm ] = useState(true)
	const searchParams = useSearchParams()
	const dependant = searchParams.get('dependant')
	const patientUid = searchParams.get('patientUid')
	const dependantUidOrUndefined = dependant !== 'false' ? dependant : undefined
	const userCoverages = useCoverages(asyncAtt?.attInView ? asyncAtt.attInView.patient?.uid : patientUid as string ?? 'NO', asyncAtt?.attInView ? (asyncAtt.attInView.patient?.uid_dependant || undefined) : dependantUidOrUndefined as string, {
		enabled: (!!patientUid || !!asyncAtt?.attInView?.patient?.uid),
		refetchOnWindowFocus: false
	})
	const nameOsInput = useWatch({name: 'coverage.name', control})

	const resetFields = () =>{
		resetReactHookFormFields<IRecipeForm>({
			medicine: watch('medicine'),
			diagnosis: watch('diagnosis'),
			coverage: {
				plan: '',
				afiliateId: '',
				name: '',
				credentialVersion: '',
			}}, reset)
	}

	const onAddAnotherCoverageClick = () => {
		/** Sólo si no estan bien el formulario cargo que clickearon, porque sino estan clickeando para ocultarlo, yo sólo quiero cuando clickean para mostarlo */
		setSeeForm(!seeForm)
	}

	useMemo(() => {
		if (seeForm && !editForm) {
			resetFields()
		}
	},[seeForm, editForm])

	useMemo(() => {
		if (userCoverages.data?.length) setSeeForm(false)
	}, [userCoverages.data?.length])

	useMemo(() => {
		const iomaAppData = userCoverages.data?.find(el => el.id === 'IOMA-APP')
		const primaryOs = userCoverages.data?.find(el => el.primary)
		if (iomaAppData) {
			setValue('coverage.name', `OS - ${iomaAppData?.id || iomaAppData?.userInput}`)
			return
		}
		if (primaryOs) {
			setValue('coverage.name', `OS - ${primaryOs?.id || primaryOs?.userInput}`)
			return
		}
	}, [userCoverages.data])
	
	const userCoveragesOptions = useMemo(() => userCoverages.data?.filter(coverage => coverage.id !== 'UMA AR' || coverage.userInput !== 'UMA AR')?.map(coverage =>{
		return { value: `OS - ${coverage?.id || coverage?.userInput}`, label: `${coverage?.id || coverage?.userInput} - N°: ${coverage?.affiliate_id} ${coverage?.plan && `- Plan: ${coverage.plan}`}` }
	}), [userCoverages.data])
	
	useMemo(() =>{
		if(/OS - /i.test(nameOsInput)){
			const osName = nameOsInput?.split(' - ')[1]
			if(!osName) return
			const isIomaApp = nameOsInput?.toUpperCase()?.trim()?.includes('IOMA-APP')
			const osObject = userCoverages.data?.find(coverage => isIomaApp ? (coverage.id === 'IOMA-APP' || coverage.userInput === 'IOMA-APP') : ( coverage.id === osName || coverage.userInput === osName))
			if(!osObject) return
			const { plan, affiliate_id: afiliateId, credentialVersion } = osObject
			setValue('coverage', { name: nameOsInput, plan, afiliateId, credentialVersion })
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	},[nameOsInput])

	if (userCoverages.isLoading) return <Loader size={16} color='grey-1' />

	return (
		<>
			{!!userCoverages.data?.length && <div className={style.radioButtonContainer}>
				<div className={style.radioButton} onClick={onAddAnotherCoverageClick}>
					<div className={seeForm ? style.selected : ''}></div>
				</div>
				<label htmlFor='addNew' className='text-secondary-600'>
					Agregar otra obra social
				</label>
			</div>}
			{seeForm ? <div className={style.formContainer}>
				<AddNewOs register={register} watch={watch} setValue={setValue}/>
				<label htmlFor='plan' className='text-secondary-600 ml-2'>Plan:</label>
				<Input size='full' type='text' className='border border-success-600 max-h-3' inputmode='text' label='' register={register('coverage.plan')} hasValue={!!watch('coverage.plan')} />
				<label htmlFor='afilliateId' className='text-secondary-600 ml-2'>Nº Afiliado:</label>
				<Input size='full' type='text' inputmode='text' label='' register={register('coverage.afiliateId', {
					validate: value => validateOS(value?.toUpperCase(), watch('coverage.name'))
				})} hasValue={!!watch('coverage.afiliateId')} error={errors?.coverage?.afiliateId} />
			</div> :
				<div className='mb-2'>
					{/* <Select label='Obra social' name='coverage.name' control={control} hasValue={!!watch('coverage.name')} options={userCoveragesOptions??[]} /> */}
					<p className='text-secondary-600 my-2 ml-1 font-semibold'>Obra social:</p>
					<Controller
						control={control}
						name='coverage.name'
						render={({ field }) => (
							<Select
								value={field.value}
								placeholder=''
								onValueChange={osName => setValue('coverage.name', osName)}
							>
								{userCoveragesOptions?.map(({ value, label }) => (
									<SelectItem value={value} key={label}>
										{label}
									</SelectItem>
								))}
							</Select>
						)}
					/>
				</div>}
		</>
	)
}