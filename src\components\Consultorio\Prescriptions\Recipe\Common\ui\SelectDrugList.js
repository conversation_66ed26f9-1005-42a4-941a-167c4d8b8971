import React from 'react'
import { Icon } from '@umahealth/occipital-ui'
import { removeMedication } from '@/components/Consultorio/Prescriptions/store/prescriptionsActions'
import { useAppSelector } from '@/store/hooks'

export default function SelectedDrugList() {
	const { temp } = useAppSelector((state) => state.prescriptions)

	return (
		<div className='recipe__medicinesList'>
			{temp.medicines.map((medicine, index) =>{ 
				return (
					<div className='recipe__medicines--item' key={index}>
						{
							process.env.NEXT_PUBLIC_COUNTRY === 'MX' ?
								<p className='text-product'>
									{medicine?.productName} - Cant:{medicine?.quantity}
								</p>:
								<p className='text-product'>
									{medicine?.productName} - {medicine?.drugName} - {medicine?.presentationName} - Cant:{' '}
									{medicine?.quantity}
								</p>}
						<Icon
							name='close'
							isClickable={true}
							action={() => removeMedication(medicine?.alfabetRegisterNum || medicine?.productId)} />
					</div>
				)})}
		</div>
	)
}
