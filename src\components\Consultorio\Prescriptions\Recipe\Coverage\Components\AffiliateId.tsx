import React from 'react'
import {Input, IInput} from 'occipital-new'

type IAffiliateId = Pick<IInput,'error'|'register'|'hasValue'|'disabled'|'required'>

/**
 * Input para añadir el número de afiliación de una cobertura médica
 * @returns 
 */
function AffiliateId({error, register, hasValue, disabled} : IAffiliateId){
	return (
		<Input
			inputmode='decimal'
			type='text'
			label='Número de afiliación'
			size='full'
			register={register}
			error={error}
			hasValue={hasValue}
			disabled={disabled}
			required
		/>
	)
}

export default AffiliateId