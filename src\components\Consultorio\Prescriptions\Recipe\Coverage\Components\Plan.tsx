import React from 'react'
import {Input, IInput} from 'occipital-new'

type IPlan = Pick<IInput,'error'|'register'|'hasValue'|'disabled'|'required'>

/**
 * Input para añadir el plan de una cobertura medica
 * @returns 
 */
function Plan({error, register, hasValue, disabled} : IPlan){
	return (
		<Input
			inputmode='text'
			type='text'
			label='Plan de cobertura'
			size='full'
			register={register}
			error={error}
			hasValue={hasValue}
			disabled={disabled}
		/>
	)
}

export default Plan