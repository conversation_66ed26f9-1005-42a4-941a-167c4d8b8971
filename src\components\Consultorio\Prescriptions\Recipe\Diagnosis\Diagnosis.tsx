import React, { useMemo } from 'react'
// import { Combobox, ComboboxList, ComboboxOption } from 'occipital-new'
import { Combobox } from '@/components/Shadcn/Combobox'
import { FieldErrors, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form'
import { useDebounce } from '@/hooks/useDebounce'
import { useDiagnosticOptions } from '@/services/reactQuery/useDiagnosticOptions'
import { IRecipeForm } from '@/components/Consultorio/Prescriptions/Interfaces/Interfaces'

interface IDiagnosis {
	register: UseFormRegister<IRecipeForm>
	watch: UseFormWatch<IRecipeForm>
	errors: FieldErrors<IRecipeForm>
	setValue: UseFormSetValue<IRecipeForm>
}

function Diagnosis({ watch, setValue} : IDiagnosis){
	const debouncedValue = useDebounce<string>(watch('diagnosis'))
	const diagnosticOptions = useDiagnosticOptions()

	useMemo(() => debouncedValue && diagnosticOptions.mutate(debouncedValue) ,[debouncedValue])
	
	return(
		<>
			{/* <Combobox
				type="text"
				register={register('diagnosis')}
				label="Diagnóstico asociado"
				placeholder="Fiebre"
				hasValue={!!watch('diagnosis')}
				error={errors.diagnosis}
				autocomplete='off'
				inputmode='text'
				openOnFocus={true}
				required
			>
				<ComboboxList persistSelection>
					{
						diagnosisList.map((diagnosis) => {
							return (
								<ComboboxOption
									onClick={() => setValue('diagnosis',diagnosis)}
									key={diagnosis}
									value={diagnosis}
								>
									{diagnosis}
								</ComboboxOption>
							)
						})
					}
				</ComboboxList>
			</Combobox> */}
			<Combobox 
				options={diagnosticOptions.data?.length ? diagnosticOptions.data : []} 
				onChange={diagnosis => setValue('diagnosis', diagnosis)}
				label='Diagnóstico asociado'
				placeholder='Fiebre'
				shouldFilter={false}
				emptyPlaceHolder='No encontramos ningun diagnostico'
				isLoading={diagnosticOptions.isLoading}
				className='min-h-12'
			/>
		</>
	)
}

export default Diagnosis