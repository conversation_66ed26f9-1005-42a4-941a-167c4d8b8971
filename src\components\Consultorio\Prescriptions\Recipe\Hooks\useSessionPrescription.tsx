import { useEffect, useState } from 'react'
import { IConfirmPrescriptionResponse, IPrescriptionPostFunctionParameters, PrescriptionState } from '../../Interfaces/Interfaces'
import { Event, Publisher, Session } from '@opentok/client'
import { errorHandler } from '@/config/stackdriver'

type ConfirmPrescriptionEvent = Event<'signal:confirm_prescription', Publisher> & {
	data: string
}

export const useSessionPrescription = (session: Session | null, providerUid: string) => {
	const [prescriptionState, setPrescriptionState] = useState<PrescriptionState>(null)
  const [prescriptionData, setPrescriptionData] = useState<IPrescriptionPostFunctionParameters>()

	const resetTemporalPrescription = () => {
		setPrescriptionState(null)
		setPrescriptionData(undefined)
	}

	useEffect(() => {
		if (session) {
			session.on('signal:confirm_prescription', (event) => {
				const typedEvent = event as ConfirmPrescriptionEvent
				if (typedEvent.data) {
					const { confirmed } = JSON.parse(typedEvent.data) as IConfirmPrescriptionResponse
					setPrescriptionState(confirmed ? 'confirmed' : 'rejected')
				} else {
					errorHandler?.report(`[ doctor | signal:confirm_prescription ] => Event data does not exist - provider: ${providerUid}`)
				}
			})
		}
  }, [session])

  return {
    prescriptionData,
    setPrescriptionData,
    prescriptionState,
    setPrescriptionState,
		resetTemporalPrescription
  }
}
