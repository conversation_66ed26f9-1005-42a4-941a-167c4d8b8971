import React from 'react'
import axios from 'axios'
import swal from 'sweetalert'
import {
  Button,
  Icon,
  Paragraph,
  Row,
  Spacer,
  Title,
  Loader,
} from '@umahealth/occipital-ui'
import { download_recipe } from '@/config/endpoints'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { errorHandler } from '@/config/stackdriver'
import styles from '../styles/prescriptionList.module.css'
import PredocButton from '@/modules/consultorio/presentation/components/PatientInfo/PredocButton'

export default function RecipeListDisplay({
  prescriptionArray,
  loading,
  createPrescription,
  handleDeletePrescription,
  handleViewPrescription,
}) {
  const handleSearchPDFURL = async (prescriptionId) => {
    try {
      const token = await getFirebaseIdToken()
      const prescriptionBody = { id: prescriptionId, country: 'AR' }
      const headers = {
        'Content-type': 'application/json',
        Authorization: `Bearer ${token}`,
      }
      const pdfResponse = await axios.post(download_recipe, prescriptionBody, {
        headers,
      })
      return pdfResponse.data.url
    } catch (error) {
      const timestamp = new Date().toLocaleString()
      errorHandler && errorHandler.report(error)
      console.error('Error fetching PDF:', error)
      swal(
        'Ocurrio un error obteniendo el PDF de la receta',
        `Detalle: ${timestamp}`,
        'warning'
      )
      throw error
    }
  }

  const handlePDFOpening = async (p) => {
    handleViewPrescription(true)
    const data = await handleSearchPDFURL(p.id)
    if (data && typeof window !== 'undefined') {
      window.open(data, '_blank')
    }
  }

  return (
    <>
      <Title hierarchy={3} align="left">
        Recetas
      </Title>
      {prescriptionArray?.length > 0 ? (
        <>
          {loading ? (
            <Row spacing="center">
              {' '}
              <Loader />{' '}
            </Row>
          ) : (
            prescriptionArray?.map((p, i) => {
              return (
                <Row key={i} spacing="between">
                  <div onClick={() => handlePDFOpening(p)}>
                    <p
                      key={i}
                      className={styles.prescriptionLink}
                    >{`Receta de ${p.items?.reduce(
                      (textAcum, el) => (textAcum += ` - ${el}`)
                    )}`}</p>
                  </div>
                  <Icon
                    color="primary"
                    name="trash"
                    size="xs"
                    isInline={true}
                    action={() => handleDeletePrescription(p)}
                  />
                </Row>
              )
            })
          )}
        </>
      ) : (
        <Paragraph align="left">Aún no hay recetas cargadas</Paragraph>
      )}
      <Spacer value="16px" />
      <Row spacing="center">
        <PredocButton />
        <Spacer direction="horizontal" value="16px" />
        <Button size="medium" action={() => createPrescription()}>
          <Row>
            <Icon color="white" name="plus" size="xs" isInline={true} />
            <Spacer direction="horizontal" value="8px" />
            Receta
          </Row>
        </Button>
      </Row>
    </>
  )
}
