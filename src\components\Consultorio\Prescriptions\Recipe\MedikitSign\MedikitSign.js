import React from 'react'
import { Button } from '@umahealth/occipital-ui'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import { errorHandler } from '@/config/stackdriver'

const MedikitSign = () => {
	const dispatch = useAppDispatch()
	const { profile } = useAppSelector((state) => state.user)
	const {
		token_consulta_medikit
	} = useAppSelector((state) => state.prescriptions)

	const handlePopUp = () => {
		if(typeof window === 'undefined') return
		const requestData = {
			'nombreDoctor': profile?.fullname,
			'idDoctor': profile?.medikit_token,
			'cedula': profile?.matricula,
			'idConsulta': token_consulta_medikit,
			'nombrePaciente': '',
			'cliente': 'consultorio'
		}
		window.mkSignatureInit(requestData)
			.then((successData) => {
				let url = successData.signedUrl? successData.signedUrl:successData.signature64
		
				dispatch({type: 'SET_MEDIKIT_SIGNATURE', payload: url}) 
				dispatch({type: 'SET_MEDIKIT_SIGNATURE_HASH', payload: successData.hash}) 
			})
			.catch(function(e) {
				errorHandler.report(e.error)
				console.error(e)
			})
	}
	

	return (
		<div className="medikitSignButton__container">
			<Button 
				action={handlePopUp}
				color="secondary"
				size="large">
			Firmar
			</Button>
		</div>
	)
}

export default MedikitSign