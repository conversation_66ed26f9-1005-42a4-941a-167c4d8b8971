import React from 'react'
import { Text, Icon } from 'occipital-new'
import { medicineByBrand, medicineByDrug } from '../../Interfaces/Interfaces'
import { prescriptionItems } from '.'
import style from '../styles/drugItem.module.scss'

interface IProps{
	drug: ((medicineByBrand | medicineByDrug) & {uniqueKey: string}),
	setDrugItems: React.Dispatch<React.SetStateAction<prescriptionItems>>,
	drugItems: prescriptionItems
}

export const DrugItem = ({drug, setDrugItems, drugItems} : IProps) => {
	
	const removeDrugItem = () =>{
		const drugItemsFiltered = drugItems?.filter(drugItem => drugItem?.uniqueKey !== drug?.uniqueKey)
		setDrugItems(drugItemsFiltered)
	}

	return (
		<div className={style.drugItemContainer}>
			<Text tag='span' weight='regular' size='s' color='grey-1'>{drug?.medicine?.productName} - Cantidad: {drug?.quantity}</Text>
			<div onClick={removeDrugItem}>
				<Icon name='close' size='s' color='primary'/>
			</div>
		</div>
	)
}
