import { store } from '@/store/configStore'
import {
  getEntity,
  postPrescriptionInChat,
} from '../../../store/prescriptionsActions'
import {
  IPrescriptionPostFunctionParameters,
  IPrescriptionRequest,
} from '../../../Interfaces/Interfaces'
import { validateAffiliate } from '../arRecipeHelpers'
import getFirebaseIdToken from '@/services/firebase/getFirebaseIdToken'
import { audibaires } from '@/config/endpoints'
import axios from 'axios'

const { getState } = store

export async function postAudibairesPrescription({
  assignationId,
  uid,
  formData,
  asyncAttData,
  dependantUid,
}: IPrescriptionPostFunctionParameters) {
  const state = getState()
  const { patient } = state.queries
  const { profile } = state.user

  const prescriptionData: IPrescriptionRequest = {
    assignation_id: assignationId,
    entity: getEntity(formData?.coverage?.name),
    medicines: formData?.medicine,
    diagnosis: formData?.diagnosis,
    patient: {
      corporate: 'POLICIA FEDERAL',
      dni: asyncAttData ? asyncAttData?.patient?.dni : patient?.dni || '',
      fullname: asyncAttData
        ? asyncAttData?.patient?.fullname
        : patient?.fullname || '',
      chosenName: asyncAttData ? '' : patient?.chosenName || '',
      n_afiliado: validateAffiliate(formData.coverage?.afiliateId) || '',
      plan: formData.coverage?.plan || '',
      uid,
      dependantUid,
    },
    providerUid: profile?.uid,
  }
  return postAudibairesRecipe(prescriptionData)
}

export async function postAudibairesRecipe(requestData: IPrescriptionRequest) {
  const token = await getFirebaseIdToken()
  const headers = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  }
  const prescriptionResponse = await axios.post(
    audibaires,
    requestData,
    headers
  )

  try {
    await postPrescriptionInChat(requestData, prescriptionResponse.data.id)
  } catch (chatError) {
    console.error('Error al enviar notificación por chat:', chatError)
    // No interrumpimos el flujo principal si falla la notificación
  }

  return prescriptionResponse.data
}
