import { Dispatch, SetStateAction } from 'react'
import axios, { AxiosError } from 'axios'
import swal from 'sweetalert'
import { getEntity, postPrescriptionInChat } from '../../../store/prescriptionsActions'
import { validateAffiliate } from '../arRecipeHelpers'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { osde_receta, osde_receta_quick } from '@/config/endpoints'
import { store } from '@/store/configStore'
import { errorHandler } from '@/config/stackdriver'
const { dispatch, getState } = store
import { IPrescriptionPostFunctionParameters, IPrescriptionRequest } from '../../../Interfaces/Interfaces'
import { TCurrentView } from '@/components/Consultorio/Prescriptions'

export async function postOsdePrescription({ assignationId, uid, formData, asyncAttData, goTo, dependantUid }: IPrescriptionPostFunctionParameters) {
	const state = getState()
	const { patient } = state.queries
	const { profile } = state.user

	const prescriptionData: IPrescriptionRequest = {
		assignation_id: assignationId,
		entity: getEntity(formData?.coverage?.name),
		medicines: formData?.medicine,
		diagnosis: formData?.diagnosis,
		patient: {
			corporate: 'OSDE',
			dni: asyncAttData ? asyncAttData?.patient?.dni : (patient?.dni || ''),
			fullname: asyncAttData ? asyncAttData?.patient?.fullname : (patient?.fullname || ''),
			chosenName: asyncAttData ? '' : (patient?.chosenName || ''),
			n_afiliado: validateAffiliate(formData.coverage?.afiliateId) || '',
			plan: formData.coverage?.plan || '',
			uid,
			dependantUid,
		},
		providerUid: profile?.uid,
	}
	return await osdeRecipe(prescriptionData, goTo)
}

export async function osdeRecipe(requestData: IPrescriptionRequest, goTo: Dispatch<SetStateAction<TCurrentView>>) {
	try {
		const token = await getFirebaseIdToken()
		const config = {
			headers: {
				'Authorization': `Bearer ${token}`,
				'content-type': 'application/json',
				'uid': requestData.providerUid,
				'x-api-key': process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY
			}
		}
		const response = await axios.post(osde_receta, requestData, config)
		
		try {
			await postPrescriptionInChat(requestData, response.data.prescriptionId)
		} catch (chatError) {
			console.error('Error al enviar notificación por chat:', chatError)
			// No interrumpimos el flujo principal si falla la notificación
		}
		
		goTo('prescriptionList')
		return response
	} catch (error) {
		const timestamp = new Date().toLocaleString();
		if ((error as AxiosError)?.response?.status === 422) {
			await swal(
				{
					title: 'Error',
					text: 'Verifique el n° de afiliado e intente nuevamente',
					icon: 'warning',
				}
			)
		}
		if ((error as AxiosError)?.response?.status === 400) {
			await swal(
				{
					title: 'Error',
					text: 'La credencial no puede tener mas ni menos de 11 digitos ni caracteres no numericos.',
					icon: 'warning',
				}
			)
		}
		if (errorHandler) errorHandler.report(error as Error)
		await swal(
			{
				title: 'Error',
				text: `No se pudo generar la receta (Recipe OSDE). ${timestamp}`,
				icon: 'warning',
			}
		)
		dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: true})
	}
}


export async function postOsdeQuickPrescription(prescriptionData: IPrescriptionRequest) {
	return await osdeQuickRecipe(prescriptionData)
}

export async function osdeQuickRecipe(requestData: IPrescriptionRequest) {
	try {
		const token = await getFirebaseIdToken()
		const config = {
			headers: {
				'Authorization': `Bearer ${token}`,
				'content-type': 'application/json',
				'uid': requestData.provider,
				'x-api-key': process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY
			}
		}

		await axios.post(osde_receta_quick, {...requestData, validator: 'OSDE'}, config)
		return true
	} catch (error) {
		const timestamp = new Date().toLocaleString();
		if ((error as AxiosError)?.response?.status === 422) {
			await swal(
				{
					title: 'Error',
					text: 'Verifique el n° de afiliado e intente nuevamente',
					icon: 'warning',
				}
			)
			return error
		}
		if ((error as AxiosError)?.message.includes("La credencial no puede tener mas ni menos de 11 digitos ni caracteres no numericos")) {
			await swal(
				{
					title: 'Error',
					text: 'Verifique el n° de afiliado e intente nuevamente',
					icon: 'warning',
				}
			)
		}
		if (errorHandler) errorHandler.report(error as Error)
		await swal(
			{
				title: 'Error',
				text: `No se pudo generar la receta (Quick Recipe OSDE). ${timestamp}`,
				icon: 'warning',
			}
		)
		dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: true})
		return error
	}
}
