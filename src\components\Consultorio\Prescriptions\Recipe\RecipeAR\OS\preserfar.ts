import { IPrescriptionData } from './../../../Interfaces/Interfaces'
import { Dispatch, SetStateAction } from 'react'
import { IPrescriptionPostFunctionParameters } from '@/components/Consultorio/Prescriptions/Interfaces/Interfaces'
import { preserfar_receta, preserfar_receta_quick } from '@/config/endpoints'
import { store } from '@/store/configStore'
import { TCurrentView } from '@/components/Consultorio/Prescriptions'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { errorHandler } from '@/config/stackdriver'
import axios, { AxiosError } from 'axios'
import swal from 'sweetalert'
import { IPatient } from '@umahealth/entities'
const { dispatch } = store

const formatBody = ({ assignationId, asyncAttData, formData, uid, providerUid, patient, dependantUid }: IPrescriptionPostFunctionParameters & { providerUid: string, patient: Partial<IPatient> , type: 'att'}) => ({
	assignation_id: assignationId,
	medicines: formData?.medicine,
	diagnosis: formData?.diagnosis || '',
	patient: {
		corporate: formData?.coverage?.name || '',
		dni: asyncAttData ? asyncAttData.patient?.dni || '' : patient.dni || '',
		fullname: asyncAttData ? asyncAttData.patient?.fullname || '' : patient?.fullname || '',
		chosenName: asyncAttData ? '' : patient?.chosenName || '',
		n_afiliado: formData?.coverage?.afiliateId || '',
		plan: formData?.coverage?.plan || '',
		uid,
		dependantUid,
	},
	provider: providerUid,
	type: 'att' as const
})

const formatQuickBody = ({ medicines, patient, diagnosis, providerUid }: IPrescriptionData) =>({
	medicines,
	diagnosis,
	patient,
	providerUid,
	type: 'quick' as const
})

export async function postPreserfarPrescription(data: IPrescriptionPostFunctionParameters, providerUid: string, patient: Partial<IPatient>) {
	const prescriptionData = formatBody({ ...data, providerUid, patient, type: 'att' })
	return await preserfarPrescription(prescriptionData, data.goTo)
}

export async function postPreserfarQuickPrescription(data: IPrescriptionData) {
	const prescriptionData = formatQuickBody(data)
	return await preserfarPrescription(prescriptionData)
}

export async function responseEndpoint(requestData: ReturnType<typeof formatBody> | ReturnType<typeof formatQuickBody>){
	const uid = requestData.type === 'att' ? requestData?.provider : requestData?.providerUid
	const token = await getFirebaseIdToken()
	const config = {
		headers: {
			'Authorization': `Bearer ${token}`,
			'content-type': 'application/json',
			'uid': uid,
			'x-api-key': process.env.REACT_APP_UMA_BACKEND_LOGIC_APIKEY
		}
	}
	return requestData?.type === 'att' ? 
		await axios.post(preserfar_receta, requestData, config) : 
		await axios.post(preserfar_receta_quick, requestData, config)
}

export async function responseEndpointPrescription(requestData: IPrescriptionData){
	const uid = requestData?.providerUid
	const token = await getFirebaseIdToken()
	const config = {
		headers: {
			'Authorization': `Bearer ${token}`,
			'content-type': 'application/json',
			'uid': uid,
			'x-api-key': process.env.REACT_APP_UMA_BACKEND_LOGIC_APIKEY
		}
	}
		return await axios.post(preserfar_receta, requestData, config)
}

async function preserfarPrescription(requestData: ReturnType<typeof formatBody> | ReturnType<typeof formatQuickBody>, goTo?: Dispatch<SetStateAction<TCurrentView>>) {
	try {
		const preserfarResponse = await responseEndpoint(requestData)
		dispatch({ type: 'HANDLE_REMOVE_DATA_PRESCRIPTION', payload: undefined })
		goTo && goTo('prescriptionList')
		return preserfarResponse
	} catch (error) {
		if ((error as AxiosError)?.response?.status === 422) {
			await swal(
				'Número de afiliado inválido',
				'Verifique el número colocado e intente nuevamente',
				'warning'
			)
		}
		if (errorHandler) errorHandler.report(error as Error)
	
	}
}