import { Dispatch, SetStateAction } from 'react'
import axios, { AxiosError } from 'axios'
import swal from 'sweetalert'
import { up_receta, up_receta_quick } from '@/config/endpoints'
import { store } from '@/store/configStore'
import { errorHandler } from '@/config/stackdriver'
import { validateAffiliate } from '../arRecipeHelpers'
const { getState, dispatch } = store
import * as ordersActions from '../../../store/prescriptionsActions'
import { IPrescriptionData, IPrescriptionPostFunctionParameters, IPrescriptionRequest } from '../../../Interfaces/Interfaces'
import { TCurrentView } from '@/components/Consultorio/Prescriptions'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { revalidateGetAssignation } from '@/serverServices/getAssignation'
import { revalidateMedicalRecords } from '@/serverServices/getMedicalRecords'


//type AppointmentWithChosenName = IAppointment<Timestamp> & { patient: IAppointment<Timestamp>['patient'] & { chosenName: string }} //TODO verificar que el appointment tenga el chosenName

export async function postRecipeUP({ formData, assignationId, goTo, uid, asyncAttData, dependantUid }: IPrescriptionPostFunctionParameters) {
	const state = getState()
	const { patient } = state.queries
	const { profile } = state.user
	const prescriptionData: IPrescriptionRequest = {
		assignation_id: assignationId,
		medicines: formData?.medicine,
		diagnosis: formData?.diagnosis,
		patient: {
			corporate: formData.coverage?.name?.trim(),
			dni: asyncAttData ? asyncAttData?.patient?.dni : (patient?.dni || ''),
			fullname: asyncAttData ? asyncAttData?.patient?.fullname : (patient?.fullname || ''),
			chosenName: asyncAttData ? '' : (patient?.chosenName || ''),
			n_afiliado: validateAffiliate(formData.coverage?.afiliateId) || '',
			plan: formData?.coverage?.plan || '',
			uid,
			credentialVersion: formData.coverage?.credentialVersion || '',
			dependantUid,
		},
		providerUid: profile?.uid,
	}
	return await upRecipe(prescriptionData, goTo)
}

export async function upRecipe(requestData: IPrescriptionRequest, goTo: Dispatch<SetStateAction<TCurrentView>>) {
	try {
		const token = await getFirebaseIdToken()
		const headers = {
			'Authorization': `Bearer ${token}`,
			'content-type': 'application/json',
		}
		const response = await axios.post(up_receta, requestData, { headers })
		
		try {
			await ordersActions.postPrescriptionInChat(requestData, response.data.id)
		} catch (chatError) {
			console.error('Error al enviar notificación por chat:', chatError)
			// No interrumpimos el flujo principal si falla la notificación
		}
		
		ordersActions.setRecipeLoading(false)
		goTo('prescriptionList')
		return response
	} catch (error: any) {
		if (errorHandler) errorHandler.report(error)
		if (error?.response?.data?.exception?.message === 'ERROR DE SEGURIDAD' ||
		error?.response?.data?.exception?.message === 'ERROR DE SISTEMA' ||
		error?.response?.data?.exception?.message === 'RELACION INEXISTENTE') {
			console.error(`No se pudo generar receta por ${error?.response?.data?.exception?.message}`)
			await swal(
				'La receta no pudo ser validada correctamente. ',
				'Por favor, intente nuevamente',
				'warning'
			)
		} else if (error?.response?.data?.exception?.message === 'afiliado inexistente') {
			console.error(`No se pudo generar receta por ${error?.response?.data?.exception?.message}`)
			await swal(
				'La receta no pudo ser generada.',
				'El número de afiliado no existe. Por favor, corrobore que este dato sea correcto',
				'warning'
			)
		}else if (error?.response?.data?.exception?.message.includes('afiliado no habilitado')) {
			console.error(`No se pudo generar receta por ${error?.response?.data?.exception?.message}`)
				await swal(
					'La receta no pudo ser generada.',
					'Afiliado no habilitado por la Obra Social. Corrobore con el paciente el número de afiliado.',
					'warning'
				)
		} else if (error?.response?.data?.exception?.message.includes('CANAL TECNOLOGICO NO HABILITADO')) {
			console.error(`No se pudo generar receta por ${error?.response?.data?.exception?.message}`)
				await swal(
					'La receta no pudo ser generada.',
					'Por disposición de esta cobertura no se nos permite la generación de recetas de medicamentos.',
					'warning'
				)
		} else {
			await swal(
				'La receta no pudo ser generada.',
				`Error: ${error?.response?.data?.exception?.message || ''}`,
				'warning'
			)
		}
		ordersActions.setRecipeLoading(false)
		dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: true})
	}
}

export async function postUPQuickPrescription(prescriptionData: IPrescriptionData) {
	const response = await UPQuickRecipe(prescriptionData)
	return response
}

export async function UPQuickRecipe(requestData: IPrescriptionData) {
	try {
		const firebaseIdToken = await getFirebaseIdToken()
		const config = { headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${firebaseIdToken}` } }
		const response = await axios.post(up_receta_quick, {...requestData, validator: 'UNION PERSONAL'}, config)
		if (response?.data?.created) {
			await swal(
				'Receta generada!',
				'Se guardó la receta exitosamente.',
				'success'
			)
			await revalidateGetAssignation()
			await revalidateMedicalRecords()
		}
		return response
	} catch (error) {
		const timestamp = new Date().toLocaleString();
		if (errorHandler) errorHandler.report(error as Error)
		if ((error as AxiosError).response?.data.error === 'ERROR DE SEGURIDAD' ||
			(error as AxiosError).response?.data.error === 'ERROR DE SISTEMA' ||
			(error as AxiosError).response?.data.error === 'CANAL TECNOLOGICO NO HABILITADO' ||
			(error as AxiosError).response?.data.error === 'RELACION INEXISTENTE') {
			console.error(`No se pudo generar receta por ${(error as AxiosError).response?.data.error}`)
			await swal(
				'La receta no pudo ser validada correctamente. ',
				'Por favor, intente nuevamente',
				'warning',
				{
					buttons: ['Reintentar']
				}
			)
		} else if ((error as AxiosError).response?.data.error === 'afiliado inexistente') {
			console.error(`No se pudo generar receta por ${(error as AxiosError).response?.data.error}`)
			await swal(
				'La receta no puede ser generada.',
				'El número de afiliado no existe. Por favor, corrobore que este dato sea correcto',
				'warning',
				{
					buttons: ['Reintentar']
				}
			)
		} else {
			await swal(
				`Error: ${(error as AxiosError).response?.data?.error || ''}`,
				`No se pudo generar la receta de Unión Personal.`,
				`Detalle: ${timestamp}`,
				'warning'
			)
		}
		ordersActions.setRecipeLoading(false)
		dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: true})
		return false
	}
}
