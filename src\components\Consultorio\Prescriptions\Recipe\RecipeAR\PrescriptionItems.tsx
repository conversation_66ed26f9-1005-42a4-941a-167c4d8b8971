import React, { useState } from 'react'
import { UseFormReset } from 'react-hook-form'
import { Spacer } from 'occipital-new'
import { DrugItem } from './DrugItem'
import { AddDrug, IAddDrug } from '../../Recipe/Common/ui/AddDrug'
import { medicineByDrug, medicineByBrand, IRecipeForm } from '../../Interfaces/Interfaces'
import { prescriptionItems } from './index'
import { resetReactHookFormFields } from '../../utils/resetReactHookFormFields'
import style from '../styles/prescriptionItem.module.scss'
import { useActiveServicesFromCorporate } from '@/services/reactQuery/useCoverageServices'
import { Button } from '@umahealth/occipital'

interface IProps extends IAddDrug {
	drugItems: prescriptionItems,
	setDrugItems: React.Dispatch<React.SetStateAction<prescriptionItems>>
	reset: UseFormReset<IRecipeForm>,
	editForm?: boolean,
}

export default function PrescriptionItems({ reset, drugItems, setDrugItems, register, watch, errors, setValue, control, editForm }: IProps) {
	const [creatingDrug, setCreatingDrug] = useState<boolean>(editForm ? false : true)
	const drugsByBrandReady = !!watch('medicine.medicine') && !!watch('medicine.quantity')
	const drugsByMonodrugReady = !!watch('medicine.drug') && !!watch('medicine.presentationNames') && !!watch('medicine.dosis') && !!watch('medicine.medicine') && !!watch('medicine.quantity')
	const disabled = watch('medicine.monodrugSearch') === true ? drugsByBrandReady : drugsByMonodrugReady
	const coverage = watch('coverage')?.name
	const activeServices = useActiveServicesFromCorporate(coverage??'NO')
	const maxDrug = activeServices?.data?.maxDrugItems || 2
	
	const pushDrugs = () => {
		setDrugItems([...drugItems, generateUniqueKey(watch('medicine'))])
		setCreatingDrug(false)
		resetFields()
	}

	const resetFields = () =>{
		resetReactHookFormFields<IRecipeForm>({
			medicine: {
				quantity: '1',
				monodrugSearch: true
			},
			diagnosis: watch('diagnosis'),
			coverage: watch('coverage')}, reset)
	}

	const generateUniqueKey = (drug : medicineByBrand | medicineByDrug) : ((medicineByDrug | medicineByBrand) & {uniqueKey : string}) =>{
		const uniqueKey = `${ drug?.medicine?.drugIDMonoDro }_${ new Date().getTime() }`
		return {...drug, uniqueKey}
	}

	return (
		<div>
			{drugItems?.length > 0 && drugItems.map((item) => <DrugItem drugItems={drugItems} setDrugItems={setDrugItems} key={item.uniqueKey} drug={item} />)}
			{drugItems?.length < maxDrug && !creatingDrug &&
				<div className="flex items-center justify-end">
				<Button
					type='button'
					variant='filled'
					onClick={() => setCreatingDrug(true)}
					className={
						"bg-secondary-500 hover:bg-blue-700 h-12 rounded-md text-white text-base leading-5 tracking-wide py-2 px-4 font-semibold disabled:bg-gray-50 disabled:text-gray-500 disabled:border disabled:border-gray-300 disabled:cursor-not-allowed transition-colors max-w-72"
					}
				>
					Agregar medicamento
				</Button>
			</div>
			}
			{creatingDrug && <>
				<AddDrug register={register} watch={watch} errors={errors} setValue={setValue} control={control} />
				<Spacer value='8px' direction='vertical' />
				<div className={style.buttonsSubmitDrugContainer}>
					<Button action={() => {
						setCreatingDrug(false)
						resetFields()
					}}
					type='button'
					size='full'
					variant='outlined'
					>
					Cancelar
					</Button>
					<Button action={pushDrugs}
						type='button'
						size='full'
						variant='outlined'
						disabled={!disabled}
						className='bg-secondary-500 hover:bg-blue-700 h-12 rounded-md text-white text-base leading-5 tracking-wide py-2 px-4 font-semibold disabled:bg-gray-50 disabled:text-gray-500 disabled:border disabled:border-gray-300 disabled:cursor-not-allowed transition-colors'
						>
					Agregar
					</Button>
				</div>	
			</>}
		</div>
	)
}

