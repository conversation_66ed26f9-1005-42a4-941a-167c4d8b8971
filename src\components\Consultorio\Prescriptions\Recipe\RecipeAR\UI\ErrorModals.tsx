import { Modal } from "@/storybook/components/modals/Modal/Modal";
import { setDuplicatedMedication, setInvalidAffiliateNumber, setInvalidCredential, setInvalidData, setInvalidMedication, setInvalidStructure } from "../../../store/prescriptionsActions";
import Medications from "@/assets/medications.svg";
import MedicalPrescriptions from "@/assets/medicalPrescription.svg"
import MedicationError from "@/assets/medicationError.svg"
import DoctorSearching from "@/assets/doctorSearch.svg"


export const InvalidMedication: React.FC = () => {

    const content = {
        image: Medications,
        alternativeText: 'Medicación Inválida',
        title: 'Medicación Inválida',
        description: 'Ocurrió un error con los datos de la medicación. Reintentá cambiando a genérico/por marca o dosis y/o presentación.',
        primaryButtonText: 'Reintentar',
        primaryAction: () => { 
            setInvalidMedication(false)
        }
    };

    return (
        <Modal
            image={content.image}
            alternativeText={content.alternativeText}
            title={content.title}
            description={content.description}
            primaryButtonText={content.primaryButtonText}
            primaryAction={content.primaryAction}
        />
    );
};

export const InvalidData: React.FC = () => {

    const content = {
        image: Medications,
        alternativeText: 'Datos inválidos',
        title: 'Ups... Tuvimos un problema',
        description: 'Ocurrió un error con los datos de la medicación. Te pedimos que reintentes el ingreso y generes nuevamente la receta.',
        primaryButtonText: 'Reintentar',
        primaryAction: () => { 
            setInvalidData(false)
        }
    };

    return (
        <Modal
            image={content.image}
            alternativeText={content.alternativeText}
            title={content.title}
            description={content.description}
            primaryButtonText={content.primaryButtonText}
            primaryAction={content.primaryAction}
        />
    );
};

export const InvalidCrendential: React.FC = () => {

    const content = {
        image: MedicalPrescriptions,
        alternativeText: 'Credencial inválida',
        title: 'Upss... revisá la cobertura de salud',
        description: 'El número de afiliado ingresado está fuera de vigencia o dado de baja. Verifique con el paciente y actualice los datos.',
        primaryButtonText: 'Entendido',
        primaryAction: () => { 
            setInvalidCredential(false)
        }
    };

    return (
        <Modal
            image={content.image}
            alternativeText={content.alternativeText}
            title={content.title}
            description={content.description}
            primaryButtonText={content.primaryButtonText}
            primaryAction={content.primaryAction}
        />
    );
};

export const DuplicatedMedication: React.FC = () => {

    const content = {
        image: MedicalPrescriptions,
        alternativeText: 'Medicamento duplicado',
        title: 'Medicamento duplicado',
        description: 'No se añadirá el medicamento que ingresaste porque no se admiten medicamentos repetidos en las recetas.',
        primaryButtonText: 'Entendido',
        primaryAction: () => { 
            setDuplicatedMedication(false)
        }
    };

    return (
        <Modal
            image={content.image}
            alternativeText={content.alternativeText}
            title={content.title}
            description={content.description}
            primaryButtonText={content.primaryButtonText}
            primaryAction={content.primaryAction}
        />
    );
};

export const InvalidStructure: React.FC = () => {

    const content = {
        image: MedicationError,
        alternativeText: 'Error al generar receta',
        title: 'Error al generar receta',
        description: 'Se detectó un error en el sistema de generación de recetas. Te pedimos que intentes nuevamente y si el problema persiste, lo reportes a soporte.',
        primaryButtonText: 'Reintentar',
        primaryAction: () => {
            setInvalidStructure(false)
        }
    };

    return (
        <Modal
            image={content.image}
            alternativeText={content.alternativeText}
            title={content.title}
            description={content.description}
            primaryButtonText={content.primaryButtonText}
            primaryAction={content.primaryAction}
        />
    );
};

export const InvalidAffiliateNumber: React.FC = () => {

    const content = {
        image: DoctorSearching,
        alternativeText: 'Afiliado inválido',
        title: 'Error en el número de afiliado',
        description: 'Se enviará un mensaje automático al paciente a través del chat para que decida cómo continuar como particular sin cobertura en farmacia o cambiar su cobertura de salud.',
        primaryButtonText: 'Aceptar',
        primaryAction: () => {
            setInvalidAffiliateNumber(false)
        }
    };

    return (
        <Modal
            image={content.image}
            alternativeText={content.alternativeText}
            title={content.title}
            description={content.description}
            primaryButtonText={content.primaryButtonText}
            primaryAction={content.primaryAction}
        />
    );
};