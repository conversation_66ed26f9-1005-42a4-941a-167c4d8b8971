import axios  from 'axios'
import { vademecum } from '@/config/endpoints'
import { store } from '@/store/configStore'
import * as prescriptionsTypes from '../../store/prescriptionsTypes'
import { errorHandler } from '@/config/stackdriver'
import { auth } from '@/config/firebase'
const { dispatch, getState } = store

export function validateAffiliate(affiliateNum) {
	return affiliateNum === 11 ? affiliateNum.substring(0, affiliateNum?.length - 1) : affiliateNum
}

export async function searchMedicinesAR(text = '') {
	if(text?.length < 3) return null
	const state = getState()
	const {currentUser} = state.user
	let token
	if(currentUser){
		token = auth?.currentUser?.getIdToken()
	}
	
	const headers = { 
		'Content-Type': 'application/json',
		'Authorization': `Bearer ${token}`,
	}
	
	const body = { text: text , country: process.env.NEXT_PUBLIC_COUNTRY}

	try {
		const res = await axios.post(vademecum, body, {headers: headers})
		const drugsOptions = await res.data.output.map((drug) => ({
			value: `${drug.alfabetRegisterNum}`,
			label: `${drug.productName.toUpperCase()} - ${drug.drugName} - ${drug.presentationName} - ${drug.dosis}`,
		}))
		dispatch({ type: prescriptionsTypes.HANDLE_SEARCH_RESULT, payload: res.data.output })
		dispatch({ type: prescriptionsTypes.HANDLE_DRUG_OPTIONS, payload: drugsOptions })
		return drugsOptions
	} catch (error) {
		errorHandler?.report(error)
		return console.error(error)
	}
}