import React, { useCallback, useContext, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { SubmitHandler, useForm } from 'react-hook-form'
import { Spacer } from '@umahealth/occipital-ui'
import { Button } from 'occipital-new'
import { medicineFormater } from '@/components/Consultorio/Prescriptions/Recipe/Common/Utils/recipeHelpers'
import {
  IPrescriptionPostFunctionParameters,
  IRecipeForm,
  medicineByBrand,
  medicineByDrug,
} from '../../Interfaces/Interfaces'
import Diagnosis from '@/components/Consultorio/Prescriptions/Recipe/Diagnosis/Diagnosis'
import RecipePatientOS from '../Common/ui/RecipePatientOS'
import PrescriptionItems from './PrescriptionItems'
import { TCurrentView } from './../../index'
import { AttContext, MrContext } from '@/components/AsyncAttention'
import { useAppSelector } from '@/store/hooks'
import ConfirmationState from '../Common/ui/ConfirmationState'
import { useTemporalPrescriptionMutation } from '../../Services/createTemporalPrescription'
import { useSessionPrescription } from '../Hooks/useSessionPrescription'
import { useFeaturesFlags } from '../../../../../hooks/useGetFeaturesFlags'
import { Session } from '@opentok/client'
import { usePostHog } from 'posthog-js/react'
import { store } from '@/store/configStore'
import { InvalidData, InvalidMedication } from './UI/ErrorModals'
import { useSendRecipeMutation } from '../../Services/sendRecipe'
import usePrimaryCoverage from '@/services/reactQuery/usePrimaryCoverage'
import '../styles/Recipe.scss'
import {
  trackEventCreatePrescription,
  trackEventDoctorConfirmationPrescription,
} from '@/modules/consultorio/infrastructure/analytics/posthog'
import { ErrorModal } from '@/storybook/components/modals/ErrorModal/ErrorModal'

interface ISetCurrentView {
  goTo: React.Dispatch<React.SetStateAction<TCurrentView>>
}
interface uniqueKey {
  uniqueKey: string
}
export type prescriptionItems = ((medicineByBrand | medicineByDrug) &
  uniqueKey)[]

type ExtendedSession = Session & { currentState: 'connected' | 'disconnected' }

const Recipe = ({ goTo }: ISetCurrentView) => {
  const { profile } = useAppSelector((state) => state.user)
  const { patient } = useAppSelector((state) => state.queries)
  const { prescriptionFailed, invalidMedication, invalidData } = useAppSelector(
    (state) => state.prescriptions
  )
  const { dispatch } = store
  const providerId = profile?.uid
  const asyncAtt = useContext(AttContext)
  const asyncMr = useContext(MrContext)
  const searchParams = useSearchParams()
  const patientUid = searchParams.get('patientUid')
  const dependantParam = searchParams.get('dependant')
  const dependantUid =
    dependantParam === 'false' ? false : dependantParam || false
  const assignationId = searchParams.get('assignationId')
  const [loading, setLoading] = useState(false)
  const attInViewAsync = asyncAtt?.attInView
  const assignationIdAsync = attInViewAsync?.assignation_id
  const [prescriptionItems, setPrescriptionItems] =
    React.useState<prescriptionItems>([])
  const sendRecipeMutation = useSendRecipeMutation()

  const temporalPrescriptionMutation = useTemporalPrescriptionMutation()
  const primaryCoverage = usePrimaryCoverage(patientUid as string, dependantUid)
  const {
    register,
    watch,
    handleSubmit,
    formState: { errors },
    setValue,
    control,
    reset,
  } = useForm<IRecipeForm>({
    defaultValues: {
      medicine: {
        quantity: '1',
        monodrugSearch: true,
      },
      coverage: {
        name: '',
        plan: '',
        afiliateId: '',
        credentialVersion: '',
      },
    },
  })
  const posthog = usePostHog()
  const session = useAppSelector(
    (state) => state.call.opentokSession
  ) as ExtendedSession | null
  const {
    setPrescriptionState,
    setPrescriptionData,
    prescriptionState,
    prescriptionData,
  } = useSessionPrescription(session, providerId)
  const [editForm, setEditForm] = useState(false)
  const featuresFlags = useFeaturesFlags()
  const isPatientConnected = useAppSelector(
    (state) => state.call.isPatientConnected
  )
  /** Controla si para generar una receta, le pide confirmacion del paciente */
  const shouldConfirmPrescription =
    (featuresFlags.data?.temporal_prescription as boolean) &&
    isPatientConnected &&
    session?.currentState === 'connected'

  const onSubmit: SubmitHandler<IRecipeForm> = useCallback(
    async (prescriptionFormData) => {
      dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: false })
      const dataFormated = medicineFormater(
        prescriptionFormData,
        prescriptionItems,
        assignationId,
        primaryCoverage.data?.[0]
      )

      const dataPostRecipe: IPrescriptionPostFunctionParameters = {
        formData: dataFormated,
        assignationId: attInViewAsync
          ? (assignationIdAsync as string)
          : (assignationId as string),
        uid: attInViewAsync
          ? attInViewAsync?.patient.uid
          : (patientUid as string),
        asyncAttData: asyncAtt?.attInView,
        goTo,
        dependantUid,
      }

      if (shouldConfirmPrescription) {
        try {
          await temporalPrescriptionMutation.mutateAsync({
            dataPostRecipe,
            setPrescriptionState,
            providerUid: profile.uid,
            patient,
            session,
            posthog,
          });
          trackEventCreatePrescription({
            assignationId: assignationId as string,
            provider: providerId as string,
            patient: patientUid as string,
            consultingRoomType: 'old',
          });
          setPrescriptionData(dataPostRecipe);
        } catch (error) {
          console.error('Error en temporalPrescriptionMutation:', error);
          dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: true });
          throw error;
        }
      } else {
        try {
          sendRecipeMutation.mutate({
            dataPostRecipe,
            setLoading,
            asyncMr,
            providerId,
            patient,
          }, {
            onError: (error) => {
              console.error('Error en sendRecipeMutation:', error);
              dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: true });
              throw error;
            }
          });
          trackEventCreatePrescription({
            assignationId: dataPostRecipe.assignationId,
            provider: providerId as string,
            patient: dataPostRecipe.uid,
            consultingRoomType: 'old',
          });
        } catch (error) {
          console.error('Error en try/catch de sendRecipeMutation:', error);
          dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: true });
          throw error;
        }
      }
    },
    [featuresFlags, shouldConfirmPrescription]
  )

  const disabled = prescriptionItems?.length > 0 && !!watch('diagnosis')

  return (
    <>
      <div className="prescriptions__container relative h-full">
        {prescriptionState && !prescriptionFailed ? (
          <ConfirmationState
            prescriptionState={prescriptionState}
            buttonLoading={loading}
            buttonOnClick={{
              confirmed: () => {
                if (prescriptionData) {
                  trackEventDoctorConfirmationPrescription({
                    assignationId: prescriptionData.assignationId,
                    provider: providerId as string,
                    patient: prescriptionData.uid,
                    consultingRoomType: 'old',
                  })

                  sendRecipeMutation.mutate({
                    dataPostRecipe: prescriptionData,
                    setLoading,
                    asyncMr,
                    providerId,
                    patient,
                  })
                }
              },
              rejected: () => {
                setEditForm(true)
                setPrescriptionState(null)
              },
            }}
          />
        ) : (
          <>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="recipe"
              autoComplete="off"
            >
              
              <RecipePatientOS
                reset={reset}
                register={register}
                watch={watch}
                errors={errors}
                setValue={setValue}
                control={control}
                editForm={editForm}
              />
              <PrescriptionItems
                reset={reset}
                register={register}
                setDrugItems={setPrescriptionItems}
                watch={watch}
                errors={errors}
                setValue={setValue}
                control={control}
                drugItems={prescriptionItems}
                editForm={editForm}
              />
              <Spacer />
              <Diagnosis
                register={register}
                watch={watch}
                errors={errors}
                setValue={setValue}
              />
              <Spacer />
              <Button
                disabled={!disabled}
                size="full"
                occ_type="filled"
                type="submit"
                loading={
                  sendRecipeMutation.isLoading ||
                  temporalPrescriptionMutation.isLoading ||
                  loading
                }
              >
                Generar receta
              </Button>
            </form>
            {invalidMedication && <InvalidMedication />}
            {invalidData && <InvalidData />}
            {(sendRecipeMutation.isError ||
              temporalPrescriptionMutation.isError) && (
              <ErrorModal
                error={
                  (sendRecipeMutation.error as Error) ??
                  temporalPrescriptionMutation.error
                }
                title="Falló la generación de receta"
                subtitle="Nuestro equipo ya esta trabajando en solucionarlo"
              >
                <button
                  className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 rounded mb-4"
                  onClick={() => handleSubmit(onSubmit)}
                >
                  Reintentar
                </button>
              </ErrorModal>
            )}
          </>
        )}
      </div>
    </>
  )
}

export default Recipe
