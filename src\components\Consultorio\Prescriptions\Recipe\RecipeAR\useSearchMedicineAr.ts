import { countries } from '@umahealth/entities'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import axios from 'axios'
import { useContext } from 'react'
import { AttContext } from '@/components/AsyncAttention'
import { vademecum } from '@/config/endpoints'
import { useMutation } from 'react-query'
import { medicine } from '../../Interfaces/Interfaces'
import { coverageNameFormater } from '../Common/Utils/recipeHelpers'
import { psicoMedicinesList } from '../../utils/psicomedicines'

interface recipe {
	input: {
		text: string,
		country: countries
	}
	output: medicine[]
}

interface ISearchMedicineAr extends recipe{
	names: string[]
}

type ISearchMedicineArFinal = Omit<ISearchMedicineAr, 'input'>

function useSearchMedicineAr() {
	const asyncAtt = useContext(AttContext)
	return useMutation(['search medicine AR'],
		async({ search, osName = '' }: { search: string, osName: string })=> {

			const firebaseIdToken = await getFirebaseIdToken()

			const headers = {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${firebaseIdToken}`,
			}

			const body: { country: typeof process.env.NEXT_PUBLIC_COUNTRY, text: string, o_s?: string} = {
				country: 'AR',
				text: search
			}

			if(/OSPECON/i.test(osName)){
				body.o_s = coverageNameFormater(osName)
			}

			if(/IOMA/i.test(osName)){
				body.o_s = coverageNameFormater(osName)
			}

			const response = await axios.post<recipe>(
				vademecum,
				body,
				{ headers }
			)

			const uniqueDrugsNames = new Set<string>()

			response.data.output.forEach( (drug) => {
				uniqueDrugsNames.add(drug.drugName)
			})

			/**
			 * Sort para cobertura de medicamentos
			 * @description Esta variable ordena la salida de medicamentos para IOMA, colocando primero los que tienen cobertura.
			 **/

			const outputIOMA = osName?.includes("IOMA") && response.data.output ? response.data.output.sort((a, b) => !!a.corporate_coverage > !!b.corporate_coverage ? -1 : 1) : response.data.output

			const result : ISearchMedicineArFinal = {
				output: osName.includes("IOMA") ? outputIOMA : response.data.output,
				names: Array.from(uniqueDrugsNames),
			}


			if(!asyncAtt?.attInView) return result

			const filteredOutput = result.output.filter(medicine => !psicoMedicinesList.some(psicoMedicine => psicoMedicine.toLowerCase() === medicine.drugName?.toLowerCase()))

			const filteredResult = {
				output: filteredOutput,
				names: Array.from(uniqueDrugsNames).filter(drugName => filteredOutput.some(outputItem => outputItem.drugName?.toLowerCase() === drugName?.toLowerCase()))
			}
			return filteredResult

		})
}

export default useSearchMedicineAr
