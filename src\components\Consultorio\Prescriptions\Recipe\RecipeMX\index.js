/* eslint-disable indent */
import React, { useEffect, useState } from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import { Paragraph } from '@umahealth/occipital-ui'
import NewRecipe from './ui/NewRecipe'
import { HANDLE_SEARCH_RESULT, HANDLE_DRUG_OPTIONS } from '../../store/prescriptionsTypes'
import { setRecipeLoading } from '../../store/prescriptionsActions'

const RecipeMX = ({goTo}) => {
	const dispatch = useAppDispatch()
	const { profile } = useAppSelector((state) => state.user)
	const [medikitDisclaimer, setMedikitDisclaimer] = useState('')

	useEffect(() => {
		if(!profile?.medikit_token){
			dispatch({type: 'SET_MODAL_IN_ATTENTION', payload: { state: false, content: '' }})
			setMedikitDisclaimer('No tiene permitido generar recetas digitales. Asegúrese de haber generado el token en su perfil.')
		} else {
			setRecipeLoading(true)
			setRecipeLoading(false)
		}
	}, [dispatch, profile])
    
	useEffect(() => {
		dispatch({ type: HANDLE_SEARCH_RESULT, payload: [] })
		dispatch({ type: HANDLE_DRUG_OPTIONS, payload: [] })
	}, [dispatch])

	return <>
		{medikitDisclaimer ?
			<div className='medikitDisclaimer__container'>
				<Paragraph 
					weight="semibold"
					size="xs"
					text={medikitDisclaimer} />
			</div>
			:
			<NewRecipe goTo={goTo}/>
		}
	</>
}

export default RecipeMX