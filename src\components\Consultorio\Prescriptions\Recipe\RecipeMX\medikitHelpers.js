import axios from 'axios'
import swal from 'sweetalert'
import moment from 'moment'
import { medikit_path, vademecum, save_recipe_db } from '@/config/endpoints'
import { HANDLE_DRUG_OPTIONS, HANDLE_SEARCH_RESULT, SET_MEDIKIT_SECURITY_HASH } from '../../store/prescriptionsTypes'
import { store } from '@/store/configStore'
import { auth } from '@/config/firebase'
const { dispatch, getState } = store

export async function searchMedikitMedicines(cadena = '') {
	const state = getState()
	const { currentUser } = state.user
	let token
	if(currentUser){
		token = await auth?.currentUser?.getIdToken()
	}
	
	const headers = { 
		'Content-Type': 'application/json',
		'Authorization': `Bearer ${token}`,
	}
	const body = { text: cadena, country: process.env.NEXT_PUBLIC_COUNTRY}

	try {
		const res = await axios.post(vademecum, body, {headers: headers})
		const drugsOptions = res.data.output.map((drug) => ({
			value: `${drug?.id}`,
			label: `${drug?.name} - ${drug?.sustActiva}`,
		}))
		dispatch({ type: HANDLE_SEARCH_RESULT, payload: res.data.output })
		dispatch({ type: HANDLE_DRUG_OPTIONS, payload: drugsOptions })
		return drugsOptions
	} catch (error) {
		return console.error(error)
	}
}

// OLD API

export const addMedikitDrug = async (currentMedicine) => {
	try {
		const state = getState()
		const { profile } = state.user
		const formated = {	
			'attToken' : localStorage.getItem('medikit_att_hash'),
			'catalogoNum' : 'Z759',
			'quantity' : currentMedicine?.cantidad || 1,
			'ean' : currentMedicine?.productEan,
			'uid' : profile.uid
		}
		await saveMedicine(formated)
	} catch (error) {
		swal({ title: 'Error', text: `${error}`, icon: 'warning', dangerMode: true })
	}
}

export const setMedikitConsultaToken = async () => {
	try {
		const state = getState()
		const {currentUser, profile} = state.user
		const data = {
			uid: profile?.uid
		}
		let token
		if(currentUser){
			token = await auth?.currentUser?.getIdToken()
		}
		
		const headers = { 
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${token}`,
			'x-api-key': process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY
		}

		if(!localStorage.getItem('medikit_att_hash')) {
			const res = await axios.post(`${medikit_path}/attRegister`, data, {headers})
			if(res.data.Status === 'Error') throw new Error('failed medikit att token')
			localStorage.setItem('medikit_att_hash', res.data.Data)
			return res.data.Data
		}
		return localStorage.getItem('medikit_att_hash')
	} catch(e) { 
		await swal({
			title: 'Ha ocurrido un error iniciando la receta',
			description: 'Recuerda que puedes enviarla por email al paciente.',
			icon: 'error',
		})
		throw new Error('failed medikit att token: ', e)
	}	
}

export const handleSecurityHashMedikit = async () => {
	try {
		const data = {
			'attToken': localStorage.getItem('medikit_att_hash')
		}
		const state = getState()
		const {currentUser} = state.user
		let token
		if(currentUser){
			token = await auth?.currentUser?.getIdToken()
		}
		
		const headers = { 
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${token}`,
			'x-api-key': process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY
		}

		const res = await axios.post(`${medikit_path}/securityHashRegister`, data, {headers})
		localStorage.setItem('medikit_security_hash', res.data.Data)
		dispatch({ type: SET_MEDIKIT_SECURITY_HASH, payload: res.data.Data })
		return res.data.Data
	} catch (err) {
		console.error(err)
	}
}

export const saveMedicine = async (med) => {
	const state = getState()
	const {currentUser} = state.user
	let token
	if(currentUser){
		token = await auth?.currentUser?.getIdToken()
	}
	
	const headers = { 
		'Content-Type': 'application/json',
		'Authorization': `Bearer ${token}`,
		'x-api-key': process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY
	}
	return await axios.post(`${medikit_path}/saveMedicine`, med, {headers})
		.then((res) => {
			const result = res.data
			return result.Data
		})
		.catch(e => console.error(e))
}

// NEW API

export const saveMedikitRecipeDB = async (medikitData) => {
	try {
		const state = getState()
		const { profile } = state.user
		const { currentAtt, patient } = state.queries
		const { medicines } = state.prescriptions.temp
		const { signature_medikit } = state.prescriptions
		const now = moment().format('YYYYMMDDHHMMss')
		const body = {
			assignation_id: `${currentAtt?.assignation_id}`,
			consumed: false,
			country: process.env.NEXT_PUBLIC_COUNTRY,
			date: now,
			hc: `${patient?.dni}/${currentAtt?.assignation_id}`,
			medicines,
			medical_record_id: `${currentAtt?.assignation_id  || ''}`,
			integrations: {
				medikitId: medikitData.Identifier,
				medikitSecurityHash: medikitData.SecurityHash,
				medikitMedicationItems: medikitData.Medication,
				medikitNumber: medikitData.Number
			},
			patient: {
				corporate: patient?.corporate_norm || '',
				dni: patient?.dni || '',
				fullname: patient?.fullname || '',
				uid: patient?.uid || patient.core_id || ''
			},
			prescription_number: medikitData.Number,
			provider: {
				dni: profile?.dni || '',
				fullname: profile?.fullname || '',
				matricula: profile?.matricula || '',
				signature: signature_medikit || profile?.signature || '',
				uid: profile?.core_id || '',
			},
			response: {
				approved: 'OK'
			},
			uid: patient.uid || patient.core_id,
			validator: 'MEDIKIT',
		}
		await axios.post(save_recipe_db, body)
	} catch(err) {
		console.error(err)
	}
}

export const signMedikitPrescription = async (goTo) => {
	try {
		if(typeof window !=='undefined') {
			const state = getState()
			const { profile } = state.user
			const requestData = {
				'nombreDoctor': profile.fullname,
				'idDoctor': profile.medikit_token,
				'cedula': profile.matricula,
				'idConsulta': localStorage.getItem('medikit_att_hash'),
				'nombrePaciente': '',
				'cliente': 'consultorio'
			}
			const successData = await window.mkSignatureInit(requestData)
			let url = successData.signedUrl ? successData.signedUrl : successData.signature64
			dispatch({type: 'SET_MEDIKIT_SIGNATURE', payload: url })
			localStorage.setItem('medikit_signature_hash', successData.hash)
			dispatch({type: 'SET_MEDIKIT_SIGNATURE_HASH', payload: successData.hash})
			let medikitResponse = await savePrescriptionMedikit()
			await saveMedikitRecipeDB(medikitResponse)
			goTo()
		}
	} catch(e) {
		console.error(e.error)
	}
}

export const savePrescriptionMedikit = async (goTo) => {
	const state = getState()
	const { profile, currentUser } = state.user
	const { currentAtt, patient } = state.queries
	const { medicines } = state.prescriptions.temp
	const start_date = moment().format('YYYYMMDDHHmmss')
	const end_date = moment().add(15, 'minute').format('YYYYMMDDHHmmss')
	const savePrescriptionBody = {
		assignation_id: currentAtt?.assignation_id,
		doctorUid: profile?.uid || profile?.core_id,
		endDate: end_date,
		medicines,
		patientUid: patient?.core_id,
		startDate: start_date,
		diagnosis: 'Z759' // DEFAULT MEDIKIT DIAGNOSIS, CHANGE FOR DIAGNOSIS IN ATT
	}
	let token
	if(currentUser){
		token = await auth?.currentUser?.getIdToken()
	}
	const headers = { 
		'Content-Type': 'application/json',
		'Authorization': `Bearer ${token}`,
		'x-api-key': process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY
	}
	try {
		const response = await axios.post(`${medikit_path}/partners/prescriptionRegister`, savePrescriptionBody, {headers})
		const medikitResponseData = response.data
		if(!process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID.includes('staging')) {
			if(medikitResponseData.Status === 'Error') throw new Error(medikitResponseData.Data)
		}
		await saveMedikitRecipeDB(medikitResponseData)
		goTo()
	} catch (err){
		console.error(err)
	}
}