import React, { useReducer, useState, useCallback} from 'react'
import { useSelector } from 'react-redux'
import AsyncSelect from 'react-select/async'
import { Button, Input, Paragraph, Row, Spacer, Title, Modal } from '@umahealth/occipital-ui'
import { GrAddCircle } from 'react-icons/gr'
import { TextArea } from '@/components/GeneralComponents/Input/Inputs.js'
import { addDrug } from '../../../Common/Utils/recipeHelpers'
import * as ordersActions from '@/components/Consultorio/Prescriptions/store/prescriptionsActions'

export default function AddDrug({ maxMedicines = 2, onSearchMedicine }) {
	const initialState = {
		addNew: true,
		details: '',
		quantity: 1,
		productId: null,
		productName: '',
	}
	const initialStateModal = {
		action: '',
		buttonLabel: '',
		text: '',
		title: '', 
		show: false,
	}
	const [currentMedicine, setCurrentMedicine] = useReducer(reducer, initialState)
	const [modal, setModal] = useState(initialStateModal)
	const { medicines } = useSelector(state => state.prescriptions.temp)
	const customStyles = {
		container: provided => ({ ...provided, width: '100%', height: '54px', background: '#edf2f7' }),
		control: provided => ({ 
			...provided,  
			height: '100%', background: '#edf2f7',
			'&:hover': {
				borderBottom: '2px solid #0a6dd6'
			}
		}),
		menuList: provided => ({ ...provided, color: '#000' }),
	}

	function reducer(currentMedicine, action) {
		switch (action.type) {
		case 'set_drug':
			return {...currentMedicine, ...action.payload}
		case 'set_quantity':
			return {...currentMedicine, quantity: Number(action.payload)}
		case 'set_description':
			return {...currentMedicine, details: action.payload}
		case 'add_new':
			return {...currentMedicine, addNew: !currentMedicine.addNew}
		case 'reset':
			return initialState
		default:
			throw new Error()
		}
	}

	const handleInputQuantity = useCallback((type, value) => {
		if(type === 'set_quantity') {
			if(currentMedicine.tipo_venta === 3 && value > 1) {
				setCurrentMedicine({ type: 'set_quantity', payload: initialState.quantity })
				setTimeout(() => setModal({
					action: () => {setModal({show : false})},
					buttonLabel: 'Aceptar',
					text: 'Recuerde que el máximo de unidades en psicofármacos es uno.',
					title: 'Atención', 
					show: true
				}), 500)
			} else if(currentMedicine.tipo_venta !== 3 && value > 2) {
				setCurrentMedicine({ type: 'set_quantity', payload: initialState.quantity })
				setTimeout(() => setModal({
					action: () => {setModal({show : false})},
					buttonLabel: 'Aceptar',
					text: 'Recuerde que el máximo de unidades de cada medicamento, por receta, es dos.',
					title: 'Atención', 
					show: true
				}), 500)
			} else if(currentMedicine.tipo_venta !== 3 && value < 0) {
				const q = value < 0 ? value * - 1 : value
				return setCurrentMedicine({ type: 'set_quantity', payload: q })
			} else {
				return setCurrentMedicine({ type: 'set_quantity', payload: value })
			}
		}
	}, [currentMedicine.tipo_venta])

	if(currentMedicine.addNew && !!medicines?.length) {
		return <div className="recipe__addDrugs">
			{medicines?.length < maxMedicines ?
				<Button
					action={() => setCurrentMedicine({type: 'add_new'})}
					type="text"
					size='full'>
					<Row>
						<GrAddCircle />
						<Spacer direction='horizontal' value="8px" />
						<Paragraph>Agregar</Paragraph>
					</Row>
				</Button> 
				:	
				<Row alignment='center' spacing='center'>
					<Paragraph text="Sólo se permiten dos medicamentos por receta. En caso de recetar más medicamentos, genere una nueva con los restantes." color='error'/>
				</Row>
			}
		</div>
	} else {
		return (
			<>
				<div className='recipe__AddDrug'>
					<div className='recipe__firstRow'>
						<AsyncSelect
							autoFocus
							isClearable
							openMenuOnClick={false}
							maxMenuHeight={250}
							loadOptions={onSearchMedicine}
							id='recipeInput'
							placeholder='Buscar el medicamento'
							onChange={async (e) => setCurrentMedicine({type: 'set_drug', payload: await ordersActions.handleInputMedicines(e)})}
							styles={customStyles}
							value={{value: currentMedicine.alfabetRegisterNum, label: currentMedicine.productName }}
						/>
						<Input
								type='number'
								label='Cantidad'
								className='recipe__container--input selectAmount'
								defaultValue={'1'}
								size='small'
								action={(e) => handleInputQuantity('set_quantity', e.target.value)}
							/>
					</div>
					<Spacer />
					<TextArea
						placeholder='Dosis, frecuencia y duración del tratamiento'
						className='recipe__container--input'
						id='indicaciones'
						name='indicaciones'
						rows={3}
						value={currentMedicine.details || ''}
						onChange={(e) => setCurrentMedicine({type: 'set_description', payload: e.target.value})}
						required
					/>
					<Row>
						<Button size="medium" action={() => { 
							addDrug(currentMedicine)
							setCurrentMedicine({type: 'reset'})
						}}>Añadir</Button>
						<Spacer value='8px' />
						{medicines?.length > 0 && <Button size="medium" action={() => setCurrentMedicine({type: 'add_new'})} label="Cancelar" />}
					</Row>
				</div>
				{
					modal.show === true &&
						<Modal onClose={() => {setModal({show : false})}}>
							<Title text={modal.title} color='default' size='sm' weight='normal' />
							<Spacer direction='vertical' value='32px'/> 
							<Paragraph text={modal.text}/>
							<Spacer direction='vertical' value='32px'/>
							<Button 
								label={modal.buttonLabel} 
								action={() => {
									setModal({show : false})
								}} 
								size='full'/>	
						</Modal>
				}
			</>
		)
	}
}