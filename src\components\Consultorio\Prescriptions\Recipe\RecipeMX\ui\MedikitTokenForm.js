import React, { useState } from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'

import axios from 'axios'
import Select from 'react-select'
import swal from 'sweetalert'
import { Loader } from '@umahealth/occipital-ui'
import { errorHandler } from '@/config/stackdriver'
import { medikit_path } from '@/config/endpoints'
import { medikitStates, medikitSpecialties } from '@/config/medikitFields'
import * as configActions from '@/components/User/UI/Profile/Store/configActions'
import '../../styles/Medikit.scss'
import { auth } from '@/config/firebase'

const MedikitTokenForm = () => {

	const doctor = useAppSelector(state => state.user.profile)
	const dispatch = useAppDispatch()
	const {currentUser} = useAppSelector(state => state.user)
	const [institucion, setInstitucion] = useState(doctor?.university || '')
	const [especialidad, setEspecialidad] = useState({label: '', value: ''})
	const [estado, setEstado] = useState({label: '', value: ''})
	const [city, setCity] = useState('')
	const [loading, setLoading] = useState(false)
	const [address, setAddress] = useState('')
	const [cp, setCp] = useState('')
	const [phone, setPhone] = useState('')
	const [formValidation, setFormValidation] = useState({valid: true, msg: ''})

	//CREDENCIAL
	
	const handleSubmitForm = async (e) => { 
		setLoading(true)
		e.preventDefault()
		if(
			!institucion ||
			!especialidad.value ||
			!estado.value ||
			!city ||
			!address ||
			!phone ||
			!cp
		){
			setFormValidation({valid: false, msg: 'Debe completar todos los campos antes de enviar el formulario'})
			setLoading(false)
			return false
		}

		try {
			const userData = {
				'address' : address,
				'city' : city,
				'cp' : cp,
				'phone' : phone,
				'specialty' : especialidad.value,
				'state' : estado.value,
				'uid': doctor?.uid,
				'university':institucion,
			}
			let token
			if(currentUser){
				token = await auth?.currentUser?.getIdToken()
			}
			
			const headers = { 
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${token}`,
				'x-api-key': process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY
			}
	
			const data = {
				...userData
			}
			let request = await axios.post(`${medikit_path}/partners/doctorRegister`, data, {headers})
			let responseToken = request?.data?.medikitToken
			configActions.patchProvidersData({
				address: `${address}, ${cp}, ${city.label}, ${estado.label}`,
				medikit_token: responseToken
			}, currentUser.uid)
			dispatch({type: 'SET_MEDIKIT_TOKEN', payload: responseToken})
			dispatch({type: 'SET_MODAL', payload: false})
			swal('¡Token generado!', 'Puede comenzar a operar con Medikit', 'success')
			setLoading(false)
		} catch(err) { 
			dispatch({type: 'SET_MODAL', payload: false})
			swal('Algo salió mal!', 'Por favor vuelva a intentarlo', 'warning')
			setLoading(false)
			errorHandler.report(err)
		}
	}

	if(loading) return <Loader />
	return (
		<form className="medikitData__form" onSubmit={(e) => handleSubmitForm(e)}>
			<div className="medikitData__dataContainer">
				<div className="medikitData__row">
					<div className="medikitData__form--info">
						<label>Institución educativa</label>
						<input type="text" 
							placeholder="Ingrese su institución educativa"
							name="institution name" 
							value={institucion}
							onChange={(e) => setInstitucion(e.target.value)}/>
					</div>
					<div className="medikitData__form--info">
						<label>Especialidad</label>
						<Select 
							placeholder={'Seleccione una'}
							value={especialidad}
							onChange={(data) => setEspecialidad(data)}
							options={medikitSpecialties}/>
					</div>
				</div>
				<div className="medikitData__row">
					<div className="medikitData__form--info">
						<label>Estado</label>
						<Select 
							placeholder={'Seleccione una'}
							value={estado}
							onChange={(data) =>{
								setEstado(data)}}
							options={medikitStates}/>
					</div>
					<div className="medikitData__form--info">
						<label>Ciudad</label>
						<input type="text" 
							placeholder="Ingrese su ciudad"
							name="city name" 
							value={city}
							onChange={(e) => setCity(e.target.value)}/>
					</div>
				</div>
				<div className="medikitData__row">
					<div className="medikitData__form--info">
						<label>Dirección</label>
						<input type="text" 
							placeholder="Ingrese su calle"
							name="street name" 
							value={address}
							onChange={(e) => setAddress(e.target.value)}/>
					</div>
					<div className="medikitData__form--info">
						<label>Código Postal</label>
						<input type="text" 
							placeholder="Ingrese su código postal"
							name="postal code name" 
							value={cp}
							onChange={(e) => setCp(e.target.value)}/>
					</div>	
				</div>
				<div className="medikitData__row">
					<div className="medikitData__form--info">
						<label>Teléfono del consultorio</label>
						<input type="text" 
							placeholder="Ingrese su teléfono"
							name="street name" 
							value={phone}
							onChange={(e) => setPhone(e.target.value)}/>
					</div>
				</div>
			</div>
			{!formValidation.valid &&
				<p className="medikitData__invalidMsg">{formValidation.msg}</p>}
			<button className="medikitData__submit-btn" type="submit">Generar Token</button>
		</form>
	)
}

export default MedikitTokenForm