import React, {useState} from 'react'
import { useAppSelector } from '@/store/hooks'
import { Button, Loader, Row } from '@umahealth/occipital-ui'
import { searchMedikitMedicines, savePrescriptionMedikit } from '../medikitHelpers'
import '../../styles/Recipe.scss'
import AddDrug from './Drug/AddDrugMx'
import SelectedDrugList from './Drug/SelectedDrugListMx'

const NewRecipe = ({goTo}) => {
	const {
		signature_medikit,
		temp
	} = useAppSelector((state) => state.prescriptions)
	const [loading, setLoading] = useState(false)

	const postPrescription = async () => {
		setLoading(true)
		await savePrescriptionMedikit(goTo)
		setLoading(false)
	}

	return (
		<>
			{temp?.medicines?.length >= 1 && <SelectedDrugList />}
			<AddDrug
				maxMedicines={process.env.NEXT_PUBLIC_COUNTRY === 'AR' ? 2 : 5}
				onSearchMedicine={searchMedikitMedicines}
			/>
			<div>
				<Row spacing='center'>
					{loading ?
					<Loader /> :
					<>
						{temp?.medicines?.length >= 1 && !signature_medikit && <Button 
							action={() => postPrescription()}
							color="secondary"
							size="large">
								Guardar receta
						</Button>}
					</>}
				</Row>
			</div>
		</>
	)
}

export default NewRecipe