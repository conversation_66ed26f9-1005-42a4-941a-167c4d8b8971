import React, { useRef, useState, useEffect, useCallback } from 'react'
import { collection, query, where, getDocs } from '@/config/firebase'
import { useAppSelector } from '@/store/hooks'
import JsBarcode from 'jsbarcode'
import { firestore } from '@/config/firebase'
import logoUMA from '@/assets/logo.png'
import Image from 'next/image'
import moment from 'moment'
import '../../styles/RecipePDF.scss'

class RecipePDF extends React.Component {
	componentDidMount() {
		const {
			securityHash,
			recipe
		} = this.props
		for (let i = 0; i < recipe?.length; i++) {
			const cvRecipe = `#barcodeNUR_${i}`
			if (securityHash) {
				cvRecipe &&
					JsBarcode(cvRecipe, recipe?.[i]?.productEan, {
						format: 'CODE128',
						width: 1,
						height: 40,
						fontSize: 16,
						flat: true
					})
			}
		}
	}

	render() {
		const {
			doctorInfo, 
			prescriptionDate,
			patient,
			recipe,
			signature,
			securityHash,
		} = this.props

		return (
			<div className='medikitPrint'>
				<div className='medikitPrint__container header'>
					<div className='medikitPrint__info--logo_container'>
						<Image src={logoUMA} fill={true} className='medikitPrint__container--logo' alt='Logo uma' />
					</div>
					<div className='medikitPrint__info'>
						<div className='medikitPrint__info--block'>
							<h2 className='medikitPrint__info--title'>
										DATOS DEL MEDICO
							</h2>
							<ul className='medikitPrint__info--list'>
								<li>
									<span>{doctorInfo?.fullname}</span>	
									<span>Ced.Prof.{doctorInfo?.matricula}</span>	
								</li>
								<li>
									<span>{doctorInfo?.matricula_especialidad}</span>
								</li>
							</ul>
						</div>
						<div className='medikitPrint__info--block'>
							<h2 className='medikitPrint__info--title'>
										DATOS DE LA RECETA
							</h2>
							<ul className='medikitPrint__info--list'>
								<li>
									<span>Numero de Receta: {securityHash?.slice(0,13)}</span>
								</li>
								<li>
									<span>Fecha de la receta: {prescriptionDate}</span>
								</li>
							</ul>
						</div>
					</div>
				</div>
				<div className='medikitPrint__container'>
					<div className='medikitPrint__info--block'>
						<h2 className='medikitPrint__info--title'>
									DATOS DEL PACIENTE
						</h2>
						<ul className='medikitPrint__info--list'>
							<li>
								<span>Nombre: {patient?.fullname}</span>
							</li>
						</ul>
					</div>
				</div>
				<div className='medikitPrint__container'>
					<table className='medikitPrint__container--table'>
						<thead>
							<tr>
								<th>Cantidad</th>
								<th>Medicamento / Sustancia Activ</th>
								<th>Indicaciones</th>
								<th>NUR</th>
							</tr>
						</thead>
						<tbody>
							{recipe?.map((medicine, index) => (
								<tr key={index}>
									<td>{medicine?.quantity}</td>
									<td>{medicine?.productName}</td>
									<td>{medicine?.details}</td>
									<td className="barcode">
										<canvas id={`barcodeNUR_${index}`} />
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
				<div className='medikitPrint__bottomContainer'>
					<div className='medikitPrint__container'>
						<div className="signature">
							{signature && <Image fill={true} src={signature} alt="signature" />}
							<span className="underline">
								Firma del médico
							</span>
						</div>

					</div>
					<div className='medikitPrint__container digitalStamp'>
						<label>Sello Digital:</label>
						<p>{securityHash}</p>
					</div>
					<div className='medikitPrint__container footArticle'>
						<div>
							<p>Puedes surtir tus medicamentos con precios especiales en: Farmacias Benavides, Farmatodo, HEB, Farmacentro,
								Farmacia de Dios, San Francisco de Asis, Santa Cruz, Zapotlán, Unión, GM, Walmart Express / Superama (participantes Zona Metropolitana) y DEMSA.
								Si tienes alguna duda comunícate con nosotros al 8120858085 o bien, mándanos un <NAME_EMAIL>.</p>
							<p>
								Si tienes alguna duda comunicate con nosotros al 8120858085 o bien, mándanos un <NAME_EMAIL>.
							</p>
						</div>
					</div>
				</div>
			</div>
		)
	}
}



const RecipeMx = () => {
	const compRef = useRef()
	const [recipe, setRecipe] = useState({})
	const { profile } = useAppSelector((state) => state.user)
	const { hash_medikit } = useAppSelector(state => state.prescriptions)
	
	const setPrescription = useCallback(async () => {
		try {
			const prescription_number = hash_medikit || localStorage.getItem('medikit_signature_hash')
			const prescriptionRef = collection(firestore, `events/prescriptions/${process.env.NEXT_PUBLIC_COUNTRY}`)
			const q = query(prescriptionRef, where('prescription_number', '==', prescription_number))
			const prescriptionQuery = await getDocs(q)
			let data = prescriptionQuery?.docs?.[0]?.data()
			setRecipe(data)
		} catch (err) {
			console.error(err)
		}
	}, [hash_medikit])
	
	useEffect(() => {
		setPrescription()
	}, [setPrescription])

	const dataToPrint = {
		patient: recipe?.patient,
		recipe: recipe?.items,
		doctorInfo: recipe?.provider || profile,
		ref: compRef,
		prescriptionDate: moment(recipe?.date, 'YYYYMMDDHHMMss').format('DD.MM.YYYY') || '',
		signature: recipe?.provider?.signature,
		securityHash: recipe?.prescription_number,
	}

	return(<>{recipe?.items?.length > 0 && (
		<div className='d-none'> <RecipePDF {...dataToPrint} /></div>
	)}
	</>
	)
}

export default RecipeMx 