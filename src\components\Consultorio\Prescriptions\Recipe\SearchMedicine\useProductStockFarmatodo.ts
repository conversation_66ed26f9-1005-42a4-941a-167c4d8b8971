import { useQuery, UseQueryResult } from 'react-query'
import axios from 'axios'
import { farmatodoStock } from '@/config/endpoints'

interface FarmatodoProduct {
    avg_stock: number;
    Categoría: string;
    categorie: string;
    CATEGORY: string;
    departments: string[];
    fullPrice: number;
    id_highlights: string[];
    id_suggested: string[];
    idCity: string;
    marca: string;
    objectID: string;
    stock: number;
    SubCategoría: string;
    subCategory: string;
    SUBCATEGORY: string;
    totalmax_stock: number;
    unitPrice: number;
}

function useProductStockFarmatodo(objectId: string): UseQueryResult<boolean> {
    return useQuery(
        ['productStock', objectId],
        async () => {
            const stockHeaders = {
                country: 'VEN',
                client: process.env.NEXT_PUBLIC_FARMATODO_CLIENT_ID,
                'client-secret':
                    process.env.NEXT_PUBLIC_FARMATODO_CLIENT_SECRET,
            }

            const stockResponse = await axios.get<FarmatodoProduct>(`${farmatodoStock}/${objectId}`, {
                headers: stockHeaders,
            })

            return stockResponse.data.stock
        },
        { enabled: !!objectId }
    )
}

export default useProductStockFarmatodo
