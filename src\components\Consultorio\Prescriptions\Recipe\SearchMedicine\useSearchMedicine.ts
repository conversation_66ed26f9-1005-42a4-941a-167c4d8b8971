import { countries } from '@umahealth/entities'
import useSearchMedicineAr from './useSearchMedicineAr'
import useSearchMedicineMx from './useSearchMedicineMx'
import useSearchMedicineFarmatodo from './useSearchMedicineFarmatodo'
import { isFarmatodo } from '@/config/endpoints'

//TODO modificar esto por q esta pesimo
function useSearchMedicine(country: countries){
	const medicinesAR = useSearchMedicineAr()
	const medicinesMX = useSearchMedicineMx()
	const medicinesFarmatodo = useSearchMedicineFarmatodo(country)
	const validateCountry = ['VE', 'CO'].includes(country) || isFarmatodo ? 'FARMATODO' : country
	
	const medicines = {
		AR: medicinesAR,
		MX: medicinesMX,
		FARMATODO: medicinesFarmatodo
	}
	
	return  medicines[validateCountry as keyof typeof medicines]
}



export default useSearchMedicine