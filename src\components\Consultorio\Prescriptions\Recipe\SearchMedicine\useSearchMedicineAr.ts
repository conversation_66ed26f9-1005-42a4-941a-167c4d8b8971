import { countries } from '@umahealth/entities'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import axios from 'axios'
import { vademecum } from '@/config/endpoints'
import { useMutation } from 'react-query'
import { AttContext } from '@/components/AsyncAttention'
import { coverageNameFormater } from '../Common/Utils/recipeHelpers'
import { useContext } from 'react'
import { psicoMedicinesList } from '../../utils/psicomedicines'

export interface Medicine {
	precio: number,
	drugIDMonoDro: number,
	presentationName: string,
	drugName: string,
	duplicado: number,
	laboratorio: number,
	dosis: string,
	fecha: number,
	accion_farmacologica: string,
	productName: string,
	alfabetRegisterNum: number,
	snomed: number,
	venta_tipo: number
}

interface recipe {
	input: {
		text: string,
		country: countries
	}
	output: Medicine[]
}

interface ISearchMedicineAr extends recipe{
	names: string[]
}

type ISearchMedicineArFinal = Omit<ISearchMedicineAr, 'input'>

function useSearchMedicineAr() {
	const asyncAtt = useContext(AttContext)
	return useMutation(['search medicine AR'],
	async({ search, osName = '' }: { search: string, osName: string })=> {

			const firebaseIdToken = await getFirebaseIdToken()

			const headers = {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${firebaseIdToken}`,
			}

			const body: { country: typeof process.env.NEXT_PUBLIC_COUNTRY, text: string, o_s?: string} = {
				country: 'AR',
				text: search
			}

			if(/OSPECON/i.test(osName)){
				body.o_s = coverageNameFormater(osName)
			}

			const response = await axios.post<recipe>(
				vademecum,
				body,
				{ headers }
			)

			const uniqueDrugsNames = new Set<string>()

			response.data.output.forEach( (drug) => {
				uniqueDrugsNames.add(drug.drugName)
			})

			const result : ISearchMedicineArFinal = {
				output: response.data.output,
				names: Array.from(uniqueDrugsNames),
			}

			if(!asyncAtt?.attInView) return result

			const filteredOutput = result.output.filter(medicine => !psicoMedicinesList.some(psicoMedicine => psicoMedicine.toLowerCase() === medicine.drugName?.toLowerCase()))

			const filteredResult = {
				output: filteredOutput,
				names: Array.from(uniqueDrugsNames).filter(drugName => !filteredOutput.some(outputItem => outputItem.drugName?.toLowerCase() === drugName?.toLowerCase()))
			}

			return filteredResult

		})
}

export default useSearchMedicineAr
