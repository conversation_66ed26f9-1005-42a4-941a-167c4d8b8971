import { useMutation } from 'react-query';
import algoliasearch from 'algoliasearch/lite';
import { countries } from '@umahealth/entities';

// Configuración del cliente de Algolia
const client = algoliasearch(
  process.env.NEXT_PUBLIC_FARMATODO_ALGOLIA_APP_ID as string,
  process.env.NEXT_PUBLIC_FARMATODO_ALGOLIA_SEARCH_ONLY_KEY as string
);

const getIndexName = (country: countries) => {
  switch (country) {
    case 'VE':
      return process.env.NEXT_PUBLIC_FARMATODO_ALGOLIA_INDEX_NAME_VE;
    case 'CO':
      return process.env.NEXT_PUBLIC_FARMATODO_ALGOLIA_INDEX_NAME_CO;
    default:
      return process.env.NEXT_PUBLIC_FARMATODO_ALGOLIA_INDEX_NAME_VE
  }
};

export interface MedicineFarmatodo {
  brand: string;
  detailDescription: string;
  id: number;
  largeDescription: string;
  mediaDescription: string;
  mediaImageUrl: string;
  objectId: number;
}

interface ISearchMedicineFarmatodo {
  output: MedicineFarmatodo[];
  names: string[];
}

/**
 * Busca medicinas en Algolia
 */
function useSearchMedicineFarmatodo(country: countries) {
  const index = client.initIndex(getIndexName(country) as string);

  return useMutation(['search medicine Algolia'], async (search: string) => {
    // Realiza la búsqueda en Algolia
    const algoliaResponse = await index.search<MedicineFarmatodo>(search);

    const uniqueDrugsNames = new Set<string>();


    // Procesa los resultados de Algolia
    algoliaResponse.hits.forEach((drug) => {
      uniqueDrugsNames.add(drug.brand);
    });

    const result: ISearchMedicineFarmatodo = {
      output: algoliaResponse.hits,
      names: Array.from(uniqueDrugsNames),
    };

    return result;
  });
}

export default useSearchMedicineFarmatodo;