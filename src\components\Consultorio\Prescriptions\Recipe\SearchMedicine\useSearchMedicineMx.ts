
import { countries } from '@umahealth/entities'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import axios from 'axios'
import { vademecum } from '@/config/endpoints'
import { useMutation } from 'react-query'



export interface MedicineMx {
	id: string,
	name: string,
	susActiva: string,
	laboratory: string,
	ean: number,
}

interface recipe {
	input: {
		text: string,
		country: countries
	}
	output: MedicineMx[]
}

interface ISearchMedicineMx extends recipe{
	names: string[]
}

type ISearchMedicineArFinal = Omit<ISearchMedicineMx, 'input'>
/**
 * Busca medicinas a través de medikit
 * @returns 
 */
function useSearchMedicineMx() {
	return useMutation(['search medicine MX'],
		async( search: string)=> {

			const firebaseIdToken = await getFirebaseIdToken()

			const headers = {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${firebaseIdToken}`,
			}
			const response = await axios.post<recipe>(
				vademecum,
				{
					country: 'MX',
					text: search,
				},
				{ headers }
			)
			
			const uniqueDrugsNames = new Set<string>()

			response.data.output.forEach( (drug) => {
				uniqueDrugsNames.add(drug.name)
			})

			const result : ISearchMedicineArFinal = {
				output: response.data.output,
				names: Array.from(uniqueDrugsNames),
			}

			return result
		
		})
}

export default useSearchMedicineMx