import React, {useEffect} from 'react'
import {useAppDispatch, useAppSelector } from '@/store/hooks'
import moment from 'moment-timezone'
import { ref, listAll, getDownloadURL, getMetadata } from 'firebase/storage'
import { MdArrowBack } from 'react-icons/md'
import {Paragraph, Loader, Spacer} from '@umahealth/occipital-ui'
import { storage } from '@/config/firebase'
import RecipeAR from './RecipeAR'
import RecipeMX from './RecipeMX'
import './styles/Recipe.scss'

const Recipe = ({ setCurrentView }) => {
	const dispatch = useAppDispatch()
	const patient = useAppSelector(state => state.queries.patient)
	const { loadingRecipe } = useAppSelector((state) => state.prescriptions)
	const idPatient = patient.core_id || patient.uid || patient.id

	useEffect(() => {
		(async function() {
			dispatch({ type: 'ATTACHED_LOADING', payload: true })
			try {
				const storageRef = ref(storage, `${idPatient}/attached`)
				const res = await listAll(storageRef)
				const attached = await Promise.all(res.items.map(async item => {
					const url = await getDownloadURL(item)
					const metadata = await getMetadata(item)
					return {
						url,
						date: moment(item.name.split('_')[0], 'YYYYMMDDHHmmSS').format('DD/MM/YYYY HH:mm'),
						name: item.name.split('_')[1],
						metadata
					}
				}))
	
				dispatch({ type: 'SET_SHOW_ATTACHED', payload: { files: attached } })
			} catch (err) {
				console.error(err)
			} finally {
				dispatch({ type: 'ATTACHED_LOADING', payload: false })
			}
		})()
	}, [idPatient, dispatch])

	const router = () => {
		switch (process.env.NEXT_PUBLIC_COUNTRY) {
		case 'MX':
			return <RecipeMX goTo={setCurrentView} />
		case 'AR':
			return <RecipeAR goTo={setCurrentView} />
		default:
			return <Paragraph>No se pudo determinar el país de la receta</Paragraph>
		}
	}


	if(loadingRecipe) {
		return <Loader />
	} else {
		return <>
			<div className="sectionTitle">
				{setCurrentView && <MdArrowBack onClick={() => setCurrentView()} />}
				<label>Receta digital</label>
			</div>
			<Spacer direction='vertical' value='8px'/>
			{router()}
		</>
	}
}

export default Recipe
