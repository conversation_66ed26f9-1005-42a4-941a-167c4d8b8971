@use '@/styles/global/Vars.scss';

.medikitData{
	width: 100%;
	.medikitData__footer{
		border: 1px Vars.$uma-pink dotted;
		padding: 4px;
		border-radius: 4px;
		align-self: flex-end;
		h4{
			padding-bottom: 10px,
		}
		p{
			a{
				margin-left: 10px;
				letter-spacing: 0.04rem;
				text-decoration: underline;
			}
		}
	}
	.medikitData__block{
		padding: 15px 0;
		label{
		font-weight: bold;
		margin: 10px 0;
		color: Vars.$uma-secondary;
		}
		.medikitData__success{
			color: Vars.$uma-primary;
			font-size: 0.8rem;
			svg{
				margin: 0 6px ;
				border: 1px solid Vars.$uma-secondary;
				border-radius: 50%;
				font-size: 2rem;
				padding: 5px;
				padding-bottom: 5px;
				color: Vars.$uma-secondary;
			}
		}
	}
	.medikitData__form{
		padding: 5px;
		h4{
			padding-bottom: 8px;
			border-bottom: 1px solid Vars.$primary-color;
		}
		.medikitData__dataContainer{
			.medikitData__savedData{
				 display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;
				align-items: flex-end;
			}
		.medikitData__row{
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
		}
		.medikitData__form--info{
			padding: 8px 12px;
			min-width: 50%;
			label{
				font-weight: bold;
				margin-right: 6px;
			}
			input{
				display: block;
				width: 100%;
				border: 1px solid Vars.$data-grey;
				padding: 8px;
				border-radius: 4px;
			}
		}
		}
		.medikitData__invalidMsg{
			color: tomato;
			margin: 10px auto;
		}
		
}
}

.medikitViewContainer {
	width: 80%;
}


.medikitData__submit-btn{
			margin: 10px auto !important;
			display: block !important;;
			background: Vars.$uma-primary;
			border-radius: 6px;
			border: none;
			padding: 16px 12px;
			color: Vars.$white-color;
			font-weight: bold;
			min-width: 150px;
			cursor: pointer;
	}