@use '@/styles/global/Vars.scss';

.formContainer{
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.radioButton{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    padding: 4px;
    border-radius: 50%;
    border: 1px solid #563FF0;
    div{
        background-color: white;
        border-radius: 50%;
        width: 100%;
        height: 100%;
    }
    &:hover{
        cursor: pointer;
    }
}

.selected{
    background-color: #563FF0 !important;
}
.radioButtonContainer{
    border: 1px solid Vars.$color-grey-5;
    background-color: transparent;
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    padding: 24px;
    align-items: center;
    height: 54px;
    border-radius: 8px;
    margin-bottom: 12px;
}