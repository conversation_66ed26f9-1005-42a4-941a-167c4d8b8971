import { Dispatch, SetStateAction } from "react";
import { useMutation } from "react-query"; 
import { IPrescriptionPostFunctionParameters, PrescriptionState } from "../Interfaces/Interfaces";
import { IPatient } from "@umahealth/entities";
import { Session } from "@opentok/client";
import { errorHandler } from "@/config/stackdriver";
import { backOff } from 'exponential-backoff';
import { sendPosthogConfirmEventError } from "./sendPosthogTemporalPrescriptionError";
import { PostHog } from "posthog-js/react";
import { revalidateGetAssignation } from "@/serverServices/getAssignation";
import { revalidateMedicalRecords } from "@/serverServices/getMedicalRecords";

interface ITemporalPrescriptionParams {
  dataPostRecipe: IPrescriptionPostFunctionParameters
  patient: IPatient
  posthog: PostHog
  providerUid: string
  session: Session | null
  setPrescriptionState: Dispatch<SetStateAction<PrescriptionState>>
}

const sendPrescriptionSignal = async ({
  session,
  dataPostRecipe,
  providerUid,
  patient,
  posthog,
}: Omit<ITemporalPrescriptionParams, "setPrescriptionState">) => {
  const sendSignal = async () => {
    return new Promise((resolve, reject) => {
      session?.signal(
        {
          type: 'new_prescription',
          data: JSON.stringify({
            ...dataPostRecipe,
            providerUid,
            patient,
          }),
        },
        (error) => {
          if (error) {
            console.error('Error sending new_prescription signal:', error);
            errorHandler?.report(`[ doctor | createTemporalPrescription ] => Error sending signal new_prescription for assignation ${dataPostRecipe.assignationId} - user: ${patient.id} - provider: ${providerUid} - error: ${JSON.stringify(error)}`);
            sendPosthogConfirmEventError(posthog, error.message)
            reject(error);
          } else {
            resolve(true);
          }
        }
      );
    });
  };

  return backOff(sendSignal, {
    numOfAttempts: 5,
    startingDelay: 1000,
    timeMultiple: 2,
    maxDelay: 10000,
  });
};

export const useTemporalPrescriptionMutation = () => {
  return useMutation<boolean, Error, ITemporalPrescriptionParams>(
    async (params) => {
      const { setPrescriptionState, ...signalParams } = params;
      setPrescriptionState('waiting');
      
      try {
        await sendPrescriptionSignal(signalParams);
        return true;
      } catch (error) {
        console.error('Failed to send new_prescription signal after multiple attempts:', error);
        errorHandler?.report(`[ doctor | createTemporalPrescription ] => Failed to send new_prescription signal after multiple attempts for assignation ${params.dataPostRecipe.assignationId} - user: ${params.patient.id} - provider: ${params.providerUid} - error: ${JSON.stringify(error)}`);
        sendPosthogConfirmEventError(params.posthog, 'Failed to send signal after multiple attempts');
        throw error;
      }
    },
    {
      retry: false,
      onSuccess: async() => {
            await revalidateGetAssignation()
            await revalidateMedicalRecords()
          }
    }
  );
};