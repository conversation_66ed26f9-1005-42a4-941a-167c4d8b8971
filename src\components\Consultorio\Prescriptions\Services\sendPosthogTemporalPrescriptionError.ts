import { PostHog } from 'posthog-js/react'

/**
 * sends a posthog event to track errors when confirming prescription
 * @param posthog posthog instance
 * @param confirmed whether or not the user accepts the prescription
 * @param error error message
 */
export const sendPosthogConfirmEventError = (posthog: PostHog, error: string) => {
	posthog.capture('doctor_temporalPrescription_error', {
		error,
	})
}