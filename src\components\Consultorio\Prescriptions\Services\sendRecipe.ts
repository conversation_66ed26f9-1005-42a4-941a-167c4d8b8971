import { Dispatch, SetStateAction } from "react"
import { IPrescriptionPostFunctionParameters } from "../Interfaces/Interfaces"
import { AxiosResponse } from "axios"
import { postAudibairesPrescription, postOsdePrescription, postRecipeUP } from "../Recipe/RecipeAR/OS"
import { postPreserfarPrescription } from "../Recipe/RecipeAR/OS/preserfar"
import * as ordersActions from '../store/prescriptionsActions'
import { updatePrescriptions } from "../utils/updateAsyncMrPrescriptions"
import { IMedicalRecord } from "@umahealth/entities"
import { Timestamp } from "@/config/firebase"
import { IPatientExtended } from "@/store/reducers/queriesReducers"
import { useMutation } from "react-query"
import { revalidateGetAssignation } from "@/serverServices/getAssignation"
import { useDispatch } from "react-redux"
import swal from 'sweetalert'
import { revalidateMedicalRecords } from "@/serverServices/getMedicalRecords"

interface ISendRecipeProps {
  asyncMr: {
    mrInView: IMedicalRecord<Timestamp> | undefined
    setMrInView: Dispatch<SetStateAction<IMedicalRecord<Timestamp> | undefined>>
  } | undefined
  dataPostRecipe: IPrescriptionPostFunctionParameters,
  providerId: string
  patient: IPatientExtended
}

interface IMutationParams extends ISendRecipeProps {
  setLoading: Dispatch<SetStateAction<boolean>>
  showConfirmationAlert?: boolean
}

export const useSendRecipeMutation = () => {
  const dispatch = useDispatch();

  return useMutation({
    mutationFn: async ({ asyncMr, dataPostRecipe, setLoading, providerId, patient, showConfirmationAlert = true }: IMutationParams) => {
      let successfullySent = false;
      try {
        setLoading(true)
        const isOsde = /OSDE(?!PYM)/i.test(dataPostRecipe.formData?.coverage?.name)
        const isUp = /UNI[OÓ]N?\sPERSONAL|ACCORD|UP/i.test(dataPostRecipe.formData?.coverage?.name)
        const isOspecon = /OSPECON/i.test(dataPostRecipe.formData?.coverage?.name)
        const isPfa = /POLICIA\sFEDERAL/i.test(dataPostRecipe.formData?.coverage?.name)
        let prescriptionResponse: AxiosResponse<{created: boolean, id?: string, prescriptionId?: string}> | undefined
        
        if(isUp && !prescriptionResponse) {
          prescriptionResponse = await postRecipeUP(dataPostRecipe)
        } else if (isOsde && !prescriptionResponse) {
          prescriptionResponse = await postOsdePrescription(dataPostRecipe)
        } else if (isOspecon && !prescriptionResponse) {
          prescriptionResponse = await postPreserfarPrescription(dataPostRecipe, providerId, patient)
        } else if(isPfa && !prescriptionResponse){
          prescriptionResponse = await postAudibairesPrescription(dataPostRecipe)
        } else {
          if(!prescriptionResponse && !isOsde){
            prescriptionResponse = await ordersActions.postFarmalinkPrescription(dataPostRecipe)
          }
        }
        if (prescriptionResponse?.data?.created) {
          successfullySent = true;
      }  else if (!prescriptionResponse?.data?.created){
        successfullySent = false;
        throw new Error('Error al crear la receta')
      }

        if(asyncMr?.mrInView && asyncMr.setMrInView && prescriptionResponse?.data?.created && (prescriptionResponse?.data?.id || prescriptionResponse?.data?.prescriptionId)) {
          updatePrescriptions(asyncMr.mrInView, asyncMr.setMrInView, prescriptionResponse.data.id || prescriptionResponse?.data?.prescriptionId || '')
        }

        return prescriptionResponse?.data
      } 
      catch (error) {
        console.error('Error al enviar la receta:', error)
        throw error
      }
      finally {
        await Promise.all([
          revalidateGetAssignation(),
          revalidateMedicalRecords()
        ])

        if (showConfirmationAlert && successfullySent) {
          await swal({
            title: 'Receta generada!',
            text: 'Se guardó la receta exitosamente. Se adjuntará automáticamente al hacer click en \'Finalizar atención\'',
            icon: 'success'
          });
        }
        dispatch({ type: 'HANDLE_RECIPE_ADD_DRUG', payload: [] })
        dispatch({ type: 'HANDLE_TEMP_PLANAFFILIATE', payload: '' })
        dispatch({ type: 'HANDLE_TEMP_NAFFILIATE', payload: '' })
        dispatch({ type: 'HANDLE_TEMP_SOCIALWORK', payload: '' })
        setLoading(false)
      }
    },
  })
}
