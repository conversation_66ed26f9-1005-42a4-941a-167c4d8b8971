import axios from "axios"
import swal from "sweetalert"
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken"
import { update_matricula } from '@/config/endpoints'
import { errorHandler } from "@/config/stackdriver"

export async function updateMatriculaNumber(matricula : string) {

	const data = {
		matricula: matricula
	}
	
	try {
		const firebaseToken = await getFirebaseIdToken()

		const headers = { 
			'Content-Type': 'Application/Json', 
			'Authorization': `Bearer ${firebaseToken}` 
		}
		await axios.patch(update_matricula, data, { headers })
			.then(async () => {
				await swal(
					'Matrícula añadida',
					'Puede intentar generar la receta nuevamente.',
					'success'
				)
				return true
			})
			.catch(async res => {
				if (res.response.data.error)
					await swal(
						'Error',
						'Ocurrió un error al cargar la matrícula. Comuníquese con Soporte.',
						'WARNING'
					)
				return false
			})
	} catch (error) {
        errorHandler && errorHandler.report(`error actualizando matrícula: ${error}`)
		return error
	}
}