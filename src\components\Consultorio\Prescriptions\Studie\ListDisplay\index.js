import React from 'react'
import { Button, Icon, Paragraph, Row, Spacer, Title, Loader } from '@umahealth/occipital-ui'

export default function OrdersListDisplay({orderArray, loading, handleDeleteOrder, setCurrentView}) {
	return (
		<>
			<Title hierarchy={3} align="left"><PERSON>rden<PERSON></Title>
			{orderArray?.length > 0 ? 
				<>
					{loading ? <Row spacing='center'><Loader /></Row> : 
						orderArray?.map((p, i) => {
							return  <Row key={i} spacing='between'>
								<Paragraph key={i} text={`Orden de ${p.items?.reduce((textAcum, el) => textAcum += ` - ${el}`)}`} style={{cursor: 'pointer'}} />
								<Icon color="primary" name="trash" size="xs" isInline={true} 
									action={() => handleDeleteOrder(p)}
								/>
							</Row>
						})}
				</>
				:
				<Paragraph align="left">Aún no hay órdenes cargadas</Paragraph>}
			<Spacer value="16px" />
			<Row spacing='center'>
				<Button size="medium" action={() => setCurrentView('newStudie')}>
					<Row>
						<Icon
							color="white"
							name="plus"
							size="xs"
							isInline={true}
						/>
						<Spacer direction='horizontal' value="8px"/>
									Orden
					</Row>
				</Button>
			</Row>
		</>
	)
}
