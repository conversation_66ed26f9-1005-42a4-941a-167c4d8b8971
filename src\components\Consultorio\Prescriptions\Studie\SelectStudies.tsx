import React from 'react'
import Select, { StylesConfig } from 'react-select'
import CreatableSelect from 'react-select/creatable'
import { Spacer, Paragraph, Button } from 'occipital-new'
import { useTranslations } from 'next-intl'
import SelectedStudies from './SelectedStudies'
import { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import labStudiesCombos from '@/config/labStudiesCombos.json'
import { getDocumentFB } from '@/utils/firebaseQueries'
import { GET_LABSTUDIES } from '@/store/types/ordersTypes'
import { asocciateDiagnosis } from '@/config/asocciateDiagnosis'
import { final_list, lab_list, practices_list } from './StudiesData'
import { RootState } from '@/store/configStore'
import { UseFormWatch } from 'react-hook-form'
import { IRecipeForm } from '../Interfaces/Interfaces'
import { Disclaimer } from '@/components/GeneralComponents/Disclaimer/Disclaimer'
import { getExcludedStudies } from './hooks/getExcludedStudies'
import { filterStudies } from './utils/filterStudies'
import { errorHandler } from '@/config/stackdriver'
import { LabStudy } from '../../Prescriptions/store/prescriptionTypes'

interface IProps {
	setLabStudiesArray: React.Dispatch<React.SetStateAction<LabStudy[]>>,
	labStudiesArray: any
	watch: UseFormWatch<IRecipeForm>
	isNew?: boolean // TODO: Remove this prop after the new order manager is deployed
}

// Se usa la interfaz LabStudy importada desde prescriptionTypes.ts

interface IExcludedStudies {
	[key: string]: boolean | null;
}

const SelectStudies = ({ labStudiesArray, setLabStudiesArray, watch, isNew = false }: IProps) => {
	const t = useTranslations('attention')
	const { orderStudies, labStudiesFB, orderSpecifications, diagnosis } = useAppSelector((state: RootState) => state.prescriptions)
	const [searcher, setSearcher] = useState(final_list)
	const [practices, setPractices] = useState(true)
	const [excludedStudies, setExcludedStudies] = useState<IExcludedStudies | null>(null);
	const [lab, setLab] = useState(true)
	const [diagnosisOptions, setDiagnosisOptions] = useState([{ value: '', label: '' }])
	const dispatch = useAppDispatch()
	const currentAtt = useAppSelector((state) => state.queries.currentAtt)
	useEffect(() => {
		const coverageName = watch('coverage.name') || currentAtt?.patient?.obra_social
		const fetchExcludedStudies = async () => {
			try {
				const data = await getExcludedStudies(coverageName)
				excludedStudies !== data && setExcludedStudies(data)
			} catch (error) {
				errorHandler?.report(`Error getting excluded studies for ${coverageName}: ${error}`);
			}
		}
		fetchExcludedStudies()
	}, [watch('coverage.name')])

	const labStudiesCombosOptions = Object.keys(labStudiesCombos).map((item) => ({
		label: item.replace(/_/g, ' ').toUpperCase(),
		value: item,
	}))
	
	const stdsToRender = labStudiesArray?.length > 0 ? labStudiesArray : orderStudies

	const stdsFormatter = () => {
		const helper = new Set<string>(labStudiesArray.map((i:LabStudy) => i?.codigo_nbu))
		const helperArray = Array.from(helper.values())
		const stds = helperArray.map((item) => labStudiesFB.filter((std:LabStudy) => item === std?.codigo_nbu && std)[0])
		if (stds?.length !== labStudiesArray?.length) setLabStudiesArray(stds)
	}

	const getLabStudies = async () =>{
		if (labStudiesFB?.length > 0) return
		const data:any = await getDocumentFB('/parametros/userapp/variables/lab_studies/')
			
		if(data && data.labStudies && data.practices){
			const studies = [...data.labStudies, ...data.practices]
			dispatch({ type: GET_LABSTUDIES, payload: studies }) 
		}
	}
	
	useEffect(() => {
		stdsFormatter()
	}, [labStudiesArray])
    
	useEffect(() => {
		getLabStudies()
		setDiagnosisOptions(asocciateDiagnosis.map((diag) => ({ value: diag, label: diag.replace(/_/g, ' ').toUpperCase() })))
	}, [])

	useEffect(() => {
		if (practices && !lab) {
			excludedStudies ? setSearcher(filterStudies(excludedStudies, practices_list)) : setSearcher(practices_list);
		} else if (lab && !practices) {
			excludedStudies ? setSearcher(filterStudies(excludedStudies, lab_list)) : setSearcher(lab_list);
		} else {
			excludedStudies ? setSearcher(filterStudies(excludedStudies, final_list)) : setSearcher(final_list);
		}
	}, [practices, lab, excludedStudies]);

	function selectAsocciateDiagnosis(event: any) {
		if (!event) {
			dispatch({type: 'HANDLE_ORDERS_DIAGNOSIS', payload: ''})
			return
		}
		dispatch({type: 'HANDLE_ORDERS_DIAGNOSIS', payload: event.value})
	}

	function addStudy(event: any) {
		if (!event) return
		const { label, value } = event
		setLabStudiesArray([...labStudiesArray, { nombre: label, codigo_nbu: value }])
		
	}
	
	function addCombo(event: any) {
		if (!event) return
		const value: keyof typeof labStudiesCombos = event.value
		const stds = labStudiesCombos[value]?.map(
			(item) => labStudiesFB?.filter((std: LabStudy) => item === std?.codigo_nbu && std)[0]
		)
		setLabStudiesArray([...labStudiesArray, ...stds])
		
	}
	const setDetails = (text: string) => {
		dispatch({type: 'HANDLE_ORDERS_SPECIFICATIONS', payload: text})
	}

	const practicesFunction = () =>{
		setPractices(!practices)
	}

	const laboratoriosFunction = () =>{
		setLab(!lab)
	}

	const disabledOrder = /CASA|PODER\sJUDICIAL\sDE\sLA\sNACI[ÓO]N|OSPJN|PODERJUDICIAL|PODER\sJUDICIAL/i.test(watch('coverage.name'))
	const customStyles: StylesConfig = {
		dropdownIndicator: provided => ({ ...provided, color: '#4D38D7' }),
	}
	return(
		<>
			<div className='studiesOrder__container'>
				<div className='studiesOrder__inputContainer'>
					<div className='flex justify-end mb-4 space-x-4 my-2'>
						<div className='flex items-center gap-2'>
							<label htmlFor='practices' className=''>Prácticas</label>
							<input
								id='practices'
								className='h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary'
								defaultChecked
								onChange={practicesFunction}
								type='checkbox'
							/>
						</div>
						<div className='flex items-center gap-2'>
							<label htmlFor='laboratory' className=''>Laboratorio</label>
							<input
								id='laboratory'
								className='h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary'
								onChange={laboratoriosFunction}
								type="checkbox"
							/>
						</div>
					</div>
					<h6 className='studiesOrder__container--title m-2 text-secondary-600 font-semibold'>Estudio:</h6>
					<CreatableSelect
						openMenuOnClick={true}
						isSearchable={true}
						isClearable
						options={searcher}
						onChange={addStudy}
						placeholder='Buscar estudios'
						styles={customStyles}
					/>
				</div>
			</div>

			<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
				<div className='studiesOrder__container'>
					<div className='studiesOrder__inputContainer'>
						<h6 className='studiesOrder__container--title m-2 text-secondary-600 font-semibold'>Combos de estudios:</h6>
						<Select
							openMenuOnClick={true}
							isSearchable={true}
							isClearable
							options={labStudiesCombosOptions}
							placeholder='Buscar combo de estudios'
							onChange={e => addCombo(e)}
							styles={customStyles}
						/>
					</div>
				</div>

				<div className='studiesOrder__container'>
					<div className='studiesOrder__inputContainer'>
						<h6 className='studiesOrder__container--title m-2 text-secondary-600 font-semibold'>Diagnóstico asociado</h6>
						<CreatableSelect
							openMenuOnClick={true}
							isSearchable={true}
							isClearable
							placeholder='¿Posee síntomas asociados?'
							options={diagnosisOptions}
							onChange={selectAsocciateDiagnosis}
							styles={customStyles}
						/>
					</div>
				</div>
			</div>
			<Spacer direction='vertical' value='8px'/>
			<div className="studiesOrder__especifications">
				<h6 className='studiesOrder__container--title m-2 text-secondary-600 font-semibold'>{t('textarea-studies_detail')}</h6>
				<textarea
					className='w-full resize-none rounded-2xl border border-gray-200 bg-white px-4 py-3 text-base placeholder:text-gray-400 focus:outline-none focus:ring-1 focus:ring-primary/75 focus:border-primary/75'
					placeholder='Escribí tus especificaciones aquí'
					onChange={(e) => setDetails(e?.target.value)}
					value={orderSpecifications}
				/>
			</div>
			<div className='studiesOrder__container'>
				<SelectedStudies stdsToRender={stdsToRender} labStudiesArray={labStudiesArray} setLabStudiesArray={setLabStudiesArray}/>
			</div>
			<Spacer direction='vertical' value='16px' />
			{disabledOrder && <Disclaimer title='Importante' text='Los afiliados de esta OS no tienen disponibles órdenes de estudio y laboratorio digitales. Para órdenes derive al paciente a consulta presencial' />}
			{!isNew ? labStudiesArray?.length > 0 && diagnosis !== '' ? (
				<div className='flex justify-end'>
					<div className='mx-2 w-1/6'>
						<Button
							disabled={disabledOrder}
							size='full'
							type='submit'
							occ_type='filled'
						>
							Solicitar estudios
						</Button>
					</div>
				</div>
			) :
				<div className='studiesOrder__container--submit'>
					<Paragraph size='s' weight='regular' color='state-error' className='text-center'>Indicar diagnóstico antes de generar orden</Paragraph>
				</div> : null}
		</>
	)
}

export default SelectStudies