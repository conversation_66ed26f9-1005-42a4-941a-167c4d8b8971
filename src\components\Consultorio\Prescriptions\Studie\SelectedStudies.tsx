import React from 'react'
import { deleteStd } from '../store/prescriptionsActions'
import { LabStudy } from '../store/prescriptionTypes'

interface IProps {
	stdsToRender: LabStudy[],
	setLabStudiesArray: React.Dispatch<React.SetStateAction<LabStudy[]>>,
	labStudiesArray: LabStudy[]
}
const SelectedStudies = ({ stdsToRender, setLabStudiesArray, labStudiesArray }: IProps) => {
	function removeStd({ nombre }: { nombre: string | undefined }) {
		// Filtrado seguro que maneja posibles undefined
		const helper = labStudiesArray.filter((item: LabStudy) => {
			// Si alguno es undefined, consideramos que son diferentes
			if (!item?.nombre || !nombre) return true;
			return item.nombre !== nombre;
		});
		setLabStudiesArray(helper);
		deleteStd(helper);
	}

	return (
		<div className='studiesOrder__container'>
			{stdsToRender?.length > 0 && (
				<div className='studiesOrder__container--list'>
					{stdsToRender.map((item: LabStudy, index: number) => (
						<div key={index} className="flex items-center justify-between border-b border-solid border-gray-200 py-2 px-4">
							<div className="flex items-center">
								<span className="text-gray-400">{index + 1}</span>
								<span className="ml-4 text-gray-500">{item.nombre}</span>
							</div>
							<button
								type="button"
								className="p-1.5 text-secondary-500 hover:text-secondary-600 border border-secondary-500 rounded-md hover:bg-secondary-50 transition-colors cursor-pointer"
								onClick={(e) => {
									e.preventDefault()
									removeStd({ nombre: item.nombre || '' })
								}}
							>
								<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
								</svg>
							</button>
						</div>
					))}
				</div>
			)}
		</div>
	)
}

export default SelectedStudies