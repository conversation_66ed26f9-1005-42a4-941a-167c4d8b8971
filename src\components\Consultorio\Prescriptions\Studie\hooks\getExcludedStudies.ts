import { get_excluded_studies } from "@/config/endpoints";
import { errorHandler } from "@/config/stackdriver";
import axios from "axios";

interface IExcludedStudies {
  [key: string]: boolean | null;
}

/**
 * Obtiene los estudios excluidos para una cobertura dada.
 *
 * Esta función realiza una solicitud HTTP GET para recuperar los estudios 
 * excluidos asociados con la cobertura especificada desde el endpoint de la API. 
 * Maneja los errores que pueden ocurrir durante la solicitud y los reporta 
 * utilizando el sistema de manejo de errores configurado.
 *
 * @param coverage - Una cadena que representa la cobertura para la que se 
 *                   desea recuperar los estudios excluidos.
 * @returns Una Promesa que se resuelve en un objeto que contiene los estudios 
 *          excluidos como pares clave-valor, donde las claves son los IDs de los 
 *          estudios y los valores son booleanos que indican su estado, o null 
 *          si ocurre un error. 
 *          Si el valor booleano es true, se interpreta que el estudio está excluído.
 * 
 */
export const getExcludedStudies = async (coverage: string): Promise<IExcludedStudies | null> => {
  try {
    const response = await axios.get<IExcludedStudies>(`${get_excluded_studies}/${coverage}`);
    return response.data;
  } catch (error) {
    errorHandler?.report(`Error getting excluded studies related to ${coverage}: ${error}`);
    return null; 
  }
};
