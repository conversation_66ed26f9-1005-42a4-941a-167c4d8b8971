import { AttContext } from '@/components/AsyncAttention'
import { useContext } from 'react'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import swal from 'sweetalert'
import { ButtonList } from 'sweetalert/typings/modules/options/buttons'
import { coverageForm } from '../../Interfaces/Interfaces'
import { saveOrderDB } from '../../store/prescriptionsActions'
import { LabStudy } from '../../store/prescriptionTypes'
import { revalidateGetAssignation } from '@/serverServices/getAssignation'
import { revalidateMedicalRecords } from '@/serverServices/getMedicalRecords'

interface IuseSaveOrdersProps {
	setCurrentView?: any;
	signature_medikit: string
}
const useSaveOrders = ({setCurrentView, signature_medikit}:IuseSaveOrdersProps) => {
	const dispatch = useAppDispatch()
	const { orderSpecifications, diagnosis } = useAppSelector((state) => state.prescriptions)
	const { profile } = useAppSelector((state) => state.user)
	const { patient, currentAtt } = useAppSelector((state) => state.queries)
	const appointmentInView = useContext(AttContext)
	const { temp } = useAppSelector((state) => state.prescriptions)
	const idPatient = patient.core_id || patient.uid || patient.id || appointmentInView?.attInView?.patient.uid
	const patientData = patient?.dni?.length ? patient : appointmentInView?.attInView?.patient

	async function handleOrdersSubmit(coverage: coverageForm, labStudiesArray: LabStudy[]) {
		const swalButtonList : ButtonList = { 
			cancel: {
				text: 'Cancelar'
			},
			catch: {
				text: 'Ok',
				value: true,
			}
		}
		const response = await swal({
			title: 'Confirmación',
			text: 'Verifique cuidadosamente los datos y confirme la operación.',
			icon: 'warning',
			buttons: swalButtonList,
		})
		if (response === true) {
			try {
				if (!labStudiesArray?.length) {
					throw new Error(
						'Tiene que seleccionar al menos un estudio.'
					)
				} else {
					const patientWithUpdatedData = { ...patientData, ...temp, dependant_uid: currentAtt?.patient?.dependant_uid }
					saveOrderDB(
						coverage,
						labStudiesArray,
						orderSpecifications,
						patientWithUpdatedData,
						profile,
						currentAtt?.incidente_id ? currentAtt?.incidente_id : appointmentInView?.attInView?.assignation_id,
						diagnosis,
						signature_medikit
					)
					dispatch({
						type: 'HANDLE_ORDERSTUDIES_STUDY',
						payload: labStudiesArray,
					})
					await swal('Orden adjuntada', '', 'success')
					await revalidateGetAssignation()
					await revalidateMedicalRecords()

					
					dispatch({ type: 'HANDLE_ORDERSTUDIES_STUDY', payload: [] })
					if(setCurrentView) { setCurrentView() }
				}
			} catch (error) {
				swal({ title: 'Aviso', text: `${error}`, icon: 'warning' })
			}
		}
	}
	return {handleOrdersSubmit, idPatient}
}

export default useSaveOrders