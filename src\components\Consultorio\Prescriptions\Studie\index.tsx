import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import { MdArrowBack } from 'react-icons/md'
import {Spacer, Loader, Row, Paragraph} from '@umahealth/occipital-ui'
import moment from 'moment-timezone'
import { storage } from '@/config/firebase'
import { ref, listAll, getDownloadURL, getMetadata } from 'firebase/storage'
import SelectStudies from './SelectStudies'
import CredentialAtt from '../utils/CredentialAtt'
import useSaveOrders from './hooks/useSaveOrders'
import { usePathname } from 'next/navigation'
import { useForm } from 'react-hook-form'
import PatientOS from '../Recipe/Common/ui/RecipePatientOS'
import { IRecipeForm } from '../Interfaces/Interfaces'
import { LabStudy } from '../store/prescriptionTypes'
import './styles/Studies.scss'

const StudiesOrder = ({ setCurrentView }: {setCurrentView: any}) => {
	const dispatch = useAppDispatch()
	const pathname = usePathname()
	const { orderStudies } = useAppSelector((state) => state.prescriptions) as { orderStudies : LabStudy} 
	const { files, loading } = useAppSelector(state => state.front.attached)
	const { signature_medikit } =	useAppSelector((state) => state.prescriptions)
	const [labStudiesArray, setLabStudiesArray] = useState<LabStudy[]>(orderStudies?.map((id: string) => ({ codigo_nbu: id })) || [])
	const {idPatient, handleOrdersSubmit} = useSaveOrders({setCurrentView, signature_medikit})
	const { register, watch, formState: { errors }, setValue, control, reset, handleSubmit } = useForm<IRecipeForm>({
		defaultValues: {
			coverage: {
				name: '',
				plan: '',
				afiliateId: '',
				credentialVersion: '',
			}
		}
	})

	const isChatAtt = pathname === '/chatAtt'
	useEffect(() => {
		(async function() {
			dispatch({ type: 'ATTACHED_LOADING', payload: true })
	
			try {
				const storageRef = ref(storage, `${idPatient}/attached`)
				const res = await listAll(storageRef)
				
				const attached = await Promise.all(res.items.map(async (item) => {
					const url = await getDownloadURL(item)
					const metadata = await getMetadata(item)
					return {
						url,
						date: moment(item.name.split('_')[0], 'YYYYMMDDHHmmSS').format('DD/MM/YYYY HH:mm'),
						name: item.name.split('_')[1],
						metadata
					}
				}))
	
				dispatch({ type: 'SET_SHOW_ATTACHED', payload: { files: attached } })
			} catch (err) {
				console.error(err)
			} finally {
				dispatch({ type: 'ATTACHED_LOADING', payload: false })
			}
		})()
	
	}, [idPatient, dispatch, storage])
	
	const credentialFiles = files?.filter(att => att.name === 'credential')

	

	return (
		<>
			<div className='sectionTitle'>
				{(setCurrentView && !isChatAtt) && <MdArrowBack onClick={() => setCurrentView()} />}
				<label>Ordenes de estudios y laboratorios</label>
			</div>
			<Spacer direction='vertical' value='8px'/>
			{ process.env.NEXT_PUBLIC_COUNTRY !== 'MX' &&
			<>
				{loading ? 
					<Row spacing='end'>
						<Loader />
					</Row>
					:
					(credentialFiles?.length > 0 ?
						credentialFiles.map(credential => (
							<CredentialAtt file={credential} key={credential.date} />
						))
						:
						<Row spacing='end'>
							<Paragraph align='right'>Usuario no adjuntó foto de la credencial</Paragraph>
						</Row>
					)
				}
			</>
			}
			<form className='studiesOrder' onSubmit={handleSubmit(async ({ coverage }) => await handleOrdersSubmit(coverage, labStudiesArray))}>
				<PatientOS register={register} watch={watch} errors={errors} setValue={setValue} control={control} reset={reset} />
				<SelectStudies
					watch={watch}
					labStudiesArray={labStudiesArray}
					setLabStudiesArray={setLabStudiesArray}
				/>
			</form>
		</>
	)
}

export default StudiesOrder
