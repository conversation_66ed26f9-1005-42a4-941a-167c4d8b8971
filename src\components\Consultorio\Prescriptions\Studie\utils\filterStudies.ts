interface IExcludedStudies {
	[key: string]: boolean | null;
}

/**
 * Filtra los estudios excluidos de una lista final.
 *
 * Esta función toma una lista de estudios excluidos que obtenemos de la base de datos
 * y una lista de estudios que tenemos definida en el archivo "StudiesData", 
 * y devuelve una nueva lista filtrada que excluye aquellos estudios que están marcados 
 * como excluidos para la cobertura.
 * 
 */
export const filterStudies = (
  excludedStudies: IExcludedStudies,
  list: { label: string, value: string }[]
): { label: string, value: string }[] => {

  // Si no hay estudios excluidos, se devuelve la lista completa
  if (!excludedStudies) {
    return list;
  }

  const normalizeString = (str: string): string => {
    return str
    .toLowerCase() // Convertir a minúsculas
    .normalize("NFD").replace(/[\u0300-\u036f]/g, "") // Eliminar tildes y diacríticos
    .replace(/[.,/#!$%&*;:{}=\-_`~()]/g, '')// Eliminar puntos y otros signos de puntuación
    .replace(/\s+/g, '') // Eliminar todos los espacios
    .trim(); // Eliminar espacios al principio y al final (en caso que existan, aunque ya no debería haber)
  };


  const excludedStudiesSet = new Set(
    Object.entries(excludedStudies)
      .filter(([, isExcluded]) => isExcluded === true)
      .map(([excludedStudy]) => normalizeString(excludedStudy))
  );

  return list.filter((study) => {
    const normalizedLabel = normalizeString(study.label);

    return !excludedStudiesSet.has(normalizedLabel);
  });
};


