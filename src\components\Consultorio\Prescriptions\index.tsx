import React, { useState } from 'react'
import PrescriptionList from './PrescriptionList'
import Recipe from './Recipe'
import Studie from './Studie'
import './styles/prescriptions.scss'

export type TCurrentView = 'prescriptionList' | 'newRecipe' | 'newStudie' | null

export default function Prescriptions () {
	const [currentView, setCurrentView] = useState<TCurrentView>('prescriptionList')

	switch(currentView) {
		case 'newRecipe':
			return <Recipe setCurrentView={setCurrentView} />
		case 'newStudie':
			return <Studie setCurrentView={setCurrentView} />
		default:
			return <PrescriptionList setCurrentView={setCurrentView} />
	}
}
