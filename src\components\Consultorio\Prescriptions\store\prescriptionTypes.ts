// Interface básica para acciones
export interface ActionType {
  type: string;
  [key: string]: any;
}

// Interfaces para el objeto Medicine
export interface Medicine {
  id?: string;
  name?: string;
  descripcion?: string;
  cantidad?: number;
  details?: string;
  [key: string]: any;
}

// Interface para los estudios de laboratorio
export interface LabStudy {
  id?: string;
  name?: string;
  nombre?: string;
  codigo_nbu?: number | string;
  [key: string]: any;
}

// Interface para el objeto temp
export interface TempRecipe {
  os: string;
  plan: string;
  number: string;
  medicines: Medicine[];
  orders: string[];
  currentMedicine?: Medicine;
  recipeCheck?: boolean;
}

// Interface para la receta
export interface Recipe {
  param?: string;
  [key: string]: any;
}

// Interface para la historia de recetas
export interface RecetaHistory {
  id: string;
  date: string;
  medicines: Medicine[];
  [key: string]: any;
}

// Interface principal para el state del reducer
export interface PrescriptionState {
  n_afiliado: string;
  corporate: string;
  plan: string;
  currentMedicine: {
    cantidad: number;
    [key: string]: any;
  };
  orderStudies: {codigo_nbu:number, nombre:string}[];
  labStudiesFB: LabStudy[];
  drugOptions: any[];
  orderSpecifications: string;
  diagnosis: string;
  direccion: string;
  selected_logo: string;
  loadingRecipe: boolean;
  loadingSearch: boolean;
  searchResult: any[];
  token_consulta_medikit: string;
  isUnionPersonal: boolean;
  recetasHistory: RecetaHistory[];
  recipes: Medicine[];
  recipe?: Recipe;
  temp: TempRecipe;
  signature_medikit: string;
  hash_medikit?: string;
  securityHash?: string;
  prescriptionFailed: boolean;
  invalidMedication: boolean;
  invalidData: boolean;
  invalidCredential: boolean;
  duplicatedMedication: boolean;
  invalidStructure: boolean;
  invalidAffiliateNumber: boolean;
}

// Interface para acciones del reducer de prescripciones
export interface PrescriptionAction extends ActionType {
  payload: any;
}