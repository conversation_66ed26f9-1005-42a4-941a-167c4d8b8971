import swal from 'sweetalert'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { collection, doc, query, where, onSnapshot } from '@/config/firebase'
import { store } from '@/store/configStore'
import * as ordersTypes from './prescriptionsTypes'
import { chat, farmalink, quick_farmalink, studies } from '@/config/endpoints'
import {firestore} from '@/config/firebase'
import { errorHandler } from '@/config/stackdriver'
import { regexNumbers } from '@/utils/regex'
import axios from 'axios'
import moment from 'moment-timezone'
import { download_recipe } from '@/config/endpoints'
import { coverageNameFormater } from '../Recipe/Common/Utils/recipeHelpers'
import '../styles/matriculaError.module.css'
import handlePrescriptionError from '../utils/prescriptionErrorHandler'
import { revalidateGetAssignation } from '@/serverServices/getAssignation';
import { revalidateMedicalRecords } from '@/serverServices/getMedicalRecords';

const { dispatch, getState } = store

export const handleDirection = ({ target: { value } }) => {
    return dispatch({ type: ordersTypes.HANDLE_PATIENT_DIRECTION, payload: value })
}

export const generalInputHandler = (type) => ({ target: { value } }) => {
    return dispatch({ type, payload: value })
}

export const setRecipe = (payload) => {
    return dispatch({ type: ordersTypes.SET_RECIPE, payload })
}

export const deleteStd = (helper) => {
    return dispatch({ type: ordersTypes.HANDLE_ORDERSTUDIES_STUDY, payload: helper })
}

export const setSendedRecipe = () => {
    return dispatch({ type: ordersTypes.SET_SENDED_RECIPE, payload: true })
}

export const setInvalidMedication = (value) => {
    return dispatch({ type: ordersTypes.SET_INVALID_MEDICATION_ERROR, payload: value})
}

export const setInvalidData = (value) => {
    return dispatch({ type: ordersTypes.SET_INVALID_DATA_ERROR, payload: value})
}

export const setInvalidCredential = (value) => {
    return dispatch({ type: ordersTypes.SET_INVALID_CREDENTIAL_ERROR, payload: value})
}

export const setDuplicatedMedication = (value) => {
    return dispatch({ type: ordersTypes.SET_DUPLICATED_MEDICATION_ERROR, payload: value})
}

export const setInvalidStructure = (value) => {
    return dispatch({ type: ordersTypes.SET_INVALID_STRUCTURE_ERROR, payload: value})
}

export const setInvalidAffiliateNumber = (value) => {
    return dispatch({ type: ordersTypes.SET_INVALID_AFFILIATE_NUMBER_ERROR, payload: value})
}

export function getEntity(os) {
    let entity = '751'
    if(/IOMA/i.test(os?.toUpperCase()?.trim())) {
        entity = '7001'
    } else if(/OSDIPP/i.test(os?.toUpperCase()?.trim())) {
        entity = '654'
    } else if(/MEDIFE/i.test(os?.toUpperCase()?.trim())) {
        entity = '633'
    } else if(/OSMECON/i.test(os?.toUpperCase()?.trim())) {
        entity = '605'
    } else if(/PROGRAMAS\sM[ÉE]DICOS/i.test(os?.toUpperCase()?.trim())) {
        entity = '735'
    } else if(/PODER\sJUDICIAL\sDE\sLA\sNACI[ÓO]N|OSPJN|PODERJUDICIAL|PODER\sJUDICIAL/i.test(os?.toUpperCase()?.trim())) {
        entity = '736'
    }  else if(/OSSEG/i.test(os?.toUpperCase()?.trim())) {
        entity = '755'
    } else if(/OBSBA/i.test(os?.toUpperCase()?.trim())) {
        entity = '696'
    } else if(/GALENO\sARGENTINA|GALENO/i.test(os?.toUpperCase()?.trim())) {
        entity = '622'
    } else if (/M[ÉE]DIC[UO]S/i.test(os?.toUpperCase()?.trim())){
        entity = '624'
    } else if(/DOSUBA/i.test(os?.toUpperCase()?.trim())) {
        entity = '751'
    }
    return entity 
}

export function validateAffiliate(affiliateNum) {
    return affiliateNum
        .split('')
        .filter((char) => regexNumbers.test(char))
        .reduce((acum, actual) => acum.concat(actual), '')
}

export async function postFarmalinkQuickPrescription(prescriptionData){
    const response = await farmalinkQuickRecipe(prescriptionData)
    return response
}

export async function farmalinkQuickRecipe(requestData) {
    try {
        const firebaseIdToken = await getFirebaseIdToken()
        const config = { headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${firebaseIdToken}` } }
        const endpoint = `${quick_farmalink}/createPrescription`
        const response = await axios.post(endpoint, requestData, config)
        if (response?.data?.created){
            await swal(
                'Receta generada!',
                'Se guardó la receta exitosamente.',
                'success'
            )
        }
        await revalidateGetAssignation()
        await revalidateMedicalRecords()

        return response           
    } catch (error) {
        const timestamp = new Date().toLocaleString();
        const logError = error.response?.data?.error;

        await handlePrescriptionError(logError, {
            swal,
            setInvalidCredential,
            setInvalidMedication, 
            setInvalidData,
            timestamp
        });

        dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: true})
    }
}

export async function postFarmalinkPrescription(dataPostFarmalink) {
    const { formData, assignationId, goTo, uid, asyncAttData, dependantUid } = dataPostFarmalink
    const state = getState()
    const { patient } = state.queries
    const { profile } = state.user
    const prescriptionData = {
        assignation_id: assignationId,
        entity: getEntity(formData.coverage?.name),         
        medicines: formData?.medicine,
        diagnosis: formData?.diagnosis,
        patient: { 
            corporate: formData.coverage?.name?.trim() || patient?.corporate_norm || `UMA ${process.env.NEXT_PUBLIC_COUNTRY}`,
            dni: asyncAttData ? asyncAttData?.patient?.dni : (patient?.dni || ''),
			fullname: asyncAttData ? asyncAttData?.patient?.fullname : (patient?.fullname || ''),
			chosenName: asyncAttData ? '' : (patient?.chosenName || ''),
            n_afiliado: formData.coverage?.afiliateId || '',
            plan: formData.coverage?.plan || '',
            uid,
            dependantUid,
        },
        providerUid: profile?.uid,
        validator: 'FARMALINK'
    }
    return await farmalinkRecipe(prescriptionData, goTo)
}

export async function farmalinkRecipe(requestData, goTo) {
    try {
        const token = await getFirebaseIdToken()
        const headers = { headers: { 'Content-Type': 'application/json','Authorization': `Bearer ${token}` }}
        const prescriptionId = await axios.post(farmalink, requestData, headers)
        
        try {
            await postPrescriptionInChat(requestData, prescriptionId.data.id)
        } catch (chatError) {
            console.error('Error al enviar notificación por chat:', chatError)
            // No interrumpimos el flujo principal si falla la notificación
        }
        
        setRecipeLoading(false)
        goTo('prescriptionList')
        return prescriptionId
    } catch (error) {
        errorHandler.report(error)
        const timestamp = new Date().toLocaleString();
        const logError = error?.response?.data?.exception?.message;

        await handlePrescriptionError(logError, {
            swal,
            setInvalidCredential,
            setInvalidMedication, 
            setInvalidData,
            timestamp
        });

        setRecipeLoading(false)
        dispatch({ type: 'SET_PRESCRIPTION_ERROR', payload: true})
    }
}

export async function postPrescriptionInChat(requestData, prescriptionId) {
    const firebaseIdToken = await getFirebaseIdToken()
    const uid = requestData.patient.uid
    const assignation_id = requestData.assignation_id
    const dependant_uid = false

    
    const config = { 
        headers: { 
            'Content-Type': 'application/json', 
            'Authorization': `Bearer ${firebaseIdToken}`,
            'uid': uid
        }
    }
    let prescriptionAdvice = '**Se te generó una receta**, por favor, valida los datos. <br />'
    prescriptionAdvice += '**Medicamentos:** <br />'
    
    requestData.medicines.forEach((med) => {
        prescriptionAdvice += ` - ${med.productName} (${med.drugName}): ${med.presentationName} <br /> `
    })

    prescriptionAdvice += `**Paciente:** ${requestData.patient.fullname || '-sin nombre-'} <br />`
    if(requestData?.patient?.chosenName && requestData?.patient?.chosenName !== '') {
        prescriptionAdvice += ` **Nombre escogido:** ${requestData.patient.chosenName} <br />`
    }
    prescriptionAdvice += `**Afiliado de:** ${requestData.patient.corporate} **Nro:** ${requestData.patient.n_afiliado} <br />`

  const prescriptionBody = { id: prescriptionId , country: 'AR', rewrite: false }
	const headers = { 'Content-type': 'application/json', 'Authorization': `Bearer ${firebaseIdToken}` }
	const pdfResponse = await axios.post(download_recipe, prescriptionBody, {headers})

    if(pdfResponse.data.url) {
        prescriptionAdvice += `<a href="${pdfResponse.data.url}" target="_blank" style="text-decoration: underline;">Link de descarga</a>`
    }

    let dataToSave = {
        assignation_id,
        'country': process.env.NEXT_PUBLIC_COUNTRY,
        'doctorUid': requestData.providerUid,
        dependant_uid,
        'rol': 'doctor',
        'text': prescriptionAdvice || '',
        uid
    }
    await axios.post(chat, dataToSave, config)

}

export const handleInputMedicinesMX = async (target) => {
    const state = getState()
    const { searchResult } = state.prescriptions
    const selectedDrug = searchResult.find((dr) => `${dr.id}` === `${target.value}`)
    if (!selectedDrug) return null
    return { productId: selectedDrug.id, productName: selectedDrug.name, productEan: `${selectedDrug.ean}` }
}

export const handleInputMedicinesAR = (target) => {
    if(target) {
        const state = getState()
        const { searchResult } = state.prescriptions
        const alfabetNum = target?.value
        let selectedDrug
        if(searchResult.output) {
            selectedDrug = searchResult.output.find((dr) => `${dr.alfabetRegisterNum}` === `${alfabetNum}`)
        } else {
            selectedDrug = searchResult.find((dr) => `${dr.alfabetRegisterNum}` === `${alfabetNum}`)
        }
        if (!selectedDrug) return null
        return selectedDrug
    }
}

export const handleInputMedicines = (e) => {
    switch (process.env.NEXT_PUBLIC_COUNTRY) {
    case 'MX': return handleInputMedicinesMX(e)
    case 'AR': return handleInputMedicinesAR(e)
    default:
        break
    }
}

export const removeDrug = (itemIndex, drugs) => {
    let temp = drugs.filter((a, index) => index !== itemIndex)
    return dispatch({ type: ordersTypes.HANDLE_RECIPE_ADD_DRUG, payload: temp })
}

export const resetRecipe = () => {
    dispatch({ type: ordersTypes.SET_RECIPE, payload: { prescriptionDate: '', medicines: [] } })
    dispatch({ type: ordersTypes.SET_SENDED_RECIPE, payload: false })
    return true
}

export const resetMedIndications = () => {
    return dispatch({ type: ordersTypes.HANDLE_RECIPE_CURRENTMED, payload: { cantidad: 1, indicaciones: '' } })
}

export const warnUserFromPaste = () => {
    return swal('Aviso', 'No se puede copiar y pegar en este campo.', 'warning')
}

export const removeMedication = (medicine_id) => {
    switch (process.env.NEXT_PUBLIC_COUNTRY) {
    case 'AR': return handleCancelMedicationAR(medicine_id)
    case 'MX': return handleCancelMedicationMX(medicine_id)
    default:
        break
    }
}

const handleCancelMedicationAR = (medicine_id) => {
    setRecipeLoading(true)
    const state = getState()
    let medicines = state.prescriptions.temp.medicines
    let newMeds = medicines.filter(med => med.alfabetRegisterNum !== medicine_id)
    dispatch({ type: 'HANDLE_RECIPE_ADD_DRUG', payload: newMeds })
    setRecipeLoading(false)
}

const handleCancelMedicationMX = async (medicine_id) => {
    setRecipeLoading(true)
    const state = getState()
    // TODO: CANCEL CON MEDIKIT QUEDA PARA POST PRIMER DEPLOY, YA EN PROD NO ESTA ASI.
    let medicines = state.prescriptions.temp.medicines
    let newMeds = medicines.filter(med => med.productId !== medicine_id)
    dispatch({ type: 'HANDLE_RECIPE_ADD_DRUG', payload: newMeds })
    setRecipeLoading(false)
}

export const setRecipeLoading = (value) => {
    return dispatch({ type: ordersTypes.HANDLE_RECIPE_LOADING, payload: value })
}
export const queryGetPrescripciones = (uid) => {
    return dispatch => {
        setRecipeLoading(true)

        const prescriptionsRef = collection(doc(firestore, 'events', 'prescriptions'), 'MX')
        const q = query(prescriptionsRef, where('uid', '==', uid))

        onSnapshot(q, { includeMetadataChanges: true }, snapshot => {
            let newElements = []
            snapshot.docs.forEach(doc => {
                let data = doc.data()
                data.items.forEach(item => {
                    let rta = { ...item, id: doc.id }
                    newElements.push(rta)
                })
            }, err => {
                errorHandler.report(err)
            })

            dispatch({ type: ordersTypes.SNAP_RECETAS_HISTORY, payload: newElements })
            setRecipeLoading(false)
        })
    }
}

export async function saveOrderDB(coverage, items, details, patient, doctor, att_id, diagnosis, signature_medikit ) {
    const now = moment().format('YYYYMMDDHHMMss')
    const isDependant = !!patient?.dependant_uid

    const body = {
        uid: isDependant ? patient?.core_id : patient?.id || patient?.core_id || patient?.uid || '',
        country: process.env.NEXT_PUBLIC_COUNTRY,
        date: now,
        items,
        indications: details,
        diagnosis: diagnosis,
        patient: {
            chosenName: patient?.chosenName || '',
            corporate: coverage?.name ? coverageNameFormater(coverage.name) || '' : '',
            dni: patient?.dni || '',
            fullname: patient?.fullname || '',
            plan: coverage?.plan || '',
            n_afiliado: coverage?.afiliateId || '',
            credentialVersion: coverage?.credentialVersion || '',
            dependantUid: patient?.dependant_uid
        },
        provider: {
            dni: doctor?.dni || '',
            fullname: doctor?.fullname || '',
            matricula: doctor?.matricula || '',
            signature: doctor?.signature || signature_medikit || '',
            uid: doctor?.id || doctor?.uid || doctor?.core_id || ''
        },
        hc: `${patient?.dni}/${att_id}`,
        medical_record_id: `${att_id  || ''}`,
    }
    await axios.post(studies, body)
}

export const setPrescriptionError = (prescriptionFailed) => ({
    type: ordersTypes.SET_PRESCRIPTION_ERROR,
    payload: prescriptionFailed
});
  