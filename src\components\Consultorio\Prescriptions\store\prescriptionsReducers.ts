import * as prescriptionTypes from './prescriptionsTypes';
import { PrescriptionState, PrescriptionAction } from './prescriptionTypes';

// Estado inicial del reducer
export const initialPrescriptionReducerState: PrescriptionState = {
  n_afiliado: '',
  corporate: '',
  plan: '',
  currentMedicine: {
    cantidad: 1,
  },
  orderStudies: [],
  labStudiesFB: [],
  drugOptions: [],
  orderSpecifications: '',
  diagnosis: '',
  direccion: '',
  selected_logo: '',
  loadingRecipe: false,
  loadingSearch: false,
  searchResult: [],
  token_consulta_medikit: '',
  isUnionPersonal: false,
  recetasHistory: [],
  recipes: [],
  temp: {
    os: '',
    plan: '',
    number: '',
    medicines: [],
    orders: []
  },
  signature_medikit: '',
  prescriptionFailed: false,
  invalidMedication: false,
  invalidData: false,
  invalidCredential: false,
  duplicatedMedication: false,
  invalidStructure: false,
  invalidAffiliateNumber: false
}

/**
 * Reducer para manejar el estado de prescripciones médicas
 * 
 * @param state - Estado actual de prescripciones
 * @param action - Acción a ejecutar
 * @returns Estado actualizado de prescripciones
 */
export default function prescriptionReducer(
  state: PrescriptionState = initialPrescriptionReducerState, 
  action: PrescriptionAction
): PrescriptionState {
  switch (action.type) {
    case prescriptionTypes.SET_RECIPE: {
      const recipe = { ...state.recipe } || {};
      recipe[action.payload.param] = action.payload;
      return { ...state, recipe };
    }
    case prescriptionTypes.GET_LABSTUDIES:
      return { ...state, labStudiesFB: action.payload };
    case prescriptionTypes.HANDLE_ORDERSTUDIES_STUDY:
      return { ...state, orderStudies: action.payload };
    case prescriptionTypes.HANDLE_ORDERS_SPECIFICATIONS:
      return { ...state, orderSpecifications: action.payload};
    case prescriptionTypes.HANDLE_ORDERS_DIAGNOSIS:
      return { ...state, diagnosis: action.payload};
    case prescriptionTypes.HANDLE_PATIENT_DIRECTION:
      return { ...state, direccion: action.payload };
    case prescriptionTypes.HANDLE_SELECT_LOGO:
      return { ...state, selected_logo: action.payload };
    case prescriptionTypes.HANDLE_LOADING_SEARCH:
      return { ...state, loadingSearch: action.payload };
    case prescriptionTypes.HANDLE_SEARCH_RESULT:
      return { ...state, searchResult: action.payload };
    case prescriptionTypes.HANDLE_REMOVE_DATA_PRESCRIPTION:
      return { ...state, temp: {...state.temp, medicines: [], plan: '', number: '', os: ''}};
    case prescriptionTypes.HANDLE_RECIPE_ADD_DRUG:
      return { ...state, temp: { ...state.temp, medicines: action.payload }};
    case prescriptionTypes.HANDLE_RECIPE_CHECK:
      return { ...state, temp: { ...state.temp, recipeCheck: action.payload }};
    case prescriptionTypes.HANDLE_RECIPE_CURRENTMED:
      return { ...state, temp: { ...state.temp, currentMedicine: {...state.temp.currentMedicine, ...action.payload}}};
    case prescriptionTypes.HANDLE_RECIPE_DETAILS:
      return { ...state, temp: { ...state.temp, currentMedicine: {...state.temp.currentMedicine , details: action.payload }}};
    case prescriptionTypes.HANDLE_RECIPE_QUANTIY: {
      const cantidad = typeof action.payload === 'string' ? parseInt(action.payload, 10) : action.payload;
      return { ...state, temp: { ...state.temp, currentMedicine: {...state.temp.currentMedicine , cantidad }}};
    }
    case prescriptionTypes.HANDLE_OS_PRESELECTED:
      return { ...state, temp: {...state.temp, os: action.payload.os, number: action.payload.number, plan: action.payload.plan}};
    case prescriptionTypes.HANDLE_TEMP_SOCIALWORK:
      return { ...state, temp: { ...state.temp, os: action.payload }};
    case prescriptionTypes.HANDLE_TEMP_NAFFILIATE:
      return { ...state, temp: { ...state.temp, number: action.payload }};
    case prescriptionTypes.HANDLE_TEMP_PLANAFFILIATE:
      return { ...state, temp: { ...state.temp, plan: action.payload }};
    case prescriptionTypes.HANDLE_RECIPE_LOADING:
      return { ...state, loadingRecipe: action.payload };
    case prescriptionTypes.HANDLE_DRUG_OPTIONS:
      return { ...state, drugOptions: action.payload };
    case prescriptionTypes.SNAP_RECETAS_HISTORY:
      return { ...state, recetasHistory: action.payload };
    case prescriptionTypes.SET_MEDIKIT_CONSULTA_TOKEN:
      return { ...state, token_consulta_medikit: action.payload};
    case prescriptionTypes.SET_MEDIKIT_SIGNATURE:
      return { ...state, signature_medikit: action.payload};
    case prescriptionTypes.SET_MEDIKIT_SIGNATURE_HASH:
      return { ...state, hash_medikit: action.payload};
    case prescriptionTypes.SET_MEDIKIT_SECURITY_HASH:
      return { ...state, securityHash: action.payload};
    case prescriptionTypes.RESET_MEDIKIT:
      return { ...initialPrescriptionReducerState };
    case prescriptionTypes.SET_PRESCRIPTION_ERROR:
      return {...state, prescriptionFailed: action.payload};
    case prescriptionTypes.SET_INVALID_MEDICATION_ERROR:
      return {...state, invalidMedication: action.payload};
    case prescriptionTypes.SET_INVALID_DATA_ERROR:
      return {...state, invalidData: action.payload};
    case prescriptionTypes.SET_INVALID_CREDENTIAL_ERROR:
      return {...state, invalidCredential: action.payload};
    case prescriptionTypes.SET_DUPLICATED_MEDICATION_ERROR:
      return {...state, duplicatedMedication: action.payload};
    case prescriptionTypes.SET_INVALID_STRUCTURE_ERROR:
      return {...state, invalidStructure: action.payload};
    case prescriptionTypes.SET_INVALID_AFFILIATE_NUMBER_ERROR:
      return {...state, invalidAffiliateNumber: action.payload};
    default:
      return state;
  }
}