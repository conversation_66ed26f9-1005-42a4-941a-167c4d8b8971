@use '@/styles/global/Vars.scss';

.container{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.iconContainer{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 48px;
    height: 48px;
    padding: 12px;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 50%;
    margin-bottom: 6px;
    svg{
        cursor: pointer;
        width: 100%;
        height: 100%;
        background: Vars.$white-color;
        color: Vars.$uma-primary;
    }
}
.focused{
    background-color: Vars.$uma-primary;
    svg{
        background-color: Vars.$uma-primary;
        color: Vars.$white-color;
    }
}