@use 'src/styles/index.scss';
@use '@/styles/global/Vars.scss';

.container{
    width: 30%;
    height: 240px;
    padding: 10px;
	background-color: Vars.$white-color;
	box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
	border-radius: 20px;
    text-align: center;
}
.btnContainer{
    display: flex;
    flex-direction: column;
    align-items: center;
}
.btnIconPlus{
    border-radius: 50%;
    width: max-content;
    margin: 16px auto;
    box-shadow: 0px 4px 4px rgb(0 0 0 / 25%);
    width: 54px;
    height: 54px;
    background: #EDF6FF;
    &:hover{
        cursor: pointer;
    }
}