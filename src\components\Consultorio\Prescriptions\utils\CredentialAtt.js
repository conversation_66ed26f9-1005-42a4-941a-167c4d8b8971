import React, {useState} from 'react'
import {Row, Button} from '@umahealth/occipital-ui'
import FileView from '../../Attached/Components/FileView'

function CredentialAtt({file}) {
	const [visible, setVisible] = useState(false)

	const fileVisible = () => {
		if(file.name === 'credential'){
			setVisible(true)
		}	
	}

	return (
		<>
			<Row spacing='end'>
				<Button
					key={file.date}
					action={fileVisible} 
					color="secondary"
					textSize="xs"
					type="text"
				> Ver Credencial</Button>
			</Row>						
			{visible === true && <FileView att={file} /> }
		</>
	)
}

export default CredentialAtt