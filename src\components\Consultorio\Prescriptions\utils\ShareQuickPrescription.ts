import axios from 'axios'
import swal from 'sweetalert'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { download_recipe, send_quick_prescription_ws, send_quick_prescription_mail } from '@/config/endpoints'
import { errorHandler } from '@/config/stackdriver'

export const sendWs = async (ws: string, prescriptionId: string, setLoading: React.Dispatch<React.SetStateAction<boolean>>) =>{
	try {
		setLoading(true)
		const firebaseIdToken = await getFirebaseIdToken()
		const data = {
			destination : ws,
			template: 'quick_prescription_link',
			prescriptionId: prescriptionId
		}
		const wsResponse = await axios.post(send_quick_prescription_ws, data, {headers:{ 'content-type': 'application/json', 'Authorization': `Bearer ${firebaseIdToken}`}})
		if(wsResponse) return swal('Receta enviada', 'La receta ya ha sido enviada al whatsapp del paciente', 'success')
	} catch (error : any) {
		const timestamp = new Date().toLocaleString();
		errorHandler?.report(error)
		console.error(error)
		return swal(`Error: ${error?.response?.data?.error || 'No hemos podido enviar la receta'}`, 'Por favor, intente nuevamente.', `Detalle: ${timestamp}`,  'warning')
	} finally {
		setLoading(false)
	}
}
export const sendEmail = async (email: string, prescriptionId: string, setLoading: React.Dispatch<React.SetStateAction<boolean>>) => {
	try {
		setLoading(true)
		const firebaseIdToken = await getFirebaseIdToken()
		const dataEmail = {
			email,
			prescriptionId
		}
		const emailResponse = await axios.post(send_quick_prescription_mail, dataEmail, { headers: { 'content-type': 'application/json', 'Authorization': `Bearer ${firebaseIdToken}` } })
		if(emailResponse?.data?.sent){
			return swal('Email enviado', 'La receta ya ha sido enviada al email del paciente', 'success')
		}
	} catch (error : any) {
		const timestamp = new Date().toLocaleString();
		errorHandler?.report(error)
		console.error(error)
		return swal(`Error: ${error?.response?.data?.error || 'No hemos podido enviar el email'}`, 'Por favor, intente nuevamente.', `Detalle: ${timestamp}`, 'warning')
	} finally{
		setLoading(false)
	}
}
export const downloadPdf = async (id: string, setLoading: React.Dispatch<React.SetStateAction<boolean>>) =>{
	try {
		setLoading(true)
		const firebaseIdToken = await getFirebaseIdToken()
		const prescriptionBody = {id , country: 'AR'}
		const headers = { 'Content-type': 'application/json', 'Authorization': `Bearer ${firebaseIdToken}` }
		const pdfResponse = await axios.post(download_recipe, prescriptionBody, {headers})
		const prescriptionLink = document.createElement('a')
		prescriptionLink.href = pdfResponse.data.url
		prescriptionLink.target = '_blank'
		prescriptionLink.setAttribute('download', `Receta_${id}.pdf`)
		document.body.appendChild(prescriptionLink)
		prescriptionLink.click()
	} catch (error : any) {
		const timestamp = new Date().toLocaleString();
		errorHandler?.report(error)
		console.error(error)
		return swal('Ocurrió un problema', `No se pudo generar la receta (PDF).`, `Detalle: ${timestamp}`, 'warning')
	} finally {
		setLoading(false)
	}	
}


