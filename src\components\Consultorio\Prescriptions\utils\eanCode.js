import moment from 'moment-timezone'
const timezone = 'America/Mexico_City'

const calculateEAN13 = (num) => {
	const numbers = num.split('')
	let oddSum = 0,
		evenSum = 0
	numbers.map((number, index) => {
		if ((index + 1) % 2 === 0) {
			oddSum += parseInt(number)
		} else {
			evenSum += parseInt(number)
		}
		return ''
	})
	const digit = (10 - ((3 * oddSum + evenSum) % 10)) % 10
	return `${num}${digit}`
}

const genEAN13 = () => {
	const date = moment().tz(timezone).format('MMHHmmSS')
	const baseCode = `9203${date}`
	const EAN13 = calculateEAN13(baseCode)
	return EAN13
}

const genGS1_128 = (string) => {
	const str = string
	let arrNums = str.split('')
	let checkNum = 2
	let sumResult = 0
	for (let i = arrNums?.length - 1; 0 <= i; i--) {
		const num = parseInt(arrNums[i])
		if (checkNum !== 7) {
			sumResult += num * checkNum
			checkNum++
		} else {
			sumResult += num * checkNum
			checkNum = 2
		}
	}
	const moduleSum = sumResult % 11
	const moduleSubstract = 11 - moduleSum
	if (moduleSubstract < 10) {
		return `${string}${moduleSubstract}`
	} else if (moduleSubstract === 11) {
		return `${string}0`
	} else {
		return `${string}1`
	}
}

const generateRecipeNumber = () => {
	return genEAN13()
}

module.exports = {
	calculateEAN13,
	genEAN13,
	generateRecipeNumber,
	genGS1_128
}