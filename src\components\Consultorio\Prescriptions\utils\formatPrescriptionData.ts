import { IRecipeForm, IPrescriptionData } from './../Interfaces/Interfaces'
import { getEntity } from '../store/prescriptionsActions'

interface IProviderData {
	provider?: string 
	providerUid?: string
}
interface IValidator extends IProviderData {
	validator?: string
	integrations?: {
		provider: string
	}
}

type IValidators = IValidator | IProviderData

export const baseDataPrescriptions = (prescription: IRecipeForm, assignationId?: string | undefined): IPrescriptionData => ({
	medicines: [{
		...prescription?.medicine?.medicine,
		quantity: prescription?.medicine?.quantity,
		details: prescription?.medicine?.details,
		addNew: true
	}],
	entity: getEntity(prescription?.coverage?.name),
	diagnosis: prescription?.diagnosis,
	patient: {
		dni: prescription?.patient?.dni ?? '',
		fullname: prescription?.patient?.fullname ? prescription.patient.fullname : `${prescription?.patient?.name} ${prescription?.patient?.surname}`,
		chosenName: prescription?.patient?.chosenName,
		n_afiliado: prescription?.coverage?.afiliateId,
		plan: prescription?.coverage?.plan,
		corporate: prescription?.coverage?.name?.toUpperCase(),
		...(prescription?.patient?.uid?.length && {uid:prescription?.patient?.uid}),
	},
	...(assignationId?.length && {assignation_id: assignationId}),
})

export const formatPrescriptionData = (prescription : IRecipeForm, assignationId: string) => (validator?: IValidators) => (
	{
		...baseDataPrescriptions(prescription, assignationId),
		...validator
	}
)
