/**
 * @file PrescriptionErrorHandler.ts
 * @description Módulo para manejar errores de validación de recetas.
 */

/**
 * Enum para los tipos de error.
 */
export enum ErrorType {
  SECURITY = 'ERROR DE SEGURIDAD',
  SYSTEM = 'ERROR DE SISTEMA',
  CHANNEL = 'CANAL TECNOLOGICO NO HABILITADO',
  INVALID_CREDENTIAL = 'CREDENCIAL INVALIDA',
  CREDENTIAL_DISABLED = 'CREDENCIAL DADA DE BAJA',
  IMPOSSIBLE_REGISTRATION = 'ALTA IMPOSIBLE',
  INVALID_CODE = 'CODIGO INFORMADO INEXISTENTE (PRODUCTO / DROGA / PRESENTACION)',
  INVALID_RELATION = 'RELACION INEXISTENTE',
  INVALID_FORMAT = 'FORMATO INVALIDO',
  INVALID_DATE = 'FECHA VENCIMIENTO INVALIDA',
  UNDEFINED_ERROR = 'undefined undefined',
  EMPTY_AFFILIATE = 'Validation failed. patient.n_afiliado is not allowed to be empty'
}

/**
* Interfaz para la configuración de SweetAlert.
*/
export interface SwalConfig {
icon: string;
title: string;
text?: string;
button?: string;
}

/**
* Tipo para la función de SweetAlert.
*/
export type SwalFunction = (config: SwalConfig) => Promise<void>;

/**
* Interfaz para los mensajes de error.
*/
export interface ErrorMessage {
title: string;
text?: string;
button?: string;
getText?: (timestamp: string) => string;
}

/**
* Mensajes de error predefinidos.
*/
export const ERROR_MESSAGES: Record<string, ErrorMessage> = {
general: {
  title: 'La receta no pudo ser validada correctamente.',
  text: 'Por favor, intente nuevamente. Si el error persiste, comuníquese con Soporte.',
  button: 'Reintentar'
},
impossible: {
  title: 'La receta no pudo ser validada correctamente (Alta imposible).',
  text: 'Por favor, comuníquese con Soporte',
  button: 'Reintentar'
},
unavailable: {
  title: 'La receta no puede ser validada en este momento.',
  text: 'Por favor, vuelva a intentarlo dentro de unos minutos. Si el error persiste, comuníquese con Soporte',
  button: 'Cerrar'
},
invalidLicense: {
  title: 'El número de afiliado es incorrecto o no se registra en la base de datos de Farmalink al momento de la receta.',
  text: 'Revise los datos de afiliación, reintente en unas horas en caso de que esten correctamente',
  button: 'Cerrar'
},
emptyAffiliate: {
  title: 'El número de afiliado no puede ser vacio.',
  getText: (timestamp: string) =>
    `No se pudo generar la receta - Farmalink. Detalle: ${timestamp}. Por favor, intente nuevamente.`
}
};

/**
* Claves de las acciones de error.
*/
export type ErrorActionKey =
| 'showGeneralError'
| 'setInvalidCredential'
| 'setInvalidMedication'
| 'setInvalidData';

/**
* Agrupación de errores según la acción a ejecutar.
*/
export const ERROR_GROUPS: Record<ErrorActionKey, ErrorType[]> = {
showGeneralError: [
  ErrorType.SECURITY,
  ErrorType.SYSTEM,
  ErrorType.CHANNEL
],
setInvalidCredential: [
  ErrorType.INVALID_CREDENTIAL,
  ErrorType.CREDENTIAL_DISABLED
],
setInvalidMedication: [
  ErrorType.INVALID_CODE,
  ErrorType.INVALID_RELATION
],
setInvalidData: [
  ErrorType.INVALID_FORMAT,
  ErrorType.INVALID_DATE
]
};

/**
* Interfaz para los manejadores que se utilizan al tratar un error.
*/
export interface ErrorHandlers {
swal: SwalFunction;
timestamp: string;
setInvalidCredential: (isInvalid: boolean) => void;
setInvalidMedication: (isInvalid: boolean) => void;
setInvalidData: (isInvalid: boolean) => void;
}

/**
* Tipo de unión para los posibles parámetros de los manejadores de error.
*/
export type ErrorHandlerParam = ErrorHandlers;

/**
* Mapea las acciones de error a sus respectivas funciones.
*/
export const errorActionHandlers: Record<
ErrorActionKey,
(handler: ErrorHandlerParam) => Promise<void> | void
> = {
/**
 * Muestra un error general utilizando SweetAlert.
 * @param handlers - Objeto con funciones de manejo de error.
 */
showGeneralError: async (handlers: ErrorHandlerParam): Promise<void> => {
  await handlers.swal({
    icon: 'warning',
    ...ERROR_MESSAGES.general
  });
},
/**
 * Marca la credencial como inválida.
 * @param handlers - Objeto con funciones de manejo de error.
 */
setInvalidCredential: async (handlers: ErrorHandlerParam) => {
  handlers.setInvalidCredential(true);
  await handlers.swal({
    icon: 'warning',
    ...ERROR_MESSAGES.invalidLicense
  });
},
/**
 * Marca la medicación como inválida.
 * @param handlers - Objeto con funciones de manejo de error.
 */
setInvalidMedication: async (handlers: ErrorHandlerParam) => {
  handlers.setInvalidMedication(true);
  await handlers.swal({
    icon: 'warning',
    ...ERROR_MESSAGES.general
  });
},
/**
 * Marca los datos como inválidos.
 * @param handlers - Objeto con funciones de manejo de error.
 */
setInvalidData: async (handlers: ErrorHandlerParam) => {
  handlers.setInvalidData(true);
  await handlers.swal({
    icon: 'warning',
    ...ERROR_MESSAGES.general
  });
}
};

/**
* Función auxiliar para mostrar un error mediante SweetAlert.
*
* @param swalFn - Función de SweetAlert.
* @param config - Configuración del mensaje a mostrar.
*/
const displaySwalError = async (
swalFn: SwalFunction,
config: Omit<SwalConfig, 'icon'>
): Promise<void> => {
await swalFn({
  icon: 'warning',
  ...config
});
};

/**
* Maneja el error de la prescripción y ejecuta la acción correspondiente.
*
* @param logError - Tipo de error ocurrido.
* @param handlers - Objeto con funciones y datos necesarios para manejar el error.
*/
export const handlePrescriptionError = async (
logError: ErrorType,
handlers: ErrorHandlers
): Promise<void> => {
const { swal, timestamp } = handlers;

// Verificar si el error pertenece a algún grupo predefinido
for (const [actionKey, errorTypes] of Object.entries(ERROR_GROUPS) as [
  ErrorActionKey,
  ErrorType[]
][]) {
  if (errorTypes.includes(logError)) {
    const actionHandler = errorActionHandlers[actionKey as ErrorActionKey];
    await actionHandler(handlers);
    return;
  }
}

// Manejar casos especiales que no pertenecen a un grupo predefinido
switch (logError) {
  case ErrorType.IMPOSSIBLE_REGISTRATION:
    await displaySwalError(swal, ERROR_MESSAGES.impossible);
    break;
  case ErrorType.UNDEFINED_ERROR:
    await displaySwalError(swal, ERROR_MESSAGES.unavailable);
    break;
  case ErrorType.EMPTY_AFFILIATE:
    await displaySwalError(swal, {
      ...ERROR_MESSAGES.emptyAffiliate,
      text: ERROR_MESSAGES.emptyAffiliate.getText
        ? ERROR_MESSAGES.emptyAffiliate.getText(timestamp)
        : ''
    });
    break;
  default:
    await displaySwalError(swal, {
      title: logError,
      text: `No se pudo generar la receta - Farmalink. Detalle: ${timestamp}. Por favor, intente nuevamente.`
    });
}
};

export default handlePrescriptionError;