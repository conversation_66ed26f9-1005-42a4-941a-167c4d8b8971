import { IMedicalRecord, orderOrPrescription } from '@umahealth/entities'
import { Timestamp } from '@/config/firebase'

export const updatePrescriptions = (mrInView: IMedicalRecord, setMrInView: React.Dispatch<React.SetStateAction<IMedicalRecord<Timestamp> | undefined>>, id: string) => {
	const prescriptions = mrInView.mr.prescriptions
	const newPrescription: orderOrPrescription<Timestamp> = {
		id,
		items: [''],
		type: 'prescriptions' 
	}
	const prescriptionUpdated = prescriptions ? [...prescriptions, newPrescription] : [newPrescription]
	setMrInView({ ...mrInView, mr: { ...mrInView.mr , prescriptions: prescriptionUpdated }})
}
