import React, { useEffect } from 'react'
import { Spacer } from 'occipital-new'
import { useAppSelector } from '@/store/hooks'
import ResumentStt from './components/ResumenSTT'
import TranscriptStt from './components/TranscriptStt'
import useMutatedDiagnosisAI from '@/services/reactQuery/useDiagnosisAI'
import useMutatedSaveDiagnosis from '@/services/reactQuery/useSaveDiagnosis'
import { useSearchParams } from 'next/navigation'

const Stt = () => {
	const diagnosisAI = useMutatedDiagnosisAI()
	const saveDiagnosis = useMutatedSaveDiagnosis()
	const { profile } = useAppSelector(state => state.user)
	const searchParams = useSearchParams()
	const assignationId = searchParams.get('assignationId') as string

	const autocompleteWithAI = async (motive: string) => {
		if (!motive?.length) return

		diagnosisAI.mutate({
			incidente_id: assignationId,
			transcription: motive
		})
	}

	useEffect(() => {
		if (!diagnosisAI.isSuccess) return

		const resumenDiagnosis = {
			eventId: assignationId,
			providerId: profile.uid,
			input: diagnosisAI?.data?.data?.input?.transcription,
			output: JSON.stringify(diagnosisAI?.data?.data?.output),
		}
		saveDiagnosis.mutate(resumenDiagnosis)
	}, [diagnosisAI.isSuccess])

	return (
		<>
			<TranscriptStt autocompleteWithAI={autocompleteWithAI} />
			<Spacer value="8px" />
			{diagnosisAI.isLoading && <strong>Generando resumen...</strong>}
			{diagnosisAI.isError && <strong>No se pudo generar un resumen</strong>}

			{diagnosisAI.isSuccess && (
				<ResumentStt resumen={diagnosisAI.data?.data?.output} assignationId={assignationId} providerUid={profile.uid} />
			)}
		</>
	)
}

export default Stt