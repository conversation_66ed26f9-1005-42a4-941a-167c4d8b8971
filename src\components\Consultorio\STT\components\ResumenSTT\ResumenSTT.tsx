import React from 'react'
import { Spacer } from 'occipital-new'
import { BiClipboard } from 'react-icons/bi'
import './styles.css'

export type Resumen = {
	antecedentes: string;
	evolucion: string;
	motivos_de_consulta: string;
	plan_terapeutico: string;
};

type Props = {
	resumen: Resumen;
	assignationId: string;
	providerUid: string;
};

const ResumentStt = ({ resumen }: Props) => {
	const trackClipBoard = (text: string) => {
		navigator.clipboard.writeText(text)
	}

	return (
		<div>
			<Spacer value="8px" />
			<div className="sectionTitle pointer">
				<label className="pointer">Resumen</label>
			</div>
			<Spacer value="4px" />
			<div className="container__resumen">
				{!!resumen?.motivos_de_consulta?.length && (
					<>
						<div className="containerTitle_resumen">
							<strong>Motivos de consultas</strong>
							<BiClipboard
								size={22}
								onClick={() =>
									trackClipBoard(
										resumen.motivos_de_consulta
									)
								}
								className="clipboardIcon"
							/>
						</div>
						<p>{resumen.motivos_de_consulta}</p>
						<Spacer value="4px" />
					</>
				)}
				{!!resumen?.antecedentes?.length && (
					<>
						<div className="containerTitle_resumen">
							<strong>Antecedentes</strong>
							<BiClipboard
								size={22}
								onClick={() =>
									trackClipBoard(
										resumen.antecedentes
									)
								}
								className="clipboardIcon"
							/>
						</div>
						<p>{resumen.antecedentes}</p>
						<Spacer value="4px" />
					</>
				)}
				{!!resumen?.evolucion?.length && (
					<>
						<div className="containerTitle_resumen">
							<strong>Evolución</strong>
							<BiClipboard
								size={22}
								onClick={() =>
									trackClipBoard(
										resumen.evolucion
									)
								}
								className="clipboardIcon"
							/>
						</div>
						<p>{resumen?.evolucion}</p>
						<Spacer value="4px" />
					</>
				)}

				{!!resumen?.plan_terapeutico?.length && (
					<>
						<div className="containerTitle_resumen">
							<strong>Plan terapeutico</strong>
							<BiClipboard
								size={22}
								onClick={() =>
									trackClipBoard(
										resumen.plan_terapeutico
									)
								}
								className="clipboardIcon"
							/>
						</div>
						<p>{resumen.plan_terapeutico}</p>
						<Spacer value="4px" />
					</>
				)}
			</div>
		</div>
	)
}

export default ResumentStt
