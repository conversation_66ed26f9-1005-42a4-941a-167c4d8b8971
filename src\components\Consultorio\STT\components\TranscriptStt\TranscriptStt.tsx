import React, { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON>, But<PERSON> } from 'occipital-new'
import { useSearchParams } from 'next/navigation'
import { BsFillMicFill } from 'react-icons/bs'
import { FiPauseCircle } from 'react-icons/fi'
import './styles.css'

type Props = {
	autocompleteWithAI(transcript: string): void;
}

const TranscriptStt = ({ autocompleteWithAI }: Props) => {
	const isMounted = useRef(true)
	const [value, setValue] = useState('')
	const [isListening, setIsListening] = useState(false)
	const [editTranscriptions, setEditTranscriptions] = useState(false)
	const searchParams = useSearchParams()
	const assignationId = searchParams.get('assignationId')

	const speechToTextInstance = () => {
		const SpeechRecognition = typeof window !== undefined && (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
		const recognition = new SpeechRecognition()
		recognition.lang = 'es-AR'
		recognition.continuous = true
		recognition.interimResults = false
		recognition.maxAlternatives = 1
		return recognition
	}
	const recognizer = speechToTextInstance()

	useEffect(() => {
		if (!isListening) return

		recognizer.start()
		recognizer.onresult = (event: { results: string | any[]; }) => {
			const { transcript } = event.results[event.results?.length - 1][0]
			saveStt(transcript)
		}

		recognizer.onend = () => {
			recognizer.start()
			setIsListening(true)
		}

		return () => {
			recognizer.stop()
			recognizer.onend = () => {
				recognizer.stop()
				setIsListening(false)
			}
		}
	}, [isListening])

	const saveStt = (inputValue: string) => {
		if (!inputValue?.length) return
		setValue((prev) => {
			const prevValue = `${prev} ${inputValue}`
			return prevValue.trim()
		})
	}

	const resetValue = () => {
		setIsListening(false)
		setEditTranscriptions(false)
		setValue('')
		localStorage.removeItem(`transcription_${assignationId}`)
	}

	const generateResumen = () => {
		setIsListening(false)
		autocompleteWithAI(value)
	}

	useEffect(() => {
		const valueLocalStorage = localStorage.getItem(`transcription_${assignationId}`) ?? ''
		if (valueLocalStorage?.length && isMounted.current) {
			isMounted.current = false
			return setValue(valueLocalStorage)
		}

		if (value?.length) {
			localStorage.setItem(`transcription_${assignationId}`, value)
		} else {
			localStorage.removeItem(`transcription_${assignationId}`)
		}

	}, [value])

	return (
		<>
			<div className='container__transcription'>
				<div className="sectionTitle pointer">
					<label className="pointer">Transcripcion</label>
				</div>
				<Spacer value="8px" />
				<span>{isListening && !value?.length && 'Escuchando ...'}</span>
				<span>{!isListening  && !value?.length && 'Haga click en el micrófono para comenzar a grabar'}</span>
				{!editTranscriptions && (isListening || !!value?.length) && <span>{value}</span> }
				{editTranscriptions && !isListening && !!value?.length && (
					<textarea
						autoFocus
						className='editTranscription'
						name="textarea"
						placeholder="Escriba aquí"
						rows={18}
						value={value}
						onChange={(e) => setValue(e.target.value)}
					/>
				)}
			</div>
			<div className={`container__buttonsStt ${value?.length ? 'spaceBetween' : 'flexEnd'}`}>
				{!!value?.length && (
					<>
						<Button
							type="button"
							occ_type="outlined"
							size="full"
							action={() => resetValue()}
						>
					Reiniciar
						</Button>

						<Button
							type="button"
							occ_type="filled"
							size="full"

							action={() => generateResumen()}
						>
						Generar resumen
						</Button>
					</>
				)
				}

				{isListening ? (
					<div className='container_mic'>
						<span className='microphone listening'
							onClick={() => {
								setIsListening(false)
								setEditTranscriptions(true)
							}}
						>
							<FiPauseCircle color='red' size={44} />
						</span>
					</div>
				) : (
					<div className='container_mic'>
						<span
							className='microphone stopListening'
							onClick={() => {
								setIsListening(true)
								setEditTranscriptions(false)
								recognizer.start()
							}}
						>
							<BsFillMicFill size={21} />
						</span>
					</div>
				)
				}
			</div>
		</>
	)
}

export default TranscriptStt