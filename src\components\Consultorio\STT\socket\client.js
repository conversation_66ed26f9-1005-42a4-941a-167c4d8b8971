import io from 'socket.io-client'
import {errorHandler} from '@/config/stackdriver'
let sttSocket = process.env.NEXT_PUBLIC_STT_URL
export const socket = io.connect(sttSocket)

//================= CONFIG =================
let bufferSize = 4096,
	AudioContext,
	context,
	processor,
	input,
	globalStream

let streamStreaming = false
let params = {
	startedRecording: false,
}

//================= RECORDING =================
function initRecording() {
	socket.emit('startGoogleCloudStream', '') //init socket Google Speech Connection
	streamStreaming = true
	if(typeof window === 'undefined') return
	AudioContext = window.AudioContext || window.webkitAudioContext
	context = new AudioContext({
		latencyHint: 'interactive',
	})
	processor = context.createScriptProcessor(bufferSize, 1, 1)
	processor.connect(context.destination)
	context.resume()

	let handleSuccess = function (stream) {
		input = context.createMediaStreamSource(stream)
		input.connect(processor)
		processor.onaudioprocess = function (e) {
			microphoneProcess(e)
		}
	}

	let interval = setInterval(() => {
		try {
			let vid = document.querySelectorAll('.OTSubscriberContainer .OT_video-element')
			if(vid?.[0]) {
				globalStream = vid[0].srcObject
				handleSuccess(globalStream)
				clearInterval(interval)
			}
		} catch (err) {
			console.error(err)
			errorHandler.report(err)
		}
	}, 10000)
}

function microphoneProcess(e) {
	let left = e.inputBuffer.getChannelData(0)
	let left16 = downsampleBuffer(left, 48000, 16000)
	socket.emit('binaryData', left16)
}

//================= INTERFACE =================
export function startRecording() {
	if (params.startedRecording) { return }
	params.startedRecording = true
	initRecording()
}

export function stopRecording() {
	try {
		if (!streamStreaming) { return } // stop disconnecting if already disconnected;
		streamStreaming = false
		let track = globalStream.getTracks()[0]
		track.stop()
		input.disconnect(processor)
		processor.disconnect(context.destination)
		context.close().then(function () {
			input = null
			processor = null
			context = null
			AudioContext = null
		})
	} catch (err) {
		console.error(err)
		errorHandler.report(err)
		socket.emit('endGoogleCloudStream', '')
	}
}

//================= SOCKET IO =================
socket.on('connect', function () {
	socket.emit('join', 'Server Connected to Client')
})

//================= OTHER STUFF =================

if(typeof window !== 'undefined') {
	window.onbeforeunload = function () {
		if (streamStreaming) { socket.emit('endGoogleCloudStream', '') }
	}
}

//================= SANTAS HELPERS =================

let downsampleBuffer = function (buffer, sampleRate, outSampleRate) {
	if (outSampleRate === sampleRate) {
		return buffer
	}
	let sampleRateRatio = sampleRate / outSampleRate
	let newLength = Math.round(buffer?.length / sampleRateRatio)
	let result = new Int16Array(newLength)
	let offsetResult = 0
	let offsetBuffer = 0
	while (offsetResult < result?.length) {
		let nextOffsetBuffer = Math.round((offsetResult + 1) * sampleRateRatio)
		let accum = 0, count = 0
		for (let i = offsetBuffer; i < nextOffsetBuffer && i < buffer?.length; i++) {
			accum += buffer[i]
			count++
		}

		result[offsetResult] = Math.min(1, accum / count) * 0x7FFF
		offsetResult++
		offsetBuffer = nextOffsetBuffer
	}
	return result.buffer
}