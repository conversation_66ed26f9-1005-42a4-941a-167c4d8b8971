import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import SelectStudies from './SelectStudies'
import swal from 'sweetalert'
import { saveOrderDB } from '../../../store/actions/doctorActions'
import './styles/Studies.scss'
import { revalidateGetAssignation } from '@/serverServices/getAssignation'
import { revalidateMedicalRecords } from '@/serverServices/getMedicalRecords'

const StudiesOrder = () => {
	const dispatch = useAppDispatch()
	const { profile } = useAppSelector((state) => state.user)
	const { patient, currentAtt } = useAppSelector((state) => state.queries)
	const { temp } = useAppSelector((state) => state.prescriptions)
	const { orderStudies, signature_medikit, orderSpecifications } = useAppSelector((state) => state.orders)
	const [labStudiesArray, setLabStudiesArray] = useState(orderStudies)

	async function handleOrdersSubmit(event) {
		event?.preventDefault()
		const response = await swal({
			title: 'Confirmación',
			text: 'Verifique cuidadosamente los datos y confirme la operación.',
			icon: 'warning',
			buttons: { cancel: 'Cancelar', catch: { text: 'Ok', value: true } },
		})
		if (response === true) {
			try {
				if (!labStudiesArray || labStudiesArray?.length === 0) { throw new Error('Tiene que seleccionar al menos un estudio.') }
				else {
					const patientWithUpdatedData = { ...patient, ...temp }
					saveOrderDB(labStudiesArray, orderSpecifications, patientWithUpdatedData, profile, currentAtt?.incidente_id, signature_medikit)
					dispatch({ type: 'SET_SENDED_ORDER', payload: true })
					dispatch({ type: 'HANDLE_ORDERSTUDIES_STUDY', payload: labStudiesArray })
					await swal('Orden adjuntada', '', 'success')
					await revalidateGetAssignation()
					await revalidateMedicalRecords()


					dispatch({type: 'SET_MODAL_IN_ATTENTION', payload: { state: false, content: '' }})
				}
			} catch (error) {
				swal({ title: 'Aviso', text: `${error}`, icon: 'warning' })
			}
		}
	}

	return (
		<form className='studiesOrder no-cursor' onSubmit={handleOrdersSubmit}>
			<div className="leftColumn__backgroundTitle">
				<label>Ordenes de estudios y laboratorios</label>
			</div>
			<SelectStudies 
				handleOrdersSubmit={handleOrdersSubmit}
				setLabStudiesArray={setLabStudiesArray}
				labStudiesArray={labStudiesArray}/>
		</form>
	)
}

export default StudiesOrder
