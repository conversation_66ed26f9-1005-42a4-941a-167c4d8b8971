import React from 'react'
import { useAppSelector } from '@/store/hooks'

const Assessment = () => {
	const { assessment } = useAppSelector(state => state.tags)

	return (
		<>
			{
				assessment?.length > 0 &&
		<div className="assessment-container">
			<h3 className="assessment-title">Evaluación inicial del usuario</h3>
			{
				assessment?.map((text, index) => (
					<span className="text" key={ `${index}${text}`}>{text} - </span>
				))
			}
		</div>
			}
		</>
	)
}

export default Assessment
