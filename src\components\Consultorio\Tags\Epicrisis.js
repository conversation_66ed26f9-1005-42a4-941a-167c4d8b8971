import React from 'react'
import { FaTimesCircle } from 'react-icons/fa'
import { useAppSelector } from '@/store/hooks'

const Epicrisis = ({ deleteReason }) => {
	let epicrisis = useAppSelector(state => state.tags.epicrisis)
	if(epicrisis && !Array.isArray(epicrisis)){
		epicrisis = epicrisis.split(',')
	}

	return (
		<>
			{ Array.isArray(epicrisis) &&
			epicrisis?.map((text, index) => (
				<div className="tag" key={`${text}${index}`}>
					<span className="tag-text">{text}</span>
					<FaTimesCircle className="tag-delicon" onClick={() => deleteReason(text)} />
				</div>
			))
			}
		</>
	)
}

export default Epicrisis
