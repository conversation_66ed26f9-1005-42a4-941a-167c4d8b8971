import React, { useState, useEffect } from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa'

const Motives = ({ saveLabelQuery }) => {
	const dispatch = useAppDispatch()
	const { motives } = useAppSelector(state => state.tags)
	const [tags, setTags] = useState([])

	const confirmReason = symptom => {
		let initial = motives.initial.filter(e => e !== symptom)
		dispatch({ type: 'SET_TAGS_MOTIVES', payload: initial })

		saveLabelQuery(symptom)
	}

	const cancelReason = symptom => {
		let initial = motives.initial.filter(e => e !== symptom)
		dispatch({ type: 'SET_TAGS_MOTIVES', payload: initial })

		let rejected = [...motives.rejected, symptom]
		dispatch({ type: 'SET_TAGS_MOTIVES_REJECTED', payload: rejected })
	}

	useEffect(() => {
		if(motives.initial.includes('#ALERTA')){
			motives.initial.pop()
		}
		setTags(motives.initial)
	}, [motives.initial])

	return (
		<>
			{motives && tags.map((text, i) => (
				<div className="tag motive" key={`${text}${i}`}>
					<span className="tag-text">{text}</span>
					<FaCheckCircle className="tag-confirmicon" onClick={() => confirmReason(text)} />
					<FaTimesCircle className="tag-delicon" onClick={() => cancelReason(text)} />
				</div>
			))}
		</>
	)
}

export default Motives