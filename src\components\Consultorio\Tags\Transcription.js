import React from 'react'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa'

const Transcription = ({ saveLabelQuery }) => {
	const dispatch = useAppDispatch()
	const { transcription } = useAppSelector(state => state.tags)

	const confirmReason = symptom => {
		let initial = transcription.initial.filter(e => e !== symptom)
		dispatch({ type: 'SET_TAGS_TRANSCRIPTION', payload: initial })

		saveLabelQuery(symptom)
	}

	const cancelReason = symptom => {
		let initial = transcription.initial.filter(e => e !== symptom)
		dispatch({ type: 'SET_TAGS_TRANSCRIPTION', payload: initial })

		let rejected = [...transcription.rejected, symptom]
		dispatch({ type: 'SET_TAGS_TRANSCRIPTION_REJECTED', payload: rejected })
	}

	return (
		<>
			{
				transcription?.initial.map((text, index) => (
					<div className="tag" key={ `${index}${text}`}>
						<span className="tag-text">{text}</span>
						<FaCheckCircle className="tag-confirmicon" onClick={() => confirmReason(text)} />
						<FaTimesCircle className="tag-delicon" onClick={() => cancelReason(text)} />
					</div>
				))
			}
		</>
	)
}

export default Transcription
