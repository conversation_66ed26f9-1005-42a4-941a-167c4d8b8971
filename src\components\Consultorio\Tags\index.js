import React, { useState, useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store/hooks'
import { delete_label, save_labels } from '@/config/endpoints'
import axios from 'axios'
import {BsPlusSquareFill } from 'react-icons/bs'
import { FaTag, FaCaretRight } from 'react-icons/fa'
import { errorHandler } from '@/config/stackdriver'
import MiniModal from '@/components/GeneralComponents/MiniModal/MiniModal'
import Motives from './Motives'
import Assessment from './Assessment' 
import Transcription from './Transcription'
import Epicrisis from './Epicrisis'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";

const Tags = () => {
	const dispatch = useAppDispatch()
	const currentAtt = useAppSelector(state => state.queries.currentAtt)
	const { detailbox } = useAppSelector(state => state.front)
	const appointments = useAppSelector(state => state.queries.currentAppointment)
	const [loading, setLoading] = useState(false)
	const epicrisis = useAppSelector(state => state.tags.epicrisis)
	const { motives } = useAppSelector(state => state.tags)
	const [addTag, setAddTag] = useState('')
	let mr_preds, mr, assignation_id

	if(currentAtt) {
		mr_preds = currentAtt?.mr_preds
		mr = currentAtt?.mr
		assignation_id = currentAtt.assignation_id || appointments.appointments?.[0]['14']
	}

	useEffect(() => {
		if (mr_preds) {
			let objeto = mr_preds?.abort_description?.[0]
			let arreglo = []
			for (let key in objeto) {
				if (objeto[key] === 'yes' ) {
					arreglo.push(key)
				} 
			}
			arreglo = arreglo.map(x => x?.replace(/_/g, ' '))
			dispatch({ type: 'SET_TAGS_ASSESSMENT', payload: arreglo })
		}
		// eslint-disable-next-line 
	}, [mr_preds])

	useEffect(() => {
		let epicrisis = mr?.epicrisis?.replace(/\.|"/g, '')
		if(mr?.epicrisis) {
			epicrisis = epicrisis.split(',')
			epicrisis = epicrisis.map(x => x.includes('Paciente consulta por') ? x.split('Paciente consulta por')[1] : x.trim()).filter(x => x !== '')
			dispatch({ type: 'SET_TAGS_EPICRISIS', payload: epicrisis })
		}

		let consulta = []
		if (mr?.motivos_de_consulta) {
			consulta = mr?.motivos_de_consulta.split('.')
			consulta = consulta.map(el => el?.replace(/^\s/g, '')).filter(e => e !== '')
		
			let existenMotivos = [...epicrisis]
			let tagsMotives = []
			consulta.forEach(x => !existenMotivos.includes(x) && !motives?.initial?.includes(x) && tagsMotives.push(x))
			dispatch({ type: 'SET_TAGS_MOTIVES', payload: tagsMotives })
		}
		// eslint-disable-next-line 
	}, [mr])


	const addReason = addTag => {
		if(!addTag.trim()) return
		saveLabelQuery(addTag)
		dispatch({ type: 'VIEW_DETAIL', payload: false })
		setAddTag('')
	}

	const saveLabelQuery = async symptom => {
		const token = await getFirebaseIdToken()
        const headers = {
            'Authorization': `Bearer ${token}`,
            'content-type': 'application/json',
			'uid': appointments?.patient?.uid, 
			'x-api-key': process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY 
		}
		let dataToSave = {
			assignation_id,
			etiquetas: `,${symptom}`,
			uid: appointments?.patient?.uid
		}
		setLoading(true)
		axios.post(save_labels, dataToSave, {headers})
			.then(() => {
				dispatch({ type: 'VIEW_DETAIL', payload: false })
				setLoading(false)
			})
			.catch(err => {
				errorHandler.report('Save Label error:', err)
				setLoading(false)
			})
	}

	const deleteReason = async symptom => {
		const token = await getFirebaseIdToken()
        const headers = {
            'Authorization': `Bearer ${token}`,
            'content-type': 'application/json',
			'uid': appointments?.patient?.uid, 
			'x-api-key': process.env.NEXT_PUBLIC_UMA_BACKEND_LOGIC_APIKEY 
		}
		setLoading(true)
		let dataToDelete = {
			assignation_id,
			etiqueta: `,${symptom}`,
			field: 'epicrisis',
			uid: appointments.patient.uid
		}
		
		axios.post(delete_label, dataToDelete, {headers})
			.then(() =>{
				const filteredTags = epicrisis.filter(t => t !== symptom)
				dispatch({ type: 'SET_TAGS_EPICRISIS', payload: filteredTags })
				setLoading(false)
			})
			.catch(err => {
				console.error('Delete Label error:', err)
				setLoading(false)
			})
	}

	return (
		<div className='tags-container'>
			<div className="tags-title">
				<span>Etiquetas</span>
				<div className="tag-add" onClick={() => dispatch({ type: 'VIEW_DETAIL', payload: true })}>
					<BsPlusSquareFill  aria-hidden='true' className="tag-addicon" />
					<span>Agregar etiqueta</span>
				</div>
			</div>
			<div className="tags-box">
				{detailbox &&
					<MiniModal>
						<div className="tags-input">
							<input value={addTag} type="text" onChange={e => setAddTag(e.target.value)} />
							<FaCaretRight className="tag-sendtag" onClick={() => addReason(addTag)} />
						</div>
					</MiniModal>
				}

				<div className="tag-leyend">
					<div>
						Síntomas confirmados: <FaTag  aria-hidden='true' color="#36a853" />
					</div>
					<div>
						Motivo Consulta: <FaTag  aria-hidden='true' color="#4fa1d1" />
					</div>
					<div>
						Transcripción: <FaTag  aria-hidden='true' color="#c6dbe8" />
					</div>
				</div>
				{loading ? 
					<p className="tag-loadingMsg">Guardando...</p> :
					<>
						{/* Evaluación inicial del usuario */}
						<Assessment />
						{/* Epicrisis */}
						<Epicrisis deleteReason={deleteReason} />
						{/* Motivo de consulta */}
						<Motives saveLabelQuery={saveLabelQuery} />
						{/* Transcripcion */}
						<Transcription saveLabelQuery={saveLabelQuery} />
					</>
				}
			</div>
		</div>
	)
}

export default Tags