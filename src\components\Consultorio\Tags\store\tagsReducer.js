import * as tagsTypes from './tagsTypes'

export const initialTagsReducerState = {
	attachments:{
		video:'',
		files: []
	},
	assessment: [],
	epicrisis: [],
	transcription: { initial: [], rejected: [] },
	motives: { initial: [], rejected: [] },
}

export default function tagsReducers(state = initialTagsReducerState, action) {
	switch (action.type) {
	case tagsTypes.SET_VIDEO_ATTACHMENTS:
		return { ...state, attachments: { ...state.attachments, video: action.payload} }
	case tagsTypes.SET_FILES_ATTACHMENTS:
		return { ...state, attachments: { ...state.attachments, files: action.payload} }
	case tagsTypes.ADD_FILE_ATTACHMENTS:
		return { ...state, attachments: { ...state.attachments, files: [...state.attachments.files, action.payload]} }
	case tagsTypes.SET_TAGS_MOTIVES:
		return {
			...state,
			motives: { ...state.motives, initial: action.payload } 
		}
	case tagsTypes.SET_TAGS_MOTIVES_REJECTED:
		return {
			...state,
			motives: { ...state.motives, rejected: action.payload }
		}
	case tagsTypes.SET_TAGS_TRANSCRIPTION:
		return {
			...state,
			transcription: { ...state.transcription, initial: action.payload }
		}
	case tagsTypes.SET_TAGS_TRANSCRIPTION_REJECTED:
		return {
			...state,
			transcription: { ...state.transcription, rejected: action.payload } 
		}
	case tagsTypes.SET_TAGS_ASSESSMENT: return { ...state, assessment: action.payload }
	case tagsTypes.SET_TAGS_EPICRISIS: return { ...state, epicrisis: action.payload }
	default:
		return state
	}
}
