import React from "react";
import { Meta, StoryObj } from "@storybook/react";
import ActionPanel from "./ActionPanel";
import MicToggleButton from "./MicToggleButton/MicToggleButton";
import CameraToggleButton from "./CameraToggleButton/CameraToggleButton";
import EndButtonCall from "./EndButtonCall/EndButtonCall";

const meta: Meta<typeof ActionPanel> = {
  title: 'Telehealth/ActionPanel',
  component: ActionPanel,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof ActionPanel>;

const ActionPanelExample: React.FC = () => {
  const [isMicOn, setIsMicOn] = React.useState(true);
  const [isCameraOn, setIsCameraOn] = React.useState(true);

  return (
    <ActionPanel>
      <MicToggleButton isMicOn={isMicOn} onToggleAudio={() => setIsMicOn(!isMicOn)} />
      <CameraToggleButton isCameraOn={isCameraOn} onToggleCamera={() => setIsCameraOn(!isCameraOn)} />
      <EndButtonCall onClick={() => console.log("end call")} />
    </ActionPanel>
  );
};

export const Default: Story = {
  render: () => <ActionPanelExample />,
};