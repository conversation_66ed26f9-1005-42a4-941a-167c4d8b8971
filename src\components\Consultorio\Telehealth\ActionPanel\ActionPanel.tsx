"use client";
import React from "react";
import { cn } from "@/lib/utils";

interface ActionPanelProps {
  children: React.ReactNode;
  className?: string
}

const ActionPanel: React.FC<ActionPanelProps> = ({ className, children }: { className?: string, children: React.ReactNode }) => {

  return (
    <div className={cn("flex items-center h-min justify-center gap-4 py-2 px-6 w-fit bg-white rounded-xl shadow-sm mt-0", className)}>
      {children}
    </div>
  );
};

export default ActionPanel
