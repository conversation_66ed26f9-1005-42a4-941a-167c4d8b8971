"use client";
import React from "react";
import { Button, IconsNames } from "@umahealth/occipital";
import { Icon } from "@umahealth/occipital/client";
import { cn } from "@/lib/utils";

interface ActionPanelButtonProps {
  onClick: () => void;
  className?: string;
  iconName: IconsNames;
  iconNameActive: IconsNames;
  isActive: boolean
}

const ActionPanelButton = ({
  onClick,
  className,
  iconName,
  iconNameActive,
  isActive,
}: ActionPanelButtonProps) => {
  return (
    <Button
      onClick={onClick}
      type="button"
      className={cn(
        "rounded-full min-w-[44px] h-[44px] w-[44px] bg-gray-600 hover:bg-gray-800 flex items-center justify-center",
        className
      )}
    >
      <div className="relative">
        <Icon
          name={isActive ? iconNameActive : iconName}
          size="size-6"
          color="text-background-patient"
        />
      </div>
    </Button>
  );
};

export default ActionPanelButton;