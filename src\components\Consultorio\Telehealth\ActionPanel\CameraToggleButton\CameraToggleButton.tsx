"use client"
import { usePostHog } from "posthog-js/react";
import ActionPanelButton from "../ActionPanelButton/ActionPanelButton";


const CameraToggleButton = ({ onToggleCamera, isCameraOn } : { onToggleCamera: ((isOn: boolean) => void) | null, isCameraOn: boolean | null }) => {
  const posthog = usePostHog()
  if(onToggleCamera && isCameraOn !== null) {
    const handleCameraToggle = () => {
      onToggleCamera(!isCameraOn)
      posthog.capture('camera_toggled', {
        state: !isCameraOn ? 'on' : 'off',
        timestamp: new Date().toISOString(),
      }) 
    }

    return (
      <ActionPanelButton
        onClick={handleCameraToggle}
        iconName="videocallOff"
        iconNameActive="videocall"
        isActive={isCameraOn}
      />
    );
  }
};

export default CameraToggleButton;
