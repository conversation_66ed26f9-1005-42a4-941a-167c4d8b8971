import React from "react";
import { But<PERSON> } from "@umahealth/occipital";
import { Icon } from "@umahealth/occipital/client";
import { cn } from "@/lib/utils";

const EndButtonCall = ({ onClick, className } : { onClick: () => void, className?: string }) => {
  return (
    <Button
      type="button"
      size="small"
      className={cn("rounded-full bg-error h-[44px] w-[76px] hover:bg-error-700", className)}
      onClick={onClick}
    >
      <Icon name="callEnd" size="size-6" className="text-white" />
    </Button>
  );
};

export default EndButtonCall;
