"use client"
import ActionPanelButton from "../ActionPanelButton/ActionPanelButton";


const MicToggleButton = ({ onToggleAudio, isMicOn } : { onToggleAudio: ((isOn: boolean) => void) | null, isMicOn: boolean | null }) => {

  if(onToggleAudio && isMicOn !== null) {
    const handleAudioToggle = () => {
      onToggleAudio(!isMicOn)
    }

    return (
      <ActionPanelButton
        onClick={handleAudioToggle}
        iconName="microphoneOff"
        iconNameActive="microphone"
        isActive={isMicOn}
      />
    );
  }
};

export default MicToggleButton;