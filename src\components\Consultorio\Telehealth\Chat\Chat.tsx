import React, { useEffect, useState, memo } from "react";
import { ChatList, ChatMessage, Icon } from "@umahealth/occipital/client";
import { useChatMessageLogic } from "./hooks/useChatMessageLogic";
import { useMessageLoadingLogic } from "./hooks/useMessageLoadingLogic";
import { SeparatorChat } from "../../Chat/SeparatorChat";
import { IChatMessage } from "@/storybook/components/Chat/Chat"

/*  interface IChatMessage {
  status: "isLoading" | "isOnServer" | "wasReaded" | "isError" | "wasSended";
  sendTime: Date;
  messageOrigin: "self" | "other";
}  */

const Chat = memo(() => {
  const [optimisticSent, setOptimisticSent] = useState('')
  const { messages } = useMessageLoadingLogic();
  const {
    register,
    handleSubmit,
    onSubmit,
    disabled,
    reset
  } = useChatMessageLogic();

  useEffect(() => {
    return setOptimisticSent('')
  }, [messages])

function convertTimestampToDate(time: any) {
    if (time instanceof Date) {
      return time;
    }
    if (typeof time === 'object' && time !== null &&
        typeof time.seconds === 'number' &&
        typeof time.nanoseconds === 'number') {
      const millisecondsFromSeconds = time.seconds * 1000;
      const millisecondsFromNanoseconds = time.nanoseconds / 1000000;
      const totalMilliseconds = millisecondsFromSeconds + millisecondsFromNanoseconds;
      return new Date(totalMilliseconds);
    }
  // If sendTime is neither a Date nor a valid Timestamp object, return null or handle error
  console.error('Invalid sendTime format:', time);
  return null;
}

function detectHTMLWithLinks(message: string): boolean {
  const linkRegex = /<a\s+(?:[^>]*?\s+)?href="([^"]*)"/i;
  return linkRegex.test(message);
}

function renderHTMLContent(htmlString: string) {
  return <div dangerouslySetInnerHTML={{ __html: htmlString }} />;
}

const updatedMessages = messages.map((message) => ({
  ...message,
  status: message.rol === 'patient' ? 'isOnServer' : 'wasSended',  // Override the status value to 'isOnServer
  sendTime: convertTimestampToDate(message.sendTime),  // Ensure sendTime is a valid Date
}));


  const handleSendMessage = (messageForm: { message: string }) => {
    if(messageForm.message?.length > 0) {
      setOptimisticSent(messageForm.message)
      onSubmit(messageForm);
      reset();
    } 
  };

  return (
      <div className="flex flex-col h-full w-full bg-white px-2 pb-2 rounded-lg overflow-hidden max-h-full">
        <div className="flex-1 min-h-0 overflow-y-auto [&>ul]:bg-white [&>ul>li]:text-xs">
          <ChatList>
            {updatedMessages.map((message, index) => {
              if (message.rol === 'UMA' &&
                'msg' in message && message.msg === 'separator') {
                return <SeparatorChat key={`${index}-${new Date().getFullYear()}`} />;
              }
              return (
                <ChatMessage
                  key={`${index}-${new Date().getFullYear()}`}
                  {...message as IChatMessage}
                >
                  {/* Detectar y renderizar HTML con enlaces */}
                  {typeof message.children === 'string' &&
                  detectHTMLWithLinks(message.children) ? (
                    renderHTMLContent(message.children)
                  ) : (
                    message.children
                  )}
                </ChatMessage>
              );
            })}
            {optimisticSent && (
              <ChatMessage
                status="isLoading"
                sendTime={new Date()}
                messageOrigin="self"
              >
                {optimisticSent}
              </ChatMessage>
            )}
          </ChatList>
        </div>
        <form className="flex items-center gap-2 w-full h-[40px] shrink-0" onSubmit={handleSubmit(handleSendMessage)}>
            <input
              type="text"
              autoComplete="off"
              {...register("message")}
              placeholder="Escribe un mensaje"
              className="w-full p-2 border rounded grow min-w-0"
            />
            <button
              type="submit"
              aria-label='Enviar' disabled={disabled}
              className={`bg-primary-800 rounded w-[36px] h-full shrink-0 ${disabled ? "cursor-not-allowed" : ""}`}
            >
              <Icon
                name="sendMessage"
                size="size-6"
                className="text-white m-auto"
              />
            </button>
        </form>
      </div>
  );
});

// Agregar displayName para cumplir con la regla de ESLint
Chat.displayName = 'Chat';

export default Chat;
