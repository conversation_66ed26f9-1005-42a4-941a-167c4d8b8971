import { useContext } from 'react';
import { useForm } from 'react-hook-form';
import { useSearchParams } from 'next/navigation';
import { useAppSelector } from '@/store/hooks';
import { AttContext } from '@/components/AsyncAttention';
import { TMessageData, useSendChatMessage } from '../../../Chat/useSendChatMessage';

export const useChatMessageLogic = () => {
  const searchParams = useSearchParams();
  const dependant = searchParams.get('dependant');
  const assignationId = searchParams.get('assignationId');
  const currentAssignation = useAppSelector((state: any) => state.queries.currentAppointment);
  const patient = useAppSelector((state) => state.queries.patient);
  const asyncAttContext = useContext(AttContext);
  const { currentUser } = useAppSelector((state) => state.user);
  const asyncAtt = asyncAttContext?.attInView;

  const disabled = asyncAttContext?.attInView && asyncAttContext.attInView.state === 'PENDING_DONE';

  const { register, handleSubmit, reset, setError, formState: { errors } } = useForm<{ message: string }>();

  const sendMessageMutation = useSendChatMessage();

  const onSubmit = (messageForm: { message: string }) => {
    let sendChatMessageData: TMessageData;

    if (asyncAtt) {
      sendChatMessageData = {
        assignation_id: asyncAtt?.assignation_id,
        dependant_uid: asyncAtt?.patient?.uid_dependant === 'false' || asyncAtt.patient?.uid_dependant === undefined ? false : asyncAtt.patient?.uid_dependant,
        doctorUid: currentUser.uid,
        uid: asyncAtt.patient?.uid,
        module: 'async',
        text: messageForm.message
      };
    } else {
      sendChatMessageData = {
        assignation_id: assignationId || (currentAssignation && currentAssignation?.assignation_id),
        dependant_uid: dependant === 'false' || dependant === null ? false : dependant,
        doctorUid: currentUser.uid,
        uid: (patient.uid || currentAssignation.patient.uid),
        module: 'chat',
        text: messageForm.message
      };
    }

    if (!sendChatMessageData.assignation_id) {
      setError('message', { type: 'custom', message: `No se logro encontrar el id de la consulta, vuelva a intentar mandar el mensaje` });
      return;
    }

    if (!sendChatMessageData.doctorUid) {
      setError('message', { type: 'custom', message: `No se logro encontrar el uid del doctor, vuelva a intentar mandar el mensaje` });
      return;
    }

    if (!sendChatMessageData.uid) {
      setError('message', { type: 'custom', message: `No se logro encontrar el uid del paciente, vuelva a intentar mandar el mensaje` });
      return;
    }

    if (messageForm.message === '') {
      setError('message', { type: 'custom', message: `No puede enviar un mensaje vacío` });
      return;
    }

    return sendMessageMutation.mutate(sendChatMessageData);
  };

  return {
    register,
    handleSubmit,
    onSubmit,
    errors,
    disabled,
    reset
  };
};