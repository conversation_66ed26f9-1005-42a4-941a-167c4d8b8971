import { useContext, useEffect, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { useSearchParams } from 'next/navigation';
import { AttContext } from '@/components/AsyncAttention';
import { useOnSnapshot } from '@/hooks/useOnSnapshot';
import { where } from '@/config/firebase';
import { IChatMessages } from '@umahealth/entities';
import { setMessageAsViewed } from '../../../Chat/utils/setMessageAsViewed';

export const useMessageLoadingLogic = () => {
  const asyncAtt = useContext(AttContext);
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const assignationId = searchParams.get('assignationId');
  const currentAssignation = useAppSelector(state => state.queries.currentAtt);
  const { currentUser } = useAppSelector((state) => state.user);

  const paramsAssignationId = asyncAtt?.attInView 
    ? asyncAtt.attInView.assignation_id 
    : (assignationId && assignationId !== undefined ? assignationId.toString() : '');

  const userId = asyncAtt?.attInView 
    ? asyncAtt.attInView.patient?.uid 
    : currentAssignation?.patient?.uid;

  const messages = useOnSnapshot<IChatMessages>(
    `user/${userId}/chat_messages`, 
    [
      where('assignation_id', '==', paramsAssignationId), 
      where('rol', 'in', ['doctor', 'patient', 'UMA'])
    ], 
    (!!userId && !!paramsAssignationId),
    'useMessageLoadingLogic'
  );

  const sortMessages = useMemo(() => {
    const messagesSorted = messages?.sort((a, b) => 
      (a?.timestamps?.dt_create && b?.timestamps?.dt_create) 
        ? (a?.timestamps?.dt_create < b?.timestamps?.dt_create ? -1 : 1) 
        : 1
    );
    dispatch({ type: 'SET_DATA_CHAT', payload: messagesSorted });
    return messagesSorted;
  }, [dispatch, messages]);

  useEffect(() => {
    setMessageAsViewed(asyncAtt?.attInView, currentUser);
  }, [messages, asyncAtt?.attInView, currentUser]);

  const formattedMessages = useMemo(() => {
    return sortMessages?.map(message => ({
      status: "wasSended",
      sendTime: message.timestamps?.dt_create || new Date(),
      messageOrigin: message.rol === 'doctor' ? 'self' : 'other',
      children: message.msg,
      rol: message.rol,
      type: message.type
    }));
  }, [sortMessages]);

  return {
    messages: formattedMessages,
    isLoading: !messages
  };
};