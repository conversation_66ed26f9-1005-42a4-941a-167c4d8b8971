import { Button } from '@umahealth/occipital'
import { TooltipRenderProps } from 'react-joyride'

function CustomJoyrideTooltip(props: TooltipRenderProps) {
  const {
    backProps,
    closeProps,
    continuous,
    index,
    primaryProps,
    step,
    tooltipProps,
  } = props

  return (
    <div
      className="tooltip__body bg-white max-w-[500px] rounded-xl p-4 flex flex-col z-[1001]"
      {...tooltipProps}
    >
      <button
        className="tooltip__close self-end text-3xl text-neutral-500 mb-2"
        {...closeProps}
      >
        &times;
      </button>
      {step.title && <h4 className="tooltip__title">{step.title}</h4>}
      <div className="tooltip__content flex justify-center">{step.content}</div>
      <div className="tooltip__footer flex flex-col justify-end">
        <div className="tooltip__spacer flex justify-end gap-4  mt-4">
          {index > 0 && (
            <Button
              type="button"
              size="small"
              className="bg-transparent text-secondary-500"
              {...backProps}
            >
              {backProps.title}
            </Button>
          )}
          {continuous && (
            <Button
              type="button"
              size="small"
              className="bg-secondary-500"
              {...primaryProps}
            >
              {primaryProps.title}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export default CustomJoyrideTooltip
