import { useState } from 'react'
import Joyride, { CallBackProps, Placement } from 'react-joyride'

import { setHasSeenTelehealthGuideTour } from '@/cookies/hasSeenTelehealthGuideTour'
import Step from './Step'
import StartGuideTourModal from './StartGuideTourModal'
import CustomJoyrideTooltip from './CustomJoyrideTooltip'
import { usePostHog } from 'posthog-js/react'

const placement: Placement = 'right-start'

const steps = [
  {
    disableBeacon: true,
    target: '.tab-step',
    placement,
    content: (
      <Step
        videoPath="/assets/video/guide-tour-step-1.mp4"
        stepIndex={1}
        totalSteps={3}
        title="Navegá con facilidad durante la atención"
        description="Completá la ficha de atención, generá recetas y órdenes haciendo clic en las pestañas de navegación."
      />
    ),
  },
  {
    disableBeacon: true,
    target: '.options-step',
    content: (
      <Step
        videoPath="/assets/video/guide-tour-step-2.mp4"
        stepIndex={2}
        totalSteps={3}
        title="Explorá la historia clínica del paciente"
        description="Revisá el historial de citas, recetas generadas, adjuntos o reportá un incidente durante la consulta."
      />
    ),
  },
  {
    disableBeacon: true,
    target: '.endcall-step',
    content: (
      <Step
        videoPath="/assets/video/guide-tour-step-3.mp4"
        stepIndex={3}
        totalSteps={3}
        title="Finalizá la atención"
        description="Cuando termines de completar la ficha, finalizá de un solo clic y
        continuá con tu próxima videoconsulta."
      />
    ),
  },
]

const GuidedTour = () => {
  const [showModal, setShowModal] = useState(true)
  const [isGuideTourRunning, setIsGuideTourRunning] = useState(false)
  const posthog = usePostHog()

  const joyrideCallback = (props: CallBackProps) => {
    const { action, step, index } = props

    //registrar el visionado de cada paso
    if (action === 'update') {
      posthog.capture('tour_step_viewed', {
        step_number: index + 1,
        step_name: step.target,
        timestamp: new Date().toISOString(),
      })
    }
    //Joyride no tiene un evento de 'finished', si no que dispara un 'reset' al finalizar el último step
    if (action === 'reset') {
      posthog.capture('tour_completed')
      setHasSeenTelehealthGuideTour(true)
      return
    }

    if (action === 'close') {
      posthog.capture('tour_exited', { step_number: index + 1 })
      setHasSeenTelehealthGuideTour(true)
      return
    }
  }

  return (
    <>
      {showModal && (
        <StartGuideTourModal
          showModal={showModal}
          setShowModal={(show, action) => {
            setShowModal(show)
            if(!show && action === 'exit'){
              posthog.capture('tour_exited', { step_number: 0 })
              setHasSeenTelehealthGuideTour(true)
            }
          }}
          setIsGuideTourRunning={setIsGuideTourRunning}
        />
      )}
     <Joyride
      steps={steps}
      run={isGuideTourRunning}
      tooltipComponent={(props) => <CustomJoyrideTooltip {...props} />}
      continuous
      hideBackButton
      disableCloseOnEsc
      disableOverlayClose
      callback={(props) =>
        joyrideCallback(props)
      }
      locale={{ next: 'Siguiente', last: 'Finalizar', back: 'Anterior' }}
      styles={{
        options: { width: 480, zIndex: 1002  },
        overlay: { backgroundColor: 'rgba(0, 0, 0, 0.7)', zIndex: 1001 },
        buttonNext: { backgroundColor: '#563ff0 ', color: '#fff' },
        beacon: { display: 'none' },
        spotlight: {
          borderRadius: '16px'
        },
        tooltipContent: {zIndex: 1002},
        }}
      />
    </>
  )
}

export default GuidedTour
