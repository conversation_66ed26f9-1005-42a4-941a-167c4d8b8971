import { Button } from '@umahealth/occipital'
import {
  DialogRoot,
  DialogPortal,
  DialogOverlay,
  DialogContent,
} from '@umahealth/occipital/client'
import { useEffect, useState } from 'react'
import VideoError from './VideoError'
import { usePostHog } from 'posthog-js/react'

interface Props {
  setShowModal: (show: boolean, action: 'exit' | 'start') => void
  setIsGuideTourRunning: (isGuideTourRunning: boolean) => void
  showModal: boolean
}

const StartGuideTourModal = ({
  setShowModal,
  setIsGuideTourRunning,
  showModal,
}: Props) => {
  const [videoHasError, setVideoHasError] = useState(false)
  const posthog = usePostHog()
  
  //uso un useEffect para que solo se registre una vez el evento
  useEffect(() => {
    posthog.capture('tour_started')
  }, [])

  return (
    <DialogRoot
      open={showModal}
      onOpenChange={() => setShowModal(false, 'exit')}
      modal={true}
    >
      <DialogPortal>
        <DialogOverlay className='z-[1001]' />
        <DialogContent onPointerDownOutside={(e) => e.preventDefault()}  className="max-w-[1000px] max-h-[500px] w-full p-0 rounded-xl z-[1002]">
          <div className="flex flex-row">
            {!videoHasError ? (
              <video
                className="rounded-l-xl"
                width="550px"
                height="440px"
                autoPlay
                muted
                loop
                preload="none"
                onError={() => setVideoHasError(true)}
              >
                <source
                  src="/assets/video/guide-tour-start.mp4"
                  type="video/mp4"
                />
              </video>
            ) : (
              <VideoError />
            )}
            <div className="flex flex-col justify-end gap-4 py-6 px-12">
              <p className="text-left text-2xl font-bold">
                Te presentamos el rediseño del consultorio online
              </p>
              <p className="text-left text-neutral-500">
                Descubrí las funciones rediseñadas de nuestra plataforma y
                mejorá tu eficiencia y la calidad de tus atenciones.
              </p>
              <Button
                size="full"
                type="button"
                className="bg-secondary-500"
                onClick={() => {
                  setShowModal(false, 'start'), setIsGuideTourRunning(true)
                }}
              >
                ¡Empecemos!
              </Button>
            </div>
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
}

export default StartGuideTourModal
