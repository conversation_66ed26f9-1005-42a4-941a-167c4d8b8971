import { useState } from 'react'
import VideoError from './VideoError'

interface Props {
  stepIndex: number
  totalSteps: number
  videoPath: string
  title: string
  description: string
}

const Step = ({
  stepIndex,
  totalSteps,
  videoPath,
  title,
  description,
}: Props) => {
  const [videoHasError, setVideoHasError] = useState(false)
  return (
    <div className="flex flex-col gap-4">
      {!videoHasError ? (
        <video
          className="rounded-xl"
          width="500"
          height="280"
          autoPlay
          muted
          loop
          preload="none"
          onError={() => setVideoHasError(true)}
        >
          <source src={videoPath} type="video/mp4" />
        </video>
      ) : (
        <VideoError />
      )}
      <div className="text-left flex flex-col gap-2 ">
        <p className="text-secondary-500 text-sm">
          Paso {stepIndex} de {totalSteps}
        </p>
        <p className="font-bold text-lg">{title}</p>
        <p className="text-neutral-500">{description}</p>
      </div>
    </div>
  )
}

export default Step
