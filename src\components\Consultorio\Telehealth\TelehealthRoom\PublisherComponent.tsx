import React, { useRef, useEffect, useState, RefObject } from 'react';
import { LocalVideo } from '@umahealth/occipital/client';
import { Publisher } from '@opentok/client';
import { useSessionManagerContext } from './SessionManager';

interface PublisherComponentProps {
  parentRef: RefObject<HTMLDivElement | null>;
}

export const PublisherComponent = ({ parentRef }: PublisherComponentProps) => {
  const [isConnected, setIsConnected] = useState(false);
  const publisherRef = useRef<Publisher | null>(null);
  const publisherContainerRef = useRef<HTMLDivElement | null>(null);
  const { session, isCameraOn, isMicOn }  = useSessionManagerContext()
  
  let publisher: Publisher;

  useEffect(() => {
    if (!session) return;

    // Escucha eventos de conexión
    const handleConnectionCreated = () => setIsConnected(true);
    const handleConnectionDestroyed = () => setIsConnected(false);

    session.on("connectionCreated", handleConnectionCreated);
    session.on("connectionDestroyed", handleConnectionDestroyed);

    if (publisherContainerRef.current && isConnected) {
      publisher = OT.initPublisher(publisherContainerRef.current, {
        insertMode: "append",
        width: "100%",
        height: "100%",
        showControls: false,
      });

      publisherRef.current = publisher;

      session.publish(publisher, (error: any) => {
        if (error) {
          console.error("Error publishing:", error);
        }
      });
    }

    return () => {
      if (publisher) session.unpublish(publisher);
      session.off("connectionCreated", handleConnectionCreated);
      session.off("connectionDestroyed", handleConnectionDestroyed);
    };
  }, [session, isConnected]);

  useEffect(() => {
    if (publisherRef.current) {
      publisherRef.current.publishAudio(!!isMicOn);
    }
  }, [isMicOn, publisherRef.current]);

  useEffect(() => {
    if (publisherRef.current) {
      publisherRef.current.publishVideo(!!isCameraOn);
    }
  }, [isCameraOn, publisherRef.current]);

  if (parentRef.current !== null){
    return (
      <LocalVideo
        className="[&>div>div]:overflow-hidden [&>div>div>div:first-child]:w-[100%] [&>div>div>div:first-child]:h-[100%] [&>div>div:first-child]:w-[133px] [&>div>div:first-child]:h-[82px] [&>div>div>div:nth-child(2)]:hidden"
        cameraOff={false}
        microphoneStatus="on"
        name=""
        hideControls={true}
        ref={publisherContainerRef}
        parentRef={parentRef as React.RefObject<HTMLDivElement>}
      />
    );
  }
};