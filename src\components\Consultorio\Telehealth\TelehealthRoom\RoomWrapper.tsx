'use client'
import React, { ReactNode, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAppDispatch } from '@/store/hooks'
import {
  collection,
  query,
  where,
  onSnapshot,
  firestore,
  DocumentData,
  QueryDocumentSnapshot,
} from '@/config/firebase'
import { IAppointment, ICorporateServices, IMedicalRecord, IPatient } from '@umahealth/entities'
import { RawTimestamp } from '@/components/Appointments/utils/filterMandatoryFields'
import { deserializeNestedTimestamps } from '@/lib/utils'

interface TelehealthRoomWrapper {
  assignation: IAppointment<RawTimestamp>;
  medicalRecords: IMedicalRecord<RawTimestamp>[];
  patient: IPatient<RawTimestamp>;
  activeServices: ICorporateServices;
}

export default function RoomWrapper({ children, activeServices, assignation, medicalRecords, patient } : { children : ReactNode} & TelehealthRoomWrapper) {
  const dispatch = useAppDispatch()
  const searchParams = useSearchParams()

  // Obtención de parámetros de búsqueda
  const assignationId = searchParams.get('assignationId')
  const patientUid = searchParams.get('patientUid')

  const medicalRecord = medicalRecords.find(
    (mr) => mr.assignation_id === assignation.assignation_id
  )

  const getChatMessages = () => {
    if (!assignationId || !patientUid) return

    const messagesRef = collection(
      firestore,
      `user/${patientUid}/chat_messages`
    )
    const messagesQuery = query(
      messagesRef,
      where('rol', 'in', ['doctor', 'patient', 'UMA']),
      where('assignation_id', '==', assignationId)
    )

    onSnapshot(
      messagesQuery,
      (snapshot) => {
        const messages: DocumentData[] = []
        snapshot.forEach((doc: QueryDocumentSnapshot<DocumentData>) => {
          messages.push(doc.data())
        })
        dispatch({ type: 'SET_DATA_CHAT', payload: messages })
      },
      (error) => console.error(error)
    )
  }

  useEffect(() => {
    dispatch({ type: 'SET_PLAN_DATA', payload: activeServices })
  }, [dispatch, activeServices])

  useEffect(() => {
    const timestamps = deserializeNestedTimestamps(assignation.timestamps)
        dispatch({ type: 'GET_CURRENT_APPOINTMENT', payload: {
          ...assignation,
          timestamps: {
            ...timestamps
          }
        } });
  }, [dispatch, assignation])

  useEffect(() => {
    dispatch({ type: 'GET_PATIENT', payload: { ...patient } })
  }, [dispatch, patient])

  useEffect(() => {
    dispatch({ type: 'GET_MEDICAL_RECORD', payload: medicalRecords })

    if (medicalRecord) {
      const timestamps = deserializeNestedTimestamps(medicalRecord.timestamps)

      dispatch({ type: 'GET_CURRENT_ATT', payload: {
        ...medicalRecord,
        timestamps: {
          ...timestamps
        }
      } })
    }
  }, [dispatch, medicalRecord, assignation])

  useEffect(() => {
    getChatMessages()
  }, [assignationId, patientUid])

  // Mostrar la interfaz alternativa si el consultorio no está activo
  return (
    <div className="attContainer min-[540px]:h-full w-full flex flex-col mb-0 pb-0">
      {children}
    </div>
  )
}
