import { attTypeTelehealthRoom } from '@/app/(providers)/(loggedIn)/doctor/page'
import RoomWrapper from '@/components/Consultorio/Telehealth/TelehealthRoom/RoomWrapper'
import { getActiveServicesByCorporate } from '@/serverServices/getActiveServicesByCorporate'
import { getAssignation } from '@/serverServices/getAssignation'
import { getMedicalRecords } from '@/serverServices/getMedicalRecords'
import { getPatientByUid } from '@/serverServices/getPatientByUid'
import { getAttCollectionPath } from '@/serverServices/utils/getServiceFromSpecialtyAndAttType'
import { TSpecialties } from '@umahealth/entities'
import { ReactNode } from 'react'
  
export default async function RoomWrapperServer({ assignationId, children, attType, specialty}: {attType: attTypeTelehealthRoom; assignationId: string, specialty: 'bag' | TSpecialties, children: ReactNode}) {

        const assignation = await getAssignation({
          service: getAttCollectionPath(attType, specialty) , // En AR siempre es bag
          assignationId: assignationId,
        })
    
        if (!assignation) {
          throw new Error(
            'No se encontró la asignación de este turno en la base de datos'
          )
        }
    
        const [medicalRecords, patient, activeServices] = await Promise.all([
          getMedicalRecords({
            patientUserUid: assignation.patient.uid,
          }),
          getPatientByUid({
            uid: assignation.patient?.uid_dependant ? assignation.patient.uid_dependant : assignation.patient.uid,
          }),
          getActiveServicesByCorporate({
            corporate:
              assignation.patient.corporate ?? `UMA ${process.env.NEXT_PUBLIC_COUNTRY}`,
          }),
        ])
    
        if (!patient) {
          throw new Error('No se encontró el paciente')
        }
    
        if (!activeServices) {
          throw new Error('No se encontraron los servicios activos para la OSS')
        }

    return (
        <RoomWrapper
            assignation={assignation}
            medicalRecords={medicalRecords}
            patient={patient}
            activeServices={activeServices}
        >
            {children}
        </RoomWrapper>
    )
    }
