'use client'
import OT, {  OTError, Session } from "@opentok/client";
import { useContext, useLayoutEffect, useState, createContext } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { useSearchParams } from "next/navigation";
import usePostLogs from "@/services/reactQuery/Logs/usePostLogs";
import { errorHandler } from "@/config/stackdriver";
import { ICloseChatData, useFinishCall } from "../hooks/useFinishCall";

interface SessionContextValue {
  session: Session | null;
  sessionError: OTError | null;
  finishCall: () => Promise<void>;
  isMicOn: boolean | null;
  isCameraOn: boolean | null;
  toggleMic: ((isOn: boolean) => void) | null;
  toggleCamera: ((isOn: boolean) => void) | null;
}

export const SessionContext = createContext<SessionContextValue>({
  session: null,
  sessionError: null,
  finishCall: async () => Promise.resolve(),
  isMicOn: null,
  isCameraOn: null,
  toggleMic: null,
  toggleCamera: null
});

/**
 * Componente `SessionManager` que gestiona la sesión de OpenTok y proporciona el contexto para la aplicación.
 *
 * @param {Object} props - Las propiedades del componente.
 * @param {React.ReactNode} props.children - Los elementos secundarios que serán renderizados dentro del proveedor del contexto.
 * @param {string} props.room - El ID de la sala (room) a la que se conectará la sesión de OpenTok.
 * @param {string} props.token - El token de autenticación necesario para conectar la sesión de OpenTok.
 *
 * @description
 * Este componente se encarga de inicializar y manejar una sesión de OpenTok. Utiliza `useLayoutEffect` para crear la sesión,
 * conectarse a la misma y manejar diferentes eventos como la creación de la conexión, destrucción de la conexión, entre otros.
 * También maneja la finalización de la llamada a través del método `finishCall`.
 *
 * @example
 * ```jsx
 * <SessionManager room="sala123" token="token123">
 *   <YourComponent />
 * </SessionManager>
 * ```
 */
export default function SessionManager({ children, room, token }: any)  {
  const [session, setSession] = useState<Session | null>(null);
  const [sessionError, setSessionError] = useState<OTError | null>(null);
  const { currentUser } = useAppSelector((state) => state.user);
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch()
  const dependant: string = searchParams.get("dependant") as string;
  const assignationId: string = searchParams.get("assignationId") as string;
  const { mutate } = usePostLogs(
    currentUser.uid ?? "NO",
    assignationId,
    dependant,
  );

  const { patient, currentAppointment } = useAppSelector(state => state.queries)
  const { profile } = useAppSelector((state) => state.user)
  const finishCallHook = useFinishCall()

  const [isMicOn, setIsMicOn] = useState<boolean | null>(true);
  const [isCameraOn, setIsCameraOn] = useState<boolean | null>(true);

  const toggleCamera = (isOn: boolean) => {
    setIsCameraOn(isOn)
  }

  const toggleMic = (isOn: boolean) => {
    setIsMicOn(isOn)
  }

  /* 
	Descripción de los eventos en:
	https://tokbox.com/developer/sdks/js/reference/Session.html#on
	*/
  const sessionEventHandlers = {
    connectionCreated: async () => {
      mutate({ events: "providerSessionConnectionCreated" });
    },
    // El doc se desconectó.
    // A client, other than your own, has disconnected from the session.
    connectionDestroyed: async () => {
      mutate({ events: "providerSessionConnectionDestroyed" });
    },
    sessionConnected: async () => {
      mutate({ events: "providerSessionConnected" });
    },
    // La sesión se desconectó
    sessionDisconnected: async () => {
      mutate({ events: "providerSessionDisconnected" });
    },
    // La sesión se desconectó pero está intentando reconectar
    sessionReconnecting: async () => {
      console.info("Reconectando sesión"); // TO DO: Avisarle al usuario
      mutate({ events: "providerSessionConecting" });
    },
    sessionReconnected: async () => {
      mutate({ events: "providerSessionReconnected" });
    },
    // El doc dejó de publicar
    // A stream from another client has stopped publishing to the session.
    streamCreated: async () => {
      mutate({ events: "providerSessionStreamCreated" });
    },
    streamDestroyed: async () => {
      mutate({ events: "providerSessionStreamDestroyed" });
    },
  };

  /* Me traje este método desde ActionPanel para poder armar el flujo de los elementos de video de otra manera */
   /**
   * Maneja la finalización de la llamada, desconectando la sesión de OpenTok y registrando el evento.
   *
   * @async
   * @function finishCall
   * @returns {Promise<void>}
   */
  const finishCall = async () => {
    try {
        const closeChatData: ICloseChatData = {
            assignation_id: '',
            uid: patient.core_id,
            provider_uid: profile.uid || profile.core_id || currentAppointment.patient.uid,
            type: 'online'
        };

        finishCallHook.mutate(closeChatData);
        session?.disconnect()
        mutate({ events: 'providerFinishedCall' })
    } catch (err) {
        console.error(err)
        if(err) {
            errorHandler?.report('ERROR DISCONNECTING', err)
        }
    }
}

  useLayoutEffect(() => {
    // Connect to the session
    const newSession =
      typeof OT != "undefined" ? OT?.initSession(process.env.NEXT_PUBLIC_OPENTOK_APIKEY, room) : null;
    if (newSession) {
      setSession(newSession);
      dispatch({ type: 'SET_SESSION', payload: newSession })

      newSession.connect(token, function (error) {
        // If the connection is successful, start listening for events or errors from the session
        Object.entries(sessionEventHandlers).forEach(([event, handler]) => {
          newSession.on(event, handler);
        });

    //     /** A stream has started or stopped publishing audio or video (see Publisher.publishAudio() and Publisher.publishVideo()).
    //      * This change results from a call to the publishAudio() or publishVideo() methods of the Publish object.
    //      * Note that a subscriber's video can be disabled or enabled for reasons other than the publisher disabling or enabling it.
    //      * A Subscriber object dispatches videoDisabled and videoEnabled events in all conditions that cause the subscriber's stream to be disabled or enabled.
    //      */
        newSession.on<"streamPropertyChanged">(
          "streamPropertyChanged",
          async (event) => {
            if (event.changedProperty === "hasVideo") {
              mutate({
                events: "providerSessionStreamPropertyChanged",
                value: `changes hasVideo from: ${event.oldValue}, to: ${event.newValue}`,
              });

              if (event.newValue === false) {
                event.preventDefault();
              }
            }

            if (event.changedProperty === "hasAudio") {
              mutate({
                events: "providerSessionStreamPropertyChanged",
                value: `changes hasAudio from: ${event.oldValue}, to: ${event.newValue}`,
              });
            }

            if (event.changedProperty === "videoDimensions") {
              mutate({
                events: "providerSessionStreamPropertyChanged",
                value: `changes videoDimensions from: ${event.oldValue}, to: ${event.newValue}`,
              });
            }
          },
        );

        if (
          error?.name === "OT_AUTHENTICATION_ERROR" ||
          error?.name === "OT_INVALID_SESSION_ID"
        ) {
          console.error({ events: `PROVIDER_${error?.name}` });
          mutate({ events: `PROVIDER_${error?.name}` });
          // router.replace(routes.onlinedoctor.closedAtt(assignationId))
        } else if (error) {
          console.error(error.name);
          setSessionError(error);
        }
      });
      return () => {
        Object.entries(sessionEventHandlers).forEach(([event, handler]) => {
          newSession.off(event, handler);
        });
      };
    }
  }, []);


  return (
    <SessionContext.Provider value={{ session, sessionError, finishCall, isMicOn, toggleMic, isCameraOn, toggleCamera }}>
      {children}
    </SessionContext.Provider>
  );
}


/**
 * Hook personalizado `useSessionManagerContext` que proporciona el contexto de la sesión OpenTok.
 *
 * @returns {SessionContextValue} - El valor del contexto que incluye la sesión OpenTok, cualquier error relacionado con la sesión,
 * y la función para finalizar la llamada.
 *
 * @throws {Error} - Lanza un error si el hook es utilizado fuera del proveedor de contexto `SessionContext`.
 *
 * @description
 * Este hook proporciona acceso al contexto de la sesión OpenTok en cualquier componente de la aplicación que lo necesite.
 * Permite acceder a la sesión actual, a los errores de la sesión, y proporciona una función para finalizar la llamada.
 * Si este hook es utilizado fuera de un `SessionContext.Provider`, lanzará un error para indicar que debe estar dentro del contexto adecuado.
 *
 * @example
 * ```jsx
 * const { session, sessionError, finishCall } = useSessionManagerContext();
 * ```
 */
export function useSessionManagerContext() {
  const context = useContext(SessionContext)
  if(!context) {
    throw new Error('useSessionManagerContext must be used within a SessionContext Provider')
  }

  return context;
}

