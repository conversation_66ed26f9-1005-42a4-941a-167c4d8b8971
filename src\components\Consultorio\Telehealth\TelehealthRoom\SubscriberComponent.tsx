import React, { useEffect, ReactElement, useCallback, useState } from 'react';
import { RemoteVideo } from '@umahealth/occipital/client';
import { useSessionManagerContext } from './SessionManager';
import { useAppSelector } from '@/store/hooks/useAppSelector';
import usePostLogs from '@/services/reactQuery/Logs/usePostLogs';
import { useSearchParams } from 'next/navigation';
import { useAppDispatch } from '@/store/hooks';
import { useQueryClient } from '@tanstack/react-query';
import { setHasPatientConnected, setPatientIsConnected, setPatientIsDisconnected } from '@/services/serverSideServices/getAppointmentsLogs';

export const SubscriberComponent = ({ children } : { children: ReactElement } ) => {
    const { session }  = useSessionManagerContext()
    const [stream, setStream] = useState(null);
    const { currentUser } = useAppSelector(state => state.user);
    const searchParams = useSearchParams();
    const dependant = searchParams.get('dependant') as string;
    const assignationId = searchParams.get('assignationId') as string;
    const { mutate } = usePostLogs(currentUser.uid ?? 'NO', assignationId, dependant);
    const dispatch = useAppDispatch();
    const queryClient = useQueryClient();

    const subscriberEventHandlers = {
        audioBlocked: () => {
            mutate({ events: 'providerSubscriberAudioBlocked' });
        },
        audioUnblocked: () => {
            mutate({ events: 'providerSubscriberAudioUnblocked' });
        },
        connected: async () => {
            mutate({ events: 'providerSubscriberConnected' });
            console.log('Se conectó el paciente.')
            dispatch({ type: 'SET_PATIENT_CONNECTION', payload: true });
            await setHasPatientConnected(assignationId)
            await setPatientIsConnected(assignationId)
            // Invalidar la consulta para forzar su actualización
            queryClient.invalidateQueries({ queryKey: ['hasPatientLogs', assignationId] })
        },
        destroyed: async() => {
            mutate({ events: 'providerSubscriberDestroyed' });
            dispatch({ type: 'SET_PATIENT_CONNECTION', payload: false });
            await setPatientIsDisconnected(assignationId)
        },
        disconnected: async () => {
            mutate({ events: 'providerSubscriberDisconnected' });
            dispatch({ type: 'SET_PATIENT_CONNECTION', payload: false });
            await setPatientIsDisconnected(assignationId)
        },
        videoDisabled: () => {
            mutate({ events: 'providerSubscriberVideoDisabled' });
        },
        videoDisableWarning: () => {
            mutate({ events: 'providerSubscriberVideoDisableWarning' });
        },
        videoDisableWarningLifted: () => {
            mutate({ events: 'providerSubscriberVideoDisableWarningLifted' });
        },
        videoEnabled: () => {
            mutate({ events: 'providerSubscriberVideoEnabled' });
        }
    };

    useEffect(() => {
        if (!session) return;
    
        session.on('streamCreated', (event: any) => {
            setStream(event.stream);
        });

        session.on('streamDestroyed', () => {
            setStream(null);
        });
    
        return () => {
            session.off('streamCreated');
            session.off('streamDestroyed');
        };
    }, [session]);

    const subscriberRef = useCallback((node: HTMLDivElement & { videoClassname?: string } | null) => {
            if (node && session && stream) {
                const newSubscriber = session.subscribe(stream, node, {
                        insertMode: 'append',
                        width: '100%',
                        height: '100%',
                        showControls: false
                    }, (error) => {
                        if (error) {
                            console.error('Error subscribing:', error);
                        }
                    });

                Object.entries(subscriberEventHandlers).forEach(([event, handler]) => {
                    newSubscriber.on(event, handler);
                });

                newSubscriber.on('videoElementCreated', (event) => {
                    const videoElement = event.element;
                    videoElement.style.height = '100%';
                    videoElement.style.objectFit = 'cover';
                });
            }
        },
        [session, stream]
    );

    useEffect(() => {
        if (!session || !stream) return;

        return () => {
            const subscribers = session.getSubscribersForStream(stream);
            subscribers.forEach((sub) => {
                Object.keys(subscriberEventHandlers).forEach((event) => {
                    sub.off(event);
                });
                session.unsubscribe(sub);
            });
        };
    }, [session, stream]);

    return (
        <RemoteVideo 
            className="[&>div]:w-full w-full [&>div]:h-full h-full overflow-hidden [&>div>div>div>button]:w-full" 
            ref={subscriberRef}
        >  
            {children}
        </RemoteVideo>
    );
};