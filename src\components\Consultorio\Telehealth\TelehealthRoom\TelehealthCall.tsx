import { useBeforeunload } from "../../VideoCall/Attention/useBeforeunload";
import { PublisherComponent } from "./PublisherComponent";
import { useRef } from "react";
import '../telehealthStyles.css'
import { SubscriberComponent } from "./SubscriberComponent";

const TelehealthCall = () => {
    const subscriberContainerRef = useRef<HTMLDivElement>(null);

	/* Tengo mis dudas de que esto esté haciendo algo */
	useBeforeunload(() => 'Por favor, le solicitamos no salga de la consulta, cierrela luego de los 3 minutos, sino los pacientes se quedan esperando aparezca el doctor y no pueden sacar otra consulta.')
		
	return (
		<>
			<div className="video-parent" ref={subscriberContainerRef}>
				<SubscriberComponent>
					<PublisherComponent parentRef={subscriberContainerRef} />
				</SubscriberComponent>
			</div>
		</>
	)
};

export default TelehealthCall;
