import React, { memo, useContext, useState } from "react";
import TelehealthCall from './TelehealthCall'
import ActionPanel from '../ActionPanel/ActionPanel'
import MicToggleButton from '../ActionPanel/MicToggleButton/MicToggleButton'
import CameraToggleButton from '../ActionPanel/CameraToggleButton/CameraToggleButton'
import { useSessionManagerContext } from './SessionManager'
import AppointmentManager from '@/modules/consultorio/presentation/views/AppointmentManager'
import CloseAtt from '../../Columns/CloseAtt'
import { useEndVideoCall } from '@/modules/consultorio/presentation/components/EndButton/EndVideoCallContext'
import GuidedTour from '../GuidedTour/GuidedTour'
import PatientInfo from '@/modules/consultorio/presentation/components/PatientInfo/PatientInfo'
import Chat from '../Chat/Chat'
import { useAppSelector } from "@/store/hooks";
import NotificationPopup from "@/storybook/components/NotificationPopup/NotificationPopup";
import { CurrentTabContext } from "../hooks/useCurrentTab";

interface Props {
  hasSeenGuidedTour: boolean
  withoutVideo?: boolean
  withoutChat?: boolean
}

const TelehealthRoom = ({ hasSeenGuidedTour, withoutVideo, withoutChat = false }: Props) => {
  const { isMicOn, isCameraOn, toggleCamera, toggleMic } =
    useSessionManagerContext()
  const { videoCallEnded } = useEndVideoCall()
  const appointment = useAppSelector((state) => state.queries.currentAppointment)
  const { setCurrentTabView } = useContext(CurrentTabContext)
  
  //obtengo el IncidenteId para saber si mostrar o no el pop de derivado de emergencia
  const emergenciaIncidenteId = appointment.emergenciaIncidenteId
  const [showPatientDerivedFromEMER, setshowPatientDerivedFromEMER] = useState(!!emergenciaIncidenteId)
  // Determinar si debemos mostrar la columna derecha
  const showRightColumn = !withoutVideo || !withoutChat;
  
  return (
    <div className="mx-4 grid h-full grid-rows-[1fr_auto] gap-6">
      {!hasSeenGuidedTour && <GuidedTour />}

      <div className="grid grid-cols-[3fr_1fr] gap-6 min-h-0 overflow-hidden">
        {/* Panel izquierdo - Ficha de atención */}
        <div className="rounded-xl overflow-hidden min-w-0 min-h-0 flex flex-col">
          {/* Información del paciente y OptionsMenu */}
          <PatientInfo />
          
          {/* Contenido del AppointmentManager */}
          <div className="flex-grow overflow-auto">
            <AppointmentManager />
          </div>
          {showPatientDerivedFromEMER &&
            <NotificationPopup
              title='Paciente derivado de Emergencias' 
              description='El paciente realizó un triaje telefónico con operadores del servicio de Emergencias.' 
              onClose={() => { setshowPatientDerivedFromEMER(false)}}
              primaryButton={{
                label:'Revisar Motivo de consulta', 
                onClick: () => {
                  setCurrentTabView('adjuntos')
                  setshowPatientDerivedFromEMER(false)
                }
              }}
            />}
        </div>

        {/* Panel derecho - Videollamada y chat */}
        {showRightColumn && (
        <div className="rounded-xl overflow-hidden min-w-0 min-h-0 flex flex-col gap-4">
          {/* Videollamada */}
          <div className="h-3/5 bg-slate-600 rounded-xl overflow-hidden">
            {!videoCallEnded ? (
              <TelehealthCall />
            ) : (
              <div className="hidden" />
            )}
          </div>
          
          {/* Chat */}
          <div className="w-full h-2/5 bg-white rounded-xl overflow-hidden">
            <div className="h-full w-full">
              <Chat />
            </div>
          </div>
          
        </div>
      )}
      </div>

      {/* Panel de control */}
      <div className="flex justify-center">
        <ActionPanel>
          <MicToggleButton isMicOn={isMicOn} onToggleAudio={toggleMic} />
          <CameraToggleButton
            isCameraOn={isCameraOn}
            onToggleCamera={toggleCamera}
          />
          <div className="endcall-step">
            <CloseAtt  />
          </div>
        </ActionPanel>
      </div>
    </div>
  )
}

// Memoizar el componente para evitar re-renderizados innecesarios
export default memo(TelehealthRoom)