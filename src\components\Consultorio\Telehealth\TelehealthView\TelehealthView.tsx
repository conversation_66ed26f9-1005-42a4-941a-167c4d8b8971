'use client'
import '../telehealthStyles.css'
import { useAppSelector } from '@/store/hooks';
import TelehealthRoom from '../TelehealthRoom/TelehealthRoom';
import { IAppointment, IGuardiaAppointment, IOnlineAppointment } from '@umahealth/entities';
import CurrentTabProvider from '../hooks/useCurrentTab';
import SessionManager from '../TelehealthRoom/SessionManager';
import { EndVideoCallProvider } from '@/modules/consultorio/presentation/components/EndButton/EndVideoCallContext';
import { AttFormValidationProvider } from '@/modules/consultorio/presentation/components/AttFile/AttFormValidationContext';
import { AssignationFormDataProvider, AssignationFormDataType } from '@/cookies/AssignationFormDataContext';
import { TabsModalProvider, useTabsModal } from '@/contexts/TabsModalContext';
import { useEffect } from 'react';
import dynamic from 'next/dynamic'
import { useGetPredocAgentFeature } from '@/hooks/useGetPredocAgentFeature';

interface Props {
  hasSeenGuidedTour: boolean
  initialFormData?: AssignationFormDataType
  assignationId?: string | null
  providerUid: string
}

const PredocButton = dynamic(() => import('@/modules/consultorio/presentation/components/PatientInfo/PredocButton'))
const TabsModal = dynamic(() => import('@/components/TabsModal/TabsModal'))

const TelehealthViewContent = ({ hasSeenGuidedTour, initialFormData, assignationId, providerUid }: Props) => {
  const { currentAppointment } = useAppSelector((state) => state.queries)
  const { setTabs, openModal } = useTabsModal()
  const isEnabledPredoc = useGetPredocAgentFeature(providerUid)

  // Initialize tabs with the Predoc component
  useEffect(() => {
    if (isEnabledPredoc.data) {
      setTabs([
        {
          id: 'predoc',
          title: 'Prescripción',
          content: (
            <div className="w-full h-full">
              <PredocButton />
            </div>
          )
        }
      ])
      openModal()
  }
  }, [setTabs, isEnabledPredoc.data])

  // Type guard to check if the current appointment has a room
  const hasRoom = (appointment: IAppointment): appointment is IOnlineAppointment | IGuardiaAppointment => {
    return 'room' in appointment && typeof appointment.room === 'string';
  };

  if (hasRoom(currentAppointment)) {
    return <>
        <SessionManager
          room={currentAppointment.room}
          token={currentAppointment.token}
        >
          <EndVideoCallProvider>
            <AttFormValidationProvider>
              <AssignationFormDataProvider initialData={initialFormData} assignationId={assignationId || null}>
                <CurrentTabProvider >
                  <TelehealthRoom hasSeenGuidedTour={hasSeenGuidedTour} />
                </CurrentTabProvider>
              </AssignationFormDataProvider>
            </AttFormValidationProvider>
          </EndVideoCallProvider>
        </SessionManager>
    </>
  }

  return (
    <>
        <EndVideoCallProvider>
          <AttFormValidationProvider>
            <AssignationFormDataProvider initialData={initialFormData} assignationId={assignationId || null}>
              <CurrentTabProvider>
                <TelehealthRoom hasSeenGuidedTour={hasSeenGuidedTour} withoutVideo withoutChat />
              </CurrentTabProvider>
            </AssignationFormDataProvider>
          </AttFormValidationProvider>
        </EndVideoCallProvider>
    </>
  )
}

const TelehealthView = (props: Props) => {
  return (
    <TabsModalProvider>
      <TelehealthViewContent {...props} />
      <TabsModal />
    </TabsModalProvider>
  )
}

export default TelehealthView
