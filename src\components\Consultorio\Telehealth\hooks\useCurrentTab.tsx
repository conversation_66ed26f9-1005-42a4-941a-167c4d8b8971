import React, { createContext, useState } from 'react';

export type TabView = 'consulta' | 'antecedentes' | 'monitoreos' | 'diagnostico_asistido' | 'adjuntos' | 'reportar_incidente';

interface CurrentTabContextType {
  currentTabView: TabView;
  setCurrentTabView: React.Dispatch<React.SetStateAction<TabView>>;
}

export const CurrentTabContext = createContext<CurrentTabContextType>({
  currentTabView: 'consulta',
  setCurrentTabView: () => {
    // dummy function to prevent eslint error
  }, 
});

export default function CurrentTabProvider({ children }: { children: React.ReactNode }) {
  const [currentTabView, setCurrentTabView] = useState<TabView>('consulta');

  return (
    <CurrentTabContext.Provider value={{ currentTabView, setCurrentTabView }}>
      {children}
    </CurrentTabContext.Provider>
  );
}
