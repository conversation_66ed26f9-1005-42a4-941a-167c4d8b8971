import { close_chat } from "@/config/endpoints";
import { auth } from "@/config/firebase";
import axios from "axios";
import { useMutation } from "react-query";

export interface ICloseChatData {
    assignation_id: string,
    uid: string | undefined,
    provider_uid: any,
    type: string
}


const finishCall = async (
	closeChatData: ICloseChatData
): Promise<any> => {
	const firebaseToken = await auth.currentUser?.getIdToken();
	const headers = {
		Authorization: `Bearer ${firebaseToken}`,
	};

    const response = await axios.post(close_chat, closeChatData, {headers})
	return response;
};


export const useFinishCall = () => {
    const mutator = useMutation(["closeOperation"], (closeChatData: ICloseChatData) =>
        finishCall(closeChatData),
    );
    return mutator;
  };