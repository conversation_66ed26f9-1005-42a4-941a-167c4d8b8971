import { ReactNode, useEffect, useState } from "react";
import { useMutation } from "react-query";
import { Socket } from "socket.io-client";

export interface IChatMessage {
  status: "isLoading" | "isOnServer" | "wasReaded" | "isError" | "wasSended";
  sendTime: Date;
  messageOrigin: "self" | "other";
  children: ReactNode;
}

export function useGetNewMessages(
  socket: Socket | null,
  operationId: string,
  uid: string|undefined
){
  const [newMessages, setNewMessages] = useState<null | IChatMessage>(null);
  
  useEffect(() => {
    if (socket && uid) {
      socket.emit("joinRoom", { operationId: operationId });

      socket.on(
        "receiveMessage",
        (messageData: { senderId: string; message: string }) => {
          console.log("senderId", messageData.senderId);
          setNewMessages(
            {
              messageOrigin: messageData.senderId === uid ? 'self' : 'other',
              sendTime: new Date(),
              children: messageData.message,
              status: "wasSended",
            },
          );
        },
      );
    }
  }, [operationId, socket, uid]);

  return newMessages;
}

export function useSetMessage(socket: Socket|null, operationId: string){
    return useMutation(async ({message} : {message : string}) => {
    if (socket === null){
        throw new Error('Se trato de mandar un mensaje y socket era null')
    }
    
    await new Promise((resolve) => setTimeout(resolve, 2000));

    console.log('time to emit socketss')
    socket.emit("joinRoom", { operationId: operationId });

    socket.emit("sendMessage", {
      operationId: operationId,
      message: message,
    });
    })
}