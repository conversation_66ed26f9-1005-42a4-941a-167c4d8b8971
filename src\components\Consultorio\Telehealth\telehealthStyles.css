.video-parent {
    height: 100%;
}

.att-container {
	display: flex;
	gap: 12px;
    /* background-color:antiquewhite; */
    position: absolute;
    flex-direction: column;
    margin-top: 15%;
}

.att-wrapper {
    display: flex;
	gap: 12px;
    width: 100%;
    flex-direction: row;
    flex-grow: 3;
    padding: 8px;
}

.info {
    /* background-color: aquamarine; */
    height: 100%;
    flex-grow: 3;
}

.call {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 384px;
}

.video-call-container {
    height: 110px;
}

.action-panel {
    height: 76px;
    width: 100%;
    padding: 8px;
    display: flex;
    justify-content: center;
}

@media (min-width: 1536px) { 
    .video-call-container {
        flex-grow: 3;
    }    

    .att-container {
        gap: 24px;
        margin-top: 24px;
    }

    .att-wrapper {
        gap: 24px;
        padding: 16px;
    }

    .call {
        gap: 16px;
    }

    .action-panel {
        padding: 16px;
        height: 92px;
    }
    
 }
