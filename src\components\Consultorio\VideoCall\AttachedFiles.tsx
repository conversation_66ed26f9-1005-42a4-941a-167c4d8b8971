import React from 'react'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import moment from 'moment-timezone'
import { ref, listAll, getDownloadURL, getMetadata } from 'firebase/storage'
import { storage } from '@/config/firebase'
import { MdAttachFile } from 'react-icons/md'
import { IPatient } from '../Attached/Utils/interfaces'

const AttachedFiles = () => {
	const dispatch = useAppDispatch()
	const patient: IPatient = useAppSelector((state: any): IPatient => state.queries.patient)
	const { leftColumn } = useAppSelector((state: any) => state.front.attLayout)
	const idPatient = patient.core_id || patient.uid || patient.id

	const getAttacheds = async () => {
		if (storage) {
			dispatch({ type: 'ATTACHED_LOADING', payload: true })
			dispatch({ type: 'LAYOUT_ATT_LEFT', payload: 'attached' })
			try {
				const storageRef = ref(storage, `${idPatient}/attached`)
				const res = await listAll(storageRef)
				const files = await Promise.all(res.items.map(async (item) => {
					const url = await getDownloadURL(item)
					const metadata = await getMetadata(item)
					return {
						url,
						date: moment(item.name.split('_')[0], 'YYYYMMDDHHmmSS').format('DD/MM/YYYY HH:mm'),
						name: item.name.split('_')[1],
						metadata,
					}
				}))
				dispatch({ type: 'SET_SHOW_ATTACHED', payload: { files } })
			} catch (error) {
				console.error(error)
			} finally {
				dispatch({ type: 'ATTACHED_LOADING', payload: false })
			}
		}
	}

	return (
		<button className={leftColumn === 'attached' ?'text-primary border border-primary p-[8px] my-[5px] cursor-pointer font-bold flex items-center text-[0.9rem] transition-all duration-200 rounded-[24px] bg-white shadow-[7px_7px_14px_#cccccc,-7px_-7px_14px_#ffffff]' : 'text-primary border p-[8px] my-[5px] cursor-pointer font-bold flex items-center text-[0.9rem] transition-all duration-200 rounded-[24px] bg-white shadow-[7px_7px_14px_#cccccc,-7px_-7px_14px_#ffffff]'} onClick={getAttacheds}>
			<MdAttachFile className="text-[1.2rem] mx-[10px]"  aria-hidden='true' />Adjuntos
		</button>
	)
}

export default AttachedFiles
