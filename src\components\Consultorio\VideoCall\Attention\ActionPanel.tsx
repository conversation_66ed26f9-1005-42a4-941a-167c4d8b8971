import {  MutableRefObject, ReactNode, useContext, useEffect, useState } from 'react'
import { Publisher } from '@opentok/client'
import InfoSign from './InfoSign'
import { MdChat, MdPhone, MdMicOff,  MdMic} from 'react-icons/md'
import NotificationBubble from '../utils/NotificationBubble'
import { useAppDispatch } from '@/store/hooks/useAppDispatch'
import getFirebaseIdToken from "@/services/firebase/getFirebaseIdToken";
import { useAppSelector } from '@/store/hooks/useAppSelector'
import axios from 'axios'
import { close_chat } from '@/config/endpoints'
import { errorHandler } from '@/config/stackdriver'
import { SessionContext } from './SessionManager'
import usePostLogs from '@/services/reactQuery/Logs/usePostLogs'
import { useRouter, useSearchParams } from 'next/navigation'
import { appointmentStates } from '@umahealth/entities'
import { DialogContent, DialogDescription, DialogOverlay, DialogPortal, DialogRoot, DialogTitle } from '@umahealth/occipital/client';
import { Paragraph, Title } from '@umahealth/occipital';



interface IActionPanel {
	publisher: MutableRefObject<Publisher | null>;
	children: ReactNode;
}

/** Panel de acciones que se desplega al tappear sobre la videollamada, empieza desplegado y desaparece luego de cierta cantidad breve de tiempo. */
function ActionPanel({ publisher, children } : IActionPanel ){
	const dispatch = useAppDispatch()
	const [audio, setAudio] = useState(true)
	const router = useRouter()
	const { patient, currentAppointment } = useAppSelector(state => state.queries)
	const { profile } = useAppSelector((state) => state.user)
	const { session } : {session : any} = useContext(SessionContext)
	const [sessionDisconnected, setSessionDisconnected] = useState(session?.currentState === 'disconnected' || false)
	const { currentUser } = useAppSelector(state => state.user)
	const searchParams = useSearchParams()
	const dependant : string = searchParams.get('dependant') as string
	const assignationId : string = searchParams.get('assignationId') as string
	const { mutate } = usePostLogs(currentUser.uid ?? 'NO', assignationId, dependant)
  const [isAppointmentFinishedModal, setIsAppointmentFinishedModal] = useState({open: false, message: ''})
	
	const finishCall = async () => {
		// Se comprueba que el estado de la consulta no haya sido ya cerrado por el usuario o la barredora de consultas
		if (
      currentAppointment.state === 'USER_CANCEL' ||
			currentAppointment.state === 'DONE' ||
      currentAppointment.state === 'AUTOMATIC_CANCEL' ||
      currentAppointment.state === 'AUTOMATIC_CLOSE' ||
      currentAppointment.state === 'CANCEL_BY_ELAPSED_TIME' as appointmentStates
    ) {
			
			const message = currentAppointment.state === 'DONE' ? 'Esta consulta ya fue cerrada' :	'El paciente canceló la consulta'

			setIsAppointmentFinishedModal({open: true, message})

			setTimeout(() => {
				router.replace('/appointments')
			}, 3000);
			return 
		}

		try {
			const closeChatData = {
				assignation_id: '',
				uid: patient.core_id,
				provider_uid: profile.uid || profile.core_id || currentAppointment.patient.uid,
				type: 'online'
			}
			const token = await getFirebaseIdToken()
			const headers = {
				'Content-Type': 'Application/Json',
				'Authorization': `Bearer ${token}`
			}
			session?.disconnect()
			setSessionDisconnected(true)
			await axios.post(close_chat, closeChatData, {headers})
			mutate({ events: 'providerFinishedCall' })
		} catch (err) {
			console.error(err)
			if(err) {
				errorHandler?.report('ERROR DISCONNECTING', err)
			}
		}
	}

	const handleChat = () => {
		dispatch({ type: 'LAYOUT_ATT_CHAT', payload: true })
		mutate({ events: 'providerOpenedChat' })
	}

	useEffect(() => {
		if(session?.currentState === 'disconnected') {
			setSessionDisconnected(true)
		}
	}, [session])

	return (
		<div className="h-full w-full z-0">
			{sessionDisconnected ? <>
					<div className='flex justify-center items-center absolute left-[10px] bottom-[5px] text-[0.9rem]'>
						<div className='relative flex items-center justify-center w-[40px] h-[40px]'>
							<button className='border-none cursor-pointer w-[40px] h-[40px] m-[5px] rounded-full flex items-center justify-center text-white bg-success m-0 p-0' onClick={() => handleChat()}>
								<MdChat aria-hidden='true' />
							</button>
							<NotificationBubble />
						</div>
					</div>
					<div className='absolute'> 
						{publisher?.current?.accessAllowed === false ?
							<InfoSign icon='warning' text='No se logró acceder a tu cámara y/o micrófono. Por favor comprueba los permisos.'/>
							:
							<InfoSign icon='warning' text='La videollamada está desconectada.'/>
						}
					</div> 
				</>
				:
				<div className='flex justify-center items-center absolute inset-x-0 bottom-[5px] text-[0.9rem] gap-1'>
					<button className='border-none cursor-pointer w-[40px] h-[40px] rounded-full text-white bg-error flex items-center justify-center disabled:bg-[#BDBDBD] disabled:cursor-default' onClick={finishCall}>
						<MdPhone aria-hidden='true' />
					</button>
					<div className='relative flex items-center justify-center w-[40px] h-[40px]'>
						<button className='border-none cursor-pointer w-[40px] h-[40px] rounded-full flex items-center justify-center text-white bg-success m-0 p-0' onClick={() => handleChat()}>
							<MdChat aria-hidden='true' />
						</button>
						<NotificationBubble />
					</div>
					<div className='relative flex items-center justify-center w-[40px] h-[40px]'>
						<button className='border-none cursor-pointer w-[40px] h-[40px] rounded-full flex items-center justify-center text-white bg-secondary m-0 p-0' onClick={() => {
								publisher.current?.publishAudio(!audio)
								setAudio(!audio)
								mutate({ events: 'providerMuteCall' })
							}}>
								{audio ? <MdMic aria-hidden='true' /> : <MdMicOff aria-hidden='true' />}
						</button>
					</div>
				</div>
			}
			{children}

			{/* Modal de consulta ya finalizada */}
			<DialogRoot open={isAppointmentFinishedModal.open} modal={true}>
				<DialogPortal>
					<DialogOverlay className='bg-black/30' />
					<DialogContent className="flex items-center flex-col p-8">
					<DialogTitle asChild>
					<Title
						hierarchy="h1"
						color="text-primary-800"
						size="text-l"
						weight="font-semibold"
						className="mb-4"
					>
						{isAppointmentFinishedModal.message}
					</Title>
					</DialogTitle>
					<DialogDescription asChild>
					<Paragraph className="text-center pb-6">
						Serás redirigido a la pantalla de consultas
					</Paragraph>
					</DialogDescription>
					</DialogContent>
				</DialogPortal>
			</DialogRoot>
		</div>
	)
}

export default ActionPanel
