'use client'
import SessionManager from './SessionManager'
import Subscriber from './SubscriberComponent'
import PublisherComponent from './PublisherComponent'
import {useBeforeunload} from './useBeforeunload'

const Call = ({ room, token} : { room : string, token: string}) => {

	useBeforeunload(() => 'Por favor, le solicitamos no salga de la consulta, cierrela luego de los 3 minutos, sino los pacientes se quedan esperando aparezca el doctor y no pueden sacar otra consulta.')

	if(room && token) {
		return (
			<>
				<SessionManager room={room} token={token}>
					<Subscriber  />
					<PublisherComponent  />
				</SessionManager>
			</>
		)
	} else {
		return <div>
			Cargando
		</div>
	}
}

export default Call