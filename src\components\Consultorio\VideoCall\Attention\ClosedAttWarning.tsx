'use client'
import { isFarmatodo } from '@/config/endpoints'
import { Paragraph } from '@umahealth/occipital'
import CloseFarmacoAtt from '../../FarmacoAtt/CloseFarmacoAtt'


export default function ClosedAttWarning(){
	if (isFarmatodo) {
		return (
			<div className='flex flex-col h-full py-8 px-2 gap-10'>
				<div>
					<Paragraph weight='font-bold' size='text-s' color='text-grey-1'>
						Esta sesión ha expirado. Las llamadas tienen una duración máxima de 6 horas para poder ser utilizadas.
					</Paragraph>
				</div>
				<CloseFarmacoAtt />
			</div>
		)
	}

	return (
		<div
			style={{ 
				backgroundColor: 'var(--color-grey-4)',
				padding: '32px 8px',
				minHeight: '120px',
				height: '100%',
				position: 'absolute',
			}}
		>
			<div className='mt-24'>
				<Paragraph weight='font-bold' size='text-s' color='text-grey-1'>
          Esta sesión ha expirado. Las llamadas tienen una duración máxima de 6 horas para poder ser utilizadas.
        </Paragraph>
      </div>
    </div>
  )
}