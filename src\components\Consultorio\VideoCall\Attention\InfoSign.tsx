import { Icon, IconsNames, Paragraph } from 'occipital-new'
import { ReactNode } from 'react'

function InfoSign({icon, text, children} : {icon : IconsNames, children?: ReactNode, text: string}){
	return <div className='flex bg-neutral-200 w-[90%] rounded-lg p-2'>
		{children ?
			children :
			<Icon className='pr-4' name={icon??'warning'} color='grey-1' size='xs'/>
		}
		<Paragraph color='grey-1' size='xs' weight='regular'>
			{text}
		</Paragraph>

	</div>
}

export default InfoSign