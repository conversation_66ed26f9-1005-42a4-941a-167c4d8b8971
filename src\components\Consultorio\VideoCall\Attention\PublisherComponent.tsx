import { useContext, useLayoutEffect, useRef, useState } from 'react'
import OT, { Session, Publisher, VideoFilter } from '@opentok/client'
import { Icon } from 'occipital-new'
import { SessionContext } from './SessionManager'
import ActionPanel from './ActionPanel'
import { useInterval } from '@/utils/useInterval'
import useUploadDoctorAlivedPhoto from '@/services/reactQuery/uploadDoctorAlivedPhoto'
import { useAppSelector } from '@/store/hooks'
import { useSearchParams } from 'next/navigation'
import usePostLogs from '@/services/reactQuery/Logs/usePostLogs'
import { useAttentionParameters } from '@/services/reactQuery/useAttentionParameters'
import { trackVideoCallDevice } from '@/events/videocallEvents'
import getUserUid from '@/services/tokens/getUserUid'

type VideoFilterOption = { videoFilter: VideoFilter } | Record<string, never>;

const PublisherComponent = () => {
	const {session} : {session : Session | null} = useContext(SessionContext)
	const [published, setPublished] = useState<boolean>(false)
	const publisherRef = useRef<Publisher | null>(null)
	const doctorRef = useRef<HTMLDivElement | null>(null)
	const { currentAppointment, currentAtt } = useAppSelector(state => state.queries)
	const searchParams = useSearchParams()
	const dependant : string = searchParams.get('dependant') as string
	const assignationId : string = searchParams.get('assignationId') as string
	const uid = getUserUid()
	const { mutate } = usePostLogs(uid ?? 'No', assignationId, dependant)
	const attentionParams = useAttentionParameters()
	const uploadDoctorAlivePhoto = useUploadDoctorAlivedPhoto(currentAppointment.assignation_id, currentAtt.att_category, currentAppointment.provider?.uid || uid)


  useInterval(() => {
		if (publisherRef.current){
			const imgData = publisherRef?.current?.getImgData()
			if (imgData !== null) {
				uploadDoctorAlivePhoto.mutate({
					imageData: imgData
				})
			} else {
				console.error('No image data')
			}
		}
	}, 1000*20 /*20 segundos*/ )

	const publisherEventHandlers = {
		streamDestroyed: () => {
			mutate({
				events: 'providerPublisherStreamDestroyed'
			})
		},
		accessAllowed: () => {
			mutate({
				events: 'providerPublisherAccessAllowed'
			})
		},
		accessDenied: () => {
			mutate({
				events: 'providerPublisherAccessDenied'
			})
		},
		accessDialogClosed: () => {
			mutate({
				events: 'providerPublisherAccessDialogClosed'
			})
		},
		accessDialogOpened: () => {
			mutate({
				events: 'providerPublisherAccessDialogOpened'
			})
		},
		// audioLevelUpdated: () => {},  No se loguea porque se dispara muchas veces
		destroyed: () => {
			mutate({
				events: 'providerPublisherDestroyed'
			})
		},
		mediaStopped: () => {
			mutate({
				events: 'providerPublisherMediaStopped'
			})
		},
		streamCreated: () => {
			mutate({
				events: 'providerPublisherStreamCreated'
			})
		},
		// videoDimensionsChanged: () => {}, No se loguea porque se dispara muchas veces 
		//videoElementCreated: () => {}  No se loguea porque ya lo carga el useLayoutEffect
	}

	useLayoutEffect(() => {
		if(session) {
			session.on('sessionConnected', async () => {
				if(currentAppointment && uid){
					trackVideoCallDevice(currentAppointment.assignation_id, uid)
				}
				const activeBlur = attentionParams.data?.blurDoctorBackground
				const videoFilter: VideoFilterOption = OT.hasMediaProcessorSupport() && activeBlur
				? { videoFilter: { type: 'backgroundBlur', blurStrength: 'high' } }
				: {}			
				publisherRef.current = await OT.initPublisher(
					undefined,
					{
						insertMode: 'replace',
						width: '100%',
						height: '100%',
						insertDefaultUI: false,
						...videoFilter,
					},
					(error) => { if(error) console.error(error) },
				)
				publisherRef.current.on('videoElementCreated', (videoElementEvent) => {
					if (doctorRef.current && publisherRef.current) {
						setPublished(true)
						videoElementEvent.element.style.transform = 'scale(1.4)'
						doctorRef.current.replaceChildren(videoElementEvent.element)
					}
				})

				publisherRef.current.on('streamDestroyed', function(event) {
					
					if (event.reason === 'mediaStopped') {
						event.preventDefault()
					}
				})

				Object.entries(publisherEventHandlers).forEach(([subscriberEvent, subscriberHandler]) => {
					publisherRef.current?.on(subscriberEvent, subscriberHandler)
				})

				session.publish(publisherRef.current, (error) => { if(error) console.error(error) })
			})
		}
		return () => {
			if (session) {
				session.disconnect()
			}
			/** Es muy importante sólo destruir el publisher si efectivamente fue publicado, por eso esta la variable published, sino sale un error. */
			if (publisherRef.current) {
				publisherRef.current.destroy()
			}
		}
	}
	, [session])

	return <ActionPanel publisher={publisherRef}> 
		<div className="absolute mt-2.5 right-2.5 w-20 h-20 overflow-hidden z-0 rounded-full border-4 border-blue-500" ref={doctorRef}></div>
		{!published && <div className='absolute mt-2.5 right-2.5  w-20 h-20 bg-neutral-200 rounded-full flex items-center justify-center'>
			<Icon name='warning' color='grey-2' size='m' ariaHidden />
		</div>}
	</ActionPanel>
}

export default PublisherComponent
