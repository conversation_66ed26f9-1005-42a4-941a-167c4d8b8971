import OT, {  OTError, Session } from "@opentok/client";
import { useLayoutEffect, useState } from "react";
import { createContext } from "react";
import ClosedAttWarning from "./ClosedAttWarning";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { useSearchParams } from "next/navigation";
import usePostLogs from "@/services/reactQuery/Logs/usePostLogs";

interface SessionContextValue {
  session: Session | null;
  sessionError: OTError | null;
}

export const SessionContext = createContext<SessionContextValue>({
  session: null,
  sessionError: null,
});

const SessionManager = ({ children, room, token }: any) => {
  const dispatch = useAppDispatch()
  const [session, setSession] = useState<Session | null>(null);
  const [expiredSession, setExpiredSession] = useState<boolean>(false);
  const [sessionError, setSessionError] = useState<OTError | null>(null);
  const { currentUser } = useAppSelector((state) => state.user);
  const searchParams = useSearchParams();
  const dependant: string = searchParams.get("dependant") as string;
  const assignationId: string = searchParams.get("assignationId") as string;
  const { mutate } = usePostLogs(
    currentUser.uid ?? "NO",
    assignationId,
    dependant,
  );

  /* 
	Descripción de los eventos en:
	https://tokbox.com/developer/sdks/js/reference/Session.html#on
	*/
  const sessionEventHandlers = {
    connectionCreated: async () => {
      mutate({ events: "providerSessionConnectionCreated" });
    },
    // El doc se desconectó.
    // A client, other than your own, has disconnected from the session.
    connectionDestroyed: async () => {
      mutate({ events: "providerSessionConnectionDestroyed" });
    },
    sessionConnected: async () => {
      mutate({ events: "providerSessionConnected" });
    },
    // La sesión se desconectó
    sessionDisconnected: async () => {
      mutate({ events: "providerSessionDisconnected" });
    },
    // La sesión se desconectó pero está intentando reconectar
    sessionReconnecting: async () => {
      console.info("Reconectando sesión"); // TO DO: Avisarle al usuario
      mutate({ events: "providerSessionConecting" });
    },
    sessionReconnected: async () => {
      mutate({ events: "providerSessionReconnected" });
    },
    // El doc dejó de publicar
    // A stream from another client has stopped publishing to the session.
    streamCreated: async () => {
      mutate({ events: "providerSessionStreamCreated" });
    },
    streamDestroyed: async () => {
      mutate({ events: "providerSessionStreamDestroyed" });
    },
  };

  useLayoutEffect(() => {
    // Connect to the session
    const newSession =
      typeof OT != "undefined" ? OT?.initSession(process.env.NEXT_PUBLIC_OPENTOK_APIKEY, room) : null;
    if (newSession) {
      setSession(newSession);
      dispatch({ type: 'SET_SESSION', payload: newSession })
      newSession.connect(token, function (error) {
        // If the connection is successful, start listening for events or errors from the session
        Object.entries(sessionEventHandlers).forEach(([event, handler]) => {
          newSession.on(event, handler);
        });

        /** A stream has started or stopped publishing audio or video (see Publisher.publishAudio() and Publisher.publishVideo()).
         * This change results from a call to the publishAudio() or publishVideo() methods of the Publish object.
         * Note that a subscriber's video can be disabled or enabled for reasons other than the publisher disabling or enabling it.
         * A Subscriber object dispatches videoDisabled and videoEnabled events in all conditions that cause the subscriber's stream to be disabled or enabled.
         */
        newSession.on<"streamPropertyChanged">(
          "streamPropertyChanged",
          async (event) => {
            if (event.changedProperty === "hasVideo") {
              mutate({
                events: "providerSessionStreamPropertyChanged",
                value: `changes hasVideo from: ${event.oldValue}, to: ${event.newValue}`,
              });

              if (event.newValue === false) {
                event.preventDefault();
              }
            }

            if (event.changedProperty === "hasAudio") {
              mutate({
                events: "providerSessionStreamPropertyChanged",
                value: `changes hasAudio from: ${event.oldValue}, to: ${event.newValue}`,
              });
            }

            if (event.changedProperty === "videoDimensions") {
              mutate({
                events: "providerSessionStreamPropertyChanged",
                value: `changes videoDimensions from: ${event.oldValue}, to: ${event.newValue}`,
              });
            }
          },
        );

        if (
          error?.name === "OT_AUTHENTICATION_ERROR" ||
          error?.name === "OT_INVALID_SESSION_ID"
        ) {
          console.error({ events: `PROVIDER_${error?.name}` });
          mutate({ events: `PROVIDER_${error?.name}` });

          setExpiredSession(true);
          // router.replace(routes.onlinedoctor.closedAtt(assignationId))
        } else if (error) {
          console.error(error.name);
          setSessionError(error);
        }
      });
      return () => {
        Object.entries(sessionEventHandlers).forEach(([event, handler]) => {
          newSession.off(event, handler);
        });
      };
    }
  }, [room, token]);

  return (
    <SessionContext.Provider value={{ session, sessionError }}>
      {expiredSession && <ClosedAttWarning />}
      {children}
    </SessionContext.Provider>
  );
};

export default SessionManager;
