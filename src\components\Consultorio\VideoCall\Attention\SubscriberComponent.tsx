import { Session, Subscriber } from '@opentok/client'
import { useContext, useLayoutEffect, useRef } from 'react'
import { SessionContext } from './SessionManager'
import { useAppSelector } from '@/store/hooks/useAppSelector'
import usePostLogs from '@/services/reactQuery/Logs/usePostLogs'
import { useSearchParams } from 'next/navigation'
import { useAppDispatch } from '@/store/hooks'
import { useQueryClient } from '@tanstack/react-query'

const SubscriberComponent = () => {
	const subscriberRef = useRef<Subscriber | null>(null)
	const patientRef = useRef<HTMLDivElement | null>(null)
	const { session }: { session: Session | null } = useContext(SessionContext)
	const { currentUser } = useAppSelector(state => state.user)
	const searchParams = useSearchParams()
	const dependant: string = searchParams.get('dependant') as string
	const assignationId: string = searchParams.get('assignationId') as string
	const { mutate } = usePostLogs(currentUser.uid ?? 'NO', assignationId, dependant)
	const dispatch = useAppDispatch()
	const queryClient = useQueryClient()

	/* 
	Descripción de los eventos en:
	https://tokbox.com/developer/sdks/js/reference/Subscriber.html#events
	*/

	const subscriberEventHandlers = {
		audioBlocked: () => {
			mutate({ events: 'providerSubscriberAudioBlocked' })
		},
		// No se registra este evento porque dispara demasiados logs: audioLevelUpdated: () => {},
		audioUnblocked: () => {
			mutate({ events: 'providerSubscriberAudioUnblocked' })
		},
		connected: () => {
			mutate({ events: 'providerSubscriberConnected' })
			dispatch({ type: 'SET_PATIENT_CONNECTION', payload: true })
			queryClient.invalidateQueries({
				queryKey: ['patientLogs', assignationId, currentUser.uid],
			})
		},
		// Dispatched when the Subscriber element is removed from the HTML DOM. When this event is dispatched, you may choose to adjust or remove HTML DOM elements related to the subscriber.
		destroyed: () => {
			mutate({ events: 'providerSubscriberDestroyed' })
			dispatch({ type: 'SET_PATIENT_CONNECTION', payload: false })
		},
		disconnected: () => {
			mutate({ events: 'providerSubscriberDisconnected' })
			console.log('se desconecto')
			dispatch({ type: 'SET_PATIENT_CONNECTION', payload: false })
			queryClient.invalidateQueries({
				queryKey: ['patientLogs', assignationId, currentUser.uid],
			})
		},
		// No se registra este evento porque deja logs que no nos suman videoDimensionsChanged: () => {},
		videoDisabled: () => {
			// setDoctorVideoDisabled(true)
			mutate({ events: 'providerSubscriberVideoDisabled' })
		},
		videoDisableWarning: () => {
			mutate({ events: 'providerSubscriberVideoDisableWarning' })
		},
		videoDisableWarningLifted: () => {
			mutate({ events: 'providerSubscriberVideoDisableWarningLifted' })
		},
		// Este evento no se ejecuta acá porque ya está en el layoutEffect. videoElementCreated: () => {},
		videoEnabled: () => {
			// Video del doc enabled
			mutate({ events: 'providerSubscriberVideoEnabled' })
		},
	} as const

	useLayoutEffect(() => {
		if (!subscriberRef?.current && session) {
			session.on('streamCreated', function (event) {
				subscriberRef.current = session.subscribe(
					event.stream,
					undefined,
					{
						insertMode: 'replace',
						width: '100%',
						height: '100%',
						insertDefaultUI: false,
					},
					(error) => { if (error) { console.error(error) } } // TO DO: Manejar error
				)

				subscriberRef.current.on('videoElementCreated', (doctorVideoEvent) => {
					if (patientRef.current && subscriberRef.current) {
						const videoElement = doctorVideoEvent.element
						videoElement.style.height = '100%'
						videoElement.style.objectFit = 'cover'
						patientRef.current.appendChild(videoElement)
						console.info('videoElementCreated')
					}
				})

				Object.entries(subscriberEventHandlers).forEach(([subscriberEvent, subscriberHandler]) => {
					subscriberRef.current?.on(subscriberEvent, subscriberHandler)
				})

			})
		}
		return () => {
			if (session) {
				session.disconnect()
			}
		}
	}
		, [session])

	return <div
		ref={patientRef}
		className="absolute h-full w-full min-w-[250px] bottom-0 top-0 left-0 z-0">
	</div>
}


export default SubscriberComponent

