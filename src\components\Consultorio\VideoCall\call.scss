@use '@/styles/global/Vars.scss';

.call-container {
    background-image: url('../../../assets/user.png');
    video {
        min-width: 100%;
        min-height: 100%;
        margin-left: 0px;
    }
    .OTSubscriberContainer {
        position: absolute;
        background: Vars.$main-back-gradient;
        height: 100% !important;
        width: 100% !important;
        bottom: 0;
        z-index: 0;
        top: 0;
        left: 0;
    }
    .OTPublisherContainer {
        position: absolute;
        bottom: 10px;
        right: 10px;
        background: rgba(255, 255, 255, 0.726);
        width: 80px;
        height: 80px;
        overflow: hidden;
        z-index: 1;
        border-radius: 50%;
        border: 2px solid #42a5f6;
    }
    .OT_publisher {
        left: -18%;
        bottom: -10px !important;
    }
    svg{
        font-size: 1.2rem;
        font-weight: bold;
    }
}