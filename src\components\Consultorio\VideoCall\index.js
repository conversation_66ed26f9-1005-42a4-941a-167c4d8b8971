import React from 'react'
import {useAppSelector} from '@/store/hooks'
import Call from './Attention/Call'
import './call.scss'

const VideoCall = () => {
	const { patient, currentAppointment } = useAppSelector(state => state.queries)

	return <div className="call-container relative h-[300px] w-full text-center flex justify-center items-center text-[var(--white-color)] bg-[#c6dbe8] rounded-[6px] overflow-hidden object-cover bg-[length:40%] bg-no-repeat bg-center bg-[position:calc(50%-(-14px))]">
		{(!!currentAppointment.room || !!patient?.call?.room) 
			&& <Call room={currentAppointment.room} token={currentAppointment.token} />}
	</div> 		
}

export default VideoCall
