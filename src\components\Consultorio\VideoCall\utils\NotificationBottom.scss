.NotificationBottom{
    // Position
    position:fixed;
    margin: auto;
    bottom: 12px;
    z-index: 2;
    // Box
    width: 576px;
    height: 47px;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 4px;
    padding: 12px;
    // Content
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    justify-content: center;
    animation: opacityOn 5s linear forwards;

}

@keyframes opacityOn {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}