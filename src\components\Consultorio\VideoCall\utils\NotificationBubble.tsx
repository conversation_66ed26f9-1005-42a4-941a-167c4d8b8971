import React, { ReactNode } from 'react'
import { useAppSelector } from '@/store/hooks'

function NotificationBubble () : ReactNode {
	const dataChat = useAppSelector ((state : any ) => state?.call?.dataChat)
	const unreadContent = dataChat?.filter((element: any) => {
		return element.rol === 'patient'
	})
	const messagesUnread = unreadContent?.length

	if(dataChat?.find((message:any) => message?.doctor_read === false)) {
		return (
			<span className='absolute right-[-6px] top-[-6px] text-white bg-error border border-white w-[18px] h-[18px] rounded-full font-normal text-[12px] leading-[10px] flex items-center justify-center' aria-label={ (messagesUnread) + 'mensajes sin leer'}>{messagesUnread}</span>
		)
	}

	return (<></>)
}

export default NotificationBubble