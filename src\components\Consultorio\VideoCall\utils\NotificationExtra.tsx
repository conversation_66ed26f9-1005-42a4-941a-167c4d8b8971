import React, { ReactNode } from 'react'
import { useAppSelector } from '@/store/hooks'
import unreadMessages from './unreadMessages'
import { IMedicalHistory } from '@umahealth/entities' 
import './NotificationBottom.scss'

interface Chat {
	doctor: number;
	patient: number;
}
interface IMedicalHistoryNew extends IMedicalHistory {
	chat: Chat | undefined;
	chat_notifications: number | undefined;
}

function NotificationExtra () : ReactNode {
	const messagesToRead = unreadMessages()
	const currentAtt : IMedicalHistoryNew = useAppSelector((state : any) : IMedicalHistoryNew => state?.queries?.currentAtt)
	const patientMessages: any = currentAtt?.chat?.patient
	
	if (patientMessages) {
		if (messagesToRead > 0) {
			return (
				<span className='NotificationBottom' key={patientMessages}> 
				Tienes un mensaje entrante del paciente
				</span>
			)
		}
	}

	return (<></>)
}

export default NotificationExtra