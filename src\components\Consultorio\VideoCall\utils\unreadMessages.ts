import { useAppSelector } from '@/store/hooks'
import { IMedicalHistory } from '@umahealth/entities' 

interface Chat {
	doctor: number;
	patient: number;
}

interface IMedicalHistoryNew extends IMedicalHistory {
	chat: Chat | undefined;
	chat_notifications: number | undefined;
}

const useUnreadMessages = () => {
	const currentAtt : IMedicalHistoryNew = useAppSelector((state : any) : IMedicalHistoryNew => state?.queries?.currentAtt)
	const dataChat : Array<string> = useAppSelector ( (state : any ) : Array<string>  => state?.call?.dataChat)
	const patientMessages: any = currentAtt?.chat?.patient
	const unreadContent = dataChat?.filter ( (element: any) => {return element.rol === 'patient'})
	const messagesUnread = unreadContent?.length

	const messagesToRead = patientMessages - messagesUnread

	return messagesToRead
}

export default useUnreadMessages