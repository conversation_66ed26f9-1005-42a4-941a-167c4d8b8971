import React, { ReactNode, useRef } from 'react';
import { X, Minus, Maximize2 } from "lucide-react";
import Draggable from 'react-draggable';

interface DialogProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
  isMinimized?: boolean;
  onMinimize?: () => void;
  onMaximize?: () => void;
}

const Dialog = ({ isOpen, onClose, children, isMinimized = false, onMinimize, onMaximize }: DialogProps) => {
  // Crear una referencia al nodo DOM para pasarlo a Draggable
  const nodeRef = useRef<HTMLDivElement>(null);
  
  if (!isOpen) return null;

  return (
    <section
      role='dialog'
      className="fixed inset-0 pointer-events-none z-[0]"
    >
      {/* La prop nodeRef espera un RefObject<HTMLElement>, estamos usando un casting seguro */}
      <Draggable
        nodeRef={nodeRef as React.RefObject<HTMLElement>}
        handle=".dialog-handle"
        bounds="parent"
        defaultPosition={{x: window.innerWidth - 520, y: 0}}
      >
        <div ref={nodeRef} className={`w-[500px] bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden ${isMinimized ? 'h-[45px]' : 'max-h-[90vh]'} pointer-events-auto`}>
          {/* Barra superior para arrastre */}
          <div className={`dialog-handle flex items-center justify-between px-4 ${isMinimized ? 'py-2.5' : 'py-3'} bg-gray-50 ${!isMinimized ? 'border-b border-gray-100' : ''} cursor-move`}>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-gray-300" />
              <div className="w-2 h-2 rounded-full bg-gray-300" />
              <div className="w-2 h-2 rounded-full bg-gray-300" />
            </div>
            <div className="flex items-center gap-2">
              {isMinimized ? (
                <button
                  onClick={onMaximize}
                  className="text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full p-1"
                >
                  <Maximize2 className="w-5 h-5" />
                </button>
              ) : (
                <button
                  onClick={onMinimize}
                  className="text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full p-1"
                >
                  <Minus className="w-5 h-5" />
                </button>
              )}
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full p-1"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
          
          {/* Contenido - solo se muestra si no está minimizado */}
          {!isMinimized && (
            <div className="p-4 overflow-scroll max-h-[400px]">
              {children}
            </div>
          )}
        </div>
      </Draggable>
    </section>
  );
};

export default Dialog;
