import { useMemo } from 'react'
import countriesCatalog from '@/components/ElectronicHealthRecordMx/infrastructure/data/catalogo_paises_completo.json'

export interface Country {
  id: number
  label: string
  order: number
  active: number
}

export const useCountries = () => {
  const countries = useMemo(() => {
    return countriesCatalog
      .filter((country: Country) => country.active === 1)
      .sort((a: Country, b: Country) => a.order - b.order)
  }, [])

  return countries
}