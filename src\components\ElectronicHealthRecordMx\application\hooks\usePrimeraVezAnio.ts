import { useState, useEffect } from 'react';
import { isPrimeraVezAnio } from '../../infrastructure/repositories/firestoreRepository';

const usePrimeraVezAnio = (
  patientUid: string,
  curpPaciente: string,
  clues: string
): string => {
  const [primeraVezAnio, setPrimeraVezAnio] = useState<string>('-1');

  useEffect(() => {
    const fetchPrimeraVezAnio = async () => {
      try {
        const queryPrimeraVezAnio = await isPrimeraVezAnio(patientUid, curpPaciente, clues);
        setPrimeraVezAnio(queryPrimeraVezAnio);
      } catch (error) {
        console.error('Error fetching primera vez anio:', error);
      }
    };

    fetchPrimeraVezAnio();
  }, [patientUid, curpPaciente, clues]);

  return primeraVezAnio;
};

export default usePrimeraVezAnio;
