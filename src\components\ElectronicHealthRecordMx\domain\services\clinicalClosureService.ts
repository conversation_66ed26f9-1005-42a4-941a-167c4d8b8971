import { IHealthFacility } from "../types/IHealthFacility"
import { isSpecializedFacility } from "./healthFacilityService"

export const determineFirstTimeUNEME = (
    facility: IHealthFacility,
    isFirstTimeYear: boolean
): number => {
    const isSpecialized = isSpecializedFacility(facility)

    if (!isSpecialized) {
        return -1
    }

    if (isFirstTimeYear) {
        // Aquí el usuario o la UI debe decidir entre 0 o 1
        // Ya que la regla dice "se debe registrar una de las siguientes opciones: 0 – NO, 1 – SI"
        return 0 // o 1 dependiendo de la lógica de negocio específica
    }

    return 0
}