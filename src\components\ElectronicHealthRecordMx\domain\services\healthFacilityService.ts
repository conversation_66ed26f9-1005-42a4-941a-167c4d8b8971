import { IHealthFacility } from "../types/IHealthFacility"

export const isSpecializedFacility = (facility: IHealthFacility): boolean => {
    
    // Si la institución es SSA o SME, es especializado
    if (facility.institucion === 'SSA' || facility.institucion === 'SME') {
        return true
    }

    // Si es un hospital (tipo_unidad 2) y tiene una de las abreviaciones especiales
    if (facility.tipo_unidad === 2 && ['99', 'M', 'N', 'O'].includes(facility.tip_abreviacion)) {
        return true
    }

    // Si es consulta externa (tipo_unidad 1) y tiene una de las sub-abreviaciones especiales  
    if (facility.tipo_unidad === 1 && ['T07', 'UNE05', 'UNE07', 'UNE11', 'UNE99'].includes(facility.sub_abreviacion)) {
        return true
    }

    return false
}