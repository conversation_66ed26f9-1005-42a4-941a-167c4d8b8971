export interface DiagnosisItem {
    CATALOG_KEY: string
    NOMBRE: string
    TIPO_PERSONAL_1VEZ_CE?: string
    TIPO_PERSONAL_SUBSEC_CE?: string
    SEXO?: string
    EDAD_MINIMA?: number
    EDAD_MAXIMA?: number
    CAUSA_EXTERNA?: string
    NOTIFICACION_OBLIGATORIA?: string
    CAPITULO?: string
    GRUPO?: string
    CATEGORIA?: string
    SUBCATEGORIA?: string
    LSEX?: string
    LINF?: string
    LSUP?: string
    VALIDO_SM?: string
    VALIDO_SB?: string
    VALIDO_PF?: string
    EPI_CLAVE?: string
    EPI_CLAVE_DESC?: string
    EPI_CLAVE_DESC_2024?: string
    DIA_CRONICOS?: string
    DIA_CAINFANTIL?: string
    ES_SUIVE_MORB?: string
    CLAVE_PROGRAMA_SIS?: number
    CLAVE_CAPITULO?: string
  }
  
  export interface ICDDiagnosisInputProps {
    disabled?: boolean
  }
  
  export interface DiagnosisSearchInputProps {
    diagnosisNumber: number
    disabled: boolean
    setValue: any
    watch: any
    errors: any
    validateDiagnosis: (value: string, diagnosisNumber: number) => true | string
    diagnosticoCatalog: DiagnosisItem[]
    register: any
    setError: any
    clearErrors: any
  }