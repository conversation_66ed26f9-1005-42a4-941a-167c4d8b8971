// curpValidations.ts
import entidades from '@/components/ElectronicHealthRecordMx/infrastructure/data/entidad_federativa_catalogo.json'

/**
 * Valida el formato general de la CURP.
 */
export const validateCurpFormat = (curp: string): boolean => {
    const curpRegex = /^[A-Z]{4}\d{6}[HMX]{1}[A-Z]{2}[B-DF-HJ-NP-TV-Z]{3}[A-Z0-9]{1}\d{1}$/;
    return curpRegex.test(curp);
};

/**
 * Valida el formato completo de una CURP usando expresiones regulares
 * @param curp CURP a validar
 * @returns true si el formato es válido, false si no lo es
 */
export const detailedCurpRegex = (curp: string): boolean => {
  // CURP temporal
  if (curp === "XXXX999999XXXXXX99") return true;

  // Extraer información de la CURP para validaciones específicas
  const birthYear = parseInt(curp.substring(4, 6));
  const homoclave = curp.charAt(16);

  // Construir el conjunto de entidades válidas desde el catálogo
  const validEntityCodes = entidades.map(e => e.ABREVIATURA).join('|');

  // Regex completo que valida todas las partes de la CURP
  const curpRegex = new RegExp(
    '^' + // Inicio de la cadena
    // 1. Primera letra del apellido paterno
    '[A-Z]' +
    // 2. Primera vocal del apellido paterno
    '[AEIOUX]' +
    // 3. Primera letra del apellido materno
    '[A-Z]' +
    // 4. Primera letra del nombre
    '[A-Z]' +
    // 5-6. Año de nacimiento (dos dígitos)
    '\\d{2}' +
    // 7-8. Mes de nacimiento (01-12)
    '(?:0[1-9]|1[0-2])' +
    // 9-10. Día de nacimiento (01-31)
    '(?:0[1-9]|[12]\\d|3[01])' +
    // 11. Sexo (H, M o X)
    '[HMX]' +
    // 12-13. Entidad federativa (usando los códigos del catálogo)
    `(?:${validEntityCodes})` +
    // 14-16. Consonantes internas
    '[B-DF-HJ-NP-TV-Z]{3}' +
    // 17. Homoclave (diferenciador) - se valida después
    '[A-Z0-9]' +
    // 18. Dígito verificador
    '\\d' +
    '$' // Fin de la cadena
  );

  // Validar el formato general
  if (!curpRegex.test(curp)) return false;

  const fullBirthYear = birthYear < 30 ? 2000 + birthYear : 1900 + birthYear;

  if (fullBirthYear >= 2000 && !/^[A-Z]$/.test(homoclave)) return false; // 2000 en adelante: letra
  if (fullBirthYear < 2000 && !/^[0-9]$/.test(homoclave)) return false; // Antes de 2000: número
  
  return true;
};


/**
 * Valida que la fecha de nacimiento en la CURP coincida con la fecha proporcionada.
 */
export const validateCurpBirthDate = (curp: string, dob: string): boolean => {
  const yearPrefix = Number(curp.slice(4, 6)) < 30 ? '20' : '19';
  const curpYear = `${yearPrefix}${curp.slice(4, 6)}`;
  const curpMonth = curp.slice(6, 8);
  const curpDay = curp.slice(8, 10);
  
  // Parse dd/MM/yyyy format
  const [dobDay, dobMonth, dobYear] = dob.split('/');
  
  return dobYear === curpYear && dobMonth === curpMonth && dobDay === curpDay;
};

/**
 * Valida que la fecha de nacimiento sea válida en el curp
 */
export const isValidBirthDate = (curp: string): boolean => {
  const yearPrefix = Number(curp.slice(4, 6)) < 30 ? '20' : '19';
  const curpYear = `${yearPrefix}${curp.slice(4, 6)}`;
  const curpMonth = curp.slice(6, 8);
  const curpDay = curp.slice(8, 10);

  const date = new Date(parseInt(curpYear), parseInt(curpMonth) - 1, parseInt(curpDay));
  return date.getFullYear() === parseInt(curpYear) && date.getMonth() === parseInt(curpMonth) - 1 && date.getDate() === parseInt(curpDay);
};

/**
 * Verifica que la fecha extraída de la CURP sea válida.
 */
export const validateCurpValidDate = (curp: string): boolean => {
  const yearPrefix = Number(curp.slice(4, 6)) < 30 ? '20' : '19';
  const dateStr = `${yearPrefix}${curp.slice(4, 6)}-${curp.slice(6, 8)}-${curp.slice(8, 10)}`;
  const [year, month, day] = dateStr.split('-').map(Number);
  const date = new Date(year, month - 1, day);
  return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;
};

/**
 * Valida que el sexo biólogico sea válido en el curp
 */
export const isValidSex = (curp: string): boolean => {
  const curpSex = ["H", "M", "X"].includes(curp[10]);
  return curpSex;
};


/**
 * Valida que el sexo biólogico coincida con la CURP.
 */
export const validateCurpSex = (curp: string, sex: string): boolean => {
  const curpSex = curp[10];
  return (sex === "1" && curpSex === "H") || (sex === "2" && curpSex === "M") || (sex === "0" && curpSex === "X");
};


/**
 * Valida el código de la entidad federativa en la CURP (posiciones 12-13)
 * @param curp CURP completa
 * @param entityCode Código de la entidad federativa (opcional)
 * @returns true si es válido, mensaje de error si no lo es
 */
export const validateCurpEntityCode = (curp: string, entityCode?: string): boolean | string => {
  // Extraer posiciones 12-13 de la CURP
  const curpEntity = curp.slice(11, 13);
  
  // Validar que sean letras
  if (!/^[A-Z]{2}$/.test(curpEntity)) {
    return "El código de entidad federativa debe ser dos letras mayúsculas";
  }

  // Validar que el código existe en el catálogo
  const entityExists = entidades.some(entity => entity.ABREVIATURA === curpEntity);
  if (!entityExists) {
    return "Código de entidad federativa inválido";
  }

  // Si se proporciona un código de entidad, validar que coincida
  if (entityCode) {
    const entity = entidades.find(e => e.CATALOG_KEY.toString().padStart(2, '0') === entityCode.padStart(2, '0'));
    if (!entity) {
      return "Código de entidad proporcionado no existe en el catálogo";
    }
    if (entity.ABREVIATURA !== curpEntity) {
      return "La entidad federativa no coincide con la CURP";
    }
  }
  console.log('curpEntity', curpEntity, 'entityCode', entityCode)
  return true;
};

export const isValidEntityCode = (curp: string): boolean | string => {
  // Extraer posiciones 12-13 de la CURP
  const curpEntity = curp.slice(11, 13);
  
  // Validar que sean letras
  if (!/^[A-Z]{2}$/.test(curpEntity)) {
    return "El código de entidad federativa debe ser dos letras mayúsculas";
  }

  // Validar que el código existe en el catálogo
  const entityExists = entidades.some(entity => entity.ABREVIATURA === curpEntity);
  if (!entityExists) {
    return "Código de entidad federativa inválido";
  }
  return true;
}

/**
 * Verifica que las primeras 4 letras de la CURP no contengan palabras prohibidas.
 */
export const validateCurpNoProhibitedWords = (curp: string): { isValidWord: boolean, substitution?: string, prefix?: string } => {
  const prohibitedWords = ["BACA", "BAKA", "BUEI", "BUEY", "CACA", "CACO", "CAGA", "CAGO", "CAKA", "CAKO", "COGE", "COGI", "COJA", "COJE", "COJI", "COJO", "COLA", "CULO", "FALO", "FETO", "GETA", "GUEI", "GUEY", "JETA", "JOTO", "KACA", "KACO", "KAGA", "KAGO", "KAKA", "KAKO", "KOGE", "KOGI", "KOJA", "KOJE", "KOJI", "KOJO", "KOLA", "KULO", "LILO", "LOCA", "LOCO", "LOKA", "LOKO", "MAME", "MAMO", "MEAR", "MEAS", "MEON", "MIAR", "MION", "MOCO", "MOKO", "MULA", "MULO", "NACA", "NACO", "PEDA", "PEDO", "PENE", "PIPI", "PITO", "POPO", "PUTA", "PUTO", "QULO", "RATA", "ROBA", "ROBE", "ROBO", "RUIN", "SENO", "TETA", "VACA", "VAGA", "VAGO", "VAKA", "VUEI", "VUEY", "WUEI", "WUEY"];
  const substitutions = {
    "BACA": "BXCA",
    "BAKA": "BXKA", 
    "BUEI": "BXEI",
    "BUEY": "BXEY",
    "CACA": "CXCA",
    "CACO": "CXCO", 
    "CAGA": "CXGA",
    "CAGO": "CXGO",
    "CAKA": "CXKA",
    "CAKO": "CXKO",
    "COGE": "CXGE",
    "COGI": "CXGI",
    "COJA": "CXJA",
    "COJE": "CXJE",
    "COJI": "CXJI",
    "COJO": "CXJO",
    "COLA": "CXLA",
    "CULO": "CXLO",
    "FALO": "FXLO",
    "FETO": "FXTO",
    "GETA": "GXTA",
    "GUEI": "GXEI",
    "GUEY": "GXEY",
    "JETA": "JXTA",
    "JOTO": "JXTO",
    "KACA": "KXCA",
    "KACO": "KXCO",
    "KAGA": "KXGA",
    "KAGO": "KXGO",
    "KAKA": "KXKA",
    "KAKO": "KXKO",
    "KOGE": "KXGE",
    "KOGI": "KXGI",
    "KOJA": "KXJA",
    "KOJE": "KXJE",
    "KOJI": "KXJI",
    "KOJO": "KXJO",
    "KOLA": "KXLA",
    "KULO": "KXLO",
    "LILO": "LXLO",
    "LOCA": "LXCA",
    "LOCO": "LXCO",
    "LOKA": "LXKA",
    "LOKO": "LXKO",
    "MAME": "MXME",
    "MAMO": "MXMO",
    "MEAR": "MXAR",
    "MEAS": "MXAS",
    "MEON": "MXON",
    "MIAR": "MXAR",
    "MION": "MXON",
    "MOCO": "MXCO",
    "MOKO": "MXKO",
    "MULA": "MXLA",
    "MULO": "MXLO",
    "NACA": "NXCA",
    "NACO": "NXCO",
    "PEDA": "PXDA",
    "PEDO": "PXDO",
    "PENE": "PXNE",
    "PIPI": "PXPI",
    "PITO": "PXTO",
    "POPO": "PXPO",
    "PUTA": "PXTA",
    "PUTO": "PXTO",
    "QULO": "QXLO",
    "RATA": "RXTA",
    "ROBA": "RXBA",
    "ROBE": "RXBE",
    "ROBO": "RXBO",
    "RUIN": "RXIN",
    "SENO": "SXNO",
    "TETA": "TXTA",
    "VACA": "VXCA",
    "VAGA": "VXGA",
    "VAGO": "VXGO",
    "VAKA": "VXKA",
    "VUEI": "VXEI",
    "VUEY": "VXEY",
    "WUEI": "WXEI",
    "WUEY": "WXEY"
  }
  const prefix = curp.slice(0, 4);
  return {
    isValidWord: !prohibitedWords.includes(prefix),
    substitution: substitutions[prefix as keyof typeof substitutions],
    prefix: prefix
  };
};

/**
 * Calcula el dígito verificador de la CURP y lo compara con el proporcionado.
 */
export const validateCurpVerifier = (curp: string): boolean => {
  const charValues = "0123456789ABCDEFGHIJKLMNÑOPQRSTUVWXYZ"; // Ajustar la tabla si es necesario
  let sum = 0;

  for (let i = 0; i < 17; i++) {
    const charIndex = charValues.indexOf(curp[i]);
    if (charIndex === -1) return false; // Si algún carácter no está en la tabla, la CURP no es válida
    sum += charIndex * (18 - i);
  }

  const remainder = sum % 10;
  const expectedVerifier = remainder === 0 ? 0 : 10 - remainder;

  return Number(curp[17]) === expectedVerifier;
};
/**
 * Valida homonimias en la CURP (carácter diferenciador en la posición 17).
 */
export const validateCurpHomonimia = (curp: string): boolean => {
  const homonimiaChar = curp[16];
  const validChars = /[A-Z0-9]/;
  return validChars.test(homonimiaChar);
};

/**
 * Valida una CURP con base en todas las reglas oficiales.
 * @param curp CURP a validar
 * @param dob Fecha de nacimiento (YYYY-MM-DD)
 * @param sex Sexo biológico del paciente (H/M)
 * @param entityCode Código de la entidad federativa
 * @returns Mensaje de error o true si la CURP es válida
 */
export const validateCurp = (
  curp: string,
  dob: string,
  sex: string,
  entityCode: string
): string | true => {

  console.log('curp', curp, 'dob', dob, 'sex', sex, 'entityCode', entityCode)
  const { isValidWord, substitution, prefix } = validateCurpNoProhibitedWords(curp);

  if (!curp) return "La CURP es obligatoria.";
  if (!validateCurpFormat(curp)) return "Formato de CURP inválido.";
  if (!validateCurpValidDate(curp)) return "La fecha en la CURP es inválida.";
  if (!isValidWord) return `La CURP contiene palabras no permitidas. ${substitution ? `Se sugiere reemplazar ${prefix} por ${substitution}.` : ''}`;
  if (!validateCurpBirthDate(curp, dob)) return "La fecha de nacimiento no coincide con la CURP.";
  if (!validateCurpSex(curp, sex)) return "El sexo no coincide con la CURP.";
  if (!validateCurpEntityCode(curp, entityCode)) return "La entidad federativa no coincide con la CURP.";
  if (!validateCurpHomonimia(curp)) return "El carácter diferenciador de la CURP es inválido.";
  if (!validateCurpVerifier(curp)) return "El dígito verificador de la CURP es incorrecto.";
  return true;
};


/**
 * Valida una CURP con base en todas las reglas oficiales.
 * @param curp CURP a validar
 * @param dob Fecha de nacimiento (YYYY-MM-DD)
 * @param sex Sexo biológico del paciente (H/M)
 * @param entityCode Código de la entidad federativa
 * @returns Mensaje de error o true si la CURP es válida
 */
export const validateCurpConsistency = (
  curp: string,
): string | true => {

  // const { isValidWord, substitution, prefix } = validateCurpNoProhibitedWords(curp);

  // curp genérica no se valida
  if(curp === 'XXXX999999XXXXXX99') return true

  if (!curp) return "La CURP es obligatoria.";
  if (!validateCurpFormat(curp)) return "Formato de CURP inválido.";
  if (!detailedCurpRegex(curp)) return "Formato de CURP inválido.";
  if (!validateCurpValidDate(curp)) return "La fecha en la CURP es inválida.";
  // if (!isValidWord) return `La CURP contiene palabras no permitidas. ${substitution ? `Se sugiere reemplazar ${prefix} por ${substitution}.` : ''}`;
  if (!isValidBirthDate(curp)) return "La fecha de nacimiento no coincide con la CURP.";
  if (!isValidSex(curp)) return "El sexo no es válido.";
  if (!isValidEntityCode(curp)) return "La entidad federativa no coincide con la CURP.";
  if (!validateCurpHomonimia(curp)) return "El carácter diferenciador de la CURP es inválido.";
  if (!validateCurpVerifier(curp)) return "El dígito verificador de la CURP es incorrecto.";
  return true;
};
// Ejemplo de uso:
// const result = validateCurp("GOMJ870101HDFSRN02", "1987-01-01", "H", "DF");
// console.log(result); // true o mensaje de error

export const getFirstValidName = (fullName: string) => {
  const commonNames = ["JOSÉ", "MARIA", "MARÍA", "MA.", "MA", "JOSE", "J.", "J", "M."];
  
  const names = fullName.trim().toUpperCase().split(/\s+/);

  // Manejar nombres con abreviaciones como "M." o "J."
  if (names[0].match(/^[A-Z]\.$/)) {
    return names[1] || names[0];
  }

  // Si el primer nombre es común, usar el segundo nombre si existe
  if (commonNames.includes(names[0]) && names.length > 1) {
    return names[1];
  }

  return names[0]; // Si no hay segundo nombre, se usa el primero
};


export const getNameFirstInternalConsonant = (name: string) => {
  const validName = getFirstValidName(name);

  // Si hay caracteres especiales en cualquier parte del nombre, devolver "X". REGLA SOLO PARA CONSONANTES
  // eslint-disable-next-line no-useless-escape
  if (/[\/\-\.\’]/.test(validName)) {
    return 'X';
  }

  // Si no hay caracteres especiales, buscar la primera consonante interna
  const consonantMatch = validName.slice(1).match(/[BCDFGHJKLMNPQRSTVWXYZÑ]/);

  // Si la primera consonante interna es "Ñ", devolver "X"
  if (consonantMatch && consonantMatch[0] === 'Ñ') return 'X';

  return consonantMatch ? consonantMatch[0] : 'X'; // Si no hay consonante interna, usar "X"
};


export const getLastNameFirstInternalVowel = (lastName: string) => {
  // Buscar la primera vocal después de la primera letra
  const vowels = lastName.slice(1).toUpperCase().match(/[AEIOUÁÉÍÓÚ]/)?.[0]
  
  const vowelMap: { [key: string]: string } = {
    'Á': 'A',
    'É': 'E',
    'Í': 'I',
    'Ó': 'O',
    'Ú': 'U'
  }

  return vowels ? 
    vowels.replace(/[ÁÉÍÓÚ]/g, match => vowelMap[match] || match) : 
    'X'
}

export const getLastNameFirstInternalConsonant = (lastName: string) => {
  // Buscar la primera consonante después de la primera letra

  // Si hay caracteres especiales en cualquier parte del nombre, devolver "X". REGLA SOLO PARA CONSONANTES
  // eslint-disable-next-line no-useless-escape
  if (/[\/\-\.\’]/.test(lastName)) {
    return 'X';
  }
  
  const consonants = lastName.slice(1).toUpperCase().match(/[BCDFGHJKLMNÑPQRSTVWXYZ]/)?.[0]
  return consonants === 'Ñ' ? 'X' : (consonants || 'X')
}

export const removeArticlesFromLastName = (lastName: string) => {
  return lastName.replace(/\b(de la|de los|de las|da|das|de|del|der|di|die|dd|y|el|la|los|las|le|les|mac|mc|van|von)\b/gi, '').trim();
};