export interface DiagnosticConfirmation {
    tipoPersonal: string
  }
  
  export const isValidProviderForDiagnosticConfirmation = (tipoPersonal: string): boolean => {
    const validProviders = ['1', '2', '3', '4', '19', '24']
    return validProviders.includes(tipoPersonal)
  }
  
  export const shouldShowDiagnosticConfirmation = ({
    tipoPersonal,
  }: DiagnosticConfirmation & { relation: string }): boolean => {
    if (!isValidProviderForDiagnosticConfirmation(tipoPersonal)) return false
    
    return true
  }