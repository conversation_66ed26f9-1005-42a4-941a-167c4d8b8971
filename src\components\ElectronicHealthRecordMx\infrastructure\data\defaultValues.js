import {format} from 'date-fns'

export const defaultValues = {
    countryNacPaciente: '',
    sexoBiologico: '',
    tipoPersonal: '2', // Siempre es 2 – MÉDICA(O) GENERAL
    migrante: '',
    fechaConsulta: format(new Date(), 'dd/MM/yyyy'), // Fecha de la consulta
    servicioAtencion: '4', // Servicio de atención siempre es CONSULTA EXTERNA GENERAL
    ninoSanoRT: '-1',
    edasRT: '-1',
    edasPlanTratamiento: '-1',
    pneumoniaRT: '-1',
    contraReferral: '-1',
    referralReason: '-1',
    // Auxiliares
    age: -1,
    isSpecializedFacility: false,
}

export const consultaExterna = {
    // Variables profesional
    tipoPersonal: '2', // Siempre es 2 – MÉDICA(O) GENERAL
    // Variables paciente
    curpPaciente: 'XXXX999999XXXXXX99',
    nombre: '',
    primerApellido: '',
    segundoApellido: '',
    fechaNacimiento: '',
    paisNacPaciente: '142',
    entidadNacimiento: '00',
    sexoCURP: '',
    sexoBiologico: '',
    seAutodenominaAfromexicano: '-1',
    seConsideraIndigena: '-1', 
    migrante: '-1',
    paisProcedencia: '-1',
    genero: '0',

    // Datos consulta
    fechaConsulta: format(new Date(), 'dd/MM/yyyy'), // Fecha de la consulta
    servicioAtencion: '4', // Servicio de atención siempre es CONSULTA EXTERNA GENERAL
    peso: '999',
    talla: '999',
    circuunferenciaCintura: '0',
    sistolica: '0',
    diastolica: '0',
    frecuenciaCardiaca: '0',
    frecuenciaRespiratoria: '0',
    temperatura: '0',
    saturacionOxigeno: '0',
    glucemia: '0',
    tipoMedicion: '-1',
    resultadoObtenidoaTravesde: '-1',
    embarazadaSinDiabetes: '0',
    sintomaticoRespiratorioTb: '-1',
    primeraVezAnio: '1',
    primeraVezUneme: '-1',
    relacionTemporal: '0',
    codigoCIEDiagnostico1: '-1',
    confirmacionDiagnostica1: '-1',
    primeraVezDiagnostico1: '-1',
    codigoCIEDiagnostico2: '', // nulleable
    confirmacionDiagnostica2: '-1',
    primeraVezDiagnostico2: '-1', // nulleable
    codigoCIEDiagnostico3: '', // nulleable
    confirmacionDiagnostica3: '-1',
    primeraVezDiagnostico3: '-1', // nulleable
    intervencionesSMyA: '-1',

    // Salud reproductiva
    atencionPregestacionalRT: '-1',
    riesgo: '-1',
    relacionTemporalEmbarazo: '-1',
    planSeguridad: '-1',
    trimestreGestacional: '-1',
    primeraVezAltoRiesgo: '-1',
    complicacionPorDiabetes: '-1',
    complicacionPorInfeccionUrinaria: '-1',
    complicacionPorPreeclampsiaEclampsia: '-1',
    complicacionPorHemorragia: '-1',
    sospechaCovid19: '-1',
    covid19Confirmado: '-1',
    hipertensionarterialprexistente: '-1',
    otrasAccPrescAcidoFolico: '-1',
    otrasAccApoyoTranslado: '-1',
    otrasACCApoyoTransladoAME: '-1',

    // Puerperio
    puerpera: '-1',
    infeccionPuerperal: '-1',

    // Otros eventos
    terapiaHormonal: '-1',
    periPostMenopausia: '-1',
    its: '-1',
    patologiaMamariaBenigna: '-1',
    cancerMamario: '-1',
    colposcopia: '-1',
    cancerCervicouterino: '-1',

    // Salud del niño
    ninoSanoRT: '-1',
    pruebaEDI: '-1',
    resultadoEDI: '-1',
    resultadoBattelle: '-1',

    // Enfermedades diarreicas agudas (EDA's)
    edasRT: '-1',
    edasPlanTratamiento: '-1',
    recuperadoDeshidratacion: '-1',
    numeroSobresVSOTratamiento: '0',

    // Infecciones respiratorias agudas (IRA's)
    irasRT: '-1',
    irasPlanTratamiento: '-1',
    neumoniaRT: '-1',

    // Cáncer en menores de 18 años
    aplicacionCedulaCancer: '-1',

    // Prevención de accidentes
    informaPrevencionAccidentes: '-1',

    // Intervenciones gerontológicas
    sintomaDepresiva: '-1',
    alteracionMemoria: '-1',
    'aivd-ABVD': '-1',
    sindromeCaidas: '-1',
    incontinenciaUrinaria: '-1',
    motricidad: '-1',
    asesoriaNutricional: '-1',

    // Promoción de la salud
    numeroSobresVSOPromocion: '0',
    lineaVida: '0',
    cartillaSalud: '0',
    esquemaVacunacion: '0',

    // Referencia y contrarreferencia
    referidoPor: '-1',
    contrarreferido: '0',

    // Telemedicina
    telemedicina: '0',
    teleconsulta: '0',
    estudiosTeleconsulta: '-1',
    modalidadConsulDist: '-1',

    // other
    age: -1, // auxiliar para calcular la edad
    isSpecializedFacility: false, // auxiliar para saber si es una unidad especializada
}