import { doc, updateDoc, collection, query, where, getDocs, getDoc } from '@/config/firebase'
import { firestore } from '@/config/firebase' // Asegúrate de que esta ruta sea correcta para tu configuración de Firebase

export const saveConsultaExterna = async (
  patientUid: string,
  assignationId: string,
  consultaExternaData: any,
  providerData: any
) => {
  try {
    const docRef = doc(firestore, `user/${patientUid}/medical_records/${assignationId}`)
    await updateDoc(docRef, {
      consultaExterna: consultaExternaData,
      provider: providerData,
      nom024Compliant: true
    })
    return true
  } catch (error) {
    console.error('Error saving consulta externa:', error)
    return false
  }
}

export const isPrimeraVezAnio = async (
  patientUid: string,
  pacienteCurp: string,
  clues: string,
): Promise<string> => {
  console.log(`Innumbericiando isPrimeraVezAnio para paciente: ${patientUid}, CURP: ${pacienteCurp}, CLUES: ${clues}`);
  try {
    const medicalRecordsRef = collection(firestore, `user/${patientUid}/medical_records`)
    const q = query(
      medicalRecordsRef,
      where('consultaExterna.curpPaciente', '==', pacienteCurp)
    )

    console.log(`Ejecutando query para buscar registros médicos`);
    const querySnapshot = await getDocs(q)
    console.log(`Número de documentos encontrados: ${querySnapshot.size}`);

    const currentYear = new Date().getFullYear();
    console.log(`Año actual: ${currentYear}`);

    const matchingRecords = querySnapshot.docs.filter(doc => {
      const data = doc.data()
      const matchesClues = data.consultaExterna.clues === clues;
      const matchesYear = data.timestamps.dt_assignation.toDate().getFullYear() === currentYear;
      console.log(`Documento ${doc.id}: CLUES coincide: ${matchesClues}, Año coincide: ${matchesYear}`);
      return matchesClues && matchesYear;
    })

    console.log(`Número de registros coincidentes: ${matchingRecords.length}`);

    if (matchingRecords.length === 0) {
      console.log(`Es la primera vez en el año para este paciente en esta CLUES`);
      return '1' // No se encontraron registros, es la primera vez en el año
    } else {
      console.log(`No es la primera vez en el año para este paciente en esta CLUES`);
      return '0' // Se encontró al menos un registro, no es la primera vez en el año
    }
  } catch (error) {
    console.error('Error checking if it\'s the first time in the year:', error)
    throw error // Propagar el error para que pueda ser manejado por el llamador
  }
}

export const isPrimeraVezDiagnosticoX = async (
  patientUid: string,
  pacienteCurp: string,
  clues: string,
  diagnosticoNumber: number,
  diagnosticoCie10: string
): Promise<string> => {
  try {
    const medicalRecordsRef = collection(firestore, `user/${patientUid}/medical_records`)
    const q = query(
      medicalRecordsRef,
      where('consultaExterna.curpPaciente', '==', pacienteCurp)
    )

    const querySnapshot = await getDocs(q)

    const matchingRecords = querySnapshot.docs.filter(doc => {
      const data = doc.data()
      const consultaExterna = data.consultaExterna
      return (
        consultaExterna.clues === clues &&
        data.timestamps.dt_assignation.toDate().getFullYear() === new Date().getFullYear() &&
        consultaExterna[`codigoCIEDiagnostico${diagnosticoNumber}`] === diagnosticoCie10
      )
    })

    console.log(`Diagnóstico ${diagnosticoNumber}:`, diagnosticoCie10, 'Registros encontrados:', matchingRecords.length)

    if (matchingRecords.length === 0) {
      console.log(`Primera vez para diagnóstico ${diagnosticoNumber}:`, diagnosticoCie10)
      return '1' // No se encontraron registros, es la primera vez para este diagnóstico en el año
    } else {
      console.log(`No es primera vez para diagnóstico ${diagnosticoNumber}:`, diagnosticoCie10)
      return '0' // Se encontró al menos un registro, no es la primera vez para este diagnóstico en el año
    }
  } catch (error) {
    console.error(`Error checking if it's the first time for diagnostico${diagnosticoNumber}:`, error)
    throw error // Propagar el error para que pueda ser manejado por el llamador
  }
}

export const finalizarCitaActiva = async (patientUid: string): Promise<boolean> => {
  try {
    const userDocRef = doc(firestore, `user/${patientUid}`);
    const activeServicesDocRef = doc(firestore, `user/${patientUid}/activeServices/onlineCall`);

    // Actualizar el campo active_appointment en el documento del usuario
    await updateDoc(userDocRef, {
      active_appointment: false
    });

    // Verificar si existe el documento activeServices/onlineCall
    const activeServicesDoc = await getDoc(activeServicesDocRef);
    if (activeServicesDoc.exists()) {
      // Si existe, actualizar el campo calling
      await updateDoc(activeServicesDocRef, {
        calling: false,
        room: "",
        token: "",
        assignation_id: "",
        dependant: "",
        type: "",
        activeUid: "",
        assignationPath: "",
        requested: false
      });
    }

    console.log(`Cita finalizada para el paciente: ${patientUid}`);
    return true;
  } catch (error) {
    console.error('Error al finalizar la cita activa:', error);
    return false;
  }
}

