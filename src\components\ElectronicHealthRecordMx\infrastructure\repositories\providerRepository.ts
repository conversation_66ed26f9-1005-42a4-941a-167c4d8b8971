import { doc, getDoc } from '@/config/firebase'
import { firestore } from '@/config/firebase'

export interface ProviderData {
  clues: string
  curp: string
  cuit: string
  dni?: string
  firstname: string
  lastname: string
  secondLastname: string
  paisNacimiento: string
  fullname: string
  especialidad: string
  uid: string
  tipoPersonal: string
}

export const getProviderData = async (uid: string): Promise<ProviderData | null> => {
  try {

    const docRef = doc(firestore, `providers/${uid}`)
    const docSnap = await getDoc(docRef)

    if (docSnap.exists()) {
      const data = docSnap.data()
      return {
        clues: data.clues || '',
        curp: data.cuit || '',
        cuit: data.cuit || '',
        firstname: data.firstname || '',
        lastname: data.lastname || '',
        secondLastname: data.secondLastname || '',
        paisNacimiento: data.paisNacimiento || '',
        especialidad: data.matricula_especialidad || '',
        uid: data.uid || '',
        fullname: data.fullname || '',
        tipoPersonal: '2'
      }
    }
    return null
  } catch (error) {
    console.error('Error fetching provider data:', error)
    return null
  }
}