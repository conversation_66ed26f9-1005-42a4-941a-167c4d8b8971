import React from "react";
import { IPatient } from "@umahealth/entities";
import { format, parse, differenceInYears } from "date-fns";

interface PatientDataCardProps {
  patientData?: IPatient;
}

const PatientDataCard: React.FC<PatientDataCardProps> = ({ patientData }) => {
  const formattedDOB = patientData?.dob
    ? format(parse(patientData.dob, "yyyy-dd-MM", new Date()), "MM-dd-yyyy")
    : "Fecha no disponible";

  const dobDate = patientData?.dob
    ? parse(patientData.dob, "yyyy-dd-MM", new Date())
    : null;

  const isUnderAge = dobDate ? differenceInYears(new Date(), dobDate) < 18 : false;

  return (
    <div className="p-4 bg-white rounded-lg shadow-md border">
      <h2 className="text-lg font-semibold text-gray-800">Información del paciente</h2>
      <div className="mt-2 text-gray-600">
        <p><strong>Nombre:</strong> {patientData?.firstname}</p>
        <p><strong>Apellidos:</strong> {patientData?.lastname}</p>
        <p><strong>Fecha de nacimiento:</strong> {formattedDOB}</p>
        <p><strong>CURP:</strong> {patientData?.dni}</p>
        <p><strong>Teléfono de contacto:</strong> {patientData?.ws}</p>
        {isUnderAge && (
          <div className="mt-2 text-yellow-600 bg-yellow-50 p-3 rounded-md border border-yellow-200">
            <span role="img" aria-label="warning">⚠️</span> El paciente es menor de edad, verifique que su titular esté presente para continuar la atención.
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientDataCard;
