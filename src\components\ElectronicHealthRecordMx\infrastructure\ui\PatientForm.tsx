import React from 'react'
import * as EhrComponents from '@/components/ElectronicHealthRecordMx/infrastructure/ui/ehr'
import * as PatientComponents from '@/components/ElectronicHealthRecordMx/infrastructure/ui/patient'
import { useFormContext } from 'react-hook-form'
import { useCountries } from '@/components/ElectronicHealthRecordMx/application/hooks/useCountries'
import { ICDDiagnosisInput } from './ehr/DiagnosticInput'
import { IHealthFacility } from '../../domain/types/IHealthFacility'
import { parse, isValid, differenceInYears } from 'date-fns'

interface PatientFormProps {
  providerCurp?: string;
  patientUid?: string;
  clues: IHealthFacility
}

const PatientForm: React.FC<PatientFormProps> = ({ providerCurp, clues, patientUid }) => {
  const { watch } = useFormContext();
  const fechaNacimiento = watch("fechaNacimiento");
  const sexoBiologico = watch("sexoBiologico");
  const tipoPersonal = watch("tipoPersonal");
  const paisNacPaciente = watch("paisNacPaciente");
  const migrante = watch("migrante");
  const countries = useCountries();

  // Función para calcular la edad
  const calculateAge = (birthDate: string) => {
      if (!birthDate) return 0;
  
      // Intentar parsear la fecha con el formato correcto (DD/MM/YYYY)
      const birthDateObj = parse(birthDate, 'dd/MM/yyyy', new Date());
  
      if (!isValid(birthDateObj)) {
          console.error('Fecha inválida:', birthDate);
          return 0;
      }
  
      return differenceInYears(new Date(), birthDateObj);
  };
  return (
    <>
      <FormSection title="Datos del paciente">
        <PatientComponents.PatientCurpInput providerCurp={providerCurp} />
        <PatientComponents.PatientNameInput />
        <PatientComponents.PatientLastNameInput />
        <PatientComponents.PatientSecondLastNameInput />
        <PatientComponents.PatientDobInput />
        <PatientComponents.PatientCurpSexInput />
        <PatientComponents.PatientBiologicSexInput />
        <PatientComponents.PatientGenderInput />
        <PatientComponents.PatientAfromexicanInput />
        <PatientComponents.PatientIndigenousInput />
        <div className="col-span-3 grid grid-cols-4 gap-2 grid-rows-[auto,1fr]">
          <PatientComponents.PatientCountryInput countries={countries} />
          {paisNacPaciente === "142" && (
            <PatientComponents.PatientEntityBirthInput />
          )}
          <PatientComponents.PatientMigrantInput />
          {migrante === "2" ? (
            <PatientComponents.CountryOfOriginInput countries={countries} />
          ) : (
            <div></div>
          )}
        </div>
        <div className="col-span-3 grid grid-cols-4 gap-2 grid-rows-[auto,1fr]"></div>
        {/* RESIDENCIA */}
        <PatientComponents.PatientEntityResidenceInput />
        <PatientComponents.PatientMunicipalityResidenceInput />
        <PatientComponents.PatientLocalityResidenceInput />
      </FormSection>

      <FormSection title="Consulta, somatología y exámenes">
          <div className="col-span-3 grid grid-cols-4 gap-2">
            <EhrComponents.WeightInput />
            <EhrComponents.HeightInput />
            <EhrComponents.BloodPressureInput />
          </div>
          <div className="col-span-3 grid grid-cols-3 gap-2">
            <EhrComponents.TemperatureInput />
            <EhrComponents.BloodGlucoseInput />
          </div>
      </FormSection>

      <FormSection title="Diagnóstico">
        <EhrComponents.RelacionTemporal />
        <EhrComponents.PrimeraVezUNEME curpPaciente={watch('curpPaciente')} patientUid={patientUid??''} facility={clues} />
        {(fechaNacimiento && sexoBiologico) ? (
          <ICDDiagnosisInput />
        ) : (
          <div className="text-sm text-muted-foreground p-4">
            Debe completar los datos básicos del paciente antes de seleccionar diagnóstico
          </div>
        )}
      </FormSection>


      {/* Salud del niño - Sólo para menores de 16 */}
      {calculateAge(fechaNacimiento) < 16 &&
        calculateAge(fechaNacimiento) >= 0 && (
          <FormSection title="Salud del niño">
            {calculateAge(fechaNacimiento) < 16 &&
              calculateAge(fechaNacimiento) >= 0 &&
              !["15", "16"].includes(tipoPersonal) && (
                <EhrComponents.HealthyChildRTInput
                  age={calculateAge(fechaNacimiento)}
                  tipoPersonal={tipoPersonal}
                />
              )}

          {calculateAge(fechaNacimiento) < 10 && 
            !["15", "16"].includes(tipoPersonal) && 
            <EhrComponents.AccidentPreventionInfoInput 
              age={calculateAge(fechaNacimiento)}
              tipoPersonal={tipoPersonal}
            /> }

            {calculateAge(fechaNacimiento) < 5 &&
              watch("ninoSanoRT") === "-1" &&
              !["15", "16"].includes(tipoPersonal) && (
                <>
                  {/* EDAS */}
                  <EhrComponents.AcuteDiarrheaDiseaseRTInput
                    age={calculateAge(fechaNacimiento)}
                    ninoSanoRT={watch("ninoSanoRT")}
                    tipoPersonal={tipoPersonal}
                  />
                  <EhrComponents.EDATreatmentPlanInput edasRT={watch("edasRT")} />
                  <EhrComponents.DehydrationRecoveryInput
                    edasRT={watch("edasRT")}
                    edasPlanTratamiento={watch("edasPlanTratamiento")}
                  />
                  {/* IRAS */}
                  <EhrComponents.AcuteRespiratoryInfectionRTInput
                    age={calculateAge(fechaNacimiento)}
                    ninoSanoRT={watch("ninoSanoRT")}
                    tipoPersonal={tipoPersonal}
                  />
                  <EhrComponents.ARITreatmentPlanInput irasRT={watch("irasRT")} />
                </>
              )}
          </FormSection>
        )}
    </>
  );
};

const FormSection: React.FC<{ title: string; children: React.ReactNode }> = ({
  title,
  children,
}) => (
  <div className="mb-6">
    <h2 className="text-md font-bold">{title}</h2>
    <div className="grid grid-cols-3 gap-4">{children}</div>
  </div>
);

export default PatientForm;
