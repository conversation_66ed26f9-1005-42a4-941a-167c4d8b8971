import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface BenignBreastPathologyInputProps {
  disabled?: boolean
  tipoPersonal: string
}

export const BenignBreastPathologyInput: React.FC<BenignBreastPathologyInputProps> = ({ 
  disabled = false,
  tipoPersonal
}) => {
  const { register, formState: { errors } } = useFormContext()

  const isApplicable = !['15', '16'].includes(tipoPersonal)

  return (
    <div className="space-y-2">
      <Label htmlFor="benignBreastPathology">Atención por Patología Mamaria Benigna</Label>
      <Select 
        onValueChange={(value) => register("benignBreastPathology").onChange({ target: { value } })}
        disabled={disabled || !isApplicable}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione una opción" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="0">PRIMERA VEZ</SelectItem>
          <SelectItem value="1">SUBSECUENTE</SelectItem>
        </SelectContent>
      </Select>
      {errors.benignBreastPathology && (
        <p className="text-sm text-red-500">{errors.benignBreastPathology.message as string}</p>
      )}
    </div>
  )
}