import React from 'react'
import { useFormContext, Controller } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface BloodGlucoseInputProps {
  disabled?: boolean
}

export const BloodGlucoseInput: React.FC<BloodGlucoseInputProps> = ({ disabled = false }) => {
  const { control, formState: { errors }, setValue, watch, trigger, register } = useFormContext()
  const glucemia = watch('glucemia')

  const validateGlucemia = (value: string) => {
    if (value === '' || value === null || value === undefined) return true
    if (value === '0') return true
    const numValue = Number(value)
    if (isNaN(numValue)) return "Por favor ingrese un número válido"
    if (numValue < 20) return "El mínimo de glucosa en sangre es 20 mg/dl"
    if (numValue > 999) return "El máximo de glucosa en sangre es 999 mg/dl"
    if (!Number.isInteger(numValue)) return "La glucosa en sangre debe ser un número entero"
    return true
  }

  React.useEffect(() => {
    if (glucemia && glucemia !== '0') {
      trigger(['tipoMedicion', 'resultadoObtenidoaTravesde'])
    }
  }, [glucemia, trigger])

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="glucemia" className="text-xxs h-12 flex items-end">
          Glucosa en sangre <span className="text-xxs text-gray-500">&nbsp;(mg/dl)</span>
        </Label>
        <Input
          id="glucemia"
          type="number"
          placeholder="Ingrese glucosa en sangre"
          {...register("glucemia", { validate: validateGlucemia })}
          disabled={disabled}
        />
        {errors.glucemia && (
          <p className="text-sm text-red-500">{errors.glucemia.message as string}</p>
        )}
      </div>

      {glucemia && glucemia !== '0' && (
        <>
          <div className="space-y-1">
            <Label htmlFor="tipoMedicion">Medición en ayunas</Label>
            <Controller
              name="tipoMedicion"
              control={control}
              rules={{ required: "Este campo es obligatorio" }}
              render={({ field }) => (
                <Select
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value)
                    setValue("tipoMedicion", value, { shouldValidate: true })
                  }}
                  disabled={disabled}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="¿En ayunas?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">No</SelectItem>
                    <SelectItem value="1">Sí</SelectItem>
                    <SelectItem value="-1">Se desconoce el dato</SelectItem>
                  </SelectContent>
                </Select>
              )}
            />
            {errors.tipoMedicion && (
              <p className="text-sm text-red-500">{errors.tipoMedicion.message as string}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="resultadoObtenidoaTravesde">Obtenido a través de</Label>
            <Controller
              name="resultadoObtenidoaTravesde"
              control={control}
              rules={{ required: "Este campo es obligatorio" }}
              render={({ field }) => (
                <Select
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value)
                    setValue("resultadoObtenidoaTravesde", value, { shouldValidate: true })
                  }}
                  disabled={disabled}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccione método" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">LABORATORIO</SelectItem>
                    <SelectItem value="2">TIRA DE GLUCOSA CAPILAR</SelectItem>
                  </SelectContent>
                </Select>
              )}
            />
            {errors.resultadoObtenidoaTravesde && (
              <p className="text-sm text-red-500">{errors.resultadoObtenidoaTravesde.message as string}</p>
            )}
          </div>
        </>
      )}
    </div>
  )
}
