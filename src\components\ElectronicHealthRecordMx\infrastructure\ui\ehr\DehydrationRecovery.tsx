import React from 'react'
import { useForm<PERSON>ontext, Controller } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface DehydrationRecoveryInputProps {
  disabled?: boolean
  edasRT: string
  edasPlanTratamiento: string
}

export const DehydrationRecoveryInput: React.FC<DehydrationRecoveryInputProps> = ({ 
  disabled = false,
  edasRT,
  edasPlanTratamiento
}) => {
  const { control, formState: { errors } } = useFormContext()

  const isApplicable = edasRT !== '-1' && ['2', '3'].includes(edasPlanTratamiento)

  return (
    <div className="space-y-2">
      <Label htmlFor="recuperadoDeshidratacion">Recuperación de Deshidratación</Label>
      <Controller
        name="recuperadoDeshidratacion"
        control={control}
        rules={{
          required: isApplicable ? "Este campo es obligatorio cuando se ha registrado un valor para enfermedad diarreica aguda y el plan de tratamiento es B o C" : false
        }}
        render={({ field }) => (
          <Select
            value={field.value}
            onValueChange={field.onChange}
            disabled={disabled || !isApplicable}
          >
            <SelectTrigger>
              <SelectValue placeholder="Seleccione una opción" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">NO</SelectItem>
              <SelectItem value="1">SI</SelectItem>
            </SelectContent>
          </Select>
        )}
      />
      {errors.recuperadoDeshidratacion && (
        <p className="text-sm text-red-500">{errors.recuperadoDeshidratacion.message as string}</p>
      )}
    </div>
  )
}
