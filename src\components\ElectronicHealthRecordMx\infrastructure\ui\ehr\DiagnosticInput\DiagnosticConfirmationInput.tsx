import React from 'react'
import { useFormContext, Controller } from 'react-hook-form'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface DiagnosticConfirmationInputProps {
  diagnosisNumber: number
  patientAge: number
}

export const DiagnosticConfirmationInput = React.memo(({
  diagnosisNumber,
  patientAge
}: DiagnosticConfirmationInputProps) => {
  const { control, watch } = useFormContext()
  
  const diagnosisICD = watch(`codigoCIEDiagnostico${diagnosisNumber}`)

  const relacionTemporal = watch('relacionTemporal')

  const dontShowConfirmation = diagnosisNumber === 1 && relacionTemporal === '1'

  const showConfirmation = (
    // Condiciones para pacientes >= 20 años
    (patientAge >= 20 && (
      diagnosisICD?.startsWith('E11') ||
      diagnosisICD?.startsWith('I10') ||
      diagnosisICD?.startsWith('E78')
    )) ||
    // Condiciones para pacientes < 18 años con códigos específicos
    (patientAge < 18 && (
      diagnosisICD?.startsWith('CP')
    ))
  )


  if (!showConfirmation || dontShowConfirmation) return null

  return (
    <div className="space-y-3">
      <Label>Confirmación diagnóstica {diagnosisNumber} <span className="text-red-500">*</span></Label>
      <Controller
        name={`confirmacionDiagnostica${diagnosisNumber}`}
        control={control}
        defaultValue=""
        rules={{ required: "Este campo es obligatorio" }}
        render={({ field, fieldState: { error } }) => (
          <>
            <Select onValueChange={field.onChange} value={field.value}>
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Sí</SelectItem>
                <SelectItem value="0">No</SelectItem>
              </SelectContent>
            </Select>
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </>
        )}
      />
    </div>
  )
})

DiagnosticConfirmationInput.displayName = 'DiagnosticConfirmationInput'
