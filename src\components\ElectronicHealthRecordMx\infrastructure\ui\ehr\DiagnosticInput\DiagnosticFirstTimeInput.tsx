import React from 'react'
import { useFormContext, Controller } from 'react-hook-form'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface DiagnosticFirstTimeInputProps {
  diagnosisNumber: number
}

export const DiagnosticFirstTimeInput = React.memo(({
  diagnosisNumber,
}: DiagnosticFirstTimeInputProps) => {
  const { control, watch } = useFormContext()
  
  const dontShowConfirmation = diagnosisNumber === 1 || !watch(`codigoCIEDiagnostico${diagnosisNumber}`)

  if (dontShowConfirmation) return null

  return (
    <div className="space-y-3">
      <Label>Primera vez diagnóstico {diagnosisNumber} <span className="text-red-500">*</span></Label>
      <Controller
        name={`primeraVezDiagnostico${diagnosisNumber}`}
        control={control}
        defaultValue=""
        rules={{ required: "Este campo es obligatorio" }}
        render={({ field, fieldState: { error } }) => (
          <>
            <Select onValueChange={field.onChange} value={field.value}>
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Sí</SelectItem>
                <SelectItem value="0">No</SelectItem>
              </SelectContent>
            </Select>
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </>
        )}
      />
    </div>
  )
})

DiagnosticFirstTimeInput.displayName = 'DiagnosticFirstTimeInput'
