import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { DiagnosticConfirmationInput } from './DiagnosticConfirmationInput'
import { DiagnosticFirstTimeInput } from './DiagnosticFirstTimeInput'
import { DiagnosisSearchInputProps } from '@/components/ElectronicHealthRecordMx/domain/types/diagnosticTypes'

export const DiagnosisSearchInput: React.FC<DiagnosisSearchInputProps> = ({
  diagnosisNumber,
  disabled,
  setValue,
  watch,
  errors,
  validateDiagnosis,
  diagnosticoCatalog,
  setError,
  clearErrors
}) => {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [displayValue, setDisplayValue] = useState('')
  const currentValue = React.useRef(watch(`codigoCIEDiagnostico${diagnosisNumber}`))

  useEffect(() => {
    const subscription = watch((value: { [x: string]: any }, { name }: any) => {
      if (name === `codigoCIEDiagnostico${diagnosisNumber}`) {
        currentValue.current = value[`codigoCIEDiagnostico${diagnosisNumber}`]
        const selectedDiagnosis = diagnosticoCatalog.find(d => d.CATALOG_KEY === currentValue.current)
        setDisplayValue(selectedDiagnosis ? `${selectedDiagnosis.CATALOG_KEY} - ${selectedDiagnosis.NOMBRE}` : '')
      }
    })

    return () => subscription.unsubscribe()
  }, [watch, diagnosisNumber, diagnosticoCatalog])

  const filteredDiagnoses = React.useMemo(() => {
    if (!searchTerm) return []
    return diagnosticoCatalog
      .filter(diag => {
        if (!diag.TIPO_PERSONAL_1VEZ_CE && !diag.TIPO_PERSONAL_SUBSEC_CE) return false
        const type1vez = typeof diag.TIPO_PERSONAL_1VEZ_CE === 'string' ? diag.TIPO_PERSONAL_1VEZ_CE.split(',') : []
        return type1vez.includes('2')
      })
      .filter(diag => 
        diag.CATALOG_KEY.toLowerCase().includes(searchTerm.toLowerCase()) ||
        diag.NOMBRE.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .slice(0, 100)
  }, [searchTerm, diagnosticoCatalog])

  const handleSelect = React.useCallback((selectedValue: string) => {
    const validateResult = validateDiagnosis(selectedValue, diagnosisNumber)
    if(validateResult === true) {
      setValue(`codigoCIEDiagnostico${diagnosisNumber}`, selectedValue)
      clearErrors(`codigoCIEDiagnostico${diagnosisNumber}`)
    } else {
      setError(`codigoCIEDiagnostico${diagnosisNumber}`, {
        type: 'manual',
        message: validateResult
      })
      setValue(`codigoCIEDiagnostico${diagnosisNumber}`, '')
    }
    setOpen(false)
    setSearchTerm('')
  }, [setValue, diagnosisNumber, validateDiagnosis, setError, clearErrors])

  const calculateAge = (birthDate: string): number => {
    if (!birthDate) return 0
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={`codigoCIEDiagnostico${diagnosisNumber}`}>
        {`Diagnóstico CIE ${diagnosisNumber}`}{diagnosisNumber === 1 &&<span>*</span>}
      </Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Input
            id={`codigoCIEDiagnostico${diagnosisNumber}`}
            value={displayValue}
            onClick={() => setOpen(true)}
            readOnly
            disabled={disabled}
            placeholder="Buscar diagnóstico..."
          />  
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0">
          <Input 
            placeholder="Buscar diagnóstico..." 
            value={searchTerm} 
            onChange={(e) => setSearchTerm(e.target.value)}
            autoFocus
          />
          <div className="max-h-[200px] overflow-y-auto">
            {Array.isArray(filteredDiagnoses) && filteredDiagnoses?.length > 0 ? (
              <>
                {filteredDiagnoses.map((diagnosis) => (
                  <div
                    key={diagnosis.CATALOG_KEY}
                    onClick={() => handleSelect(diagnosis.CATALOG_KEY)}
                    className="cursor-pointer p-2 hover:bg-gray-100"
                  >
                    {`${diagnosis.CATALOG_KEY} - ${diagnosis.NOMBRE}`}
                  </div>
                ))}
              </>
            ) : (
              <div className="p-2">No se encontraron resultados</div>
            )}
          </div>
        </PopoverContent>
      </Popover>
      {errors[`codigoCIEDiagnostico${diagnosisNumber}`] && (
        <p className="text-sm text-red-500">{errors[`codigoCIEDiagnostico${diagnosisNumber}`].message}</p>
      )}
      <div className="mt-2">
        <DiagnosticConfirmationInput 
          diagnosisNumber={diagnosisNumber}
          patientAge={calculateAge(watch('fechaNacimiento'))}
        />
      </div>
      <div className="mt-2">
        <DiagnosticFirstTimeInput 
          diagnosisNumber={diagnosisNumber}
        />
      </div>
    </div>
  )
}