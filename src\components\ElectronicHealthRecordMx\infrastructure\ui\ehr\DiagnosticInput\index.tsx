import React, { useState, useEffect } from 'react'
import { useFormContext } from 'react-hook-form'
import { DiagnosisItem, ICDDiagnosisInputProps } from '@/components/ElectronicHealthRecordMx/domain/types/diagnosticTypes'
import { DiagnosisSearchInput } from '@/components/ElectronicHealthRecordMx/infrastructure/ui/ehr/DiagnosticInput/DiagnosticSearchInput'

const getDiagnosticoCatalog = async (): Promise<DiagnosisItem[]> => {
  try {
    const catalog = await import('@/components/ElectronicHealthRecordMx/infrastructure/data/catalogo_diagnosticos_completo_v2.json') as { default: DiagnosisItem[] }
    return catalog.default || []
  } catch (error) {
    console.error("Error al cargar el catálogo de diagnósticos:", error)
    return []
  }
}

export const ICDDiagnosisInput: React.FC<ICDDiagnosisInputProps> = ({ 
  disabled = false, 
}) => {
  const { register, setValue, watch, formState: { errors }, setError, clearErrors } = useFormContext()
  const [diagnosticoCatalog, setDiagnosticoCatalog] = useState<DiagnosisItem[]>([])
  const watchBirthDate = watch('fechaNacimiento')
  const watchSexoBiologico = watch('sexoBiologico')

  useEffect(() => {
    getDiagnosticoCatalog().then(catalog => {
      setDiagnosticoCatalog(catalog)
    })
  }, [])

  useEffect(() => {
    [1, 2, 3].forEach(diagNumber => {
      const currentValue = watch(`codigoCIEDiagnostico${diagNumber}`)
      
      if (currentValue) {
        const diagnosisCode = currentValue.includes('-') ? currentValue?.split('-')[0].trim() : currentValue
        
        const validateResult = validateDiagnosis(diagnosisCode, diagNumber)
        
        if (validateResult !== true) {
          setError(`codigoCIEDiagnostico${diagNumber}`, {
            type: 'manual',
            message: validateResult
          })
          setValue(`codigoCIEDiagnostico${diagNumber}`, '')
        }
      }
    })
  }, [watchBirthDate, watchSexoBiologico])

  const validateDiagnosis = (value: string, diagnosisNumber: number) => {
    const newBirthDate = watch('fechaNacimiento')
    const currentSexoBiologico = watch('sexoBiologico')

    //Validamos que no sea igual a otros diagnósticos
    const otherDiagnoses = [1, 2, 3]
    .filter(num => num !== diagnosisNumber) // excluimos el diagnóstico actual
    .map(num => watch(`codigoCIEDiagnostico${num}`))
    .filter(Boolean) // filtramos valores vacíos

    if (otherDiagnoses.includes(value)) {
      return "Este diagnóstico ya ha sido seleccionado. Por favor, elija uno diferente."
    }

    if (!value && diagnosisNumber !== 1) return true 
    if (!value) return "Este campo es obligatorio"
    
    const diagnosis = diagnosticoCatalog.find(d => d.CATALOG_KEY === value)
    if (!diagnosis) return "Código CIE-10 no válido"

    let limitSex = 'NO'
    if(diagnosis.LSEX === 'MUJER') {
      limitSex = '2'
    } else if (diagnosis.LSEX === 'HOMBRE') {
      limitSex = '1'
    }
    if (currentSexoBiologico !== '3' && (limitSex !== "NO" && limitSex !== currentSexoBiologico)) {
      return "Código no válido para el sexo del paciente"
    }
  
    const birthDateObj = new Date(newBirthDate)
    const now = new Date()
  
    const getMilliseconds = (timeString: string) => {
      if (!timeString || timeString === "NO") return null
      const value = parseInt(timeString.slice(0, 3))
      const unit = timeString.slice(-1)
      
      const multipliers = {
        'H': 1000 * 60 * 60,
        'D': 1000 * 60 * 60 * 24,
        'S': 1000 * 60 * 60 * 24 * 7,
        'M': 1000 * 60 * 60 * 24 * 30,
        'A': 1000 * 60 * 60 * 24 * 365
      }
      
      return value * multipliers[unit as keyof typeof multipliers]
    }
  
    if(diagnosis.LINF && diagnosis.LSUP) {
      const minAge = diagnosis.LINF === "NO" ? null : getMilliseconds(diagnosis.LINF)
      const maxAge = diagnosis.LSUP === "NO" ? null : getMilliseconds(diagnosis.LSUP)
      const patientAgeMs = now.getTime() - birthDateObj.getTime()
    
      if (
        (minAge !== null && patientAgeMs < minAge) ||
        (maxAge !== null && patientAgeMs > maxAge)
      ) {
        return "Código no válido para la edad del paciente"
      }
    }
  
    return true
  }

  return <>
      {[1, 2, 3].map((diagnosisNumber: any) => (
        <DiagnosisSearchInput
          key={diagnosisNumber}
          diagnosisNumber={diagnosisNumber}
          disabled={disabled || (diagnosisNumber !== 1 && !watch(`codigoCIEDiagnostico${diagnosisNumber - 1}`))}
          setValue={setValue}
          watch={watch}
          errors={errors}
          setError={setError}
          validateDiagnosis={validateDiagnosis}
          diagnosticoCatalog={diagnosticoCatalog}
          register={register}
          clearErrors={clearErrors}
        />
      ))}
    </>
}