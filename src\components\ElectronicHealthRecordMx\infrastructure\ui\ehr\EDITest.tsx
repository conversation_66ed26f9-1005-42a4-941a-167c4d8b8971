import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface EDITestInputProps {
  disabled?: boolean
  age: number
  ninoSanoRT: string
  tipoPersonal: string
}

export const EDITestInput: React.FC<EDITestInputProps> = ({ 
  disabled = false,
  age,
  ninoSanoRT,
  tipoPersonal
}) => {
  const { register, formState: { errors } } = useFormContext()

  const isApplicable = age < 6 && (ninoSanoRT !== '-1' || ['15', '16'].includes(tipoPersonal))

  return (
    <div className="space-y-2">
      <Label htmlFor="ediTest">Prueba de Evaluación del Desarrollo Infantil (EDI)</Label>
      <Select 
        onValueChange={(value) => register("ediTest").onChange({ target: { value } })}
        disabled={disabled || !isApplicable}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione una opción" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1">INICIAL</SelectItem>
          <SelectItem value="2">SUBSECUENTE</SelectItem>
        </SelectContent>
      </Select>
      {errors.ediTest && (
        <p className="text-sm text-red-500">{errors.ediTest.message as string}</p>
      )}
    </div>
  )
}