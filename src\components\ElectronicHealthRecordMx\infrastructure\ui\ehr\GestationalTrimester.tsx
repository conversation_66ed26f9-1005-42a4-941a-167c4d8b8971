import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface GestationalTrimesterInputProps {
  disabled?: boolean
  pregnancyTemporalRelation: string
}

export const GestationalTrimesterInput: React.FC<GestationalTrimesterInputProps> = ({ 
  disabled = false,
  pregnancyTemporalRelation
}) => {
  const { register, formState: { errors } } = useFormContext()

  const isApplicable = pregnancyTemporalRelation !== '-1'

  return (
    <div className="space-y-2">
      <Label htmlFor="gestationalTrimester">Trimestre Gestacional</Label>
      <Select 
        onValueChange={(value) => register("gestationalTrimester").onChange({ target: { value } })}
        disabled={disabled || !isApplicable}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione el trimestre" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1">PRIMERO</SelectItem>
          <SelectItem value="2">SEGUNDO</SelectItem>
          <SelectItem value="3">TERCERO</SelectItem>
        </SelectContent>
      </Select>
      {errors.gestationalTrimester && (
        <p className="text-sm text-red-500">{errors.gestationalTrimester.message as string}</p>
      )}
    </div>
  )
}