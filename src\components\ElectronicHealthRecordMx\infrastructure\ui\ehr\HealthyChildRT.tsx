import React from 'react'
import { useForm<PERSON>ontex<PERSON>, Controller } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface HealthyChildRTInputProps {
  disabled?: boolean
  age: number
  tipoPersonal: string
}

export const HealthyChildRTInput: React.FC<HealthyChildRTInputProps> = ({ 
  disabled = false,
  age,
  tipoPersonal
}) => {
  const { control } = useFormContext()

  const isApplicable = age < 10 && !['15', '16'].includes(tipoPersonal)

  return (
    <div className="space-y-2">
      <Label htmlFor="ninoSanoRT">Consulta a Niño Sano</Label>
      <Controller
        name="ninoSanoRT"
        control={control}
        rules={{
          required: isApplicable ? "Este campo es obligatorio si la edad del paciente es menor a 10 años" : false
        }}      
        render={({ field, fieldState: { error } }) => (
          <>
            <Select
              onValueChange={field.onChange}
              value={field.value}
              disabled={disabled || !isApplicable}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccione una opción" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">PRIMERA VEZ</SelectItem>
                <SelectItem value="1">SUBSECUENTE</SelectItem>
                <SelectItem value="-1">NO APLICA</SelectItem>
              </SelectContent>
            </Select>
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </>
        )}
      />
    </div>
  )
}