import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface HearthRateInputProps {
  disabled?: boolean
}

export const HearthRateInput: React.FC<HearthRateInputProps> = ({ disabled = false }) => {
  const { register, formState: { errors } } = useFormContext()

  const validateHeartRate = (value: string) => {
    if (value === '' || value === null || value === undefined) return true
    if (value === '0') return true
    const numValue = Number(value)
    if (isNaN(numValue)) return "Por favor ingrese un número válido"
    if (numValue < 40) return "La frecuencia cardiaca mínima es 40 latidos/min"
    if (numValue > 220) return "La frecuencia cardiaca máxima es 220 latidos/min"
    if (!Number.isInteger(numValue)) return "La frecuencia cardiaca debe ser un número entero"
    return true
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="frecuenciaCardiaca" className="text-xxs h-12 flex items-end">
        F. Cardiaca&nbsp;<span className="text-xxs text-gray-500">(latidos/min)</span>
      </Label>
      <Input
        id="frecuenciaCardiaca"
        type="number"
        placeholder="Ingrese la frecuencia cardiaca"
        {...register("frecuenciaCardiaca", { validate: validateHeartRate })}
        disabled={disabled}
      />
      {errors.frecuenciaCardiaca && (
        <p className="text-sm text-red-500">{errors.frecuenciaCardiaca.message as string}</p>
      )}
    </div>
  )
}