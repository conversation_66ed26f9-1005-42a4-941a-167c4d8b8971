import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface HeightInputProps {
  disabled?: boolean
}

export const HeightInput: React.FC<HeightInputProps> = ({ disabled = false }) => {
  const { register, formState: { errors } } = useFormContext()

  return (
    <div className="space-y-2">
      <Label htmlFor="talla" className="text-xxs">Talla <span className="text-xxs text-gray-500">(cm) </span></Label>
      <Input
        id="talla"
        type="number"
        placeholder="Ingrese la talla en cm"
        {...register("talla", {
          required: "La talla es obligatoria",
          validate: (value) => {
            if (!value) return "Este campo es obligatorio"
            if (value === 999 || value === '999') return true
            const numValue = Number(value)
            if (isNaN(numValue)) return "Por favor ingrese un número válido"
            if (numValue < 30) return "La talla mínima es 30 cm"
            if (numValue > 220) return "La talla máxima es 220 cm"
            if (!Number.isInteger(numValue)) return "La talla debe ser un número entero"
            return true
          }
        })}
        disabled={disabled}
      />
      {errors.talla && (
        <p className="text-sm text-red-500">{errors.talla.message as string}</p>
      )}
    </div>
  )
}