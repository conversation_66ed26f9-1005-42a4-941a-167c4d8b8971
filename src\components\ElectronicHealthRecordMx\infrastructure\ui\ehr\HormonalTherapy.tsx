import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface HormonalTherapyInputProps {
  disabled?: boolean
  sexoBiologico: string
  tipoPersonal: string
}

export const HormonalTherapyInput: React.FC<HormonalTherapyInputProps> = ({ 
  disabled = false,
  sexoBiologico,
  tipoPersonal
}) => {
  const { register, formState: { errors } } = useFormContext()

  const isApplicable = (sexoBiologico === '2' || sexoBiologico === '3') && 
                       !['15', '16'].includes(tipoPersonal)

  return (
    <div className="space-y-2">
      <Label htmlFor="hormonalTherapy">Terapia Hormonal (Menopausia)</Label>
      <Select 
        onValueChange={(value) => register("hormonalTherapy").onChange({ target: { value } })}
        disabled={disabled || !isApplicable}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione una opción" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="0">NO</SelectItem>
          <SelectItem value="1">SI</SelectItem>
        </SelectContent>
      </Select>
      {errors.hormonalTherapy && (
        <p className="text-sm text-red-500">{errors.hormonalTherapy.message as string}</p>
      )}
    </div>
  )
}