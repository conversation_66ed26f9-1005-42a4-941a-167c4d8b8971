import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface MentalHealthInterventionsInputProps {
  disabled?: boolean
  tipoPersonal: string
}

export const MentalHealthInterventionsInput: React.FC<MentalHealthInterventionsInputProps> = ({ 
  disabled = false,
  tipoPersonal
}) => {
  const { register, formState: { errors } } = useFormContext()

  const isApplicable = ['1', '2', '3', '4', '9', '19', '24'].includes(tipoPersonal)

  return (
    <div className="space-y-2">
      <Label htmlFor="mentalHealthInterventions">Intervenciones de salud mental</Label>
      <Select 
        onValueChange={(value) => register("mentalHealthInterventions").onChange({ target: { value } })}
        disabled={disabled || !isApplicable}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione una opción" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1">INTERVENCIÓN PSICOSOCIAL</SelectItem>
          <SelectItem value="2">TRATAMIENTO FARMACOLÓGICO</SelectItem>
          <SelectItem value="3">AMBOS</SelectItem>
        </SelectContent>
      </Select>
      {errors.mentalHealthInterventions && (
        <p className="text-sm text-red-500">{errors.mentalHealthInterventions.message as string}</p>
      )}
    </div>
  )
}