import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface OxygenSaturationInputProps {
  disabled?: boolean
}

export const OxygenSaturationInput: React.FC<OxygenSaturationInputProps> = ({ disabled = false }) => {
  const { register, formState: { errors } } = useFormContext()

  const validateOxygenSaturation = (value: string) => {
    if (value === '' || value === null || value === undefined) return true
    if (value === '0') return true
    const numValue = Number(value)
    if (isNaN(numValue)) return "Por favor ingrese un número válido"
    if (numValue < 1) return "Mínimo de saturación de oxígeno es 1%"
    if (numValue > 100) return "Máximo de saturación de oxígeno es 100%"
    if (!Number.isInteger(numValue)) return "La saturación de oxígeno debe ser un número entero"
    return true
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="saturacionOxigeno" className="text-xxs h-12 flex items-end">
        Saturación de Oxígeno <span className="text-xxs text-gray-500">&nbsp;(%)</span>
      </Label>
      <Input
        id="saturacionOxigeno"
        type="number"
        placeholder="Saturación de Oxígeno (%)"
        {...register("saturacionOxigeno", { validate: validateOxygenSaturation })}
        disabled={disabled}
      />
      {errors.saturacionOxigeno && (
        <p className="text-sm text-red-500">{errors.saturacionOxigeno.message as string}</p>
      )}
    </div>
  )
}