import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface PregnantWithoutDiabetesInputProps {
  disabled?: boolean
}

export const PregnantWithoutDiabetesInput: React.FC<PregnantWithoutDiabetesInputProps> = ({ disabled = false }) => {
  const { register, formState: { errors }, watch } = useFormContext()
  const sexoBiologico = watch('sexoBiologico')
  const bloodGlucose = watch('bloodGlucose')
  const resultObtainedThrough = watch('resultObtainedThrough')

  const isApplicable = sexoBiologico === '2' && bloodGlucose !== '0' && resultObtainedThrough === '2'

  return (
    <div className="space-y-2">
      <Label htmlFor="pregnantWithoutDiabetes">Glucose Strips Used for Healthy Pregnant Women</Label>
      <Input
        id="pregnantWithoutDiabetes"
        type="number"
        placeholder="Enter number of strips used"
        {...register("pregnantWithoutDiabetes", {
          min: {
            value: 1,
            message: "Minimum value is 1"
          },
          max: {
            value: 2,
            message: "Maximum value is 2"
          },
          validate: (value) => {
            if (!isApplicable) return true
            return Number.isInteger(Number(value)) || "Value must be an integer"
          }
        })}
        disabled={disabled || !isApplicable}
      />
      {errors.pregnantWithoutDiabetes && (
        <p className="text-sm text-red-500">{errors.pregnantWithoutDiabetes.message as string}</p>
      )}
    </div>
  )
}