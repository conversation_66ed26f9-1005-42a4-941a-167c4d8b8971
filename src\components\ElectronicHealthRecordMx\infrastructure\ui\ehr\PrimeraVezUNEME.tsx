import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { isSpecializedFacility } from '@/components/ElectronicHealthRecordMx/domain/services/healthFacilityService'
import { IHealthFacility } from '@/components/ElectronicHealthRecordMx/domain/types/IHealthFacility'
import usePrimeraVezAnio from '@/components/ElectronicHealthRecordMx/application/hooks/usePrimeraVezAnio'

interface PrimeraVezUNEMEProps {
  disabled?: boolean
  patientUid: string
  curpPaciente: string
  facility: IHealthFacility
}

export const PrimeraVezUNEME: React.FC<PrimeraVezUNEMEProps> = ({ 
  disabled = false,
  patientUid,
  curpPaciente,
  facility
}) => {
  const { register, formState: { errors }, setValue, watch } = useFormContext()
  const primeraVezAnio = usePrimeraVezAnio(patientUid, curpPaciente, facility.clues)

  const isSpecialized = isSpecializedFacility(facility)

  if (!isSpecialized || primeraVezAnio === '0') {
    return (
      <input
        type="hidden"
        {...register('primeraVezUneme', {
          value: '-1'
        })}
      />
    )
  }

  if (primeraVezAnio === '0') {
    return (
      <input
        type="hidden"
        {...register('primeraVezUneme', {
          value: '0'
        })}
      />
    )
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="primeraVezUneme" className="text-xxs">
        Identificación de caso en seguimiento en UNEME
      </Label>
      <Select
        onValueChange={(value) => setValue('primeraVezUneme', value)}
        value={watch('primeraVezUneme')}
        disabled={disabled}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione si es primera vez" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="0">No</SelectItem>
          <SelectItem value="1">Sí</SelectItem>
        </SelectContent>
      </Select>
      <input
        type="hidden"
        {...register('primeraVezUneme', {
          required: "Este campo es obligatorio",
          validate: (value) => {
            if (!value) return "Este campo es obligatorio"
            if (!['0', '1', '-1'].includes(value)) return "Valor no válido"
            return true
          }
        })}
      />
      {errors.primeraVezUneme && (
        <p className="text-sm text-red-500">{errors.primeraVezUneme.message as string}</p>
      )}
    </div>
  )
}