import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface ReferralReasonInputProps {
  disabled?: boolean
}

export const ReferralReasonInput: React.FC<ReferralReasonInputProps> = ({ 
  disabled = false,
}) => {
  const { register, formState: { errors } } = useFormContext()

  return (
    <div className="space-y-2">
      <Label htmlFor="referralReason" className="h-12 flex items-end">Motivo de Referencia</Label>
      <Select 
        onValueChange={(value) => register("referralReason").onChange({ target: { value } })}
        disabled={disabled}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione un motivo" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1">EMBARAZO ALTO RIESGO</SelectItem>
          <SelectItem value="2">SOSPECHA CÁNCER MÁS DE 18 AÑOS</SelectItem>
          <SelectItem value="3">POR IRAS</SelectItem>
          <SelectItem value="4">POR NEUMONÍA</SelectItem>
          <SelectItem value="5">OTRAS</SelectItem>
          <SelectItem value="6">CISTICERCOSIS</SelectItem>
          <SelectItem value="7">EMERGENCIA OBSTÉTRICA – PREECLAMPSIA</SelectItem>
          <SelectItem value="8">EMERGENCIA OBSTÉTRICA – HEMORRAGIA</SelectItem>
          <SelectItem value="9">OTRA EMERGENCIA OBSTÉTRICA</SelectItem>
        </SelectContent>
      </Select>
      {errors.referralReason && (
        <p className="text-sm text-red-500">{errors.referralReason.message as string}</p>
      )}
    </div>
  )
}