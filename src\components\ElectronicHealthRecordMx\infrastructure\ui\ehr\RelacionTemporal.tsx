import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface RelacionTemporalProps {
  disabled?: boolean
}

export const RelacionTemporal: React.FC<RelacionTemporalProps> = ({ disabled = false }) => {
  const { register, formState: { errors }, setValue, watch } = useFormContext()

  return (
    <div className="space-y-2">
      <Label htmlFor="relacionTemporal" className="text-xxs">
        Relación temporal para el primer diagnóstico
      </Label>
      <Select
        onValueChange={(value) => setValue('relacionTemporal', value)}
        value={watch('relacionTemporal')}
        disabled={disabled}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione la relación temporal" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="0">Primera vez</SelectItem>
          <SelectItem value="1">Subsecuente</SelectItem>
        </SelectContent>
      </Select>
      <input
        type="hidden"
        {...register('relacionTemporal', {
          required: "La relación temporal es obligatoria",
          validate: (value) => {
            if (!value) return "Este campo es obligatorio"
            if (!['0', '1'].includes(value)) return "Valor no válido"
            return true
          }
        })}
      />
      {errors.relacionTemporal && (
        <p className="text-sm text-red-500">{errors.relacionTemporal.message as string}</p>
      )}
    </div>
  )
}