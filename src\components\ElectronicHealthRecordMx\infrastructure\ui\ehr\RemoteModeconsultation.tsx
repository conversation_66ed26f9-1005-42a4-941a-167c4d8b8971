import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface RemoteConsultationModeInputProps {
  disabled?: boolean
  teleconsultation: string
  teleconsultationStudies: string
}

export const RemoteConsultationModeInput: React.FC<RemoteConsultationModeInputProps> = ({ 
  disabled = false,
  teleconsultation,
  teleconsultationStudies
}) => {
  const { register, formState: { errors } } = useFormContext()

  const isApplicable = teleconsultation === '1' || teleconsultationStudies !== '-1'

  return (
    <div className="space-y-2">
      <Label htmlFor="remoteConsultationMode">Modalidad de Consulta a Distancia</Label>
      <Select 
        onValueChange={(value) => register("remoteConsultationMode").onChange({ target: { value } })}
        disabled={disabled || !isApplicable}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione una modalidad" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1">EN TIEMPO REAL</SelectItem>
          <SelectItem value="2">DIFERIDA</SelectItem>
        </SelectContent>
      </Select>
      {errors.remoteConsultationMode && (
        <p className="text-sm text-red-500">{errors.remoteConsultationMode.message as string}</p>
      )}
    </div>
  )
}