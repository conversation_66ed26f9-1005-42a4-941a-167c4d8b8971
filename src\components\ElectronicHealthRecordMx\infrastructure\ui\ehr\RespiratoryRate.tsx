import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface RespiratoryRateInputProps {
  disabled?: boolean
}

export const RespiratoryRateInput: React.FC<RespiratoryRateInputProps> = ({ disabled = false }) => {
  const { register, formState: { errors } } = useFormContext()

  const validateRespiratoryRate = (value: string) => {
    if (value === '' || value === null || value === undefined) return true
    if (value === '0') return true
    const numValue = Number(value)
    if (isNaN(numValue)) return "Por favor ingrese un número válido"
    if (numValue < 10) return "La frecuencia respiratoria mínima es 10 respiraciones/min"
    if (numValue > 99) return "La frecuencia respiratoria máxima es 99 respiraciones/min"
    if (!Number.isInteger(numValue)) return "La frecuencia respiratoria debe ser un número entero"
    return true
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="frecuenciaRespiratoria" className="text-xxs h-12 flex items-end">
        F. Respiratoria <br/>
        <span className="text-xxs text-gray-500">&nbsp;(r/min) </span>
      </Label>
      <Input
        id="frecuenciaRespiratoria"
        type="number"
        placeholder="Ingrese la frecuencia respiratoria"
        {...register("frecuenciaRespiratoria", { validate: validateRespiratoryRate })}
        disabled={disabled}
      />
      {errors.frecuenciaRespiratoria && (
        <p className="text-sm text-red-500">{errors.frecuenciaRespiratoria.message as string}</p>
      )}
    </div>
  )
}