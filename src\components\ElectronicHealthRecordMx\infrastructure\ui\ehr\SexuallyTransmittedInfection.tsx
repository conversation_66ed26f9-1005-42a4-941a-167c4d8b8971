import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface SexuallyTransmittedInfectionInputProps {
  disabled?: boolean
  tipoPersonal: string
}

export const SexuallyTransmittedInfectionInput: React.FC<SexuallyTransmittedInfectionInputProps> = ({ 
  disabled = false,
  tipoPersonal
}) => {
  const { register, formState: { errors } } = useFormContext()

  const isApplicable = !['15', '16'].includes(tipoPersonal)

  return (
    <div className="space-y-2">
      <Label htmlFor="sexuallyTransmittedInfection">Consulta por Infección de Transmisión Sexual</Label>
      <Select 
        onValueChange={(value) => register("sexuallyTransmittedInfection").onChange({ target: { value } })}
        disabled={disabled || !isApplicable}
      >
        <SelectTrigger>
          <SelectValue placeholder="Seleccione una opción" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="0">PRIMERA VEZ</SelectItem>
          <SelectItem value="1">SUBSECUENTE</SelectItem>
        </SelectContent>
      </Select>
      {errors.sexuallyTransmittedInfection && (
        <p className="text-sm text-red-500">{errors.sexuallyTransmittedInfection.message as string}</p>
      )}
    </div>
  )
}