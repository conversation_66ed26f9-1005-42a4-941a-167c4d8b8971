import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface TemperatureInputProps {
  disabled?: boolean
}

export const TemperatureInput: React.FC<TemperatureInputProps> = ({ disabled = false }) => {
  const { register, formState: { errors } } = useFormContext()

  return (
    <div className="space-y-2">
      <Label htmlFor="temperatura" className="text-xxs h-12 flex items-end">Temperatura <span className="text-xxs text-gray-500">&nbsp;(°C)</span></Label>
      <Input
        id="temperatura"
        type="number"
        step="0.1"
        placeholder="Temperatura"
        {...register("temperatura", {
          min: {
            value: 30,
            message: "Mínimo de temperatura 30°C"
          },
          max: {
            value: 44,
            message: "Máximo de temperatura 44°C"
          },
          validate: (value) => {
            if (value === '' || value === null || value === undefined) return true
            if (value === '0') return true
            const [, decimals] = value.toString().split('.')
            return !decimals || decimals?.length <= 1 || "Máximo de 1 decimal"
          }
        })}
        disabled={disabled}
      />
      {errors.temperatura && (
        <p className="text-sm text-red-500">{errors.temperatura.message as string}</p>
      )}
    </div>
  )
}