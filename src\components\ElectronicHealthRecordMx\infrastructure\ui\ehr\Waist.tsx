import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface WaistInputProps {
  disabled?: boolean
}

export const WaistInput: React.FC<WaistInputProps> = ({ disabled = false }) => {
  const { register, formState: { errors } } = useFormContext()

  return (
    <div className="space-y-2">
      <Label htmlFor="circunferenciaCintura" className="text-xxs">Circunferencia de Cintura <span className="text-xxs text-gray-500">(cm)</span></Label>
      <Input
        id="circunferenciaCintura"
        type="number"
        placeholder="Ingrese la circunferencia de cintura en cm"
        {...register("circunferenciaCintura", {
          min: {
            value: 20,
            message: "La circunferencia mínima es 20 cm"
          },
          max: {
            value: 300,
            message: "La circunferencia máxima es 300 cm"
          },
          validate: (value) => {
            if (value === '' || value === null || value === undefined) return true
            if (value === '0') return true
            return Number.isInteger(Number(value)) || "La circunferencia debe ser un número entero"
          }
        })}
        disabled={disabled}
      />
      {errors.circunferenciaCintura && (
        <p className="text-sm text-red-500">{errors.circunferenciaCintura.message as string}</p>
      )}
    </div>
  )
}