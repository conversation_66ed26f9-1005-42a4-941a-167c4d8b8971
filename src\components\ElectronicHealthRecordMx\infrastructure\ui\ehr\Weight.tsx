import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface WeightInputProps {
  disabled?: boolean
}

export const WeightInput: React.FC<WeightInputProps> = ({ disabled = false }) => {
  const { register, formState: { errors } } = useFormContext()

  return (
    <div className="space-y-2">
      <Label htmlFor="peso" className="text-xxs">Peso <span className="text-xxs text-gray-500">(kg)</span> </Label>
      <Input
        id="peso"
        type="number"
        step="0.001"
        placeholder="Ingrese el peso en kg"
        {...register("peso", {
          required: "El peso es obligatorio",
          validate: (value) => {
            if (!value) return "Este campo es obligatorio"
            if (value === 999 || value === '999') return true
            const numValue = Number(value)
            if (isNaN(numValue)) return "Por favor ingrese un número válido"
            if (numValue < 1) return "El peso mínimo es 1 kg"
            if (numValue > 400) return "El peso máximo es 400 kg"
            const [, decimals] = value.toString().split('.')
            if (decimals && decimals?.length > 3) return "Máximo 3 decimales permitidos"
            return true
          }
        })}
        disabled={disabled}
      />
      {errors.peso && (
        <p className="text-sm text-red-500">{errors.peso.message as string}</p>
      )}
    </div>
  )
}