import React from 'react'
import { useFormContext, Controller } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface PatientCurpSexInputProps {
  disabled?: boolean
}

export const PatientCurpSexInput: React.FC<PatientCurpSexInputProps> = ({ disabled = false }) => {
  const { control, watch, setValue } = useFormContext()
  const curpPaciente = watch('curpPaciente')

  const getSexoFromCURP = (curp: string) => {
    if (!curp || curp === "XXXX999999XXXXXX99") return '' // No prellenar si es la genérica

    if (curp.length >= 11) {
      const sexoChar = curp.charAt(10)
      if (sexoChar === 'H') return '1'
      if (sexoChar === 'M') return '2'
      if (sexoChar === 'X') return '3'
    }
    return ''
  }

  React.useEffect(() => {
    const sexoFromCURP = getSexoFromCURP(curpPaciente)
    
    if (sexoFromCURP) {
      setValue('sexoCURP', sexoFromCURP, { shouldValidate: true })
    } else {
      setValue('sexoCURP', '', { shouldValidate: true }) // Vaciar si es la genérica
    }
  }, [curpPaciente, setValue])

  const sexoFromCURP = getSexoFromCURP(curpPaciente)
  const isDisabledByCurp = !!sexoFromCURP && curpPaciente !== "XXXX999999XXXXXX99"

  return (
    <div className="space-y-2">
      <Label htmlFor="sexoCURP">Sexo CURP <span className="text-red-500">*</span></Label>
      <Controller
        name="sexoCURP"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <>
            <Select
              onValueChange={field.onChange}
              disabled={disabled || isDisabledByCurp}
              value={field.value}
            >
              <SelectTrigger className={isDisabledByCurp ? "bg-gray-100" : ""}>
                <SelectValue placeholder="Seleccione el sexo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">HOMBRE</SelectItem>
                <SelectItem value="2">MUJER</SelectItem>
                <SelectItem value="3">NO BINARIO</SelectItem>
              </SelectContent>
            </Select>
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
            {isDisabledByCurp && (
              <p className="text-sm text-gray-500">Dato completado a partir del CURP</p>
            )}
          </>
        )}
      />
    </div>
  )
}
