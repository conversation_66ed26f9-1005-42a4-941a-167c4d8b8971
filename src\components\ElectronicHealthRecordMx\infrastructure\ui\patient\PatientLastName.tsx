import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { getLastNameFirstInternalConsonant, getLastNameFirstInternalVowel, removeArticlesFromLastName } from '@/components/ElectronicHealthRecordMx/domain/valueObjects/curpValidations'

interface PatientLastNameInputProps {
  disabled?: boolean
}

export const PatientLastNameInput: React.FC<PatientLastNameInputProps> = ({ disabled = false }) => {
  const { register, formState: { errors }, watch } = useFormContext()
  const curpPaciente = watch('curpPaciente')

  const validateLastName = (value: string) => {
    if (!value) return "Este campo es obligatorio"
    if (value === "XX") return true
    if (value?.length < 2) return "El apellido debe tener al menos 2 caracteres"
    if (value?.length > 50) return "El apellido no debe exceder los 50 caracteres"
    
    // eslint-disable-next-line no-useless-escape
    if (!/^[A-ZÑa-zñ\s.',-\/]*$/.test(value)) return "El apellido contiene caracteres no permitidos";
    
    // No se permite más de un carácter especial consecutivo
    if (/(,|-|\.|\/|')\1/.test(value)) {
      return "No se permiten caracteres especiales consecutivos"
    }

    // Validar espacios continuos y espacios al inicio/final
    if (value.trim() !== value) {
      return "No se permiten espacios al inicio o final del apellido"
    }
    if (/\s\s/.test(value)) {
      return "No se permiten espacios dobles en el apellido"
    }

    if (curpPaciente && curpPaciente !== "XXXX999999XXXXXX99") {
      const cleanLastname = removeArticlesFromLastName(value)
      
      const curpLastNameFirstLetter = curpPaciente.charAt(0)?.toUpperCase()
      const curpLastNameVowel = curpPaciente.charAt(1)?.toUpperCase()
      const curpFourteenthLetter = curpPaciente.charAt(13)?.toUpperCase()

      // Obtener la primera letra del apellido
      const lastNameFirstLetter = cleanLastname.charAt(0).toUpperCase()

      
      // Si empieza con Ñ, debe ser X en la CURP
      if (lastNameFirstLetter === 'Ñ' && curpLastNameFirstLetter !== 'X') {
        return "El primer apellido no coincide con la CURP proporcionada"
      }

      // Para otros casos
      if (lastNameFirstLetter !== 'Ñ' && lastNameFirstLetter !== curpLastNameFirstLetter) {
        return "El primer apellido no coincide con la CURP proporcionada"
      }

      // Validar primera vocal interna
      const lastNameFirstVowel = getLastNameFirstInternalVowel(cleanLastname)
      if (lastNameFirstVowel !== curpLastNameVowel) {
        return "El primer apellido no coincide con la CURP proporcionada"
      }

      // Validar primera consonante interna
      const lastNameFirstConsonant = getLastNameFirstInternalConsonant(cleanLastname)
      if (lastNameFirstConsonant !== curpFourteenthLetter) {
        return "El primer apellido no coincide con la CURP proporcionada"
      }
    }
    return true
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="primerApellido">Primer Apellido <span>*</span> </Label>
      <Input
        id="primerApellido"
        placeholder="Ingrese el primer apellido del paciente"
        {...register("primerApellido", { validate: validateLastName })}
        disabled={disabled}
      />
      {errors.primerApellido && (
        <p className="text-sm text-red-500">{errors.primerApellido.message as string}</p>
      )}
    </div>
  )
}