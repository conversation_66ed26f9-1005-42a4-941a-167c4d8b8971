import React, { useState, useMemo } from 'react';
import { useFormContext, Controller } from 'react-hook-form';
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@umahealth/occipital/client';
import { MapPin } from 'lucide-react';
import localidadesCatalogo from '@/components/ElectronicHealthRecordMx/infrastructure/data/localidades_catalogo.json'
import municipiosCatalogo from '@/components/ElectronicHealthRecordMx/infrastructure/data/municipios_catalogo.json'
import { IMunicipality } from './PatientMunicipalityResidence';

interface ILocality {
    EFE_KEY: number
    MUN_KEY:number
    CATALOG_KEY: number
    LOCALIDAD: string
}

const normalizeText = (text: string) => {
    return text.normalize("NFD").replace(/[̀-ͯ]/g, "").toLowerCase();
};

export const PatientLocalityResidenceInput = () => {
    const { control, watch } = useFormContext();
    const [open, setOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [displayValue, setDisplayValue] = useState('');

    const municipioName = watch('municipioResidenciaPaciente')   
    const municipioMunKey = municipiosCatalogo.find((m: IMunicipality) => m.MUNICIPIO === municipioName)?.CATALOG_KEY

    const filteredLocalidades = useMemo(() => {
        if (!searchTerm) return [];
        const normalizedSearch = normalizeText(searchTerm);
        return localidadesCatalogo
            .filter((localidad: ILocality) => normalizeText(localidad.LOCALIDAD).includes(normalizedSearch))
            .slice(0, 100);
    }, [searchTerm]);

    return (
        <div className="space-y-2">
            <Label htmlFor="localidadResidenciaPaciente">Localidad de Residencia</Label>
            <Controller
                name="localidadResidenciaPaciente"
                control={control}
                rules={{
                    validate: (value) => {
                        if (!value || value === "") return true; // Permite pasar si no hay selección

                        const selectedLocality = localidadesCatalogo.find((l: ILocality) => l.LOCALIDAD === value);
                        if (selectedLocality?.MUN_KEY !== municipioMunKey) {
                            return "La localidad no pertenece al municipio seleccionado";
                        }
                        return true;
                    }
                }}
                render={({ field, formState: { errors } }) => (                    <>
                        <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                                <div className="relative">
                                    <Input
                                        id="localidadResidenciaPaciente"
                                        value={displayValue}
                                        onClick={() => setOpen(true)}
                                        readOnly
                                        placeholder="Buscar localidad..."
                                        className="text-left pl-10"
                                        disabled={!municipioName}
                                    />
                                    <MapPin
                                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                                        size={18}
                                    />
                                </div>
                            </PopoverTrigger>
                            <PopoverContent className="w-[400px] p-0">
                                <Input 
                                    placeholder="Buscar localidad..." 
                                    value={searchTerm} 
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    autoFocus
                                />
                                <div className="max-h-[200px] overflow-y-auto">
                                    {filteredLocalidades.length > 0 ? (
                                        filteredLocalidades.map((localidad: ILocality) => (
                                            <div
                                                key={`localidad_${localidad.LOCALIDAD}_${localidad.CATALOG_KEY}_${localidad.MUN_KEY}`}
                                                onClick={() => {
                                                    field.onChange(localidad.LOCALIDAD);
                                                    setDisplayValue(localidad.LOCALIDAD)
                                                    setOpen(false);
                                                    setSearchTerm('');
                                                }}
                                                className="cursor-pointer p-2 hover:bg-gray-100 text-left"
                                            >
                                                {localidad.LOCALIDAD}
                                            </div>
                                        ))
                                    ) : (
                                        <div className="p-2 text-left">No se encontraron resultados</div>
                                    )}
                                </div>
                            </PopoverContent>
                        </Popover>
                        {errors.localidadResidenciaPaciente && (
                            <p className="text-red-500 text-sm">
                                {errors.localidadResidenciaPaciente.message as string}
                            </p>
                        )}
                    </>
                )}
            />
        </div>
    );
};
