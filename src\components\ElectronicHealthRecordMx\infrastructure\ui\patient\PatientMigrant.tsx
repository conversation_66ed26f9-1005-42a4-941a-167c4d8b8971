import React from 'react'
import { useForm<PERSON>ontext, Controller } from 'react-hook-form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface PatientMigrantInputProps {
  disabled?: boolean
}

export const PatientMigrantInput: React.FC<PatientMigrantInputProps> = ({ disabled = false }) => {
  const { control, setValue } = useFormContext()

  return (
    <div className="space-y-2">
      <Label htmlFor="migrante" className='h-12 flex items-end'>¿El paciente es migrante? <span className="text-red-500">*</span></Label>
      <Controller
        name="migrante"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <>
            <Select
              onValueChange={(value) => {
                field.onChange(value)
                if (value === '1' || value === '3') {
                  setValue("paisProcedencia", '142', { shouldValidate: true })
                } else if (value === '0' || value === '-1') {
                  setValue("paisProcedencia", '-1', { shouldValidate: true })
                }
              }}
              value={field.value}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccione una opción" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">NO</SelectItem>
                <SelectItem value="1">NACIONAL</SelectItem>
                <SelectItem value="2">INTERNACIONAL</SelectItem>
                <SelectItem value="3">RETORNADO (Sólo nacional)</SelectItem>
                <SelectItem value="-1">Se desconoce el dato</SelectItem>
              </SelectContent>
            </Select>
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </>
        )}
      />
    </div>
  )
}