'use client'
import React from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import { useRouter, useSearchParams } from 'next/navigation'
import PatientForm from '@/components/ElectronicHealthRecordMx/infrastructure/ui/PatientForm'
import VideoCallChat from '@/components/ElectronicHealthRecordMx/infrastructure/ui/VideoCallChat'
import { IAppointmentWithPath } from '@/store/actions/appointments/utils/IAppointmentWithPath'
import { But<PERSON> } from '@/components/Shadcn/Button'
import { defaultValues, consultaExterna } from '@/components/ElectronicHealthRecordMx/infrastructure/data/defaultValues'
import { saveConsultaExterna, isPrimeraVezAnio, finalizarCitaActiva } from '@/components/ElectronicHealthRecordMx/infrastructure/repositories/firestoreRepository'
import useCloseAtt from '@/components/Consultorio/Columns/hooks/useCloseAtt'
import { FullLoader } from '@umahealth/occipital'
import { IHealthFacility } from '../../domain/types/IHealthFacility'
import PatientDataCard from '../ui/PatientDataCard'
import { usePatient } from '@umahealth/fe-firebase'
import { firestore } from "@/config/firebase";

type ConsultaExternaViewProps = {
  appointment: IAppointmentWithPath
  providerData: any
  cluesInfo: IHealthFacility
}

const ConsultaExternaView = ({ appointment, providerData, cluesInfo } : ConsultaExternaViewProps) => {
  const searchParams = useSearchParams()
  const patientUid = searchParams.get('patientUid') || ''
  const assignationId = searchParams.get('assignationId') || ''
  const closeAttRequest = useCloseAtt()
  const history = useRouter()
  const [loader, setLoader] = React.useState(false)
  const patientData = usePatient(firestore, patientUid)

  const methods = useForm({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: defaultValues
  })

  const closeAtt = async (diagnostic: string, providerUid: any) => {
    let final_destination = 'En domicilio con instrucciones'
    if(diagnostic === 'PROVIDER CANCEL') final_destination = diagnostic

    const closeAttData = {
      event: {
        appointment_path: `assignations/bag/MX/${assignationId}`,
        assignation_id: `${assignationId}`,
        type: 'online' as const,
      },
      mr: {
        diagnostic: diagnostic,
        final_destination: final_destination,
        notes: ''
      },
      patient: {
        corporate: 'UMA MX',
        isdependant: false,
        uid: patientUid,
      },
      provider: {
        uid: providerUid.uid
      },
    }
    closeAttRequest.mutate(closeAttData, {
      onError: async (error) => {
        console.error(error)
      }
    })
  }

  const onSubmit = async (data: any) => {
    setLoader(true)
    try {
      const completeData = Object.keys(consultaExterna).reduce((acc, key) => {
        if (key === 'fechaConsulta') {
          acc[key] = data[key] || new Date()
        } else {
          acc[key] = data[key] !== undefined && data[key] !== null && data[key] !== '' 
            ? data[key] 
            : consultaExterna[key as keyof typeof consultaExterna]
        }
        return acc
      }, {} as Record<string, any>)

      completeData['clues'] = providerData?.['clues']
      completeData['nombrePrestador'] = providerData?.['firstname']
      completeData['primerApellidoPrestador'] = providerData?.['lastname']
      completeData['segundoApellidoPrestador'] = providerData?.['secondLastname']
      completeData['paisNacimientoPrestador'] = providerData?.['paisNacimiento']
      completeData['curpPrestador'] = providerData?.['dni'] || providerData?.['cuit']

      if(completeData.codigoCIEDiagnostico1 === '-1') throw new Error('Se debe seleccionar al menos un diagnóstico')

      // Truncar los códigos CIE a los primeros 4 caracteres
      completeData.codigoCIEDiagnostico1 = completeData.codigoCIEDiagnostico1.slice(0, 4);

      if (completeData.codigoCIEDiagnostico2 && completeData.codigoCIEDiagnostico2 !== '-1') {
        completeData.codigoCIEDiagnostico2 = completeData.codigoCIEDiagnostico2.slice(0, 4);
      } else {
        delete completeData.codigoCIEDiagnostico2;
      }

      if (completeData.codigoCIEDiagnostico3 && completeData.codigoCIEDiagnostico3 !== '-1') {
        completeData.codigoCIEDiagnostico3 = completeData.codigoCIEDiagnostico3.slice(0, 4);
      } else {
        delete completeData.codigoCIEDiagnostico3;
      }

      const primeraVezAnio = await isPrimeraVezAnio(patientUid, completeData.curpPaciente, completeData.clues)
      // const primeraVezDiagnostico1 = await isPrimeraVezDiagnosticoX(patientUid, completeData.curpPaciente, completeData.clues, 1, completeData.codigoCIEDiagnostico1)
      
      // // Solo los consulta si existen
      // const primeraVezDiagnostico2 = completeData.codigoCIEDiagnostico2 ? await isPrimeraVezDiagnosticoX(patientUid, completeData.curpPaciente, completeData.clues, 2, completeData.codigoCIEDiagnostico2) : null
      // const primeraVezDiagnostico3 = completeData.codigoCIEDiagnostico3 ? await isPrimeraVezDiagnosticoX(patientUid, completeData.curpPaciente, completeData.clues, 3, completeData.codigoCIEDiagnostico3) : null
      
      completeData['primeraVezAnio'] = primeraVezAnio
      // completeData['primeraVezDiagnostico1'] = primeraVezDiagnostico1

      // // Solo los añade si existen 
      // if (primeraVezDiagnostico2 !== null) completeData['primeraVezDiagnostico2'] = primeraVezDiagnostico2
      // if (primeraVezDiagnostico3 !== null) completeData['primeraVezDiagnostico3'] = primeraVezDiagnostico3

      const success = await saveConsultaExterna(
        patientUid, 
        assignationId, 
        { ...completeData },
        providerData)
      console.log('Consulta externa guardada exitosamente', success, 'datos guardados --->', completeData)

      const patientUpdate = await finalizarCitaActiva(patientUid)
      console.log('Paciente actualizado exitosamente', patientUpdate)
      
      if (success/*  && Math.random() < 0.00000000000001 */) { // 1 in a trillion chance xD
        console.log('Consulta externa guardada exitosamente')
        await closeAtt(completeData.codigoCIEDiagnostico1, providerData)
        console.log('Cerrada exitosamente')
        history.push('/appointments')
      } else {
        throw new Error('Error al guardar la consulta externa')
      }
    } catch (error) {
      console.error(error)
    } finally {
      setLoader(false)
    }
  }

  if(loader) {
    return <FullLoader />
  }

  return (
    <div className="flex h-screen bg-gray-100 p-4 space-x-4">
      <div className="w-3/4 bg-white rounded-lg shadow-lg overflow-y-auto">
        <PatientDataCard patientData={patientData.data} />
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="p-6">
            <PatientForm providerCurp={providerData?.curp} clues={cluesInfo} patientUid={patientUid} />
          </form>
        </FormProvider>
      </div>
      <div className="w-1/4 rounded-lg">
        <VideoCallChat session={appointment.room} token={appointment.token} />
        <div className="mt-4 flex-none bg-white-200 flex flex-col items-center justify-center">
          <Button
            type="submit" 
            onClick={methods.handleSubmit(onSubmit)}
            className="font-bold text-white py-2 px-4 rounded w-full"
          >
            Finalizar
          </Button>
          <hr />
          <Button
            type="button" 
            variant="outline"
            onClick={() => {
              setLoader(true)
              closeAtt('PROVIDER CANCEL', 'providerUid')
              console.log('Cerrada exitosamente')
              setLoader(false)
              history.push('/appointments')
            }}
            className="font-bold text-black py-2 px-4 rounded w-full"
          >
            Cancelar atención
          </Button>
        </div>
      </div>
    </div>
  )
}

export default ConsultaExternaView