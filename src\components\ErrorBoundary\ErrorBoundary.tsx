// components/ErrorBoundary.tsx
import { errorHandler } from "@/config/stackdriver";
import React from "react";
import { FaExclamationTriangle } from "react-icons/fa"; // Opcional, para agregar un ícono

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Actualiza el estado para mostrar la interfaz de error
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Puedes registrar el error en un servicio de reporte de errores
    console.error("ErrorBoundary capturó un error:", error, errorInfo);
    errorHandler?.report(
      `ErrorBoundary capturó un error:", ${error}, ${errorInfo}`
    );
    this.setState({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      // Interfaz de error personalizada con Tailwind CSS
      return (
        <div className="flex items-center justify-center min-h-screen bg-gray-100 p-4">
          <div className="bg-white p-8 rounded-lg shadow-lg max-w-7xl text-center">
            <FaExclamationTriangle className="text-red-500 text-6xl mb-4 mx-auto" />
            <h1 className="text-3xl font-semibold text-red-500 mb-4">
              ¡Oops! Algo salió mal.
            </h1>
            <p className="text-gray-700 mb-6">
              Lamentamos las molestias. Ha ocurrido un error inesperado, puede
              comunicarse con soporte, reintentar, o esperar que el equipo
              técnico lo resuelva.
            </p>
            {this.state.error && (
              <div className="bg-gray-200 p-4 rounded mb-4 max-h-80 overflow-hidden">
                <h2 className="text-lg font-semibold text-gray-800 mb-2 ">
                  Detalles del error:
                </h2>
                <pre className="text-left text-sm text-gray-600 overflow-auto max-h-80 w-full whitespace-pre-wrap">
                  {this.state.error.toString()}
                  <br />
                  {this.state.errorInfo?.componentStack}
                </pre>
              </div>
            )}
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition duration-200"
            >
              Recargar página
            </button>
          </div>
        </div>
      );
    }

    // Si no hay errores, renderiza los componentes hijos normalmente
    return this.props.children;
  }
}

export default ErrorBoundary;
