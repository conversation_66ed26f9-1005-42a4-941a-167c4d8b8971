import React, { useEffect, useState } from 'react'
import { Button } from '@umahealth/occipital-ui'
import './Results.scss'

export const Results = ({ data, text }) => {
	const [newOutput, setNewOutput] = useState([])
	const [groupData, setGroupData] = useState([])
	const [buttonClicked, setButtonClicked] = useState([])

	useEffect(() => {
		getMarkers(data, text)
		// eslint-disable-next-line
	}, [data])

	useEffect(() => {
		let group = groupBy(newOutput, 'word_label')
		let entries = Object.entries(group)
		setGroupData(entries)
	}, [newOutput])
	
	function groupBy(objectArray, property) {
		return objectArray.reduce(function (acc, obj) {
			let key = obj[property]
			if (!acc[key]) {
				acc[key] = []
			}
			acc[key].push(obj)
			return acc
		}, {})
	}


	const escapeRegExp = (string) =>{
		return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') 
	}

	const pos_sent = (split_text) => { 
		let newArr = []
		split_text.map(element => {
			let elementTrimed = element.trim()
			let result = text.match(escapeRegExp(elementTrimed))
			if(result) {
				newArr.push({
					sent: elementTrimed,
					start: result.index, 
					end:result.index+result[0]?.length
				})
			}
			return null
		})
		return newArr
	}

	const getSentences = (data, text) => {
		const split_text = text.split('.') 
		let manipulatedText = pos_sent(split_text)
		const newOutput = data.map(element => {
			if(element) {
				const context = manipulatedText.filter(csent => csent.start <= element.start && csent.end >= element.end)
				if(context[0]?.sent) {
					return {
						word:element['word'],
						word_start:element['start'],
						word_end:element['end'],
						word_label:element['label_uma'],
						sent:context[0].sent,
						sent_start:context[0].start,
						sent_end:context[0].end
					}
				}
			}
			return null
		})
		return newOutput
	}

	const getKeywords = (data) => {
		let keys = []
		data.forEach(element => {
			keys.push(element.label_uma)
		})
		let extractSentences = getSentences(data, text)
		return setNewOutput(extractSentences)
		
	}

	const markSelections = (text, markers, data) => {
		getKeywords(data)
		const sortedMarkers = [...markers].sort((m1, m2) => m1.start - m2.start)
		let markedText = ''
		let characterPointer = 0
		sortedMarkers.forEach(({start, end}) => {
			markedText += text.substring(characterPointer, start)
			markedText += ' <mark> '
			markedText += text.substring(start, end)
			markedText += ' </mark> '
			characterPointer = end
		})
		markedText += text.substring(characterPointer)
		document.getElementById('result').innerHTML = markedText
	}

	const getMarkers = (data, text) => {
		let markers = []
		const values = Object.values(data['output'])
		values.forEach(element => {
			let start = element.start
			let end = element.end
			markers.push({start, end })
		})
		return markSelections(text, markers, values)
	}

	const handleResumeView = (index) => {
		let cloneClicked = [...buttonClicked]
		if(cloneClicked.includes(index)){
			setButtonClicked(cloneClicked.filter(click => click !== index))
		} else {
			cloneClicked.push(index)
			setButtonClicked(cloneClicked)
		}
	}
 
	return (
		<section className='antecedents__results'>
			<div className='text__container'>
				<p>Antecedentes del paciente:</p>
				<p id='result'></p>
			</div>
			<div className='keywords__container'>
				<p>Clickea en un antecedente para generar un resumen más especifico</p>
				{groupData && groupData?.length >= 0 &&
					groupData.map((sentence, index) => {
						if(sentence) {
							return (
								<div className='sentences' key={index}>
									<Button size="large" action={() => handleResumeView(index)} key={index}>
										{sentence[0]}
									</Button>
									{buttonClicked.includes(index) &&
									<>
										<p>Resumen del antecedente:</p>
										{sentence[1].map(item => <p key={item.sent}>{item.sent}</p>)}
									</>
									}
								</div>
							)
						} else {
							return null
						}
					})
				}
			</div>
		</section>
	)
}