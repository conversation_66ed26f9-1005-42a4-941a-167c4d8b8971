@use '@/styles/global/Vars.scss';

.antecedents__results {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 2rem;
	height: 100%;
}


.keywords__container {
	flex-wrap: wrap;
	width: 50%;
	padding: 10px 15px;
	border-radius: 10px;
	overflow-y: scroll;
	box-sizing: border-box;
	height: 60vh;
	box-shadow: Vars.$tune-shadow;
	position: relative;
	z-index:10;
	background-color: #fff;
} 

.keywords__container p {
	text-align: center;
	color: Vars.$deep-blue;
	margin: 2px;
}

.keywords__container p::after {
	content: '';
	display: block;
	width: 100%;
	margin-top: 5px;
	height: 1px;
	background-color: #4895ef;
}

.keywords__container button {
	border: transparent;
	margin: 10px 0;
	cursor: pointer;
	text-transform: capitalize;
	background-color:#4895ef;
	padding: 10px;
	color: white;
	font-weight: bold;
	border-radius: 5px;
	width: 100%;
	transition: 0.3s all ease-in-out;
	position: relative;
}

.keywords__container button:focus {
	outline: transparent;
	background-color: #2a6ab4;
}

.keywords__container button:hover {
	background-color: #4086d6;
	filter: brightness(1.3);
}

.text__container {
	overflow-y: auto;
	width: 50%;
	height: 60vh;
	padding: 10px;
	border-radius: 10px;
	position: relative;
	box-sizing: border-box;
	box-shadow: Vars.$tune-shadow;
	background-color: #fff;
}

.text__container.buttonSave {
	background-color: #4086d6;
	color: #4086d6;
	border-radius: 5px;
	padding: 5px 5px;
	border: 1px solid #4086d6;
	background-color: transparent;
	font-weight: bold;
	cursor: pointer;
	position: absolute;
	right: 5px;
	top: 5px;
}

.text__container p mark {
	display: inline-block;
	padding: 1px 3px;
	background-color: #A13DDF;
	color: whitesmoke;
}

.text__container p {
	line-height: 1.6rem;
	color: Vars.$deep-blue;
}

::-webkit-scrollbar {
	width: 10px;
	height: min-content;
}

::-webkit-scrollbar-track {
	box-shadow: 0px 0px 1px 2px rgba(98, 154, 251, 13%);
}

::-webkit-scrollbar-thumb {
	background-color: #619BFF;
}

.sentences {
	overflow-y: auto;
	position: relative;
	box-sizing: border-box;
}
