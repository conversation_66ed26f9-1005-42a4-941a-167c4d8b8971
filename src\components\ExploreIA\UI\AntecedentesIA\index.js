'use client'
import React, { useState } from 'react'
import{ Results }from './Results/Results'
import { But<PERSON>, Loader, Paragraph, Spacer, Title} from '@umahealth/occipital-ui'
import { errorHandler } from '@/config/stackdriver'
import axios from 'axios'
import {history_nlp} from '@/config/endpoints'
import FormulaEffects from '@/components/ExploreIA/UI/FormulaEffects'
import swal from 'sweetalert'
import '../../styles/AntecedentesAI.scss'
import { auth } from '@/config/firebase'

const Main = () => {
	const [text, setText] = useState('HIPERTENSIÓN ARTERIAL de más de 30 años de evolución. HIPERTROFIA VENTRICULAR IZQUIERDA. Esclerosis valvular aórtica, insuficiencia mitral leve. EXTRASISTOLIA VENTRICULAR ASILADA monofmorfa. GASTRITIS en 1999 (FEDA). HISTERECTOMÍA TOTAL en 1968 por MIOMATOSIS UTERINA. CIRUGÍA DE CADERA IZQUIERDA (fractura) con colocación de placa de titanio en 1995.HIPOACUSIA PERCETPTIVA bilateral, OTOESCLEROSIS IZQUIERDA. Piel: Verrugas seborreicas en mejillas, cuello. Manchas actinicas en cara. Seborrea del cuero cabelludo. Olvidos irrelevantes, fue medicada con memantine en dosis de 20 mg pero lo suspendió porque le daba cefaleas. Tiene una RM (atendida en FLENI) que no mostraba alteraciones relevantes. Medicación: AMLODIPINA 5 mg (Pelmec 5) diarios; SIMVASTATINA 10 mg (Labistatin) uno por día. No toma AAS por antecedentes de gastritis.')
	const [response, setResponse] = useState('')
	const [localLoading, setLocalLoading] = useState(false)

	const handleResponse = async() => {
		setLocalLoading(true)
		try{
			auth?.currentUser?.getIdToken().then(async token => {
				await axios.post(history_nlp,
					{'text': text},
					{headers: { 
						'Content-Type': 'Application/Json', 
						'Authorization': `Bearer ${token}`,
					}})
					.then(res => {
						setResponse(res.data)
						setLocalLoading(false)
					})
					.catch(error =>{ 
						setLocalLoading(false)
						swal(
							{
								title: 'Hubo un error',
								text: 'El modelo no se pudo ejecutar. Prueba de nuevo.',
								icon: 'warning',
							}
						)
						errorHandler.report(error)
					})
			})
		} catch (err) {
			setLocalLoading(false)
			errorHandler.report(err)
		}
	}


	if(localLoading) return <div className="antecedents__loader">
		<Loader/>
		</div>
	return (
		<section className='antecedents__main'>
			
			{
				response === '' &&
			<>
				<FormulaEffects />
				<AntecedentesExplanation
					handleResponse={handleResponse}
					text={text}
					setText={setText}
				/>
			</>
			}
			{
				response !== '' &&
				<Results data={response} text={text} />
			}
		</section>
	)
}

export default Main



const AntecedentesExplanation = ({
	handleResponse,
	text,
	setText
}) => {
	const bullets = [
		'Haga click sobre el boton "Analizar".' ,
		'Esto iniciara el análisis de los antecedentes de prueba.',
		'Aguarde los resultados.',
		'Pruebe con otro texto.'
	]

	return(
		<div className="exploreIa__col">
			<Title text="Modelo de Antecedentes" size="xl" />
			<Spacer direction="vertical"
				value="8px"/>
			<Paragraph 
				weight="semibold"
				size="sm"
				text="El Modelo de Antecedentes es una herramienta que ayuda al doctor haciendo un análisis y etiquetado de los antecedentes de un paciente." />
			<Spacer direction="vertical"
				value="8px"/>
			<Paragraph 
				weight="semibold"
				size="sm"
				text="A traves de la tecnología de NLP (Natural Language Processing), evalúa el historial y detecta los hitos mas relevantes." />
			<Spacer 
				direction="vertical"
				value="8px"/>
			<Title text="Cómo utilizarlo" hierarchy="3" size="lg" color="secondary"/>
			<div className="exploreIa__infoModelo">
				<ul className="exploreIa__infoBullets">
					{bullets.map((bull, index) => <li key={index}>{bull}</li>)}
				</ul>
			</div>
			<Spacer direction="vertical" value="8px"/>
			<section className='antecedents__input'>
				<article className='antecedents'>
				</article>
				<p>
				**En el campo figura un texto de prueba.**
				</p>
				<form>
					<textarea value={text} onChange={(e)=> setText(e.target.value)} />
				</form>
				<Spacer direction="vertical" value="8px"/>
				<Button action={handleResponse}>Analizar</Button>
			</section>
			<Spacer direction="vertical" value="8px"/>
			<Title text="Dónde se encuentra" hierarchy="3" size="lg" color="secondary"/>
			<Paragraph text="Esta herramienta se encuentra en el Consultorio Online de ÜMA." />
		</div>
	)
}