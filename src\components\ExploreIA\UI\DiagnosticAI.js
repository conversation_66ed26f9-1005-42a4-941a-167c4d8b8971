'use client'
import React, {useState} from 'react'
import swal from 'sweetalert'
import axios from 'axios'
import {Button, Title, Spacer, Paragraph} from '@umahealth/occipital-ui'
import { errorHandler } from '@/config/stackdriver'
import {diagnostic_url} from '@/config/endpoints'
import Spinner from '@/components/GeneralComponents/Loaders/Spinner'
import Diagnostic from '@/components/Consultorio/Diagnostic'

const DiagnosticPage = () => {
	const [textareaValue, setTextareaValue] = useState('')
	const [diagnostic, setDiagnostic] = useState('')
	const [localLoading, setLocalLoading] = useState(false)

	const bullets = [
		'En el Area de texto de la columna izquierda escriba, a modo prueba "Paciente con disnea y precordalgia."',
		'Haga click sobre el boton Análizar',
		'Observe como se modifica el diagnóstico predictivo.',
		'Haga la prueba con otras oraciones.'
	]

	const diagnostic_predict = async() => {
		if(textareaValue?.length < 3){
			swal(
				{
					title: 'Error',
					text: 'El modelo necesita un texto mayor para ejecutarse. Prueba de nuevo.',
					icon: 'warning',
				})
			return
		}
		setLocalLoading(true)
		try{
			let response = await axios.post(diagnostic_url, {
				'text': textareaValue
			},{ headers: {
				'Content-type': 'application/json',
			}})

			let data = await response['data']['output']
			setDiagnostic(data)
			setLocalLoading(false)
		}catch(err){
			errorHandler.report(err)
			setLocalLoading(false)
			swal(
				{
					title: 'Hubo un error',
					text: 'El modelo no se pudo ejecutar. Prueba de nuevo.',
					icon: 'warning',
				}
			)
		}
	}


	if(localLoading) return <Spinner />
	return(
		<div className="exploreIa__exampleContainer">
			<div className="exploreIa__col">
				<Title text="Diagnóstico Predictivo" size="xl"/>
				<Spacer direction="vertical" value="8px"/>
				<Paragraph 
					weight="semibold"
					size="sm"
					text="El modelo de Diagnóstico predictivo es una herramienta que ayuda al doctor en la evaluación del paciente." />
				<Spacer direction="vertical" value="8px"/>
				<Paragraph 
					weight="semibold"
					size="sm"
					text="El Diagóstico Predictivo analiza los sintomas que presenta el paciente y predice un posible diagnóstico." />
				<Spacer direction="vertical" value="8px"/>
				<Title text="Cómo utilizarlo" hierarchy="3" size="lg" color="secondary"/>
				<div className="exploreIa__infoModelo">
					<ul className="exploreIa__infoBullets">
						{bullets.map((bull, index) => <li key={index}>{bull}</li>)}
					</ul>
				</div>
				<Spacer direction="vertical" value="8px"/>
				<div className="exploreIa__diagnosticText">
					<div className="exploreIa__label">Area de texto</div>
					<textarea 
						name="textarea" 
						placeholder='Escriba sintomas. Ejemplo: "Paciente con disnea y precordalgia.". '
						rows={8}
						value={textareaValue}
						onChange={(e) => setTextareaValue(e.target.value)} 
					/>
				</div>
				<Diagnostic 
					diagnosticAsProp={diagnostic}
				/>
				<Spacer direction="vertical" value="8px"/>
				<Button action={diagnostic_predict}>Analizar</Button>
				<Spacer direction="vertical" value="8px"/>
				<Title text="Dónde se encuentra" hierarchy="3" size="lg" color="secondary"/>
				<div className="exploreIa__infoModelo">
					<ul className="exploreIa__infoBullets">
						Esta herramienta se encuentra en el Consultorio Online de ÜMA
					</ul>
				</div>
			</div>
		</div>
	)
}

export default DiagnosticPage