import React from 'react'
import '../styles/FormulaEffects.scss'

const formulas = [
	'ln(odds) = ln(p/(1-p))',
	'Y = f (X)',
	'P(x) = e ^ (b0 +b1x) / (1 + e(b0 + b1x)) ',
	'P(h|d)= (P(d|h) P(h)) / P(d)',
	'Y = c + m₁X₁ + m₂X₂ + ….. +mnXn',
	'P(A|B) = P(B|A)P(A) / P(B)',
	'h(x)= 1/ (1 + ex)',
	'x=(-b +- sqrt(b^2 - 4ac))/(2a)',
	'Log (1) = 0, log (10) = 1, log (100) = 2.',
	'2 (298) + 1 2 (299) + 1 2 (300) + 1 ··· 2 (1688) + 1',
	'sin x = √1 − t',
	'2 sin A sin B = cos (A − B) − cos (A + B)',
	'x2 +1= A (x − 1) (x + 1) + Bx (x + 1) + Cx (x − 1)',
	'P(A < x < B) = f(B) - f(A)'
]

const writingModes = [
	'vertical-rl',
	'horizontal-tb',
	'vertical-lr'
]

const textOrientation = [
	'mixed',
	'upright',
	'sideways'
]

const FormulaEffects = () => {
	return (
		<div 
			className="formulaEffects__container">
			{
				formulas.map((form, index) => (
					<div
						className="formulaEffects__form"
						key={index}
						style={{
							WebkitAnimationDuration: `${Math.random().toFixed(2) * 4.5 + 1}s`,
							animationDuration: `${Math.random().toFixed(2) * 4.5 + 1}s`,
							fontSize: `${Math.random() * 1.1 + 0.5}rem`,
							top: `${Math.random() * 100}%`,
							left: `${Math.random() * 100}%`,
							opacity: `${Math.random().toFixed(2) * 0.95}`,
							transition: `${Math.random().toFixed(2) * 2 + 0.5}s opacity ease`,
							writingMode: `${writingModes[Math.floor(Math.random() * 3)]}`,
							textOrientation: `${textOrientation[Math.floor(Math.random() * 3)]}`
						}}>
						{form}
					</div>
				))
			}
		</div>
	)
}

export default FormulaEffects