'use client'
import React from 'react'
import {Title, Spacer, Paragraph} from '@umahealth/occipital-ui'
import AsyncSelect from 'react-select/async'
import { searchMedicinesAR } from '@/components/Consultorio/Prescriptions/Recipe/RecipeAR/arRecipeHelpers'
import { searchMedikitMedicines } from '@/components/Consultorio/Prescriptions/Recipe/RecipeMX/medikitHelpers'
import { debounce } from '@/lib/utils'

const MedSearchPage = () => {
	const bullets = [
		'Haga click sobre el campo del buscador.',
		'A modo de prueba escriba un medicamento con errores de ortografía',
		'Aguarde los resultados',
		'Si resulto bien, debería poder ver un campo donde se despliegan los medicamentos que el modelo interpreto de su búsqueda.',
		'Pruebe con otras búsquedas'
	]

	const debounceSearcher = (value, cb) =>{
		searchMedicine(value)
		.then(res => cb(res))
	}

	const debouncedSearch = debounce(debounceSearcher, 500)

	const searchMedicine = (e) => {
		switch (process.env.NEXT_PUBLIC_COUNTRY) {
		case 'AR': return searchMedicinesAR(e)
		case 'MX': return searchMedikitMedicines(e)
		default:
			return searchMedicinesAR(e)
		}
	}

	return(
		<div className="exploreIa__exampleContainer">
			<div className="exploreIa__col">
				<Title text="Buscador Inteligente" size="xl"/>
				<Spacer direction="vertical" value="8px"/>
				<Paragraph 
					weight="semibold"
					size="sm"
					text="El Buscador Inteligente es una herramienta que ayuda al doctor en la relización de sus recetas." />
				<Spacer direction="vertical" value="8px"/>
				<Paragraph 
					weight="semibold"
					size="sm"
					text="A traves de la tecnología de NLP (Natural Language Processing), evalúa su búsqueda y reconoce potenciales medicamentos que este buscando." />
				<Spacer direction="vertical" value="8px"/>
				<Title text="Cómo utilizarlo" hierarchy="3" size="lg" color="secondary"/>
				<div className="exploreIa__infoModelo">
					<ul className="exploreIa__infoBullets">
						{bullets.map((bull, index) => <li key={index}>{bull}</li>)}
					</ul>
				</div>
				<Spacer direction="vertical" value="8px"/>
				<div className='exploreIa__col--searcher'>
					<label>
						Producto y presentación*
					</label>
					<AsyncSelect
						autoFocus
						isClearable
						openMenuOnClick={false}
						maxMenuHeight={250}
						loadOptions={debouncedSearch}
						id='recipeInput'
						placeholder='Escriba el medicamento y aguarde las opciones'/>
				</div>
				<Spacer direction="vertical" value="8px"/>
				<Title text="Dónde se encuentra" hierarchy="3" size="lg" color="secondary"/>
				<div className="exploreIa__infoModelo">
					<ul className="exploreIa__infoBullets">
						Esta herramienta se encuentra el Consultorio Online de ÜMA, cuando realiza una receta.
					</ul>
				</div>
			</div>
		</div>
	)
}

export default MedSearchPage