'use client'
import React from 'react'
import FormulaEffects from './FormulaEffects'
import {Button, Spacer} from '@umahealth/occipital-ui'
import { useRouter } from 'next/navigation'

function Welcome() {
	const router = useRouter()

	return (
		<>
			<div className="exploreIa__landing" >
				<FormulaEffects />
				<div className={'exploreIa__landingInfo'}>
					<h2>Explorar IA</h2>
					<p>En esta sección podrá descubrir y experimentar con los modelos de Inteligencia Artificial que ÜMA ofrece a nuestros profesionales.</p>
					<div className="exploreIa__callToActions">
						<Button size="large" action={() => router.push('/exploreIa?page=antecedentes')} title="Identificación automática de antecedentes">Antecedentes</Button>
						<Spacer value="24px" />
						<Button size="large" action={() => router.push('/exploreIa?page=vademecum')} title="Buscador inteligente de vademecum">Vademecum</Button>
						<Spacer value="24px" />
						<Button size="large" action={() => router.push('/exploreIa?page=diagnostic')} title="Predicción de diagnósticos más probables">Diagnósticos</Button>
					</div>
				</div>
			</div>
		</>
	)
}

export default Welcome