// '@/components/ExploreIA/index.tsx'

import React from "react";
import Welcome from "./UI/Welcome";
import DiagnosticAI from "./UI/DiagnosticAI";
import AntecedentesIA from "@/components/ExploreIA/UI/AntecedentesIA";
import Vademecum from "./UI/VademecumAI";
import "./styles/ExploreAI.scss";
import HeaderSuspense from "@/components/GeneralComponents/Header";

interface Props {
	searchParams: { page?: string }
}

const ExploreIa = ({ searchParams }: Props) => {
	const page = searchParams.page;

	const SwitchView = () => {
		switch (page) {
			case "main":
				return <Welcome />;
			case "antecedentes":
				return <AntecedentesIA />;
			case "diagnostic":
				return <DiagnosticAI />;
			case "vademecum":
				return <Vademecum />;
			default:
				return <Welcome />;
		}
	};

	const title: Record<string, string> = {
		antecedentes: "Antecedentes",
		diagnostic: "Diagnós<PERSON><PERSON>",
		vademecum: "Vademecum",
	};

	return (
		<>
			{!page || page === "main" ? (
				<HeaderSuspense title="Explorar IA" />
			) : (
				<HeaderSuspense title={title[page as keyof typeof title]} arrowBack={true} />
			)}
			<div className="exploreIa__container">{SwitchView()}</div>
		</>
	);
};

export default ExploreIa;
