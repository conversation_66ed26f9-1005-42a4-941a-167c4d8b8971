@use '@/styles/global/Vars.scss';


.antecedents__main {
	width: 100%;
	background-color: rgba(Vars.$uma-primary, 0.05);
	padding: 32px;
	position: relative;
	min-height: 90vh;
}
.antecedents__loader {
	width: 100% !important;
	height: 80vh !important;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.antecedents__input {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin: 32px 0 32px 0;
}

.antecedents__input .logoUma {
	display: block;
	margin: 10px auto;
	width: 150px;
}

.antecedents__input .mainImg {
	width: 32%;
	padding: 20px;
}

.antecedents__input article {
	max-width: 400px;
	text-align: center;
	color: #404040;
}

.antecedents__input p{
	color: Vars.$deep-blue;
}

.antecedents__input form {
	min-width: 370px;
	width: 90%;
	box-shadow: -4px 4px 20px rgba(0, 0, 0, 0.15);
	border: 1px solid Vars.$deep-blue;
	border-radius: 10px;
}

.antecedents__input form textarea {
	width: 100%;
	padding: 10px;
	height: 330px;
	box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.05);
	border: none;
}

.antecedents__input form textarea:focus {
	outline: none;
}

.antecedents__input .btnAction {
	background-color: #0A6DD6;
	color: white;
	border-radius: 30px;
	width: 200px;
	height: 40px;
	margin-top: 10px;
	border: none;
	font-weight: 600;
	cursor: pointer;
	font-size: 20px;
}

.antecedents__input .textButton {
	margin-top: 10px;
	border: none;
	background-color: transparent;
	color: Vars.$uma-primary;
	font-size: 14px;
	font-weight: bold;
	cursor: pointer;
}

.antecedents__input .backBtn{
	margin-top: 10px;
	border: none;
	background-color: transparent;
	color: Vars.$uma-primary;
	font-size: 14px;
	font-weight: bold;
	cursor: pointer;
}

.antecedents__input .textButton:focus {
	outline: transparent;
}

.antecedents__input .btnAction:hover {
	background-color: #0356AD;
	transition: ease-in-out 0.3s;
}

.antecedents__input .btnAction:focus {
	outline: none;
}