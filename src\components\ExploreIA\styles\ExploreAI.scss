@use '@/styles/global/Vars.scss';

.exploreIa__container{
	position: relative;
	.exploreIa__backBtn{
		position: absolute;
		font-size: 1.1rem;
		left: 3%;
		top: 3%;
		z-index: 1;
		background: none;
		button{
			background: none;
		}
	}
}

.exploreIa__landing{
	display: flex;
	justify-content: space-around;
	align-items: center;
	overflow: hidden;
	height: 90vh;
	position: relative;
	.exploreIa__logo{
		position: relative;
		z-index: 2;
		margin-bottom: 400px;
		width: 400px;
		opacity: 0;
		transition: 1.5s opacity ease;
		filter: drop-shadow(4px 4px 10px rgba(0,0,0,.5));
		&.appear{
			opacity: 1;
		}
	}
}

.exploreIa__landingInfo{
	box-shadow: Vars.$box-shadow;
	border-radius: 8px;
	z-index: 1;
	width: 50%;
	height: 50%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: Vars.$uma-background;
	img{
		width: 200px;
	}
	h2{
		font-style: normal;
		font-weight: 600;
		font-size: 5rem;
		line-height: 131px;
		color: Vars.$uma-primary;
		background: linear-gradient(to right,#095fab 8%, Vars.$uma-secondary-dark 50%, Vars.$uma-pink 60%);
		background-size: auto auto;
		background-clip: border-box;
		background-size: 200% auto;
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		animation: textclip 3.5s linear infinite;
		-webkit-animation:textclip 3.5s linear infinite;
		-moz-animation:textclip 3.5s linear infinite;
		-o-animation:textclip 3.5s linear infinite;
	}
	p{
		font-style: normal;
		font-weight: 600;
		font-size: 22px;
		line-height: 30px;
		width: 80%;
		color: #000;
		text-shadow: 0 1px 2px rgba(#fff, 0.8);
		text-align: center;
		background-color: rgba(#fff, 0.9);
	}
	&.hide{
		visibility: hidden;
	}
	
	.exploreIa__callToActions {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 10px;
	}
	}								 

@keyframes textclip {
	to {
		background-position: 200% center;
	}
}

@-webkit-keyframes textclip {
	to {
		background-position: 200% center;
	}
}


@-ms-keyframes textclip {
	to {
		background-position: 200% center;
	}
}

@-moz-keyframes textclip {
	to {
		background-position: 200% center;
	}
}

@-o-keyframes textclip {
	to {
		background-position: 200% center;
	}
}

.exploreIa__Bubbles{
	visibility: hidden;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
	// background: linear-gradient(to top,Vars.$uma-primary 1%,#fff 60%, #fff 70%);
	opacity: 0;
	transition: 1s all ease;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 5rem;
	.exploreIa_bubble{
		position: relative;
		width: 200px;
		height: 210px;
		border-radius: 50%;
		border: 1px solid #CACACA;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #FFF;
		box-shadow: Vars.$tune-shadow;
		transition: 0.4s opacity ease;
		transition: 2s transform ease;
		cursor: pointer;
		h3{
			color: #fff;
		}
		p{
			width: 160px;
			margin: 0;
		}
	}
	.exploreIa_bubble:nth-child(1){
		background: linear-gradient(45deg,Vars.$blue-input-border 10%, #2680ce 40%, Vars.$uma-primary 60%);
		transform: translate(-40%, 60%);
		-moz-transform:translate(-40%, 60%);
		-webkit-transform:translate(-40%, 60%);
		-o-transform: translate(-40%, 60%);
		-ms-transform: translate(-40%, 60%);
		.exploreIa_bubbleDraw{
			position: absolute;
			top: 0;
			width: 40px;
			height:80px;
			border-radius: 90% 50% 50% 90%;
			border-left: 7px solid #fff;
			transform: translate(30px)rotate(120deg);
		}
	}
	.exploreIa_bubble:nth-child(2){
		transform: translate(-50%, -80%);
		-webkit-transform:translate(-50%, -80%);
		-o-transform: translate(-50%, -80%);
		-ms-transform: translate(-50%, -80%);
		background: linear-gradient(45deg, rgb(213, 107, 255) 10%, Vars.$uma-secondary 40%, Vars.$uma-secondary-dark 70%);
		.exploreIa_bubbleDraw{
			position: absolute;
			top: 0;
			width: 40px;
			height:80px;
			border-radius: 90% 50% 50% 90%;
			border-left: 7px solid #fff;
			transform: translate(30px)rotate(120deg);
		}
	}
	.exploreIa_bubble:nth-child(3){
		transform: translate(-50%, 90%);
		-webkit-transform:translate(-50%, 90%);
		-o-transform: translate(-50%, 90%);
		-ms-transform: translate(-50%, 90%);
		background: linear-gradient(45deg, rgb(211, 182, 223) 10%, rgb(220, 128, 220) 20%, Vars.$uma-pink 80%);
		.exploreIa_bubbleDraw{
			position: absolute;
			top: 0;
			width: 40px;
			height:80px;
			border-radius: 90% 50% 50% 90%;
			border-left: 7px solid #fff;
			transform: translate(30px)rotate(120deg);
		}
	}
	
	&.show{
		opacity: 1;
		visibility: visible;
		.exploreIa_bubble{
			transform: translate(0,0);
		}
		.exploreIa_bubble:hover{
			transform: scale(1.1);
		}
	}
}




.exploreIa__exampleContainer{
	display: flex;
	justify-content: space-between;
	background-color: rgba(Vars.$uma-primary, 0.05);
	padding: 16px 0;
	width: 100%;
	position: relative;
	.exploreIa__col{
		width: 70%;
		padding: 64px 32px;
	}
}

.exploreIa__infoModelo{
	.exploreIa__infoBullets{
		padding: 0 16px;
		line-height: 1;
		li{
			list-style: none;
			padding: 5px 8px;
			font-size: 1rem;
			line-height: 16px;
		}
		li::before{
			content: "\2022";
			color: Vars.$uma-secondary;
			font-weight: bold;
			display: inline-block;
			width: 1em;
			margin-left: -1em;
		}
	}
}


.exploreIa__col{
	z-index: 2;
	width: 70%;
	background-color: #fff;
	margin: 0 auto;
	padding: 64px 32px;
	border-radius: 8px;
	box-shadow: Vars.$tune-shadow;
	button{
		display: block;
		margin: 15px auto;
	}
}

.exploreIa__col--searcher{
	background: rgba(Vars.$uma-secondary, 0.1);
	padding: 16px;
	margin: 32px auto;
	label{
		font-size: 1rem;
		font-weight: bold;
		text-align: center;
		color: Vars.$uma-secondary;
	}
}


.exploreIa__diagnosticText{
	margin: 16px 0;
	.exploreIa__label {
		border-radius: 5px;
		padding: 10px;
		background: Vars.$gray-navigation-color;
		color: #51626d;
		font-weight: bold;
		min-height: 40px;
		display: flex;
		justify-content: space-between;
		font-size: 16px;
	}
	textarea{
		width: 100%;
		border: 1px solid #CACACA;
		border-radius: 4px;
		margin-top: 4px;
	}
}
