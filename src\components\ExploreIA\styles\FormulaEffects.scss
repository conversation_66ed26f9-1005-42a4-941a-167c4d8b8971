@use '@/styles/global/Vars.scss';


.formulaEffects__container{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: -1;
	-webkit-perspective: 1000px;
	perspective: 800px;
	perspective-origin: 50% 50%;
	
}

.formulaEffects__form{
	position: absolute;
	color: Vars.$deep-blue;
	letter-spacing: 0.13rem;
	opacity: 0;
	-webkit-animation: moveOnZ infinite ease-in-out alternate;
	animation: moveOnZ infinite ease-in-out alternate;
}



@-webkit-keyframes moveOnZ {
	from {
	-webkit-transform: translateZ(-150px);
			transform: translateZ(-150px);
	}
	to {
	-webkit-transform: translateZ(150px);
			transform: translateZ(150px);
	}
}

@keyframes moveOnZ {
	from {
	-webkit-transform: translateZ(-150px);
			transform: translateZ(-150px);
	}
	to {
	-webkit-transform: translateZ(150px);
			transform: translateZ(150px);
	}
}