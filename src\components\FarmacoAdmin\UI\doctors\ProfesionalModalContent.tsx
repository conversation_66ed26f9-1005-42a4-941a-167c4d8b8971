import { IPractitioner } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPractitioner'
import { Button, Input, Title, Text } from 'occipital-new'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { transformDataToPractitioner } from '../../utils/transformDataToPractitioner'
import { createFhirResource } from '@/components/MyPatients/infraestructure/services/writeFhirResource'
import { IModal } from './Profesionals'
import swal from 'sweetalert'
import style from '../../styles/profesionalModalContent.module.scss'
import { updateFhirResource } from '@/components/MyPatients/infraestructure/services/updateFhirResource'

interface IProps{
    action: 'edit' | 'create',
    setModal: React.Dispatch<React.SetStateAction<IModal>>,
    practitionerToEdit?: IPractitioner,
    setPractitionersList: React.Dispatch<React.SetStateAction<IPractitioner[]>>
}

export interface PractitionerForm{
    name: string,
    surname: string,
    phone: string,
    email: string,
    country: string,
    specialties: string
}

const getDefaultValues = (practitioner?: IPractitioner): undefined | PractitionerForm => {
    if(!practitioner) return undefined
    const completeName = practitioner.name?.find(name => name.use === 'usual') 
    const name = completeName?.given?.[0] || ''
    const surname = completeName?.family || ''
    const phone = practitioner.telecom?.find(tel => tel.system === 'phone')?.value || ''
    const email = practitioner.telecom?.find(tel => tel.system === 'email')?.value || ''
    const country = practitioner.address?.find(address => address.type = 'physical')?.country || ''
    return {
        country,
        email,
        phone,
        name,
        surname,
        specialties: ''
    }
}

export const ProfesionalModalContent = ({ action, setModal, practitionerToEdit, setPractitionersList }: IProps) => {
    const [specialties, setSpecialties] = useState<string[]>(!practitionerToEdit ? [] : practitionerToEdit?.extension?.map(extension => extension?.valueString || '' ) || [])
    const { register, watch, handleSubmit, formState: { errors } } = useForm<PractitionerForm>({
        defaultValues: getDefaultValues(practitionerToEdit)
    })
    const addSpecialties = () => {
        setSpecialties(prev => [...prev, watch('specialties')])
    }

    const submitForm = async (dataForm: PractitionerForm) => {
        const practitionerObject = transformDataToPractitioner(dataForm, specialties)
        const timestamp = new Date().toLocaleString();
        if(action === 'create'){
            const fhirResponse = await createFhirResource('Practitioner', JSON.stringify(practitionerObject))
            if(!fhirResponse) return await swal('Ha ocurrido un error creando el médico', 'Por favor, intente nuevamente', `Detalle: ${timestamp}` ,'warning')
            await swal('Médico creado correctamente', '', 'success')
            setModal({
                create: false,
                edit: false
            })
            return
        }
        const fhirResponse = await updateFhirResource(practitionerToEdit?.id || '', 'Practitioner', JSON.stringify({ ...practitionerToEdit, ...practitionerObject}))
        if(!fhirResponse) return await swal('Ha ocurrido un error editando el médico', 'Por favor, intente nuevamente',  `Detalle: ${timestamp}`, 'warning')
        await swal('Médico editado correctamente', '', 'success')
        setModal({
                create: false,
                edit: false
        })
        setPractitionersList([])
        return
    }

  return (<>
        <Title color='text-primary' size='m' hierarchy='h2' weight='regular'>Formulario de médico</Title>
        <form className={style.formContainer} onSubmit={handleSubmit(submitForm)}>
            <Input inputmode='text' label='Nombre' register={register('name', {
                required: 'Por favor ingrese nombre'
            })} hasValue={!!watch('name')} size='full' type='text' />
            {errors.name && <Text color='state-error' size='s' tag='span' weight='regular'>{errors.name.message}</Text>}
            <Input inputmode='text' label='Apellido' register={register('surname', {
                required: 'Por favor ingrese apellido'
            })} hasValue={!!watch('surname')} size='full' type='text' />
            {errors.surname && <Text color='state-error' size='s' tag='span' weight='regular'>{errors.surname.message}</Text>}
            <Input inputmode='text' label='Celular' register={register('phone')} hasValue={!!watch('phone')} size='full' type='text' />
            <Input inputmode='text' label='Email' register={register('email')} hasValue={!!watch('email')} size='full' type='text' />
            <div className={style.specialtiesSection}>
                <Text color='text-primary' size='s' tag='span' weight='regular'>
                    {!specialties?.length ? 'No especifica especialidad': specialties?.length === 1 ? `Especialidad: ${specialties[0]}` : `Especialidades: ${specialties.join('-')}`}
                </Text>
                <Input inputmode='text' label='Especialidad' register={register('specialties')} hasValue={!!watch('specialties')} size='full' type='text' />
                <Button disabled={!watch('specialties')} action={addSpecialties} occ_type='filled' size='full' type='button'>Agregar especialidad</Button>
            </div>
            <Input inputmode='text' label='Pais' register={register('country')} hasValue={!!watch('country')} size='full' type='text' />
            <Button occ_type='filled' size='full' type='submit'>{action === 'edit' ? 'Editar' : 'Guardar'}</Button>
        </form>
    </>
  )
}
