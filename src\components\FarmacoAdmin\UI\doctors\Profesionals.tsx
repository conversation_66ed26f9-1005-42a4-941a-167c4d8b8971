'use client'
import React, { useState } from 'react'
import { ProfesionalsSearcher } from './ProfesionalsSearcher'
import { ProfesionalsList } from './ProfesionalsList'
import { IPractitioner } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPractitioner'
import { Button, Modal } from 'occipital-new'
import { ProfesionalModalContent } from './ProfesionalModalContent'

export interface IModal {
  edit: IPractitioner | false, 
  create: boolean
}


export const Profesionals = () => {
    const [practitionersList, setPractitionersList] = useState<IPractitioner[]>([])
    const [loading, setLoading] = useState<boolean>(false)
    const [modal, setModal] = useState<IModal>({
      edit: false,
      create: false
    })
    
  return (
    <div>
        <Button action={() => setModal({ edit: false, create: true })} type='button' occ_type='filled' size='extended'>Agregar médico</Button>
        <ProfesionalsSearcher loading={loading} setLoading={setLoading} setPractitionersList={setPractitionersList} />
        <ProfesionalsList setModal={setModal} loading={loading} practitionerList={practitionersList} />
        {(modal.create || modal.edit) && <Modal onClose={() => setModal({ edit: false, create: false })} width={'400px'}>
            <ProfesionalModalContent setPractitionersList={setPractitionersList} practitionerToEdit={modal.edit || undefined} setModal={setModal}  action={modal.create ? 'create' : 'edit'} />
        </Modal>}
    </div>
  )
}