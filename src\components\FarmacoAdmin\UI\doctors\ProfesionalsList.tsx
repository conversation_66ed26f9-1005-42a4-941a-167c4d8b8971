import React from 'react'
import { IPractitioner } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPractitioner'
import style from '../../styles/profesionalsList.module.scss'
import { Loader } from 'occipital-new'
import { IModal } from './Profesionals'
import { patchFhirResource } from '@/components/MyPatients/infraestructure/services/patchFhirResource'
import swal from 'sweetalert'

interface IProps {
    practitionerList: IPractitioner[],
    loading: boolean,
    setModal: React.Dispatch<React.SetStateAction<IModal>>
}

export const ProfesionalsList = ({ practitionerList, loading, setModal }: IProps) => {

    const removePractitioner = async (practitioner: IPractitioner) => {
        const response = await patchFhirResource(practitioner.id || '', 'Practitioner', '/active', JSON.stringify(false))
        if(!response) return await swal('No hemos podido desactivar el médico seleccionado', 'Por favor, intente nuevamente', 'warning')
        return await swal('Médico desactivado correctamente', '', 'success')
    } 

    const getRow = (practitioner: IPractitioner) => {
        const completeName = practitioner.name?.find(name => name.use === 'usual')
        const name = completeName?.given?.[0]
        const surname = completeName?.family
        const tel = practitioner.telecom?.find(tel => tel.system === 'phone')?.value
        const email = practitioner.telecom?.find(tel => tel.system === 'email')?.value
        const country = practitioner.address?.find(address => address.type = 'physical')?.country
        return (<tr className={style.rowData} key={practitioner.id}>
            <td>{name || ''}</td>
            <td>{surname || ''}</td>
            <td>{tel || ''}</td>
            <td>{email || ''}</td>
            <td>{country || ''}</td>
            <td><span onClick={() => setModal({
                edit: practitioner,
                create: false
            })}>editar</span></td>
            <td><span onClick={() => removePractitioner(practitioner)}>
                Eliminar
                </span></td>
        </tr>)
    }

    return (
        <div className={style.tableContainer}>
            <table className={style.table}>
                <thead>
                    <tr>
                        <th>Nombre</th>
                        <th>Apellido</th>
                        <th>Telefono</th>
                        <th>Email</th>
                        <th>Pais</th>
                        <th>Editar</th>
                        <th>Eliminar</th>
                    </tr>
                </thead>
                <tbody>
                    {loading ?
                        <Loader color='text-primary' size={32} />
                        :
                        !practitionerList?.length ? <tr className={style.tbodyWithoutData}>No hemos encontrado ningun resultado</tr>
                            :
                            practitionerList.map(patient => getRow(patient))
                    }
                </tbody>
            </table>
        </div>
    )
}
