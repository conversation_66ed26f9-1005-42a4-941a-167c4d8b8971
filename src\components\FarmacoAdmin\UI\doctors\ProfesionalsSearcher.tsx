import { getResourceByFilters } from '@/components/MyPatients/infraestructure/services/getResourceByFilters'
import React from 'react'
import { useForm } from 'react-hook-form'
import { IPractitioner } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPractitioner'
import { Text, Icon, Loader } from 'occipital-new'
import style from '../../styles/profesionalsSearcher.module.scss'

interface IProps{
    setPractitionersList: React.Dispatch<React.SetStateAction<IPractitioner[]>>,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    loading: boolean
}

interface IForm{
    search: string,
}

export const ProfesionalsSearcher = ({ setPractitionersList, setLoading, loading }: IProps) => {
    const { register, handleSubmit } = useForm<IForm>()
    const searchPatient = async (data: IForm) =>{
        try {
            setLoading(true)
            const response = await getResourceByFilters<IPractitioner>('Practitioner', `name=${data?.search?.trim()}`)
            setPractitionersList(response.data?.filter((item) => item.resource?.active !== false).map((item) => item.resource ) || [])
        } catch (error) {
          setPractitionersList([])
        } finally {
            setLoading(false)
        }
    }

  return (
    <form className={style.searcherContainer} onSubmit={handleSubmit(searchPatient)}>
        <Text tag='h3' weight='bold' size='m' color='text-primary'>Buscar médico</Text>
        <div className={style.inputContainer}>   
            <input type='text' placeholder='Nombre'  {...register('search')} />
            <div className={style.buttonContainer}>
                <div className={style.button}>
                    <button type='submit'>{loading ? 'Cargando' : 'Buscar'}</button>
                    {loading ? <Loader size={12} color='background-light'/> : <Icon name='arrowFoward' size='xs' color='background-light' />}
                </div>
            </div>
        </div> 
    </form>
  )
}
