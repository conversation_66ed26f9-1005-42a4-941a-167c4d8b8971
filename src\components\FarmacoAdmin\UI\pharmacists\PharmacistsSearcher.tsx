import { getResourceByFilters } from '@/components/MyPatients/infraestructure/services/getResourceByFilters'
import React from 'react'
import { useForm } from 'react-hook-form'
import { IPractitioner } from '@smile-cdr/fhirts/dist/FHIR-R4/interfaces/IPractitioner'
import { Text, Icon, Loader } from 'occipital-new'
import { stringMatcher } from '@/utils/stringMatcher';
import style from '../../styles/profesionalsSearcher.module.scss'

interface IProps {
    setPractitionersList: React.Dispatch<React.SetStateAction<IPractitioner[]>>,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    loading: boolean
}

interface IForm {
    search: string,
}

export const PharmacistsSearcher = ({ setPractitionersList, setLoading, loading }: IProps) => {
    const { register, handleSubmit } = useForm<IForm>()
    const searchPatient = async (data: IForm) => {
        try {
            setLoading(true)
            const response = await getResourceByFilters<IPractitioner>('Practitioner', `active:not=${false}&_count=${1000}`)
            setPractitionersList(response.data?.filter((item) => (item.resource?.extension?.some(ext => ext?.valueString?.toLowerCase() === 'farmaceutico')) && item.resource?.identifier?.some(identifier => !!identifier?.value && stringMatcher(identifier.value, data?.search)))?.map((item) => item.resource) || [])
        } catch (error) {
            setPractitionersList([])
        } finally {
            setLoading(false)
        }
    }

    return (
        <form className={style.searcherContainer} onSubmit={handleSubmit(searchPatient)}>
            <Text tag='h3' weight='bold' size='m' color='text-primary'>Buscar farmaceutico</Text>
            <div className={style.inputContainer}>
                <input type='text' placeholder='Nombre'  {...register('search')} />
                <div className={style.buttonContainer}>
                    <div className={style.button}>
                        <button type='submit'>{loading ? 'Cargando' : 'Buscar'}</button>
                        {loading ? <Loader size={12} color='background-light' /> : <Icon name='arrowFoward' size='xs' color='background-light' />}
                    </div>
                </div>
            </div>
        </form>
    )
}
