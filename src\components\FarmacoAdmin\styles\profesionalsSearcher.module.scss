.searcherContainer{
    background-color: white;
    width: 100%;
    margin: 12px 0;
    padding: 32px 24px;
    border-radius: 36px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.inputContainer{
    width: 100%;
    padding: 4px;
    display: flex;
    justify-content: space-between;
    input{
        width: 80%;
        border-bottom: 1px solid black;
        padding: 4px 0;
        outline: none;
    }
    .buttonContainer{
        width: 15%;
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
    }
}

.button{
    width: 100%;
    padding: 12px 32px;
    background-color: #0A91E4;
    color: white;
    height: max-content;
    border-radius: 13px;
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
    &:hover{
        cursor: pointer;
    }
}